{"version": 3, "sources": ["../../@ant-design/colors/es/generate.js", "../../@ant-design/colors/es/index.js"], "sourcesContent": ["import { inputToRGB, rgbToHex, rgbToHsv } from '@ctrl/tinycolor';\nvar hueStep = 2; // 色相阶梯\nvar saturationStep = 0.16; // 饱和度阶梯，浅色部分\nvar saturationStep2 = 0.05; // 饱和度阶梯，深色部分\nvar brightnessStep1 = 0.05; // 亮度阶梯，浅色部分\nvar brightnessStep2 = 0.15; // 亮度阶梯，深色部分\nvar lightColorCount = 5; // 浅色数量，主色上\nvar darkColorCount = 4; // 深色数量，主色下\n// 暗色主题颜色映射关系表\nvar darkColorMap = [{\n  index: 7,\n  opacity: 0.15\n}, {\n  index: 6,\n  opacity: 0.25\n}, {\n  index: 5,\n  opacity: 0.3\n}, {\n  index: 5,\n  opacity: 0.45\n}, {\n  index: 5,\n  opacity: 0.65\n}, {\n  index: 5,\n  opacity: 0.85\n}, {\n  index: 4,\n  opacity: 0.9\n}, {\n  index: 3,\n  opacity: 0.95\n}, {\n  index: 2,\n  opacity: 0.97\n}, {\n  index: 1,\n  opacity: 0.98\n}];\n// Wrapper function ported from TinyColor.prototype.toHsv\n// Keep it here because of `hsv.h * 360`\nfunction toHsv(_ref) {\n  var r = _ref.r,\n    g = _ref.g,\n    b = _ref.b;\n  var hsv = rgbToHsv(r, g, b);\n  return {\n    h: hsv.h * 360,\n    s: hsv.s,\n    v: hsv.v\n  };\n}\n\n// Wrapper function ported from TinyColor.prototype.toHexString\n// Keep it here because of the prefix `#`\nfunction toHex(_ref2) {\n  var r = _ref2.r,\n    g = _ref2.g,\n    b = _ref2.b;\n  return \"#\".concat(rgbToHex(r, g, b, false));\n}\n\n// Wrapper function ported from TinyColor.prototype.mix, not treeshakable.\n// Amount in range [0, 1]\n// Assume color1 & color2 has no alpha, since the following src code did so.\nfunction mix(rgb1, rgb2, amount) {\n  var p = amount / 100;\n  var rgb = {\n    r: (rgb2.r - rgb1.r) * p + rgb1.r,\n    g: (rgb2.g - rgb1.g) * p + rgb1.g,\n    b: (rgb2.b - rgb1.b) * p + rgb1.b\n  };\n  return rgb;\n}\nfunction getHue(hsv, i, light) {\n  var hue;\n  // 根据色相不同，色相转向不同\n  if (Math.round(hsv.h) >= 60 && Math.round(hsv.h) <= 240) {\n    hue = light ? Math.round(hsv.h) - hueStep * i : Math.round(hsv.h) + hueStep * i;\n  } else {\n    hue = light ? Math.round(hsv.h) + hueStep * i : Math.round(hsv.h) - hueStep * i;\n  }\n  if (hue < 0) {\n    hue += 360;\n  } else if (hue >= 360) {\n    hue -= 360;\n  }\n  return hue;\n}\nfunction getSaturation(hsv, i, light) {\n  // grey color don't change saturation\n  if (hsv.h === 0 && hsv.s === 0) {\n    return hsv.s;\n  }\n  var saturation;\n  if (light) {\n    saturation = hsv.s - saturationStep * i;\n  } else if (i === darkColorCount) {\n    saturation = hsv.s + saturationStep;\n  } else {\n    saturation = hsv.s + saturationStep2 * i;\n  }\n  // 边界值修正\n  if (saturation > 1) {\n    saturation = 1;\n  }\n  // 第一格的 s 限制在 0.06-0.1 之间\n  if (light && i === lightColorCount && saturation > 0.1) {\n    saturation = 0.1;\n  }\n  if (saturation < 0.06) {\n    saturation = 0.06;\n  }\n  return Number(saturation.toFixed(2));\n}\nfunction getValue(hsv, i, light) {\n  var value;\n  if (light) {\n    value = hsv.v + brightnessStep1 * i;\n  } else {\n    value = hsv.v - brightnessStep2 * i;\n  }\n  if (value > 1) {\n    value = 1;\n  }\n  return Number(value.toFixed(2));\n}\nexport default function generate(color) {\n  var opts = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var patterns = [];\n  var pColor = inputToRGB(color);\n  for (var i = lightColorCount; i > 0; i -= 1) {\n    var hsv = toHsv(pColor);\n    var colorString = toHex(inputToRGB({\n      h: getHue(hsv, i, true),\n      s: getSaturation(hsv, i, true),\n      v: getValue(hsv, i, true)\n    }));\n    patterns.push(colorString);\n  }\n  patterns.push(toHex(pColor));\n  for (var _i = 1; _i <= darkColorCount; _i += 1) {\n    var _hsv = toHsv(pColor);\n    var _colorString = toHex(inputToRGB({\n      h: getHue(_hsv, _i),\n      s: getSaturation(_hsv, _i),\n      v: getValue(_hsv, _i)\n    }));\n    patterns.push(_colorString);\n  }\n\n  // dark theme patterns\n  if (opts.theme === 'dark') {\n    return darkColorMap.map(function (_ref3) {\n      var index = _ref3.index,\n        opacity = _ref3.opacity;\n      var darkColorString = toHex(mix(inputToRGB(opts.backgroundColor || '#141414'), inputToRGB(patterns[index]), opacity * 100));\n      return darkColorString;\n    });\n  }\n  return patterns;\n}", "import generate from \"./generate\";\nvar presetPrimaryColors = {\n  red: '#F5222D',\n  volcano: '#FA541C',\n  orange: '#FA8C16',\n  gold: '#FAAD14',\n  yellow: '#FADB14',\n  lime: '#A0D911',\n  green: '#52C41A',\n  cyan: '#13C2C2',\n  blue: '#1677FF',\n  geekblue: '#2F54EB',\n  purple: '#722ED1',\n  magenta: '#EB2F96',\n  grey: '#666666'\n};\nvar presetPalettes = {};\nvar presetDarkPalettes = {};\nObject.keys(presetPrimaryColors).forEach(function (key) {\n  presetPalettes[key] = generate(presetPrimaryColors[key]);\n  presetPalettes[key].primary = presetPalettes[key][5];\n\n  // dark presetPalettes\n  presetDarkPalettes[key] = generate(presetPrimaryColors[key], {\n    theme: 'dark',\n    backgroundColor: '#141414'\n  });\n  presetDarkPalettes[key].primary = presetDarkPalettes[key][5];\n});\nvar red = presetPalettes.red;\nvar volcano = presetPalettes.volcano;\nvar gold = presetPalettes.gold;\nvar orange = presetPalettes.orange;\nvar yellow = presetPalettes.yellow;\nvar lime = presetPalettes.lime;\nvar green = presetPalettes.green;\nvar cyan = presetPalettes.cyan;\nvar blue = presetPalettes.blue;\nvar geekblue = presetPalettes.geekblue;\nvar purple = presetPalettes.purple;\nvar magenta = presetPalettes.magenta;\nvar grey = presetPalettes.grey;\nvar gray = presetPalettes.grey;\nexport { generate, presetPalettes, presetDarkPalettes, presetPrimaryColors, red, volcano, orange, gold, yellow, lime, green, cyan, blue, geekblue, purple, magenta, grey, gray };"], "mappings": ";;;;;;;;;;;;AA0CA,SAAS,MAAM,MAAM;AACnB,MAAI,IAAI,KAAK,GACX,IAAI,KAAK,GACT,IAAI,KAAK;AACX,MAAI,MAAM,SAAS,GAAG,GAAG,CAAC;AAC1B,SAAO;AAAA,IACL,GAAG,IAAI,IAAI;AAAA,IACX,GAAG,IAAI;AAAA,IACP,GAAG,IAAI;AAAA,EACT;AACF;AAIA,SAAS,MAAM,OAAO;AACpB,MAAI,IAAI,MAAM,GACZ,IAAI,MAAM,GACV,IAAI,MAAM;AACZ,SAAO,IAAI,OAAO,SAAS,GAAG,GAAG,GAAG,KAAK,CAAC;AAC5C;AAKA,SAAS,IAAI,MAAM,MAAM,QAAQ;AAC/B,MAAI,IAAI,SAAS;AACjB,MAAI,MAAM;AAAA,IACR,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK;AAAA,IAChC,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK;AAAA,IAChC,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK;AAAA,EAClC;AACA,SAAO;AACT;AACA,SAAS,OAAO,KAAK,GAAG,OAAO;AAC7B,MAAI;AAEJ,MAAI,KAAK,MAAM,IAAI,CAAC,KAAK,MAAM,KAAK,MAAM,IAAI,CAAC,KAAK,KAAK;AACvD,UAAM,QAAQ,KAAK,MAAM,IAAI,CAAC,IAAI,UAAU,IAAI,KAAK,MAAM,IAAI,CAAC,IAAI,UAAU;AAAA,EAChF,OAAO;AACL,UAAM,QAAQ,KAAK,MAAM,IAAI,CAAC,IAAI,UAAU,IAAI,KAAK,MAAM,IAAI,CAAC,IAAI,UAAU;AAAA,EAChF;AACA,MAAI,MAAM,GAAG;AACX,WAAO;AAAA,EACT,WAAW,OAAO,KAAK;AACrB,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,SAAS,cAAc,KAAK,GAAG,OAAO;AAEpC,MAAI,IAAI,MAAM,KAAK,IAAI,MAAM,GAAG;AAC9B,WAAO,IAAI;AAAA,EACb;AACA,MAAI;AACJ,MAAI,OAAO;AACT,iBAAa,IAAI,IAAI,iBAAiB;AAAA,EACxC,WAAW,MAAM,gBAAgB;AAC/B,iBAAa,IAAI,IAAI;AAAA,EACvB,OAAO;AACL,iBAAa,IAAI,IAAI,kBAAkB;AAAA,EACzC;AAEA,MAAI,aAAa,GAAG;AAClB,iBAAa;AAAA,EACf;AAEA,MAAI,SAAS,MAAM,mBAAmB,aAAa,KAAK;AACtD,iBAAa;AAAA,EACf;AACA,MAAI,aAAa,MAAM;AACrB,iBAAa;AAAA,EACf;AACA,SAAO,OAAO,WAAW,QAAQ,CAAC,CAAC;AACrC;AACA,SAAS,SAAS,KAAK,GAAG,OAAO;AAC/B,MAAI;AACJ,MAAI,OAAO;AACT,YAAQ,IAAI,IAAI,kBAAkB;AAAA,EACpC,OAAO;AACL,YAAQ,IAAI,IAAI,kBAAkB;AAAA,EACpC;AACA,MAAI,QAAQ,GAAG;AACb,YAAQ;AAAA,EACV;AACA,SAAO,OAAO,MAAM,QAAQ,CAAC,CAAC;AAChC;AACe,SAAR,SAA0B,OAAO;AACtC,MAAI,OAAO,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAChF,MAAI,WAAW,CAAC;AAChB,MAAI,SAAS,WAAW,KAAK;AAC7B,WAAS,IAAI,iBAAiB,IAAI,GAAG,KAAK,GAAG;AAC3C,QAAI,MAAM,MAAM,MAAM;AACtB,QAAI,cAAc,MAAM,WAAW;AAAA,MACjC,GAAG,OAAO,KAAK,GAAG,IAAI;AAAA,MACtB,GAAG,cAAc,KAAK,GAAG,IAAI;AAAA,MAC7B,GAAG,SAAS,KAAK,GAAG,IAAI;AAAA,IAC1B,CAAC,CAAC;AACF,aAAS,KAAK,WAAW;AAAA,EAC3B;AACA,WAAS,KAAK,MAAM,MAAM,CAAC;AAC3B,WAAS,KAAK,GAAG,MAAM,gBAAgB,MAAM,GAAG;AAC9C,QAAI,OAAO,MAAM,MAAM;AACvB,QAAI,eAAe,MAAM,WAAW;AAAA,MAClC,GAAG,OAAO,MAAM,EAAE;AAAA,MAClB,GAAG,cAAc,MAAM,EAAE;AAAA,MACzB,GAAG,SAAS,MAAM,EAAE;AAAA,IACtB,CAAC,CAAC;AACF,aAAS,KAAK,YAAY;AAAA,EAC5B;AAGA,MAAI,KAAK,UAAU,QAAQ;AACzB,WAAO,aAAa,IAAI,SAAU,OAAO;AACvC,UAAI,QAAQ,MAAM,OAChB,UAAU,MAAM;AAClB,UAAI,kBAAkB,MAAM,IAAI,WAAW,KAAK,mBAAmB,SAAS,GAAG,WAAW,SAAS,KAAK,CAAC,GAAG,UAAU,GAAG,CAAC;AAC1H,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AACA,SAAO;AACT;AAlKA,IACI,SACA,gBACA,iBACA,iBACA,iBACA,iBACA,gBAEA;AATJ;AAAA;AAAA;AACA,IAAI,UAAU;AACd,IAAI,iBAAiB;AACrB,IAAI,kBAAkB;AACtB,IAAI,kBAAkB;AACtB,IAAI,kBAAkB;AACtB,IAAI,kBAAkB;AACtB,IAAI,iBAAiB;AAErB,IAAI,eAAe,CAAC;AAAA,MAClB,OAAO;AAAA,MACP,SAAS;AAAA,IACX,GAAG;AAAA,MACD,OAAO;AAAA,MACP,SAAS;AAAA,IACX,GAAG;AAAA,MACD,OAAO;AAAA,MACP,SAAS;AAAA,IACX,GAAG;AAAA,MACD,OAAO;AAAA,MACP,SAAS;AAAA,IACX,GAAG;AAAA,MACD,OAAO;AAAA,MACP,SAAS;AAAA,IACX,GAAG;AAAA,MACD,OAAO;AAAA,MACP,SAAS;AAAA,IACX,GAAG;AAAA,MACD,OAAO;AAAA,MACP,SAAS;AAAA,IACX,GAAG;AAAA,MACD,OAAO;AAAA,MACP,SAAS;AAAA,IACX,GAAG;AAAA,MACD,OAAO;AAAA,MACP,SAAS;AAAA,IACX,GAAG;AAAA,MACD,OAAO;AAAA,MACP,SAAS;AAAA,IACX,CAAC;AAAA;AAAA;;;ACvCD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IACI,qBAeA,gBACA,oBAYA,KACA,SACA,MACA,QACA,QACA,MACA,OACA,MACA,MACA,UACA,QACA,SACA,MACA;AA1CJ;AAAA;AAAA;AACA,IAAI,sBAAsB;AAAA,MACxB,KAAK;AAAA,MACL,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,OAAO;AAAA,MACP,MAAM;AAAA,MACN,MAAM;AAAA,MACN,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,MAAM;AAAA,IACR;AACA,IAAI,iBAAiB,CAAC;AACtB,IAAI,qBAAqB,CAAC;AAC1B,WAAO,KAAK,mBAAmB,EAAE,QAAQ,SAAU,KAAK;AACtD,qBAAe,GAAG,IAAI,SAAS,oBAAoB,GAAG,CAAC;AACvD,qBAAe,GAAG,EAAE,UAAU,eAAe,GAAG,EAAE,CAAC;AAGnD,yBAAmB,GAAG,IAAI,SAAS,oBAAoB,GAAG,GAAG;AAAA,QAC3D,OAAO;AAAA,QACP,iBAAiB;AAAA,MACnB,CAAC;AACD,yBAAmB,GAAG,EAAE,UAAU,mBAAmB,GAAG,EAAE,CAAC;AAAA,IAC7D,CAAC;AACD,IAAI,MAAM,eAAe;AACzB,IAAI,UAAU,eAAe;AAC7B,IAAI,OAAO,eAAe;AAC1B,IAAI,SAAS,eAAe;AAC5B,IAAI,SAAS,eAAe;AAC5B,IAAI,OAAO,eAAe;AAC1B,IAAI,QAAQ,eAAe;AAC3B,IAAI,OAAO,eAAe;AAC1B,IAAI,OAAO,eAAe;AAC1B,IAAI,WAAW,eAAe;AAC9B,IAAI,SAAS,eAAe;AAC5B,IAAI,UAAU,eAAe;AAC7B,IAAI,OAAO,eAAe;AAC1B,IAAI,OAAO,eAAe;AAAA;AAAA;", "names": []}