import{j as e,av as p,ax as f,ay as y,R as b,aB as v,aC as C,aD as w,a6 as S,aE as k,r as d,u as z}from"./index-C8YzRPez.js";import{P}from"./progress-DT8MvaHz.js";import{S as R}from"./SimplePieChart-wfHmodzB.js";import{T as F}from"./Timeline-x8bERb7R.js";function I({role:o,timetaken:t,progress:s,status:n,color:l}){return e.jsx("div",{children:e.jsxs("div",{style:{display:"flex",justifyContent:"flex-start",marginTop:"20px",flexDirection:"column"},children:[e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"10px",justifyContent:"space-between"},children:[e.jsx("p",{style:{fontSize:"16px",margin:0,fontWeight:500},children:o}),e.jsxs("p",{style:{fontSize:"14px",margin:0,display:"flex",alignItems:"center",gap:"5px"},children:[e.jsx(p,{icon:"weui:time-outlined",width:"18",height:"18"})," ",t]})]}),e.jsx(P,{percent:s,status:n,showInfo:!1,strokeColor:l})]})})}function W({title:o,chartData:t}){const n={chart:{type:"column",height:440},title:{text:""},xAxis:{categories:["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],title:{text:""},labels:{style:{fontFamily:"Poppins",fontSize:"13px"}}},yAxis:{min:0,title:{text:"Number of Candidates",style:{fontFamily:"Poppins",fontSize:"14px"}}},tooltip:{shared:!0,valueSuffix:""},legend:{itemStyle:{fontFamily:"Poppins",fontSize:"13px"}},plotOptions:{column:{grouping:!0,stacking:"normal",borderRadius:5,dataLabels:{enabled:!0,style:{fontFamily:"Poppins",fontSize:"11px",fontWeight:"bold",fontStyle:"normal"}}}},series:t};return e.jsxs("div",{style:{flexGrow:1},children:[e.jsx("div",{style:{display:"flex",justifyContent:"space-between",marginBottom:"10px"},children:e.jsx("p",{style:{fontSize:"20px",fontWeight:600},children:o})}),e.jsx(f,{highcharts:y,options:n})]})}const D=b.forwardRef(function(t,s){return e.jsx(v,{direction:"left",ref:s,...t,timeout:{enter:1e3,exit:500},easing:{enter:"cubic-bezier(0.4, 0, 0.2, 1)",exit:"cubic-bezier(0.4, 0, 0.2, 1)"}})});function L({open:o,onClose:t}){return e.jsx("div",{children:e.jsxs(C,{open:o,onClose:t,TransitionComponent:D,keepMounted:!0,fullScreen:!0,PaperProps:{sx:{ml:"auto",mt:0,mr:0,width:"85%",height:"100vh",overflowY:"hidden",borderRadius:0}},BackdropProps:{sx:{backgroundColor:"rgba(0, 0, 0, 0.5)"}},children:[e.jsx(w,{children:e.jsxs(S,{display:"flex",justifyContent:"space-between",alignItems:"center",children:[e.jsx("p",{style:{fontSize:"22px",fontWeight:"bold",fontFamily:"Poppins"},children:"Candidates Details"}),e.jsx(p,{icon:"material-symbols:close-rounded",width:"24",height:"24",style:{cursor:"pointer"},onClick:t})]})}),e.jsx(k,{sx:{width:"100%",overflowY:"auto","&::-webkit-scrollbar":{width:"5px",height:"5px"},"&::-webkit-scrollbar-thumb":{backgroundColor:"#d9d9d9",borderRadius:"4px"},"&::-webkit-scrollbar-thumb:hover":{backgroundColor:"#cccccc"},"&::-webkit-scrollbar-track":{backgroundColor:"#f0f0f0"}}})]})})}function T({chartData:o,title:t,showDetails:s}){const n=o.map(r=>r.month),a=Array.from(new Set(o.flatMap(r=>r.data.map(i=>i.name)))).map(r=>{var i,u;return{name:r,data:n.map(x=>{const h=o.find(g=>g.month===x),j=h==null?void 0:h.data.find(g=>g.name===r);return j?j.count:0}),color:(u=(i=o.find(x=>x.data.find(h=>h.name===r)))==null?void 0:i.data.find(x=>x.name===r))==null?void 0:u.color}}),c={chart:{type:"column",height:440},title:{text:""},xAxis:{categories:n,title:{text:""},labels:{style:{fontFamily:"Poppins",fontSize:"13px"}}},yAxis:{min:0,title:{text:"Number of Roles",style:{fontFamily:"Poppins",fontSize:"14px"}}},tooltip:{shared:!0,valueSuffix:" roles"},legend:{itemStyle:{fontFamily:"Poppins",fontSize:"13px"}},plotOptions:{column:{grouping:!0,borderRadius:5,dataLabels:{enabled:!0,style:{fontFamily:"Poppins",fontSize:"12px"}}}},series:a};return e.jsxs("div",{style:{flexGrow:1},children:[e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",marginBottom:"10px"},children:[e.jsx("p",{style:{fontSize:"20px",fontWeight:600},children:t}),s&&e.jsxs("p",{style:{fontSize:"16px",fontWeight:400,color:"#489eea",cursor:"pointer",textDecoration:"underline",display:"flex",alignItems:"center",gap:"5px"},children:["View Details ",e.jsx(p,{icon:"material-symbols-light:arrow-forward-rounded",width:"20",height:"20"})]})]}),e.jsx(f,{highcharts:y,options:c})]})}function E({title:o,chartData:t}){const[s,n]=d.useState([]),[l,a]=d.useState(!1);d.useEffect(()=>{t.length>0&&n(t)},[t]);const c={chart:{type:"pie",height:400,zooming:{type:"xy"},panning:{enabled:!0,type:"xy"},panKey:"shift"},title:{text:""},tooltip:{valueSuffix:"",headerFormat:"",pointFormat:"<b>{point.name}: {point.y}</b> "},subtitle:{text:""},plotOptions:{pie:{allowPointSelect:!0,cursor:"pointer",dataLabels:{enabled:!0,distance:-40,formatter:function(){return["Rejected Cvs"].includes(this.point.name)?`
                  <span style="font-size:14px;">${this.percentage.toFixed(1)}%</span><br/>
                  <span style="font-size:10px;">Click to see details</span>
                `:`<span style="font-size:14px;">${this.percentage.toFixed(1)}%</span>`},style:{fontSize:"12px",color:"black",fontFamily:"Poppins",textOutline:"none"}},showInLegend:!0,point:{events:{click:function(){this.name==="Rejected Cvs"&&(a(!0),n([{name:"By QA",value:10,color:"#fcb92c"},{name:"By Client",value:5,color:"#4eb1fd"},{name:"By ACM",value:10,color:"#a47de8"}]))}}}}},series:[{name:"Pie Chart",colorByPoint:!0,data:s.map(i=>({name:i.name,y:i.value,color:i.color,dataLabels:i.name==="Rejected Cvs"?{enabled:!0,format:'<span style="font-size:14px;">{point.percentage:.1f}%</span><br/><span style="font-size:10px;">Click to see details</span>',style:{fontSize:"12px",color:"black",fontFamily:"Poppins",textOutline:"none"},distance:-60}:{}}))}]},r=()=>{n(t),setCurrentSubheader(subheader),a(!1)};return s.length?e.jsx("div",{children:e.jsxs("div",{style:{flexGrow:1},children:[e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",marginBottom:"10px"},children:[e.jsx("p",{style:{fontSize:"20px",fontWeight:600},children:o}),l&&e.jsx(p,{icon:"ep:back",style:{fontSize:"25px",cursor:"pointer"},onClick:r})]}),e.jsx("div",{style:{marginTop:"-30px"},children:e.jsx(f,{highcharts:y,options:c})})]})}):e.jsx("div",{children:"Loading..."})}function O({title:o,chartData:t}){const[s,n]=d.useState([]),[l,a]=d.useState(!1);d.useEffect(()=>{t.length>0&&n(t)},[t]);const c={chart:{type:"pie",height:380,zooming:{type:"xy"},panning:{enabled:!0,type:"xy"},panKey:"shift"},title:{text:""},tooltip:{valueSuffix:"",headerFormat:"",pointFormat:"<b>{point.name}: {point.y}</b> "},subtitle:{text:""},plotOptions:{pie:{allowPointSelect:!0,cursor:"pointer",dataLabels:{enabled:!0,distance:-40,formatter:function(){return["Open to Work","Simple","With Emails"].includes(this.point.name)?`
                  <span style="font-size:14px;">${this.percentage.toFixed(1)}%</span><br/>
                  <span style="font-size:10px;">Click to see details</span>
                `:`<span style="font-size:14px;">${this.percentage.toFixed(1)}%</span>`},style:{fontSize:"12px",color:"black",fontFamily:"Poppins",textOutline:"none"}},showInLegend:!0,point:{events:{click:function(){this.name==="Open to Work"||this.name==="Simple"?(a(!0),n([{name:"With Emails",value:10,color:"#24e6a4"},{name:"Without Emails",value:5,color:"#ef6f5e"}])):this.name==="With Emails"&&(a(!0),n([{name:"Bounced Emails",value:8,color:"#ff751a"},{name:"Not Bounced Emails",value:2,color:"#fcb92c"}]))}}}}},series:[{name:"Pie Chart",colorByPoint:!0,data:s.map(i=>({name:i.name,y:i.value,color:i.color,dataLabels:i.name==="Open to Work"?{enabled:!0,format:'<span style="font-size:14px;">{point.percentage:.1f}%</span><br/><span style="font-size:10px;">Click to see details</span>',style:{fontSize:"12px",color:"black",fontFamily:"Poppins",textOutline:"none"},distance:-60}:i.name==="Simple"?{enabled:!0,format:'<span style="font-size:14px;">{point.percentage:.1f}%</span><br/><span style="font-size:10px;">Click to see details</span>',style:{fontSize:"12px",color:"black",fontFamily:"Poppins",textOutline:"none"},distance:-60}:i.name==="With Emails"?{enabled:!0,format:'<span style="font-size:14px;">{point.percentage:.1f}%</span><br/><span style="font-size:10px;">Click to see details</span>',style:{fontSize:"12px",color:"black",fontFamily:"Poppins",textOutline:"none"},distance:-60}:{}}))}]},r=()=>{n(t),setCurrentSubheader(subheader),a(!1)};return s.length?e.jsx("div",{children:e.jsxs("div",{style:{flexGrow:1},children:[e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",marginBottom:"10px"},children:[e.jsx("p",{style:{fontSize:"20px",fontWeight:600},children:o}),l&&e.jsx(p,{icon:"ep:back",style:{fontSize:"25px",cursor:"pointer"},onClick:r})]}),e.jsx("div",{style:{marginTop:"-30px"},children:e.jsx(f,{highcharts:y,options:c})})]})}):e.jsx("div",{children:"Loading..."})}const m=[{role:"Designer - Client 50",timeTaken:"25 mints",progress:30,status:"active",color:"#2caffe"},{role:"Doctor - Client 50",timeTaken:"25 mints",progress:10,status:"active",color:"#2caffe"},{role:"Developer - Client 50",timeTaken:"1 hr 30 mints",progress:100,status:"",color:"#24e6a4"},{role:"Manager - Client 12",timeTaken:"25 mints",progress:100,status:"",color:"#24e6a4"},{role:"AI Engineer - Client 12",timeTaken:"25 mints",progress:100,status:"",color:"#24e6a4"},{role:"ML Engineer - Client 12",timeTaken:"25 mints",progress:100,status:"exception",color:"#ef6f5e"}],A=[{name:"In-Progress",y:20,color:"#2caffe",count:60},{name:"Done",y:20,color:"#41cc83",count:60},{name:"Issue",y:20,color:"#ef6e5d",count:30},{name:"Paused",y:20,color:"#fcb92c",count:80},{name:"Cancelled",y:20,color:"#a47de8",count:60},{name:"Left Roles",y:20,color:"#ff751a",count:90}],B=[{name:"Approved - CVs",stack:"Approved",data:[10,8,12,15,10,5],color:"#4eb1fd"},{name:"Approved - LIS",stack:"Approved",data:[10,7,13,15,10,5],color:"#24e6a4"},{name:"Rejected - CVs",stack:"Rejected",data:[5,9,13,5,8,8],color:"#ef6e5d"},{name:"Rejected - LIS",stack:"Rejected",data:[5,5,7,5,3,8],color:"#a47de8"}],M=[{date:"14/11/24",time:"08:00 PM",by:"Started working on Designer role."},{date:"14/11/24",time:"05:00 PM",by:"Added query for role Designer"},{date:"14/11/24",time:"03:00 PM",by:"Marked Manufacturer role as done"},{date:"14/11/24",time:"02:00 PM",by:"Left Developer role."},{date:"14/11/24",time:"12:02 PM",by:"Added suggestion for role Designer"},{date:"14/11/24",time:"12:00 PM",by:"Started working on Developer role."}],V=[{month:"January",data:[{name:"Total Roles",count:18,color:"#2caffe"},{name:"CVs",count:6,color:"#fcb92c"},{name:"LIs",count:12,color:"#00e272"}]},{month:"February",data:[{name:"Total Roles",count:18,color:"#2caffe"},{name:"CVs",count:6,color:"#fcb92c"},{name:"LIs",count:12,color:"#00e272"}]},{month:"March",data:[{name:"Total Roles",count:18,color:"#2caffe"},{name:"CVs",count:6,color:"#fcb92c"},{name:"LIs",count:12,color:"#00e272"}]},{month:"April",data:[{name:"Total Roles",count:2,color:"#2caffe"},{name:"LIs",count:8,color:"#00e272"},{name:"CVs",count:6,color:"#fcb92c"}]}],N=[{name:"Accepted CVs",value:65,color:"#24e6a4"},{name:"Rejected Cvs",value:35,color:"#ef6f5e"}],H=[{name:"Open to Work",value:50,color:"#4eb1fd"},{name:"Simple",value:50,color:"#a47de8"}];function Y(o){const t=z(),[s,n]=d.useState(!1);return e.jsxs("div",{style:{width:"100%"},children:[e.jsx("div",{style:{borderRadius:15,overflow:"hidden",backgroundColor:"white",padding:"15px"},children:e.jsx("h2",{style:{margin:0},children:"Welcome Back User!"})}),e.jsxs("div",{style:{display:"flex",marginTop:20},children:[e.jsxs("div",{style:{width:"32%",borderRadius:15,overflow:"hidden",backgroundColor:"white",padding:"20px",height:"550px"},children:[e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.jsx("p",{style:{fontSize:"20px",fontWeight:600,margin:0},children:"Today Progress"}),e.jsxs("div",{style:{display:"flex",gap:"15px",alignItems:"center"},children:[e.jsxs("div",{style:{display:"flex",gap:"5px",alignItems:"center"},children:[e.jsx("div",{style:{borderRadius:"50%",width:"15px",height:"15px",backgroundColor:"#2caffe"}}),e.jsx("p",{style:{fontSize:"14px"},children:"In Progress"})]}),e.jsxs("div",{style:{display:"flex",gap:"5px",alignItems:"center"},children:[e.jsx("div",{style:{borderRadius:"50%",width:"15px",height:"15px",backgroundColor:"#24e6a4"}}),e.jsx("p",{style:{fontSize:"14px"},children:"Done"})]}),e.jsxs("div",{style:{display:"flex",gap:"5px",alignItems:"center"},children:[e.jsx("div",{style:{borderRadius:"50%",width:"15px",height:"15px",backgroundColor:"#ef6f5e"}}),e.jsx("p",{style:{fontSize:"14px"},children:"Issue"})]})]})]}),m==null?void 0:m.map((l,a)=>e.jsx(I,{role:l.role,timetaken:l.timeTaken,progress:l.progress,status:l.status,color:l.color},a))]}),e.jsx("div",{style:{width:"40%",borderRadius:15,overflow:"hidden",backgroundColor:"white",padding:"15px",height:"550px",marginLeft:"1%"},children:e.jsx("div",{style:{marginTop:"-15px"},children:e.jsx(T,{chartData:V,title:"Monthly Roles Overview",showDetails:!1})})}),e.jsx("div",{style:{width:"28%",borderRadius:15,overflow:"hidden",backgroundColor:"white",padding:"15px",height:"550px",marginLeft:"1%"},children:e.jsx("div",{style:{marginTop:"-15px"},children:e.jsx(R,{chartData:A,title:"Current Month Roles",onDetailsClick:()=>t("/trial-roles/all-roles"),labelMarginTop:"20px",showDetails:!0})})})]}),e.jsxs("div",{style:{display:"flex",marginTop:20},children:[e.jsx("div",{style:{width:"42%",borderRadius:15,overflow:"hidden",backgroundColor:"white",padding:"15px",height:"550px"},children:e.jsx("div",{style:{marginTop:"-15px"},children:e.jsx(W,{chartData:B,title:"Weekly Candidates Overview"})})}),e.jsxs("div",{style:{width:"29%",borderRadius:15,overflow:"hidden",backgroundColor:"white",padding:"15px",height:"550px",marginLeft:"1%"},children:[e.jsx("p",{style:{fontSize:"20px",fontWeight:600,marginTop:"-1px"},children:"Role Notifications"}),e.jsxs("div",{style:{display:"flex",alignItems:"center",padding:"15px 15px",width:"100%",borderBottom:"2px solid #CED0DA",backgroundColor:"white",marginTop:"-10px"},children:[e.jsx("div",{style:{width:"20%"},children:e.jsx("div",{style:{position:"relative",backgroundColor:"white"},children:e.jsx("div",{style:{width:"56px",height:"56px",fontWeight:500,display:"flex",justifyContent:"center",alignItems:"center",borderRadius:"50%",marginLeft:"-5px",fontFamily:"Poppins",backgroundColor:"#fee8cd",color:"#b99d7b"},children:e.jsx("p",{style:{fontSize:"18px",margin:0},children:"H"})})})}),e.jsxs("div",{style:{width:"50%"},children:[e.jsx("p",{style:{color:"#676C7E",fontWeight:500,fontSize:"13px",fontFamily:"Poppins",margin:0},children:"Hania Saeed"}),e.jsx("div",{style:{display:"flex",gap:"5px",alignItems:"center"},children:e.jsx("p",{style:{color:"#676C7E",fontWeight:500,fontSize:"10px",fontFamily:"Poppins",margin:0},children:"Resolved your query on Manager Role 1s ago"})})]}),e.jsx("div",{children:e.jsx("p",{style:{color:"white",fontWeight:500,fontSize:"12px",fontFamily:"Poppins",display:"flex",justifyContent:"center",alignItems:"center",marginLeft:"30px",width:"70px",height:"30px",backgroundColor:"#1a84de",cursor:"pointer"},onClick:()=>t("/CV Sourcing/1/role-details"),children:"View"})})]}),e.jsxs("div",{style:{display:"flex",alignItems:"center",padding:"15px 15px",width:"100%",borderBottom:"2px solid #CED0DA",backgroundColor:"white"},children:[e.jsx("div",{style:{width:"20%"},children:e.jsx("div",{style:{position:"relative",backgroundColor:"white"},children:e.jsx("div",{style:{width:"56px",height:"56px",fontWeight:500,display:"flex",justifyContent:"center",alignItems:"center",borderRadius:"50%",marginLeft:"-5px",fontFamily:"Poppins",backgroundColor:"#fee8cd",color:"#b99d7b"},children:e.jsx("p",{style:{fontSize:"18px",margin:0},children:"H"})})})}),e.jsxs("div",{style:{width:"50%"},children:[e.jsx("p",{style:{color:"#676C7E",fontWeight:500,fontSize:"13px",fontFamily:"Poppins",margin:0},children:"Hania Saeed"}),e.jsx("div",{style:{display:"flex",gap:"5px",alignItems:"center"},children:e.jsx("p",{style:{color:"#676C7E",fontWeight:500,fontSize:"10px",fontFamily:"Poppins",margin:0},children:"Paused role you are working on."})})]}),e.jsx("div",{children:e.jsx("p",{style:{color:"white",fontWeight:500,fontSize:"12px",fontFamily:"Poppins",display:"flex",justifyContent:"center",alignItems:"center",marginLeft:"30px",width:"70px",height:"30px",backgroundColor:"#1a84de",cursor:"pointer"},onClick:()=>t("/CV Sourcing/1/role-details"),children:"View"})})]}),e.jsxs("div",{style:{display:"flex",alignItems:"center",padding:"15px 15px",width:"100%",borderBottom:"2px solid #CED0DA",backgroundColor:"white"},children:[e.jsx("div",{style:{width:"20%"},children:e.jsx("div",{style:{position:"relative",backgroundColor:"white"},children:e.jsx("div",{style:{width:"56px",height:"56px",fontWeight:500,display:"flex",justifyContent:"center",alignItems:"center",borderRadius:"50%",marginLeft:"-5px",fontFamily:"Poppins",backgroundColor:"#fee8cd",color:"#b99d7b"},children:e.jsx("p",{style:{fontSize:"18px",margin:0},children:"H"})})})}),e.jsxs("div",{style:{width:"50%"},children:[e.jsx("p",{style:{color:"#676C7E",fontWeight:500,fontSize:"13px",fontFamily:"Poppins",margin:0},children:"Hania Saeed"}),e.jsx("div",{style:{display:"flex",gap:"5px",alignItems:"center"},children:e.jsx("p",{style:{color:"#676C7E",fontWeight:500,fontSize:"10px",fontFamily:"Poppins",margin:0},children:"Resume your paused role."})})]}),e.jsx("div",{children:e.jsx("p",{style:{color:"white",fontWeight:500,fontSize:"12px",fontFamily:"Poppins",display:"flex",justifyContent:"center",alignItems:"center",marginLeft:"30px",width:"70px",height:"30px",backgroundColor:"#1a84de"},children:"View"})})]}),e.jsxs("div",{style:{display:"flex",alignItems:"center",padding:"15px 15px",width:"100%",borderBottom:"2px solid #CED0DA",backgroundColor:"white"},children:[e.jsx("div",{style:{width:"20%"},children:e.jsx("div",{style:{position:"relative",backgroundColor:"white"},children:e.jsx("div",{style:{width:"56px",height:"56px",fontWeight:500,display:"flex",justifyContent:"center",alignItems:"center",borderRadius:"50%",marginLeft:"-5px",fontFamily:"Poppins",backgroundColor:"#fee8cd",color:"#b99d7b"},children:e.jsx("p",{style:{fontSize:"18px",margin:0},children:"H"})})})}),e.jsxs("div",{style:{width:"50%"},children:[e.jsx("p",{style:{color:"#676C7E",fontWeight:500,fontSize:"13px",fontFamily:"Poppins",margin:0},children:"Hania Saeed"}),e.jsx("div",{style:{display:"flex",gap:"5px",alignItems:"center"},children:e.jsx("p",{style:{color:"#676C7E",fontWeight:500,fontSize:"10px",fontFamily:"Poppins",margin:0},children:"Resolved your issue in the Manager role."})})]}),e.jsx("div",{children:e.jsx("p",{style:{color:"white",fontWeight:500,fontSize:"12px",fontFamily:"Poppins",display:"flex",justifyContent:"center",alignItems:"center",marginLeft:"30px",width:"70px",height:"30px",backgroundColor:"#1a84de"},children:"View"})})]}),e.jsxs("div",{style:{display:"flex",alignItems:"center",padding:"15px 15px",width:"100%",borderBottom:"2px solid #CED0DA",backgroundColor:"white"},children:[e.jsx("div",{style:{width:"20%"},children:e.jsx("div",{style:{position:"relative",backgroundColor:"white"},children:e.jsx("div",{style:{width:"56px",height:"56px",fontWeight:500,display:"flex",justifyContent:"center",alignItems:"center",borderRadius:"50%",marginLeft:"-5px",fontFamily:"Poppins",backgroundColor:"#fee8cd",color:"#b99d7b"},children:e.jsx("p",{style:{fontSize:"18px",margin:0},children:"H"})})})}),e.jsxs("div",{style:{width:"50%"},children:[e.jsx("p",{style:{color:"#676C7E",fontWeight:500,fontSize:"13px",fontFamily:"Poppins",margin:0},children:"Hania Saeed"}),e.jsx("div",{style:{display:"flex",gap:"5px",alignItems:"center"},children:e.jsx("p",{style:{color:"#676C7E",fontWeight:500,fontSize:"10px",fontFamily:"Poppins",margin:0},children:"Resolved your issue in the Manager role."})})]}),e.jsx("div",{children:e.jsx("p",{style:{color:"white",fontWeight:500,fontSize:"12px",fontFamily:"Poppins",display:"flex",justifyContent:"center",alignItems:"center",marginLeft:"30px",width:"70px",height:"30px",backgroundColor:"#1a84de"},children:"View"})})]})]}),e.jsxs("div",{style:{width:"28%",borderRadius:15,overflow:"hidden",backgroundColor:"white",padding:"15px",height:"550px",marginLeft:"1%"},children:[e.jsx("p",{style:{fontSize:"20px",fontWeight:600,marginTop:"-1px"},children:"Recent Activity"}),e.jsx("div",{style:{width:"110%",marginLeft:"-30%",height:"100%"},children:e.jsx(F,{mode:"alternate",items:[{children:e.jsx("div",{}),dot:e.jsx("span",{style:{display:"none"}})},...M.flatMap(l=>[{children:e.jsx("div",{style:{flexDirection:"column"},children:e.jsx("p",{style:{fontWeight:400,fontFamily:"Poppins",fontSize:"12px",marginTop:"-10px"},children:l.time})}),color:"#1A84DE",dot:e.jsx("span",{style:{display:"none"}})},{children:e.jsx("div",{children:e.jsx("p",{style:{fontWeight:400,fontFamily:"Poppins",fontSize:"12px",marginTop:"-30px",marginLeft:"15px"},children:l.by})}),dot:e.jsx(p,{icon:"line-md:circle",width:"14",height:"14"})}])]})})]})]}),e.jsxs("div",{style:{display:"flex",marginTop:20},children:[e.jsx("div",{style:{width:"30%",borderRadius:15,overflow:"hidden",backgroundColor:"white",padding:"15px",height:"500px"},children:e.jsx(E,{title:"Overall CVs Stats",chartData:N})}),e.jsx("div",{style:{width:"30%",borderRadius:15,overflow:"hidden",backgroundColor:"white",padding:"15px",height:"500px",marginLeft:"1%"},children:e.jsx(O,{title:"Overall LI Stats",chartData:H})})]}),e.jsx("br",{}),s&&e.jsx(L,{open:s,onClose:()=>n(!1)})]})}export{Y as default};
