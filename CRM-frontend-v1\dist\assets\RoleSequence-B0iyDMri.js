var zh=Object.defineProperty;var Fh=(e,t,n)=>t in e?zh(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var jl=(e,t,n)=>(Fh(e,typeof t!="symbol"?t+"":t,n),n);import{r as v,ai as Uu,aj as qu,ak as Gu,bI as Hh,ar as Ci,al as mn,bJ as Oo,an as _a,bK as Wh,am as Xu,j as h,aq as Yu,as as Ku,E as oo,M as io,N as ze,U as ur,i as Me,bL as Zu,bM as Ju,z as Vh,bN as Uh,a1 as qh,bO as Gh,bP as Xh,bQ as Yh,bR as Kh,bS as Ml,bT as Zh,bU as Jh,bV as Qh,bW as eg,bX as tg,bY as dr,bZ as Qu,J as vs,b_ as ed,b$ as ys,R as z,c0 as td,Y as ng,W as rg,Z as og,$ as ig,c1 as ag,c2 as sg,af as $e,ag as _e,c3 as lg,c4 as nd,c5 as rd,c6 as un,c7 as dn,av as Se,c8 as fn,c9 as kl,n as cg,ca as $l,cb as ug,cc as dg,cd as fg,ce as pg,bh as zr,g as bs,a3 as od,aB as hg,a4 as _n,aC as gg,aD as mg,a6 as _l,T as vg,aE as yg,a5 as bg,a2 as he,ae as ao,az as Fr,e as Pe,aI as Et,aK as be,bp as On,bo as nt,s as Ee,d as xg,aH as wg,bi as Sg,bn as Ht,au as Ie,cf as wn,y as Sn,a7 as id,cg as Cg,ch as Eg,ci as Ig,bm as xs,H as Dg}from"./index-C8YzRPez.js";import{a as Bl,o as Tr,V as ws,n as Ll,f as Pg,v as ad,u as Ag,T as Ng}from"./TextEditor-DytKcPMl.js";import{F as Og}from"./FilePreview-Dz6aRE4w.js";import{a as Ss,R as Rg,b as Tg,c as Ro}from"./EditOutlined-tFjA3QW-.js";import{P as Cs}from"./progress-DT8MvaHz.js";import{M as Hr}from"./index-1vqLypFX.js";import{T as Jt}from"./index-C2EZYIAX.js";import{R as sd}from"./EyeOutlined-DmE8zkUh.js";import{C as ld}from"./Collapse-Cwu689ta.js";import{R as jg}from"./UserOutlined-D1tHQaiy.js";import"./index-BWafDGV1.js";import"./CustomSelectForForms-BI2fpcGx.js";import"./CloseOutlined-CK98qAnm.js";import"./index-CV2s8tJR.js";import"./DeleteOutlined-BzDmogJC.js";import"./useClosable-D2fLMMij.js";function Mg(e){return v.Children.toArray(e).filter(t=>v.isValidElement(t))}function kg(e){return qu("MuiToggleButton",e)}const Xn=Uu("MuiToggleButton",["root","disabled","selected","standard","primary","secondary","sizeSmall","sizeMedium","sizeLarge","fullWidth"]),cd=v.createContext({}),ud=v.createContext(void 0);function $g(e,t){return t===void 0||e===void 0?!1:Array.isArray(t)?t.indexOf(e)>=0:e===t}const _g=["value"],Bg=["children","className","color","disabled","disableFocusRipple","fullWidth","onChange","onClick","selected","size","value"],Lg=e=>{const{classes:t,fullWidth:n,selected:r,disabled:o,size:i,color:a}=e,s={root:["root",r&&"selected",o&&"disabled",n&&"fullWidth",`size${Ci(i)}`,a]};return Ku(s,kg,t)},zg=Gu(Hh,{name:"MuiToggleButton",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[t.root,t[`size${Ci(n.size)}`]]}})(({theme:e,ownerState:t})=>{let n=t.color==="standard"?e.palette.text.primary:e.palette[t.color].main,r;return e.vars&&(n=t.color==="standard"?e.vars.palette.text.primary:e.vars.palette[t.color].main,r=t.color==="standard"?e.vars.palette.text.primaryChannel:e.vars.palette[t.color].mainChannel),mn({},e.typography.button,{borderRadius:(e.vars||e).shape.borderRadius,padding:11,border:`1px solid ${(e.vars||e).palette.divider}`,color:(e.vars||e).palette.action.active},t.fullWidth&&{width:"100%"},{[`&.${Xn.disabled}`]:{color:(e.vars||e).palette.action.disabled,border:`1px solid ${(e.vars||e).palette.action.disabledBackground}`},"&:hover":{textDecoration:"none",backgroundColor:e.vars?`rgba(${e.vars.palette.text.primaryChannel} / ${e.vars.palette.action.hoverOpacity})`:Oo(e.palette.text.primary,e.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:"transparent"}},[`&.${Xn.selected}`]:{color:n,backgroundColor:e.vars?`rgba(${r} / ${e.vars.palette.action.selectedOpacity})`:Oo(n,e.palette.action.selectedOpacity),"&:hover":{backgroundColor:e.vars?`rgba(${r} / calc(${e.vars.palette.action.selectedOpacity} + ${e.vars.palette.action.hoverOpacity}))`:Oo(n,e.palette.action.selectedOpacity+e.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:e.vars?`rgba(${r} / ${e.vars.palette.action.selectedOpacity})`:Oo(n,e.palette.action.selectedOpacity)}}}},t.size==="small"&&{padding:7,fontSize:e.typography.pxToRem(13)},t.size==="large"&&{padding:15,fontSize:e.typography.pxToRem(15)})}),zl=v.forwardRef(function(t,n){const r=v.useContext(cd),{value:o}=r,i=_a(r,_g),a=v.useContext(ud),s=Wh(mn({},i,{selected:$g(t.value,o)}),t),c=Xu({props:s,name:"MuiToggleButton"}),{children:u,className:l,color:d="standard",disabled:f=!1,disableFocusRipple:p=!1,fullWidth:g=!1,onChange:m,onClick:x,selected:w,size:y="medium",value:E}=c,S=_a(c,Bg),b=mn({},c,{color:d,disabled:f,disableFocusRipple:p,fullWidth:g,size:y}),I=Lg(b),P=D=>{x&&(x(D,E),D.defaultPrevented)||m&&m(D,E)},A=a||"";return h.jsx(zg,mn({className:Yu(i.className,I.root,l,A),disabled:f,focusRipple:!p,ref:n,onClick:P,onChange:m,value:E,ownerState:b,"aria-pressed":w},S,{children:u}))});function Fg(e){return qu("MuiToggleButtonGroup",e)}const Te=Uu("MuiToggleButtonGroup",["root","selected","vertical","disabled","grouped","groupedHorizontal","groupedVertical","fullWidth","firstButton","lastButton","middleButton"]),Hg=["children","className","color","disabled","exclusive","fullWidth","onChange","orientation","size","value"],Wg=e=>{const{classes:t,orientation:n,fullWidth:r,disabled:o}=e,i={root:["root",n==="vertical"&&"vertical",r&&"fullWidth"],grouped:["grouped",`grouped${Ci(n)}`,o&&"disabled"],firstButton:["firstButton"],lastButton:["lastButton"],middleButton:["middleButton"]};return Ku(i,Fg,t)},Vg=Gu("div",{name:"MuiToggleButtonGroup",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:n}=e;return[{[`& .${Te.grouped}`]:t.grouped},{[`& .${Te.grouped}`]:t[`grouped${Ci(n.orientation)}`]},{[`& .${Te.firstButton}`]:t.firstButton},{[`& .${Te.lastButton}`]:t.lastButton},{[`& .${Te.middleButton}`]:t.middleButton},t.root,n.orientation==="vertical"&&t.vertical,n.fullWidth&&t.fullWidth]}})(({ownerState:e,theme:t})=>mn({display:"inline-flex",borderRadius:(t.vars||t).shape.borderRadius},e.orientation==="vertical"&&{flexDirection:"column"},e.fullWidth&&{width:"100%"},{[`& .${Te.grouped}`]:mn({},e.orientation==="horizontal"?{[`&.${Te.selected} + .${Te.grouped}.${Te.selected}`]:{borderLeft:0,marginLeft:0}}:{[`&.${Te.selected} + .${Te.grouped}.${Te.selected}`]:{borderTop:0,marginTop:0}})},e.orientation==="horizontal"?{[`& .${Te.firstButton},& .${Te.middleButton}`]:{borderTopRightRadius:0,borderBottomRightRadius:0},[`& .${Te.lastButton},& .${Te.middleButton}`]:{marginLeft:-1,borderLeft:"1px solid transparent",borderTopLeftRadius:0,borderBottomLeftRadius:0}}:{[`& .${Te.firstButton},& .${Te.middleButton}`]:{borderBottomLeftRadius:0,borderBottomRightRadius:0},[`& .${Te.lastButton},& .${Te.middleButton}`]:{marginTop:-1,borderTop:"1px solid transparent",borderTopLeftRadius:0,borderTopRightRadius:0}},e.orientation==="horizontal"?{[`& .${Te.lastButton}.${Xn.disabled},& .${Te.middleButton}.${Xn.disabled}`]:{borderLeft:"1px solid transparent"}}:{[`& .${Te.lastButton}.${Xn.disabled},& .${Te.middleButton}.${Xn.disabled}`]:{borderTop:"1px solid transparent"}})),Ug=v.forwardRef(function(t,n){const r=Xu({props:t,name:"MuiToggleButtonGroup"}),{children:o,className:i,color:a="standard",disabled:s=!1,exclusive:c=!1,fullWidth:u=!1,onChange:l,orientation:d="horizontal",size:f="medium",value:p}=r,g=_a(r,Hg),m=mn({},r,{disabled:s,fullWidth:u,orientation:d,size:f}),x=Wg(m),w=v.useCallback((P,A)=>{if(!l)return;const D=p&&p.indexOf(A);let k;p&&D>=0?(k=p.slice(),k.splice(D,1)):k=p?p.concat(A):[A],l(P,k)},[l,p]),y=v.useCallback((P,A)=>{l&&l(P,p===A?null:A)},[l,p]),E=v.useMemo(()=>({className:x.grouped,onChange:c?y:w,value:p,size:f,fullWidth:u,color:a,disabled:s}),[x.grouped,c,y,w,p,f,u,a,s]),S=Mg(o),b=S.length,I=P=>{const A=P===0,D=P===b-1;return A&&D?"":A?x.firstButton:D?x.lastButton:x.middleButton};return h.jsx(Vg,mn({role:"group",className:Yu(x.root,i),ref:n,ownerState:m},g,{children:h.jsx(cd.Provider,{value:E,children:S.map((P,A)=>h.jsx(ud.Provider,{value:I(A),children:P},A))})}))}),To=(e,t,n,r,o)=>({background:e,border:`${ze(r.lineWidth)} ${r.lineType} ${t}`,[`${o}-icon`]:{color:n}}),qg=e=>{const{componentCls:t,motionDurationSlow:n,marginXS:r,marginSM:o,fontSize:i,fontSizeLG:a,lineHeight:s,borderRadiusLG:c,motionEaseInOutCirc:u,withDescriptionIconSize:l,colorText:d,colorTextHeading:f,withDescriptionPadding:p,defaultPadding:g}=e;return{[t]:Object.assign(Object.assign({},io(e)),{position:"relative",display:"flex",alignItems:"center",padding:g,wordWrap:"break-word",borderRadius:c,[`&${t}-rtl`]:{direction:"rtl"},[`${t}-content`]:{flex:1,minWidth:0},[`${t}-icon`]:{marginInlineEnd:r,lineHeight:0},"&-description":{display:"none",fontSize:i,lineHeight:s},"&-message":{color:f},[`&${t}-motion-leave`]:{overflow:"hidden",opacity:1,transition:`max-height ${n} ${u}, opacity ${n} ${u},
        padding-top ${n} ${u}, padding-bottom ${n} ${u},
        margin-bottom ${n} ${u}`},[`&${t}-motion-leave-active`]:{maxHeight:0,marginBottom:"0 !important",paddingTop:0,paddingBottom:0,opacity:0}}),[`${t}-with-description`]:{alignItems:"flex-start",padding:p,[`${t}-icon`]:{marginInlineEnd:o,fontSize:l,lineHeight:0},[`${t}-message`]:{display:"block",marginBottom:r,color:f,fontSize:a},[`${t}-description`]:{display:"block",color:d}},[`${t}-banner`]:{marginBottom:0,border:"0 !important",borderRadius:0}}},Gg=e=>{const{componentCls:t,colorSuccess:n,colorSuccessBorder:r,colorSuccessBg:o,colorWarning:i,colorWarningBorder:a,colorWarningBg:s,colorError:c,colorErrorBorder:u,colorErrorBg:l,colorInfo:d,colorInfoBorder:f,colorInfoBg:p}=e;return{[t]:{"&-success":To(o,r,n,e,t),"&-info":To(p,f,d,e,t),"&-warning":To(s,a,i,e,t),"&-error":Object.assign(Object.assign({},To(l,u,c,e,t)),{[`${t}-description > pre`]:{margin:0,padding:0}})}}},Xg=e=>{const{componentCls:t,iconCls:n,motionDurationMid:r,marginXS:o,fontSizeIcon:i,colorIcon:a,colorIconHover:s}=e;return{[t]:{"&-action":{marginInlineStart:o},[`${t}-close-icon`]:{marginInlineStart:o,padding:0,overflow:"hidden",fontSize:i,lineHeight:ze(i),backgroundColor:"transparent",border:"none",outline:"none",cursor:"pointer",[`${n}-close`]:{color:a,transition:`color ${r}`,"&:hover":{color:s}}},"&-close-text":{color:a,transition:`color ${r}`,"&:hover":{color:s}}}}},Yg=e=>({withDescriptionIconSize:e.fontSizeHeading3,defaultPadding:`${e.paddingContentVerticalSM}px 12px`,withDescriptionPadding:`${e.paddingMD}px ${e.paddingContentHorizontalLG}px`}),Kg=oo("Alert",e=>[qg(e),Gg(e),Xg(e)],Yg);var Fl=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const Zg={success:Gh,info:Xh,error:Yh,warning:Kh},Jg=e=>{const{icon:t,prefixCls:n,type:r}=e,o=Zg[r]||null;return t?Uh(t,v.createElement("span",{className:`${n}-icon`},t),()=>({className:Me(`${n}-icon`,t.props.className)})):v.createElement(o,{className:`${n}-icon`})},Qg=e=>{const{isClosable:t,prefixCls:n,closeIcon:r,handleClose:o,ariaProps:i}=e,a=r===!0||r===void 0?v.createElement(qh,null):r;return t?v.createElement("button",Object.assign({type:"button",onClick:o,className:`${n}-close-icon`,tabIndex:0},i),a):null},dd=v.forwardRef((e,t)=>{const{description:n,prefixCls:r,message:o,banner:i,className:a,rootClassName:s,style:c,onMouseEnter:u,onMouseLeave:l,onClick:d,afterClose:f,showIcon:p,closable:g,closeText:m,closeIcon:x,action:w,id:y}=e,E=Fl(e,["description","prefixCls","message","banner","className","rootClassName","style","onMouseEnter","onMouseLeave","onClick","afterClose","showIcon","closable","closeText","closeIcon","action","id"]),[S,b]=v.useState(!1),I=v.useRef(null);v.useImperativeHandle(t,()=>({nativeElement:I.current}));const{getPrefixCls:P,direction:A,alert:D}=v.useContext(ur),k=P("alert",r),[L,B,j]=Kg(k),C=M=>{var U;b(!0),(U=e.onClose)===null||U===void 0||U.call(e,M)},O=v.useMemo(()=>e.type!==void 0?e.type:i?"warning":"info",[e.type,i]),N=v.useMemo(()=>typeof g=="object"&&g.closeIcon||m?!0:typeof g=="boolean"?g:x!==!1&&x!==null&&x!==void 0?!0:!!(D!=null&&D.closable),[m,x,g,D==null?void 0:D.closable]),_=i&&p===void 0?!0:p,W=Me(k,`${k}-${O}`,{[`${k}-with-description`]:!!n,[`${k}-no-icon`]:!_,[`${k}-banner`]:!!i,[`${k}-rtl`]:A==="rtl"},D==null?void 0:D.className,a,s,j,B),T=Zu(E,{aria:!0,data:!0}),H=v.useMemo(()=>{var M,U;return typeof g=="object"&&g.closeIcon?g.closeIcon:m||(x!==void 0?x:typeof(D==null?void 0:D.closable)=="object"&&(!((M=D==null?void 0:D.closable)===null||M===void 0)&&M.closeIcon)?(U=D==null?void 0:D.closable)===null||U===void 0?void 0:U.closeIcon:D==null?void 0:D.closeIcon)},[x,g,m,D==null?void 0:D.closeIcon]),R=v.useMemo(()=>{const M=g??(D==null?void 0:D.closable);return typeof M=="object"?Fl(M,["closeIcon"]):{}},[g,D==null?void 0:D.closable]);return L(v.createElement(Ju,{visible:!S,motionName:`${k}-motion`,motionAppear:!1,motionEnter:!1,onLeaveStart:M=>({maxHeight:M.offsetHeight}),onLeaveEnd:f},(M,U)=>{let{className:G,style:q}=M;return v.createElement("div",Object.assign({id:y,ref:Vh(I,U),"data-show":!S,className:Me(W,G),style:Object.assign(Object.assign(Object.assign({},D==null?void 0:D.style),c),q),onMouseEnter:u,onMouseLeave:l,onClick:d,role:"alert"},T),_?v.createElement(Jg,{description:n,icon:e.icon,prefixCls:k,type:O}):null,v.createElement("div",{className:`${k}-content`},o?v.createElement("div",{className:`${k}-message`},o):null,n?v.createElement("div",{className:`${k}-description`},n):null),w?v.createElement("div",{className:`${k}-action`},w):null,v.createElement(Qg,{isClosable:N,prefixCls:k,closeIcon:H,handleClose:C,ariaProps:R}))}))});function em(e,t,n){return t=Ml(t),Zh(e,Jh()?Reflect.construct(t,n||[],Ml(e).constructor):t.apply(e,n))}let tm=function(e){function t(){var n;return tg(this,t),n=em(this,t,arguments),n.state={error:void 0,info:{componentStack:""}},n}return Qh(t,e),eg(t,[{key:"componentDidCatch",value:function(r,o){this.setState({error:r,info:o})}},{key:"render",value:function(){const{message:r,description:o,id:i,children:a}=this.props,{error:s,info:c}=this.state,u=(c==null?void 0:c.componentStack)||null,l=typeof r>"u"?(s||"").toString():r,d=typeof o>"u"?u:o;return s?v.createElement(dd,{id:i,type:"error",message:l,description:v.createElement("pre",{style:{fontSize:"0.9em",overflowX:"auto"}},d)}):a}}])}(v.Component);const zt=dd;zt.ErrorBoundary=tm;const nm=new dr("antStatusProcessing",{"0%":{transform:"scale(0.8)",opacity:.5},"100%":{transform:"scale(2.4)",opacity:0}}),rm=new dr("antZoomBadgeIn",{"0%":{transform:"scale(0) translate(50%, -50%)",opacity:0},"100%":{transform:"scale(1) translate(50%, -50%)"}}),om=new dr("antZoomBadgeOut",{"0%":{transform:"scale(1) translate(50%, -50%)"},"100%":{transform:"scale(0) translate(50%, -50%)",opacity:0}}),im=new dr("antNoWrapperZoomBadgeIn",{"0%":{transform:"scale(0)",opacity:0},"100%":{transform:"scale(1)"}}),am=new dr("antNoWrapperZoomBadgeOut",{"0%":{transform:"scale(1)"},"100%":{transform:"scale(0)",opacity:0}}),sm=new dr("antBadgeLoadingCircle",{"0%":{transformOrigin:"50%"},"100%":{transform:"translate(50%, -50%) rotate(360deg)",transformOrigin:"50%"}}),lm=e=>{const{componentCls:t,iconCls:n,antCls:r,badgeShadowSize:o,textFontSize:i,textFontSizeSM:a,statusSize:s,dotSize:c,textFontWeight:u,indicatorHeight:l,indicatorHeightSM:d,marginXS:f,calc:p}=e,g=`${r}-scroll-number`,m=Qu(e,(x,w)=>{let{darkColor:y}=w;return{[`&${t} ${t}-color-${x}`]:{background:y,[`&:not(${t}-count)`]:{color:y},"a:hover &":{background:y}}}});return{[t]:Object.assign(Object.assign(Object.assign(Object.assign({},io(e)),{position:"relative",display:"inline-block",width:"fit-content",lineHeight:1,[`${t}-count`]:{display:"inline-flex",justifyContent:"center",zIndex:e.indicatorZIndex,minWidth:l,height:l,color:e.badgeTextColor,fontWeight:u,fontSize:i,lineHeight:ze(l),whiteSpace:"nowrap",textAlign:"center",background:e.badgeColor,borderRadius:p(l).div(2).equal(),boxShadow:`0 0 0 ${ze(o)} ${e.badgeShadowColor}`,transition:`background ${e.motionDurationMid}`,a:{color:e.badgeTextColor},"a:hover":{color:e.badgeTextColor},"a:hover &":{background:e.badgeColorHover}},[`${t}-count-sm`]:{minWidth:d,height:d,fontSize:a,lineHeight:ze(d),borderRadius:p(d).div(2).equal()},[`${t}-multiple-words`]:{padding:`0 ${ze(e.paddingXS)}`,bdi:{unicodeBidi:"plaintext"}},[`${t}-dot`]:{zIndex:e.indicatorZIndex,width:c,minWidth:c,height:c,background:e.badgeColor,borderRadius:"100%",boxShadow:`0 0 0 ${ze(o)} ${e.badgeShadowColor}`},[`${t}-count, ${t}-dot, ${g}-custom-component`]:{position:"absolute",top:0,insetInlineEnd:0,transform:"translate(50%, -50%)",transformOrigin:"100% 0%",[`&${n}-spin`]:{animationName:sm,animationDuration:"1s",animationIterationCount:"infinite",animationTimingFunction:"linear"}},[`&${t}-status`]:{lineHeight:"inherit",verticalAlign:"baseline",[`${t}-status-dot`]:{position:"relative",top:-1,display:"inline-block",width:s,height:s,verticalAlign:"middle",borderRadius:"50%"},[`${t}-status-success`]:{backgroundColor:e.colorSuccess},[`${t}-status-processing`]:{overflow:"visible",color:e.colorInfo,backgroundColor:e.colorInfo,borderColor:"currentcolor","&::after":{position:"absolute",top:0,insetInlineStart:0,width:"100%",height:"100%",borderWidth:o,borderStyle:"solid",borderColor:"inherit",borderRadius:"50%",animationName:nm,animationDuration:e.badgeProcessingDuration,animationIterationCount:"infinite",animationTimingFunction:"ease-in-out",content:'""'}},[`${t}-status-default`]:{backgroundColor:e.colorTextPlaceholder},[`${t}-status-error`]:{backgroundColor:e.colorError},[`${t}-status-warning`]:{backgroundColor:e.colorWarning},[`${t}-status-text`]:{marginInlineStart:f,color:e.colorText,fontSize:e.fontSize}}}),m),{[`${t}-zoom-appear, ${t}-zoom-enter`]:{animationName:rm,animationDuration:e.motionDurationSlow,animationTimingFunction:e.motionEaseOutBack,animationFillMode:"both"},[`${t}-zoom-leave`]:{animationName:om,animationDuration:e.motionDurationSlow,animationTimingFunction:e.motionEaseOutBack,animationFillMode:"both"},[`&${t}-not-a-wrapper`]:{[`${t}-zoom-appear, ${t}-zoom-enter`]:{animationName:im,animationDuration:e.motionDurationSlow,animationTimingFunction:e.motionEaseOutBack},[`${t}-zoom-leave`]:{animationName:am,animationDuration:e.motionDurationSlow,animationTimingFunction:e.motionEaseOutBack},[`&:not(${t}-status)`]:{verticalAlign:"middle"},[`${g}-custom-component, ${t}-count`]:{transform:"none"},[`${g}-custom-component, ${g}`]:{position:"relative",top:"auto",display:"block",transformOrigin:"50% 50%"}},[g]:{overflow:"hidden",transition:`all ${e.motionDurationMid} ${e.motionEaseOutBack}`,[`${g}-only`]:{position:"relative",display:"inline-block",height:l,transition:`all ${e.motionDurationSlow} ${e.motionEaseOutBack}`,WebkitTransformStyle:"preserve-3d",WebkitBackfaceVisibility:"hidden",[`> p${g}-only-unit`]:{height:l,margin:0,WebkitTransformStyle:"preserve-3d",WebkitBackfaceVisibility:"hidden"}},[`${g}-symbol`]:{verticalAlign:"top"}},"&-rtl":{direction:"rtl",[`${t}-count, ${t}-dot, ${g}-custom-component`]:{transform:"translate(-50%, -50%)"}}})}},fd=e=>{const{fontHeight:t,lineWidth:n,marginXS:r,colorBorderBg:o}=e,i=t,a=n,s=e.colorTextLightSolid,c=e.colorError,u=e.colorErrorHover;return vs(e,{badgeFontHeight:i,badgeShadowSize:a,badgeTextColor:s,badgeColor:c,badgeColorHover:u,badgeShadowColor:o,badgeProcessingDuration:"1.2s",badgeRibbonOffset:r,badgeRibbonCornerTransform:"scaleY(0.75)",badgeRibbonCornerFilter:"brightness(75%)"})},pd=e=>{const{fontSize:t,lineHeight:n,fontSizeSM:r,lineWidth:o}=e;return{indicatorZIndex:"auto",indicatorHeight:Math.round(t*n)-2*o,indicatorHeightSM:t,dotSize:r/2,textFontSize:r,textFontSizeSM:r,textFontWeight:"normal",statusSize:r/2}},cm=oo("Badge",e=>{const t=fd(e);return lm(t)},pd),um=e=>{const{antCls:t,badgeFontHeight:n,marginXS:r,badgeRibbonOffset:o,calc:i}=e,a=`${t}-ribbon`,s=`${t}-ribbon-wrapper`,c=Qu(e,(u,l)=>{let{darkColor:d}=l;return{[`&${a}-color-${u}`]:{background:d,color:d}}});return{[s]:{position:"relative"},[a]:Object.assign(Object.assign(Object.assign(Object.assign({},io(e)),{position:"absolute",top:r,padding:`0 ${ze(e.paddingXS)}`,color:e.colorPrimary,lineHeight:ze(n),whiteSpace:"nowrap",backgroundColor:e.colorPrimary,borderRadius:e.borderRadiusSM,[`${a}-text`]:{color:e.badgeTextColor},[`${a}-corner`]:{position:"absolute",top:"100%",width:o,height:o,color:"currentcolor",border:`${ze(i(o).div(2).equal())} solid`,transform:e.badgeRibbonCornerTransform,transformOrigin:"top",filter:e.badgeRibbonCornerFilter}}),c),{[`&${a}-placement-end`]:{insetInlineEnd:i(o).mul(-1).equal(),borderEndEndRadius:0,[`${a}-corner`]:{insetInlineEnd:0,borderInlineEndColor:"transparent",borderBlockEndColor:"transparent"}},[`&${a}-placement-start`]:{insetInlineStart:i(o).mul(-1).equal(),borderEndStartRadius:0,[`${a}-corner`]:{insetInlineStart:0,borderBlockEndColor:"transparent",borderInlineStartColor:"transparent"}},"&-rtl":{direction:"rtl"}})}},dm=oo(["Badge","Ribbon"],e=>{const t=fd(e);return um(t)},pd),fm=e=>{const{className:t,prefixCls:n,style:r,color:o,children:i,text:a,placement:s="end",rootClassName:c}=e,{getPrefixCls:u,direction:l}=v.useContext(ur),d=u("ribbon",n),f=`${d}-wrapper`,[p,g,m]=dm(d,f),x=ed(o,!1),w=Me(d,`${d}-placement-${s}`,{[`${d}-rtl`]:l==="rtl",[`${d}-color-${o}`]:x},t),y={},E={};return o&&!x&&(y.background=o,E.color=o),p(v.createElement("div",{className:Me(f,c,g,m)},i,v.createElement("div",{className:Me(w,g),style:Object.assign(Object.assign({},y),r)},v.createElement("span",{className:`${d}-text`},a),v.createElement("div",{className:`${d}-corner`,style:E}))))},Hl=e=>{const{prefixCls:t,value:n,current:r,offset:o=0}=e;let i;return o&&(i={position:"absolute",top:`${o}00%`,left:0}),v.createElement("span",{style:i,className:Me(`${t}-only-unit`,{current:r})},n)};function pm(e,t,n){let r=e,o=0;for(;(r+10)%10!==t;)r+=n,o+=n;return o}const hm=e=>{const{prefixCls:t,count:n,value:r}=e,o=Number(r),i=Math.abs(n),[a,s]=v.useState(o),[c,u]=v.useState(i),l=()=>{s(o),u(i)};v.useEffect(()=>{const p=setTimeout(l,1e3);return()=>clearTimeout(p)},[o]);let d,f;if(a===o||Number.isNaN(o)||Number.isNaN(a))d=[v.createElement(Hl,Object.assign({},e,{key:o,current:!0}))],f={transition:"none"};else{d=[];const p=o+10,g=[];for(let y=o;y<=p;y+=1)g.push(y);const m=c<i?1:-1,x=g.findIndex(y=>y%10===a);d=(m<0?g.slice(0,x+1):g.slice(x)).map((y,E)=>{const S=y%10;return v.createElement(Hl,Object.assign({},e,{key:y,value:S,offset:m<0?E-x:E,current:E===x}))}),f={transform:`translateY(${-pm(a,o,m)}00%)`}}return v.createElement("span",{className:`${t}-only`,style:f,onTransitionEnd:l},d)};var gm=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const mm=v.forwardRef((e,t)=>{const{prefixCls:n,count:r,className:o,motionClassName:i,style:a,title:s,show:c,component:u="sup",children:l}=e,d=gm(e,["prefixCls","count","className","motionClassName","style","title","show","component","children"]),{getPrefixCls:f}=v.useContext(ur),p=f("scroll-number",n),g=Object.assign(Object.assign({},d),{"data-show":c,style:a,className:Me(p,o,i),title:s});let m=r;if(r&&Number(r)%1===0){const x=String(r).split("");m=v.createElement("bdi",null,x.map((w,y)=>v.createElement(hm,{prefixCls:p,count:Number(r),value:w,key:x.length-y})))}return a!=null&&a.borderColor&&(g.style=Object.assign(Object.assign({},a),{boxShadow:`0 0 0 1px ${a.borderColor} inset`})),l?ys(l,x=>({className:Me(`${p}-custom-component`,x==null?void 0:x.className,i)})):v.createElement(u,Object.assign({},g,{ref:t}),m)});var vm=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const ym=v.forwardRef((e,t)=>{var n,r,o,i,a;const{prefixCls:s,scrollNumberPrefixCls:c,children:u,status:l,text:d,color:f,count:p=null,overflowCount:g=99,dot:m=!1,size:x="default",title:w,offset:y,style:E,className:S,rootClassName:b,classNames:I,styles:P,showZero:A=!1}=e,D=vm(e,["prefixCls","scrollNumberPrefixCls","children","status","text","color","count","overflowCount","dot","size","title","offset","style","className","rootClassName","classNames","styles","showZero"]),{getPrefixCls:k,direction:L,badge:B}=v.useContext(ur),j=k("badge",s),[C,O,N]=cm(j),_=p>g?`${g}+`:p,W=_==="0"||_===0,T=p===null||W&&!A,H=(l!=null||f!=null)&&T,R=m&&!W,M=R?"":_,U=v.useMemo(()=>(M==null||M===""||W&&!A)&&!R,[M,W,A,R]),G=v.useRef(p);U||(G.current=p);const q=G.current,Z=v.useRef(M);U||(Z.current=M);const Q=Z.current,$=v.useRef(R);U||($.current=R);const V=v.useMemo(()=>{if(!y)return Object.assign(Object.assign({},B==null?void 0:B.style),E);const re={marginTop:y[1]};return L==="rtl"?re.left=parseInt(y[0],10):re.right=-parseInt(y[0],10),Object.assign(Object.assign(Object.assign({},re),B==null?void 0:B.style),E)},[L,y,E,B==null?void 0:B.style]),X=w??(typeof q=="string"||typeof q=="number"?q:void 0),ne=U||!d?null:v.createElement("span",{className:`${j}-status-text`},d),F=!q||typeof q!="object"?void 0:ys(q,re=>({style:Object.assign(Object.assign({},V),re.style)})),Y=ed(f,!1),ee=Me(I==null?void 0:I.indicator,(n=B==null?void 0:B.classNames)===null||n===void 0?void 0:n.indicator,{[`${j}-status-dot`]:H,[`${j}-status-${l}`]:!!l,[`${j}-color-${f}`]:Y}),oe={};f&&!Y&&(oe.color=f,oe.background=f);const ae=Me(j,{[`${j}-status`]:H,[`${j}-not-a-wrapper`]:!u,[`${j}-rtl`]:L==="rtl"},S,b,B==null?void 0:B.className,(r=B==null?void 0:B.classNames)===null||r===void 0?void 0:r.root,I==null?void 0:I.root,O,N);if(!u&&H){const re=V.color;return C(v.createElement("span",Object.assign({},D,{className:ae,style:Object.assign(Object.assign(Object.assign({},P==null?void 0:P.root),(o=B==null?void 0:B.styles)===null||o===void 0?void 0:o.root),V)}),v.createElement("span",{className:ee,style:Object.assign(Object.assign(Object.assign({},P==null?void 0:P.indicator),(i=B==null?void 0:B.styles)===null||i===void 0?void 0:i.indicator),oe)}),d&&v.createElement("span",{style:{color:re},className:`${j}-status-text`},d)))}return C(v.createElement("span",Object.assign({ref:t},D,{className:ae,style:Object.assign(Object.assign({},(a=B==null?void 0:B.styles)===null||a===void 0?void 0:a.root),P==null?void 0:P.root)}),u,v.createElement(Ju,{visible:!U,motionName:`${j}-zoom`,motionAppear:!1,motionDeadline:1e3},re=>{let{className:K}=re;var se,me;const ve=k("scroll-number",c),xe=$.current,Ce=Me(I==null?void 0:I.indicator,(se=B==null?void 0:B.classNames)===null||se===void 0?void 0:se.indicator,{[`${j}-dot`]:xe,[`${j}-count`]:!xe,[`${j}-count-sm`]:x==="small",[`${j}-multiple-words`]:!xe&&Q&&Q.toString().length>1,[`${j}-status-${l}`]:!!l,[`${j}-color-${f}`]:Y});let je=Object.assign(Object.assign(Object.assign({},P==null?void 0:P.indicator),(me=B==null?void 0:B.styles)===null||me===void 0?void 0:me.indicator),V);return f&&!Y&&(je=je||{},je.background=f),v.createElement(mm,{prefixCls:ve,show:!U,motionClassName:K,className:Ce,count:Q,title:X,style:je,key:"scrollNumber"},F)}),ne))}),nn=ym;nn.Ribbon=fm;const bm={xxl:3,xl:3,lg:3,md:3,sm:2,xs:1},Es=z.createContext({});var xm=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const wm=e=>ng(e).map(t=>Object.assign(Object.assign({},t==null?void 0:t.props),{key:t.key}));function Sm(e,t,n){const r=v.useMemo(()=>t||wm(n),[t,n]);return v.useMemo(()=>r.map(i=>{var{span:a}=i,s=xm(i,["span"]);return a==="filled"?Object.assign(Object.assign({},s),{filled:!0}):Object.assign(Object.assign({},s),{span:typeof a=="number"?a:td(e,a)})}),[r,e])}var Cm=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};function Em(e,t){let n=[],r=[],o=!1,i=0;return e.filter(a=>a).forEach(a=>{const{filled:s}=a,c=Cm(a,["filled"]);if(s){r.push(c),n.push(r),r=[],i=0;return}const u=t-i;i+=a.span||1,i>=t?(i>t?(o=!0,r.push(Object.assign(Object.assign({},c),{span:u}))):r.push(c),n.push(r),r=[],i=0):r.push(c)}),r.length>0&&n.push(r),n=n.map(a=>{const s=a.reduce((c,u)=>c+(u.span||1),0);if(s<t){const c=a[a.length-1];return c.span=t-(s-(c.span||1)),a}return a}),[n,o]}const Im=(e,t)=>{const[n,r]=v.useMemo(()=>Em(t,e),[t,e]);return n},Dm=e=>{let{children:t}=e;return t};function Wl(e){return e!=null}const Ji=e=>{const{itemPrefixCls:t,component:n,span:r,className:o,style:i,labelStyle:a,contentStyle:s,bordered:c,label:u,content:l,colon:d,type:f,styles:p}=e,g=n,m=v.useContext(Es),{classNames:x}=m;return c?v.createElement(g,{className:Me({[`${t}-item-label`]:f==="label",[`${t}-item-content`]:f==="content",[`${x==null?void 0:x.label}`]:f==="label",[`${x==null?void 0:x.content}`]:f==="content"},o),style:i,colSpan:r},Wl(u)&&v.createElement("span",{style:Object.assign(Object.assign({},a),p==null?void 0:p.label)},u),Wl(l)&&v.createElement("span",{style:Object.assign(Object.assign({},a),p==null?void 0:p.content)},l)):v.createElement(g,{className:Me(`${t}-item`,o),style:i,colSpan:r},v.createElement("div",{className:`${t}-item-container`},(u||u===0)&&v.createElement("span",{className:Me(`${t}-item-label`,x==null?void 0:x.label,{[`${t}-item-no-colon`]:!d}),style:Object.assign(Object.assign({},a),p==null?void 0:p.label)},u),(l||l===0)&&v.createElement("span",{className:Me(`${t}-item-content`,x==null?void 0:x.content),style:Object.assign(Object.assign({},s),p==null?void 0:p.content)},l)))};function Qi(e,t,n){let{colon:r,prefixCls:o,bordered:i}=t,{component:a,type:s,showLabel:c,showContent:u,labelStyle:l,contentStyle:d,styles:f}=n;return e.map((p,g)=>{let{label:m,children:x,prefixCls:w=o,className:y,style:E,labelStyle:S,contentStyle:b,span:I=1,key:P,styles:A}=p;return typeof a=="string"?v.createElement(Ji,{key:`${s}-${P||g}`,className:y,style:E,styles:{label:Object.assign(Object.assign(Object.assign(Object.assign({},l),f==null?void 0:f.label),S),A==null?void 0:A.label),content:Object.assign(Object.assign(Object.assign(Object.assign({},d),f==null?void 0:f.content),b),A==null?void 0:A.content)},span:I,colon:r,component:a,itemPrefixCls:w,bordered:i,label:c?m:null,content:u?x:null,type:s}):[v.createElement(Ji,{key:`label-${P||g}`,className:y,style:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},l),f==null?void 0:f.label),E),S),A==null?void 0:A.label),span:1,colon:r,component:a[0],itemPrefixCls:w,bordered:i,label:m,type:"label"}),v.createElement(Ji,{key:`content-${P||g}`,className:y,style:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},d),f==null?void 0:f.content),E),b),A==null?void 0:A.content),span:I*2-1,component:a[1],itemPrefixCls:w,bordered:i,content:x,type:"content"})]})}const Pm=e=>{const t=v.useContext(Es),{prefixCls:n,vertical:r,row:o,index:i,bordered:a}=e;return r?v.createElement(v.Fragment,null,v.createElement("tr",{key:`label-${i}`,className:`${n}-row`},Qi(o,e,Object.assign({component:"th",type:"label",showLabel:!0},t))),v.createElement("tr",{key:`content-${i}`,className:`${n}-row`},Qi(o,e,Object.assign({component:"td",type:"content",showContent:!0},t)))):v.createElement("tr",{key:i,className:`${n}-row`},Qi(o,e,Object.assign({component:a?["th","td"]:"td",type:"item",showLabel:!0,showContent:!0},t)))},Am=e=>{const{componentCls:t,labelBg:n}=e;return{[`&${t}-bordered`]:{[`> ${t}-view`]:{border:`${ze(e.lineWidth)} ${e.lineType} ${e.colorSplit}`,"> table":{tableLayout:"auto"},[`${t}-row`]:{borderBottom:`${ze(e.lineWidth)} ${e.lineType} ${e.colorSplit}`,"&:last-child":{borderBottom:"none"},[`> ${t}-item-label, > ${t}-item-content`]:{padding:`${ze(e.padding)} ${ze(e.paddingLG)}`,borderInlineEnd:`${ze(e.lineWidth)} ${e.lineType} ${e.colorSplit}`,"&:last-child":{borderInlineEnd:"none"}},[`> ${t}-item-label`]:{color:e.colorTextSecondary,backgroundColor:n,"&::after":{display:"none"}}}},[`&${t}-middle`]:{[`${t}-row`]:{[`> ${t}-item-label, > ${t}-item-content`]:{padding:`${ze(e.paddingSM)} ${ze(e.paddingLG)}`}}},[`&${t}-small`]:{[`${t}-row`]:{[`> ${t}-item-label, > ${t}-item-content`]:{padding:`${ze(e.paddingXS)} ${ze(e.padding)}`}}}}}},Nm=e=>{const{componentCls:t,extraColor:n,itemPaddingBottom:r,itemPaddingEnd:o,colonMarginRight:i,colonMarginLeft:a,titleMarginBottom:s}=e;return{[t]:Object.assign(Object.assign(Object.assign({},io(e)),Am(e)),{"&-rtl":{direction:"rtl"},[`${t}-header`]:{display:"flex",alignItems:"center",marginBottom:s},[`${t}-title`]:Object.assign(Object.assign({},rg),{flex:"auto",color:e.titleColor,fontWeight:e.fontWeightStrong,fontSize:e.fontSizeLG,lineHeight:e.lineHeightLG}),[`${t}-extra`]:{marginInlineStart:"auto",color:n,fontSize:e.fontSize},[`${t}-view`]:{width:"100%",borderRadius:e.borderRadiusLG,table:{width:"100%",tableLayout:"fixed",borderCollapse:"collapse"}},[`${t}-row`]:{"> th, > td":{paddingBottom:r,paddingInlineEnd:o},"> th:last-child, > td:last-child":{paddingInlineEnd:0},"&:last-child":{borderBottom:"none","> th, > td":{paddingBottom:0}}},[`${t}-item-label`]:{color:e.colorTextTertiary,fontWeight:"normal",fontSize:e.fontSize,lineHeight:e.lineHeight,textAlign:"start","&::after":{content:'":"',position:"relative",top:-.5,marginInline:`${ze(a)} ${ze(i)}`},[`&${t}-item-no-colon::after`]:{content:'""'}},[`${t}-item-no-label`]:{"&::after":{margin:0,content:'""'}},[`${t}-item-content`]:{display:"table-cell",flex:1,color:e.contentColor,fontSize:e.fontSize,lineHeight:e.lineHeight,wordBreak:"break-word",overflowWrap:"break-word"},[`${t}-item`]:{paddingBottom:0,verticalAlign:"top","&-container":{display:"flex",[`${t}-item-label`]:{display:"inline-flex",alignItems:"baseline"},[`${t}-item-content`]:{display:"inline-flex",alignItems:"baseline",minWidth:"1em"}}},"&-middle":{[`${t}-row`]:{"> th, > td":{paddingBottom:e.paddingSM}}},"&-small":{[`${t}-row`]:{"> th, > td":{paddingBottom:e.paddingXS}}}})}},Om=e=>({labelBg:e.colorFillAlter,titleColor:e.colorText,titleMarginBottom:e.fontSizeSM*e.lineHeightSM,itemPaddingBottom:e.padding,itemPaddingEnd:e.padding,colonMarginRight:e.marginXS,colonMarginLeft:e.marginXXS/2,contentColor:e.colorText,extraColor:e.colorText}),Rm=oo("Descriptions",e=>{const t=vs(e,{});return Nm(t)},Om);var Tm=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const Ct=e=>{var t,n,r,o,i,a,s,c;const{prefixCls:u,title:l,extra:d,column:f,colon:p=!0,bordered:g,layout:m,children:x,className:w,rootClassName:y,style:E,size:S,labelStyle:b,contentStyle:I,styles:P,items:A,classNames:D}=e,k=Tm(e,["prefixCls","title","extra","column","colon","bordered","layout","children","className","rootClassName","style","size","labelStyle","contentStyle","styles","items","classNames"]),{getPrefixCls:L,direction:B,descriptions:j}=v.useContext(ur),C=L("descriptions",u),O=og(),N=v.useMemo(()=>{var G;return typeof f=="number"?f:(G=td(O,Object.assign(Object.assign({},bm),f)))!==null&&G!==void 0?G:3},[O,f]),_=Sm(O,A,x),W=ig(S),T=Im(N,_),[H,R,M]=Rm(C),U=v.useMemo(()=>{var G,q,Z,Q;return{labelStyle:b,contentStyle:I,styles:{content:Object.assign(Object.assign({},(G=j==null?void 0:j.styles)===null||G===void 0?void 0:G.content),P==null?void 0:P.content),label:Object.assign(Object.assign({},(q=j==null?void 0:j.styles)===null||q===void 0?void 0:q.label),P==null?void 0:P.label)},classNames:{label:Me((Z=j==null?void 0:j.classNames)===null||Z===void 0?void 0:Z.label,D==null?void 0:D.label),content:Me((Q=j==null?void 0:j.classNames)===null||Q===void 0?void 0:Q.content,D==null?void 0:D.content)}}},[b,I,P,D,j]);return H(v.createElement(Es.Provider,{value:U},v.createElement("div",Object.assign({className:Me(C,j==null?void 0:j.className,(t=j==null?void 0:j.classNames)===null||t===void 0?void 0:t.root,D==null?void 0:D.root,{[`${C}-${W}`]:W&&W!=="default",[`${C}-bordered`]:!!g,[`${C}-rtl`]:B==="rtl"},w,y,R,M),style:Object.assign(Object.assign(Object.assign(Object.assign({},j==null?void 0:j.style),(n=j==null?void 0:j.styles)===null||n===void 0?void 0:n.root),P==null?void 0:P.root),E)},k),(l||d)&&v.createElement("div",{className:Me(`${C}-header`,(r=j==null?void 0:j.classNames)===null||r===void 0?void 0:r.header,D==null?void 0:D.header),style:Object.assign(Object.assign({},(o=j==null?void 0:j.styles)===null||o===void 0?void 0:o.header),P==null?void 0:P.header)},l&&v.createElement("div",{className:Me(`${C}-title`,(i=j==null?void 0:j.classNames)===null||i===void 0?void 0:i.title,D==null?void 0:D.title),style:Object.assign(Object.assign({},(a=j==null?void 0:j.styles)===null||a===void 0?void 0:a.title),P==null?void 0:P.title)},l),d&&v.createElement("div",{className:Me(`${C}-extra`,(s=j==null?void 0:j.classNames)===null||s===void 0?void 0:s.extra,D==null?void 0:D.extra),style:Object.assign(Object.assign({},(c=j==null?void 0:j.styles)===null||c===void 0?void 0:c.extra),P==null?void 0:P.extra)},d)),v.createElement("div",{className:`${C}-view`},v.createElement("table",null,v.createElement("tbody",null,T.map((G,q)=>v.createElement(Pm,{key:q,index:q,colon:p,prefixCls:C,vertical:m==="vertical",bordered:g,row:G}))))))))};Ct.Item=Dm;var jm={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M888 792H200V168c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v688c0 4.4 3.6 8 8 8h752c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm-600-80h56c4.4 0 8-3.6 8-8V560c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v144c0 4.4 3.6 8 8 8zm152 0h56c4.4 0 8-3.6 8-8V384c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v320c0 4.4 3.6 8 8 8zm152 0h56c4.4 0 8-3.6 8-8V462c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v242c0 4.4 3.6 8 8 8zm152 0h56c4.4 0 8-3.6 8-8V304c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v400c0 4.4 3.6 8 8 8z"}}]},name:"bar-chart",theme:"outlined"},Mm={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M304 280h56c4.4 0 8-3.6 8-8 0-28.3 5.9-53.2 17.1-73.5 10.6-19.4 26-34.8 45.4-45.4C450.9 142 475.7 136 504 136h16c28.3 0 53.2 5.9 73.5 17.1 19.4 10.6 34.8 26 45.4 45.4C650 218.9 656 243.7 656 272c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8 0-40-8.8-76.7-25.9-108.1a184.31 184.31 0 00-74-74C596.7 72.8 560 64 520 64h-16c-40 0-76.7 8.8-108.1 25.9a184.31 184.31 0 00-74 74C304.8 195.3 296 232 296 272c0 4.4 3.6 8 8 8z"}},{tag:"path",attrs:{d:"M940 512H792V412c76.8 0 139-62.2 139-139 0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8a63 63 0 01-63 63H232a63 63 0 01-63-63c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8 0 76.8 62.2 139 139 139v100H84c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h148v96c0 6.5.2 13 .7 19.3C164.1 728.6 116 796.7 116 876c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8 0-44.2 23.9-82.9 59.6-103.7a273 273 0 0022.7 49c24.3 41.5 59 76.2 100.5 100.5S460.5 960 512 960s99.8-13.9 141.3-38.2a281.38 281.38 0 00123.2-149.5A120 120 0 01836 876c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8 0-79.3-48.1-147.4-116.7-176.7.4-6.4.7-12.8.7-19.3v-96h148c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM716 680c0 36.8-9.7 72-27.8 102.9-17.7 30.3-43 55.6-73.3 73.3C584 874.3 548.8 884 512 884s-72-9.7-102.9-27.8c-30.3-17.7-55.6-43-73.3-73.3A202.75 202.75 0 01308 680V412h408v268z"}}]},name:"bug",theme:"outlined"},km={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0051.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z"}},{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}}]},name:"check-circle",theme:"outlined"},$m={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm0 76c-205.4 0-372 166.6-372 372s166.6 372 372 372 372-166.6 372-372-166.6-372-372-372zm128.01 198.83c.03 0 .05.01.09.06l45.02 45.01a.2.2 0 01.05.09.12.12 0 010 .07c0 .02-.01.04-.05.08L557.25 512l127.87 127.86a.27.27 0 01.05.06v.02a.12.12 0 010 .07c0 .03-.01.05-.05.09l-45.02 45.02a.2.2 0 01-.09.05.12.12 0 01-.07 0c-.02 0-.04-.01-.08-.05L512 557.25 384.14 685.12c-.04.04-.06.05-.08.05a.12.12 0 01-.07 0c-.03 0-.05-.01-.09-.05l-45.02-45.02a.2.2 0 01-.05-.09.12.12 0 010-.07c0-.02.01-.04.06-.08L466.75 512 338.88 384.14a.27.27 0 01-.05-.06l-.01-.02a.12.12 0 010-.07c0-.03.01-.05.05-.09l45.02-45.02a.2.2 0 01.09-.05.12.12 0 01.07 0c.02 0 .04.01.08.06L512 466.75l127.86-127.86c.04-.05.06-.06.08-.06a.12.12 0 01.07 0z"}}]},name:"close-circle",theme:"outlined"},_m={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M464 688a48 48 0 1096 0 48 48 0 10-96 0zm24-112h48c4.4 0 8-3.6 8-8V296c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8z"}}]},name:"exclamation-circle",theme:"outlined"},Bm={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880.1 154H143.9c-24.5 0-39.8 26.7-27.5 48L349 597.4V838c0 17.7 14.2 32 31.8 32h262.4c17.6 0 31.8-14.3 31.8-32V597.4L907.7 202c12.2-21.3-3.1-48-27.6-48zM603.4 798H420.6V642h182.9v156zm9.6-236.6l-9.5 16.6h-183l-9.5-16.6L212.7 226h598.6L613 561.4z"}}]},name:"filter",theme:"outlined"},Lm={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M847.7 112H176.3c-35.5 0-64.3 28.8-64.3 64.3v671.4c0 35.5 28.8 64.3 64.3 64.3h671.4c35.5 0 64.3-28.8 64.3-64.3V176.3c0-35.5-28.8-64.3-64.3-64.3zm0 736c-447.8-.1-671.7-.2-671.7-.3.1-447.8.2-671.7.3-671.7 447.8.1 671.7.2 671.7.3-.1 447.8-.2 671.7-.3 671.7zM230.6 411.9h118.7v381.8H230.6zm59.4-52.2c37.9 0 68.8-30.8 68.8-68.8a68.8 68.8 0 10-137.6 0c-.1 38 30.7 68.8 68.8 68.8zm252.3 245.1c0-49.8 9.5-98 71.2-98 60.8 0 61.7 56.9 61.7 101.2v185.7h118.6V584.3c0-102.8-22.2-181.9-142.3-181.9-57.7 0-96.4 31.7-112.3 61.7h-1.6v-52.2H423.7v381.8h118.6V604.8z"}}]},name:"linkedin",theme:"outlined"},zm={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 110.8V792H136V270.8l-27.6-21.5 39.3-50.5 42.8 33.3h643.1l42.8-33.3 39.3 50.5-27.7 21.5zM833.6 232L512 482 190.4 232l-42.8-33.3-39.3 50.5 27.6 21.5 341.6 265.6a55.99 55.99 0 0068.7 0L888 270.8l27.6-21.5-39.3-50.5-42.7 33.2z"}}]},name:"mail",theme:"outlined"},Fm={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M464 512a48 48 0 1096 0 48 48 0 10-96 0zm200 0a48 48 0 1096 0 48 48 0 10-96 0zm-400 0a48 48 0 1096 0 48 48 0 10-96 0zm661.2-173.6c-22.6-53.7-55-101.9-96.3-143.3a444.35 444.35 0 00-143.3-96.3C630.6 75.7 572.2 64 512 64h-2c-60.6.3-119.3 12.3-174.5 35.9a445.35 445.35 0 00-142 96.5c-40.9 41.3-73 89.3-95.2 142.8-23 55.4-34.6 114.3-34.3 174.9A449.4 449.4 0 00112 714v152a46 46 0 0046 46h152.1A449.4 449.4 0 00510 960h2.1c59.9 0 118-11.6 172.7-34.3a444.48 444.48 0 00142.8-95.2c41.3-40.9 73.8-88.7 96.5-142 23.6-55.2 35.6-113.9 35.9-174.5.3-60.9-11.5-120-34.8-175.6zm-151.1 438C704 845.8 611 884 512 884h-1.7c-60.3-.3-120.2-15.3-173.1-43.5l-8.4-4.5H188V695.2l-4.5-8.4C155.3 633.9 140.3 574 140 513.7c-.4-99.7 37.7-193.3 107.6-263.8 69.8-70.5 163.1-109.5 262.8-109.9h1.7c50 0 98.5 9.7 144.2 28.9 44.6 18.7 84.6 45.6 119 80 34.3 34.3 61.3 74.4 80 119 19.4 46.2 29.1 95.2 28.9 145.8-.6 99.6-39.7 192.9-110.1 262.7z"}}]},name:"message",theme:"outlined"},Hm={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M692.8 412.7l.2-.2-34.6-44.3a7.97 7.97 0 00-11.2-1.4l-50.4 39.3-70.5-90.1a7.97 7.97 0 00-11.2-1.4l-37.9 29.7a7.97 7.97 0 00-1.4 11.2l70.5 90.2-.2.1 34.6 44.3c2.7 3.5 7.7 4.1 11.2 1.4l50.4-39.3 64.1 82c2.7 3.5 7.7 4.1 11.2 1.4l37.9-29.6c3.5-2.7 4.1-7.7 1.4-11.2l-64.1-82.1zM608 112c-167.9 0-304 136.1-304 304 0 70.3 23.9 135 63.9 186.5L114.3 856.1a8.03 8.03 0 000 11.3l42.3 42.3c3.1 3.1 8.2 3.1 11.3 0l253.6-253.6C473 696.1 537.7 720 608 720c167.9 0 304-136.1 304-304S775.9 112 608 112zm161.2 465.2C726.2 620.3 668.9 644 608 644s-118.2-23.7-161.2-66.8C403.7 534.2 380 476.9 380 416s23.7-118.2 66.8-161.2c43-43.1 100.3-66.8 161.2-66.8s118.2 23.7 161.2 66.8c43.1 43 66.8 100.3 66.8 161.2s-23.7 118.2-66.8 161.2z"}}]},name:"monitor",theme:"outlined"},Wm={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372zm-88-532h-48c-4.4 0-8 3.6-8 8v304c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V360c0-4.4-3.6-8-8-8zm224 0h-48c-4.4 0-8 3.6-8 8v304c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V360c0-4.4-3.6-8-8-8z"}}]},name:"pause-circle",theme:"outlined"},Vm={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M877.1 238.7L770.6 132.3c-13-13-30.4-20.3-48.8-20.3s-35.8 7.2-48.8 20.3L558.3 246.8c-13 13-20.3 30.5-20.3 48.9 0 18.5 7.2 35.8 20.3 48.9l89.6 89.7a405.46 405.46 0 01-86.4 127.3c-36.7 36.9-79.6 66-127.2 86.6l-89.6-89.7c-13-13-30.4-20.3-48.8-20.3a68.2 68.2 0 00-48.8 20.3L132.3 673c-13 13-20.3 30.5-20.3 48.9 0 18.5 7.2 35.8 20.3 48.9l106.4 106.4c22.2 22.2 52.8 34.9 84.2 34.9 6.5 0 12.8-.5 19.2-1.6 132.4-21.8 263.8-92.3 369.9-198.3C818 606 888.4 474.6 910.4 342.1c6.3-37.6-6.3-76.3-33.3-103.4zm-37.6 91.5c-19.5 117.9-82.9 235.5-178.4 331s-213 158.9-330.9 178.4c-14.8 2.5-30-2.5-40.8-13.2L184.9 721.9 295.7 611l119.8 120 .9.9 21.6-8a481.29 481.29 0 00285.7-285.8l8-21.6-120.8-120.7 110.8-110.9 104.5 104.5c10.8 10.8 15.8 26 13.3 40.8z"}}]},name:"phone",theme:"outlined"},Um={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M719.4 499.1l-296.1-215A15.9 15.9 0 00398 297v430c0 13.1 14.8 20.5 25.3 12.9l296.1-215a15.9 15.9 0 000-25.8zm-257.6 134V390.9L628.5 512 461.8 633.1z"}}]},name:"play-circle",theme:"outlined"},qm={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M758.2 839.1C851.8 765.9 912 651.9 912 523.9 912 303 733.5 124.3 512.6 124 291.4 123.7 112 302.8 112 523.9c0 125.2 57.5 236.9 147.6 310.2 3.5 2.8 8.6 2.2 11.4-1.3l39.4-50.5c2.7-3.4 2.1-8.3-1.2-11.1-8.1-6.6-15.9-13.7-23.4-21.2a318.64 318.64 0 01-68.6-101.7C200.4 609 192 567.1 192 523.9s8.4-85.1 25.1-124.5c16.1-38.1 39.2-72.3 68.6-101.7 29.4-29.4 63.6-52.5 101.7-68.6C426.9 212.4 468.8 204 512 204s85.1 8.4 124.5 25.1c38.1 16.1 72.3 39.2 101.7 68.6 29.4 29.4 52.5 63.6 68.6 101.7 16.7 39.4 25.1 81.3 25.1 124.5s-8.4 85.1-25.1 124.5a318.64 318.64 0 01-68.6 101.7c-9.3 9.3-19.1 18-29.3 26L668.2 724a8 8 0 00-14.1 3l-39.6 162.2c-1.2 5 2.6 9.9 7.7 9.9l167 .8c6.7 0 10.5-7.7 6.3-12.9l-37.3-47.9z"}}]},name:"redo",theme:"outlined"},Gm={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M909.1 209.3l-56.4 44.1C775.8 155.1 656.2 92 521.9 92 290 92 102.3 279.5 102 511.5 101.7 743.7 289.8 932 521.9 932c181.3 0 335.8-115 394.6-276.1 1.5-4.2-.7-8.9-4.9-10.3l-56.7-19.5a8 8 0 00-10.1 4.8c-1.8 5-3.8 10-5.9 14.9-17.3 41-42.1 77.8-73.7 109.4A344.77 344.77 0 01655.9 829c-42.3 17.9-87.4 27-133.8 27-46.5 0-91.5-9.1-133.8-27A341.5 341.5 0 01279 755.2a342.16 342.16 0 01-73.7-109.4c-17.9-42.4-27-87.4-27-133.9s9.1-91.5 27-133.9c17.3-41 42.1-77.8 73.7-109.4 31.6-31.6 68.4-56.4 109.3-73.8 42.3-17.9 87.4-27 133.8-27 46.5 0 91.5 9.1 133.8 27a341.5 341.5 0 01109.3 73.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.6 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c-.1-6.6-7.8-10.3-13-6.2z"}}]},name:"reload",theme:"outlined"},Xm={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M931.4 498.9L94.9 79.5c-3.4-1.7-7.3-2.1-11-1.2a15.99 15.99 0 00-11.7 19.3l86.2 352.2c1.3 5.3 5.2 9.6 10.4 11.3l147.7 50.7-147.6 50.7c-5.2 1.8-9.1 6-10.3 11.3L72.2 926.5c-.9 3.7-.5 7.6 1.2 10.9 3.9 7.9 13.5 11.1 21.5 7.2l836.5-417c3.1-1.5 5.6-4.1 7.2-7.1 3.9-8 .7-17.6-7.2-21.6zM170.8 826.3l50.3-205.6 295.2-101.3c2.3-.8 4.2-2.6 5-5 1.4-4.2-.8-8.7-5-10.2L221.1 403 171 198.2l628 314.9-628.2 313.2z"}}]},name:"send",theme:"outlined"},Ym={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M924.8 625.7l-65.5-56c3.1-19 4.7-38.4 4.7-57.8s-1.6-38.8-4.7-57.8l65.5-56a32.03 32.03 0 009.3-35.2l-.9-2.6a443.74 443.74 0 00-79.7-137.9l-1.8-2.1a32.12 32.12 0 00-35.1-9.5l-81.3 28.9c-30-24.6-63.5-44-99.7-57.6l-15.7-85a32.05 32.05 0 00-25.8-25.7l-2.7-.5c-52.1-9.4-106.9-9.4-159 0l-2.7.5a32.05 32.05 0 00-25.8 25.7l-15.8 85.4a351.86 351.86 0 00-99 57.4l-81.9-29.1a32 32 0 00-35.1 9.5l-1.8 2.1a446.02 446.02 0 00-79.7 137.9l-.9 2.6c-4.5 12.5-.8 26.5 9.3 35.2l66.3 56.6c-3.1 18.8-4.6 38-4.6 57.1 0 19.2 1.5 38.4 4.6 57.1L99 625.5a32.03 32.03 0 00-9.3 35.2l.9 2.6c18.1 50.4 44.9 96.9 79.7 137.9l1.8 2.1a32.12 32.12 0 0035.1 9.5l81.9-29.1c29.8 24.5 63.1 43.9 99 57.4l15.8 85.4a32.05 32.05 0 0025.8 25.7l2.7.5a449.4 449.4 0 00159 0l2.7-.5a32.05 32.05 0 0025.8-25.7l15.7-85a350 350 0 0099.7-57.6l81.3 28.9a32 32 0 0035.1-9.5l1.8-2.1c34.8-41.1 61.6-87.5 79.7-137.9l.9-2.6c4.5-12.3.8-26.3-9.3-35zM788.3 465.9c2.5 15.1 3.8 30.6 3.8 46.1s-1.3 31-3.8 46.1l-6.6 40.1 74.7 63.9a370.03 370.03 0 01-42.6 73.6L721 702.8l-31.4 25.8c-23.9 19.6-50.5 35-79.3 45.8l-38.1 14.3-17.9 97a377.5 377.5 0 01-85 0l-17.9-97.2-37.8-14.5c-28.5-10.8-55-26.2-78.7-45.7l-31.4-25.9-93.4 33.2c-17-22.9-31.2-47.6-42.6-73.6l75.5-64.5-6.5-40c-2.4-14.9-3.7-30.3-3.7-45.5 0-15.3 1.2-30.6 3.7-45.5l6.5-40-75.5-64.5c11.3-26.1 25.6-50.7 42.6-73.6l93.4 33.2 31.4-25.9c23.7-19.5 50.2-34.9 78.7-45.7l37.9-14.3 17.9-97.2c28.1-3.2 56.8-3.2 85 0l17.9 97 38.1 14.3c28.7 10.8 55.4 26.2 79.3 45.8l31.4 25.8 92.8-32.9c17 22.9 31.2 47.6 42.6 73.6L781.8 426l6.5 39.9zM512 326c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm79.2 255.2A111.6 111.6 0 01512 614c-29.9 0-58-11.7-79.2-32.8A111.6 111.6 0 01400 502c0-29.9 11.7-58 32.8-79.2C454 401.6 482.1 390 512 390c29.9 0 58 11.6 79.2 32.8A111.6 111.6 0 01624 502c0 29.9-11.7 58-32.8 79.2z"}}]},name:"setting",theme:"outlined"},Km={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372 0-89 31.3-170.8 83.5-234.8l523.3 523.3C682.8 852.7 601 884 512 884zm288.5-137.2L277.2 223.5C341.2 171.3 423 140 512 140c205.4 0 372 166.6 372 372 0 89-31.3 170.8-83.5 234.8z"}}]},name:"stop",theme:"outlined"},Zm={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M511.4 124C290.5 124.3 112 303 112 523.9c0 128 60.2 242 153.8 315.2l-37.5 48c-4.1 5.3-.3 13 6.3 12.9l167-.8c5.2 0 9-4.9 7.7-9.9L369.8 727a8 8 0 00-14.1-3L315 776.1c-10.2-8-20-16.7-29.3-26a318.64 318.64 0 01-68.6-101.7C200.4 609 192 567.1 192 523.9s8.4-85.1 25.1-124.5c16.1-38.1 39.2-72.3 68.6-101.7 29.4-29.4 63.6-52.5 101.7-68.6C426.9 212.4 468.8 204 512 204s85.1 8.4 124.5 25.1c38.1 16.1 72.3 39.2 101.7 68.6 29.4 29.4 52.5 63.6 68.6 101.7 16.7 39.4 25.1 81.3 25.1 124.5s-8.4 85.1-25.1 124.5a318.64 318.64 0 01-68.6 101.7c-7.5 7.5-15.3 14.5-23.4 21.2a7.93 7.93 0 00-1.2 11.1l39.4 50.5c2.8 3.5 7.9 4.1 11.4 1.3C854.5 760.8 912 649.1 912 523.9c0-221.1-179.4-400.2-400.6-399.9z"}}]},name:"undo",theme:"outlined"},Jm={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M912 192H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM104 228a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"unordered-list",theme:"outlined"},Qm={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M713.5 599.9c-10.9-5.6-65.2-32.2-75.3-35.8-10.1-3.8-17.5-5.6-24.8 5.6-7.4 11.1-28.4 35.8-35 43.3-6.4 7.4-12.9 8.3-23.8 2.8-64.8-32.4-107.3-57.8-150-131.1-11.3-19.5 11.3-18.1 32.4-60.2 3.6-7.4 1.8-13.7-1-19.3-2.8-5.6-24.8-59.8-34-81.9-8.9-21.5-18.1-18.5-24.8-18.9-6.4-.4-13.7-.4-21.1-.4-7.4 0-19.3 2.8-29.4 13.7-10.1 11.1-38.6 37.8-38.6 92s39.5 106.7 44.9 114.1c5.6 7.4 77.7 118.6 188.4 166.5 70 30.2 97.4 32.8 132.4 27.6 21.3-3.2 65.2-26.6 74.3-52.5 9.1-25.8 9.1-47.9 6.4-52.5-2.7-4.9-10.1-7.7-21-13z"}},{tag:"path",attrs:{d:"M925.2 338.4c-22.6-53.7-55-101.9-96.3-143.3a444.35 444.35 0 00-143.3-96.3C630.6 75.7 572.2 64 512 64h-2c-60.6.3-119.3 12.3-174.5 35.9a445.35 445.35 0 00-142 96.5c-40.9 41.3-73 89.3-95.2 142.8-23 55.4-34.6 114.3-34.3 174.9A449.4 449.4 0 00112 714v152a46 46 0 0046 46h152.1A449.4 449.4 0 00510 960h2.1c59.9 0 118-11.6 172.7-34.3a444.48 444.48 0 00142.8-95.2c41.3-40.9 73.8-88.7 96.5-142 23.6-55.2 35.6-113.9 35.9-174.5.3-60.9-11.5-120-34.8-175.6zm-151.1 438C704 845.8 611 884 512 884h-1.7c-60.3-.3-120.2-15.3-173.1-43.5l-8.4-4.5H188V695.2l-4.5-8.4C155.3 633.9 140.3 574 140 513.7c-.4-99.7 37.7-193.3 107.6-263.8 69.8-70.5 163.1-109.5 262.8-109.9h1.7c50 0 98.5 9.7 144.2 28.9 44.6 18.7 84.6 45.6 119 80 34.3 34.3 61.3 74.4 80 119 19.4 46.2 29.1 95.2 28.9 145.8-.6 99.6-39.7 192.9-110.1 262.7z"}}]},name:"whats-app",theme:"outlined"};const ev=e=>{const{value:t,formatter:n,precision:r,decimalSeparator:o,groupSeparator:i="",prefixCls:a}=e;let s;if(typeof n=="function")s=n(t);else{const c=String(t),u=c.match(/^(-?)(\d*)(\.(\d+))?$/);if(!u||c==="-")s=c;else{const l=u[1];let d=u[2]||"0",f=u[4]||"";d=d.replace(/\B(?=(\d{3})+(?!\d))/g,i),typeof r=="number"&&(f=f.padEnd(r,"0").slice(0,r>0?r:0)),f&&(f=`${o}${f}`),s=[v.createElement("span",{key:"int",className:`${a}-content-value-int`},l,d),f&&v.createElement("span",{key:"decimal",className:`${a}-content-value-decimal`},f)]}}return v.createElement("span",{className:`${a}-content-value`},s)},tv=e=>{const{componentCls:t,marginXXS:n,padding:r,colorTextDescription:o,titleFontSize:i,colorTextHeading:a,contentFontSize:s,fontFamily:c}=e;return{[t]:Object.assign(Object.assign({},io(e)),{[`${t}-title`]:{marginBottom:n,color:o,fontSize:i},[`${t}-skeleton`]:{paddingTop:r},[`${t}-content`]:{color:a,fontSize:s,fontFamily:c,[`${t}-content-value`]:{display:"inline-block",direction:"ltr"},[`${t}-content-prefix, ${t}-content-suffix`]:{display:"inline-block"},[`${t}-content-prefix`]:{marginInlineEnd:n},[`${t}-content-suffix`]:{marginInlineStart:n}}})}},nv=e=>{const{fontSizeHeading3:t,fontSize:n}=e;return{titleFontSize:n,contentFontSize:t}},rv=oo("Statistic",e=>{const t=vs(e,{});return[tv(t)]},nv);var ov=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const Fe=e=>{const{prefixCls:t,className:n,rootClassName:r,style:o,valueStyle:i,value:a=0,title:s,valueRender:c,prefix:u,suffix:l,loading:d=!1,formatter:f,precision:p,decimalSeparator:g=".",groupSeparator:m=",",onMouseEnter:x,onMouseLeave:w}=e,y=ov(e,["prefixCls","className","rootClassName","style","valueStyle","value","title","valueRender","prefix","suffix","loading","formatter","precision","decimalSeparator","groupSeparator","onMouseEnter","onMouseLeave"]),{getPrefixCls:E,direction:S,statistic:b}=v.useContext(ur),I=E("statistic",t),[P,A,D]=rv(I),k=v.createElement(ev,{decimalSeparator:g,groupSeparator:m,prefixCls:I,formatter:f,precision:p,value:a}),L=Me(I,{[`${I}-rtl`]:S==="rtl"},b==null?void 0:b.className,n,r,A,D),B=Zu(y,{aria:!0,data:!0});return P(v.createElement("div",Object.assign({},B,{className:L,style:Object.assign(Object.assign({},b==null?void 0:b.style),o),onMouseEnter:x,onMouseLeave:w}),s&&v.createElement("div",{className:`${I}-title`},s),v.createElement(ag,{paragraph:!1,loading:d,className:`${I}-skeleton`},v.createElement("div",{style:i,className:`${I}-content`},u&&v.createElement("span",{className:`${I}-content-prefix`},u),c?c(k):k,l&&v.createElement("span",{className:`${I}-content-suffix`},l)))))},iv=[["Y",1e3*60*60*24*365],["M",1e3*60*60*24*30],["D",1e3*60*60*24],["H",1e3*60*60],["m",1e3*60],["s",1e3],["S",1]];function av(e,t){let n=e;const r=/\[[^\]]*]/g,o=(t.match(r)||[]).map(c=>c.slice(1,-1)),i=t.replace(r,"[]"),a=iv.reduce((c,u)=>{let[l,d]=u;if(c.includes(l)){const f=Math.floor(n/d);return n-=f*d,c.replace(new RegExp(`${l}+`,"g"),p=>{const g=p.length;return f.toString().padStart(g,"0")})}return c},i);let s=0;return a.replace(r,()=>{const c=o[s];return s+=1,c})}function sv(e,t){const{format:n=""}=t,r=new Date(e).getTime(),o=Date.now(),i=Math.max(r-o,0);return av(i,n)}var lv=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const cv=1e3/30;function uv(e){return new Date(e).getTime()}const dv=e=>{const{value:t,format:n="HH:mm:ss",onChange:r,onFinish:o}=e,i=lv(e,["value","format","onChange","onFinish"]),a=sg(),s=v.useRef(null),c=()=>{o==null||o(),s.current&&(clearInterval(s.current),s.current=null)},u=()=>{const f=uv(t);f>=Date.now()&&(s.current=setInterval(()=>{a(),r==null||r(f-Date.now()),f<Date.now()&&c()},cv))};v.useEffect(()=>(u(),()=>{s.current&&(clearInterval(s.current),s.current=null)}),[t]);const l=(f,p)=>sv(f,Object.assign(Object.assign({},p),{format:n})),d=f=>ys(f,{title:void 0});return v.createElement(Fe,Object.assign({},i,{value:t,valueRender:d,formatter:l}))},fv=v.memo(dv);Fe.Countdown=fv;var pv=function(t,n){return v.createElement($e,_e({},t,{ref:n,icon:jm}))},Vl=v.forwardRef(pv),hv=function(t,n){return v.createElement($e,_e({},t,{ref:n,icon:Mm}))},gv=v.forwardRef(hv),mv=function(t,n){return v.createElement($e,_e({},t,{ref:n,icon:km}))},tr=v.forwardRef(mv),vv=function(t,n){return v.createElement($e,_e({},t,{ref:n,icon:lg}))},Kn=v.forwardRef(vv),yv=function(t,n){return v.createElement($e,_e({},t,{ref:n,icon:$m}))},bv=v.forwardRef(yv),xv=function(t,n){return v.createElement($e,_e({},t,{ref:n,icon:_m}))},ea=v.forwardRef(xv),wv=function(t,n){return v.createElement($e,_e({},t,{ref:n,icon:Bm}))},Sv=v.forwardRef(wv),Cv=function(t,n){return v.createElement($e,_e({},t,{ref:n,icon:Lm}))},Ba=v.forwardRef(Cv),Ev=function(t,n){return v.createElement($e,_e({},t,{ref:n,icon:zm}))},$r=v.forwardRef(Ev),Iv=function(t,n){return v.createElement($e,_e({},t,{ref:n,icon:Fm}))},La=v.forwardRef(Iv),Dv=function(t,n){return v.createElement($e,_e({},t,{ref:n,icon:Hm}))},Ul=v.forwardRef(Dv),Pv=function(t,n){return v.createElement($e,_e({},t,{ref:n,icon:Wm}))},Av=v.forwardRef(Pv),Nv=function(t,n){return v.createElement($e,_e({},t,{ref:n,icon:Vm}))},za=v.forwardRef(Nv),Ov=function(t,n){return v.createElement($e,_e({},t,{ref:n,icon:Um}))},Yo=v.forwardRef(Ov),Rv=function(t,n){return v.createElement($e,_e({},t,{ref:n,icon:qm}))},Tv=v.forwardRef(Rv),jv=function(t,n){return v.createElement($e,_e({},t,{ref:n,icon:Gm}))},jn=v.forwardRef(jv),Mv=function(t,n){return v.createElement($e,_e({},t,{ref:n,icon:Xm}))},ql=v.forwardRef(Mv),kv=function(t,n){return v.createElement($e,_e({},t,{ref:n,icon:Ym}))},hd=v.forwardRef(kv),$v=function(t,n){return v.createElement($e,_e({},t,{ref:n,icon:Km}))},ri=v.forwardRef($v),_v=function(t,n){return v.createElement($e,_e({},t,{ref:n,icon:Zm}))},Bv=v.forwardRef(_v),Lv=function(t,n){return v.createElement($e,_e({},t,{ref:n,icon:Jm}))},zv=v.forwardRef(Lv),Fv=function(t,n){return v.createElement($e,_e({},t,{ref:n,icon:Qm}))},Fa=v.forwardRef(Fv);function fr(e){const t=[];let n=-1,r=0,o=0;for(;++n<e.length;){const i=e.charCodeAt(n);let a="";if(i===37&&Bl(e.charCodeAt(n+1))&&Bl(e.charCodeAt(n+2)))o=2;else if(i<128)/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(i))||(a=String.fromCharCode(i));else if(i>55295&&i<57344){const s=e.charCodeAt(n+1);i<56320&&s>56319&&s<57344?(a=String.fromCharCode(i,s),o=1):a="�"}else a=String.fromCharCode(i);a&&(t.push(e.slice(r,n),encodeURIComponent(a)),r=n+o+1,a=""),o&&(n+=o,o=0)}return t.join("")+e.slice(r)}const Hv=[{id:1,alias:"1st",total_candidates:100,title:"Outreach Email",is_active:!0,icon:"mdi:email",stats:[{id:1,title:"Email Sent",value:0,icon:"mdi:tick",color:"green"},{id:2,title:"Email Failed",value:0,icon:"mdi:close",color:"red"},{id:3,title:"Auto Reply",value:0,icon:"mdi:auto-mode",color:"blue"},{id:4,title:"Potential Reply",value:0,icon:"tabler:message-up",color:"green"}]},{id:2,alias:"2nd",total_candidates:50,title:"Follow up Email",is_active:!1,icon:"mdi:email",stats:[{id:1,title:"Email Sent",value:0,icon:"mdi:tick",color:"green"},{id:2,title:"Email Failed",value:0,icon:"mdi:close",color:"red"},{id:3,title:"Auto Reply",value:0,icon:"mdi:auto-mode",color:"blue"},{id:4,title:"Potential Reply",value:0,icon:"tabler:message-up",color:"green"}]},{id:3,alias:"3rd",total_candidates:45,title:"WhatsApp",is_active:!1,icon:"logos:whatsapp-icon",stats:[{id:1,title:"WhatsApp Sent",value:0,icon:"mdi:tick",color:"green"},{id:2,title:"WhatsApp Replied",value:0,icon:"hugeicons:message-02",color:"blue"},{id:3,title:"WhatsApp Failed",value:0,icon:"mdi:close",color:"red"}]},{id:4,alias:"4th",total_candidates:45,title:"SMS",is_active:!1,icon:"material-symbols:sms",stats:[{id:1,title:"SMS Sent",value:0,icon:"mdi:tick",color:"green"},{id:2,title:"SMS Replied",value:0,icon:"hugeicons:message-02",color:"blue"},{id:3,title:"SMS Failed",value:0,icon:"bitcoin-icons:cross-filled",color:"red"}]},{id:5,alias:"5th",total_candidates:40,title:"LinkedIn Connection Request",is_active:!1,icon:"flowbite:linkedin-solid",stats:[{id:1,title:"Connection Request Sent",value:0,icon:"mdi:tick",color:"green"},{id:2,title:"Connection Request Replied",value:0,icon:"codicon:verified-filled",color:"#E5B137"},{id:3,title:"Connection Request Failed",value:0,icon:"lucide:refresh-cw-off",color:"red"}]},{id:6,alias:"6th",total_candidates:40,title:"Call",is_active:!1,icon:"mdi:phone",stats:[{id:1,title:"Called",value:0,icon:"mdi:tick",color:"green"},{id:2,title:"Call Replied",value:0,icon:"solar:outgoing-call-bold",color:"#E5B137"},{id:3,title:"Call Failed",value:0,icon:"subway:call-3",color:"red"}]},{id:7,alias:"7th",total_candidates:35,title:"LinkedIn InMail",is_active:!1,icon:"flowbite:linkedin-solid",stats:[{id:1,title:"InMail Sent",value:0,icon:"mdi:tick",color:"green"},{id:2,title:"InMail Replied",value:0,icon:"codicon:verified-filled",color:"#E5B137"},{id:3,title:"InMail Failed",value:0,icon:"lucide:refresh-cw-off",color:"red"}]}];function Wv({open:e,anchorEl:t,handlePopoverClose:n,handleAddStep:r}){return h.jsx("div",{children:h.jsx(nd,{open:e,anchorEl:t,onClose:n,anchorOrigin:{vertical:"bottom",horizontal:"center"},children:h.jsxs(rd,{children:[h.jsxs(un,{style:{cursor:"pointer"},onClick:()=>r("Follow up Email"),children:[h.jsx(dn,{children:h.jsx(Se,{icon:"ic:baseline-email",style:{fontSize:"20px",color:"#00C1FE"}})}),h.jsx(fn,{primary:"Email",sx:{fontSize:"8px",color:"#00C1FE"}})]}),h.jsxs(un,{style:{cursor:"pointer"},onClick:()=>r("WhatsApp"),children:[h.jsx(dn,{children:h.jsx(Se,{icon:"logos:whatsapp-icon",style:{fontSize:"20px"}})}),h.jsx(fn,{primary:"WhatsApp",sx:{fontSize:"12px",color:"#60D669"}})]}),h.jsxs(un,{style:{cursor:"pointer"},onClick:()=>r("SMS"),children:[h.jsx(dn,{children:h.jsx(Se,{icon:"material-symbols:sms",style:{fontSize:"20px",color:"#009688"}})}),h.jsx(fn,{primary:"SMS",sx:{fontSize:"12px",color:"#009688"}})]}),h.jsxs(un,{style:{cursor:"pointer"},onClick:()=>r("LinkedIn Connection Request"),children:[h.jsx(dn,{children:h.jsx(Se,{icon:"flowbite:linkedin-solid",style:{fontSize:"20px",color:"#007"}})}),h.jsx(fn,{primary:"Connection Request",sx:{fontSize:"12px",color:"#7D8592"}})]}),h.jsxs(un,{style:{cursor:"pointer"},onClick:()=>r("Call"),children:[h.jsx(dn,{children:h.jsx(Se,{icon:"material-symbols:call",style:{fontSize:"20px",color:"#D1D42B"}})}),h.jsx(fn,{primary:"Call",sx:{fontSize:"12px",color:"#7D8592"}})]}),h.jsxs(un,{style:{cursor:"pointer"},onClick:()=>r("LinkedIn InMail"),children:[h.jsx(dn,{children:h.jsx(Se,{icon:"mdi:linkedin",style:{fontSize:"20px",color:"#C8773F"}})}),h.jsx(fn,{primary:"InMail",sx:{fontSize:"12px",color:"#7D8592"}})]})]})})})}function Ha(e,t){return Ha=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,r){return n.__proto__=r,n},Ha(e,t)}function gd(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,Ha(e,t)}function de(){return de=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},de.apply(null,arguments)}var md=z.createContext(null);function Vv(e){e()}var vd=Vv,Uv=function(t){return vd=t},qv=function(){return vd};function Gv(){var e=qv(),t=null,n=null;return{clear:function(){t=null,n=null},notify:function(){e(function(){for(var o=t;o;)o.callback(),o=o.next})},get:function(){for(var o=[],i=t;i;)o.push(i),i=i.next;return o},subscribe:function(o){var i=!0,a=n={callback:o,next:null,prev:n};return a.prev?a.prev.next=a:t=a,function(){!i||t===null||(i=!1,a.next?a.next.prev=a.prev:n=a.prev,a.prev?a.prev.next=a.next:t=a.next)}}}}var Gl={notify:function(){},get:function(){return[]}};function yd(e,t){var n,r=Gl;function o(d){return c(),r.subscribe(d)}function i(){r.notify()}function a(){l.onStateChange&&l.onStateChange()}function s(){return!!n}function c(){n||(n=t?t.addNestedSub(a):e.subscribe(a),r=Gv())}function u(){n&&(n(),n=void 0,r.clear(),r=Gl)}var l={addNestedSub:o,notifyNestedSubs:i,handleChangeWrapper:a,isSubscribed:s,trySubscribe:c,tryUnsubscribe:u,getListeners:function(){return r}};return l}var bd=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u"?v.useLayoutEffect:v.useEffect;function Xv(e){var t=e.store,n=e.context,r=e.children,o=v.useMemo(function(){var s=yd(t);return{store:t,subscription:s}},[t]),i=v.useMemo(function(){return t.getState()},[t]);bd(function(){var s=o.subscription;return s.onStateChange=s.notifyNestedSubs,s.trySubscribe(),i!==t.getState()&&s.notifyNestedSubs(),function(){s.tryUnsubscribe(),s.onStateChange=null}},[o,i]);var a=n||md;return z.createElement(a.Provider,{value:o},r)}function oi(e,t){if(e==null)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(t.indexOf(r)!==-1)continue;n[r]=e[r]}return n}var xd={exports:{}},Ae={};/** @license React v17.0.2
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ei=60103,Ii=60106,so=60107,lo=60108,co=60114,uo=60109,fo=60110,po=60112,ho=60113,Is=60120,go=60115,mo=60116,wd=60121,Sd=60122,Cd=60117,Ed=60129,Id=60131;if(typeof Symbol=="function"&&Symbol.for){var Ye=Symbol.for;Ei=Ye("react.element"),Ii=Ye("react.portal"),so=Ye("react.fragment"),lo=Ye("react.strict_mode"),co=Ye("react.profiler"),uo=Ye("react.provider"),fo=Ye("react.context"),po=Ye("react.forward_ref"),ho=Ye("react.suspense"),Is=Ye("react.suspense_list"),go=Ye("react.memo"),mo=Ye("react.lazy"),wd=Ye("react.block"),Sd=Ye("react.server.block"),Cd=Ye("react.fundamental"),Ed=Ye("react.debug_trace_mode"),Id=Ye("react.legacy_hidden")}function qt(e){if(typeof e=="object"&&e!==null){var t=e.$$typeof;switch(t){case Ei:switch(e=e.type,e){case so:case co:case lo:case ho:case Is:return e;default:switch(e=e&&e.$$typeof,e){case fo:case po:case mo:case go:case uo:return e;default:return t}}case Ii:return t}}}var Yv=uo,Kv=Ei,Zv=po,Jv=so,Qv=mo,e0=go,t0=Ii,n0=co,r0=lo,o0=ho;Ae.ContextConsumer=fo;Ae.ContextProvider=Yv;Ae.Element=Kv;Ae.ForwardRef=Zv;Ae.Fragment=Jv;Ae.Lazy=Qv;Ae.Memo=e0;Ae.Portal=t0;Ae.Profiler=n0;Ae.StrictMode=r0;Ae.Suspense=o0;Ae.isAsyncMode=function(){return!1};Ae.isConcurrentMode=function(){return!1};Ae.isContextConsumer=function(e){return qt(e)===fo};Ae.isContextProvider=function(e){return qt(e)===uo};Ae.isElement=function(e){return typeof e=="object"&&e!==null&&e.$$typeof===Ei};Ae.isForwardRef=function(e){return qt(e)===po};Ae.isFragment=function(e){return qt(e)===so};Ae.isLazy=function(e){return qt(e)===mo};Ae.isMemo=function(e){return qt(e)===go};Ae.isPortal=function(e){return qt(e)===Ii};Ae.isProfiler=function(e){return qt(e)===co};Ae.isStrictMode=function(e){return qt(e)===lo};Ae.isSuspense=function(e){return qt(e)===ho};Ae.isValidElementType=function(e){return typeof e=="string"||typeof e=="function"||e===so||e===co||e===Ed||e===lo||e===ho||e===Is||e===Id||typeof e=="object"&&e!==null&&(e.$$typeof===mo||e.$$typeof===go||e.$$typeof===uo||e.$$typeof===fo||e.$$typeof===po||e.$$typeof===Cd||e.$$typeof===wd||e[0]===Sd)};Ae.typeOf=qt;xd.exports=Ae;var i0=xd.exports,a0=["getDisplayName","methodName","renderCountProp","shouldHandleStateChanges","storeKey","withRef","forwardRef","context"],s0=["reactReduxForwardedRef"],l0=[],c0=[null,null];function u0(e,t){var n=e[1];return[t.payload,n+1]}function Xl(e,t,n){bd(function(){return e.apply(void 0,t)},n)}function d0(e,t,n,r,o,i,a){e.current=r,t.current=o,n.current=!1,i.current&&(i.current=null,a())}function f0(e,t,n,r,o,i,a,s,c,u){if(e){var l=!1,d=null,f=function(){if(!l){var m=t.getState(),x,w;try{x=r(m,o.current)}catch(y){w=y,d=y}w||(d=null),x===i.current?a.current||c():(i.current=x,s.current=x,a.current=!0,u({type:"STORE_UPDATED",payload:{error:w}}))}};n.onStateChange=f,n.trySubscribe(),f();var p=function(){if(l=!0,n.tryUnsubscribe(),n.onStateChange=null,d)throw d};return p}}var p0=function(){return[null,0]};function h0(e,t){t===void 0&&(t={});var n=t,r=n.getDisplayName,o=r===void 0?function(E){return"ConnectAdvanced("+E+")"}:r,i=n.methodName,a=i===void 0?"connectAdvanced":i,s=n.renderCountProp,c=s===void 0?void 0:s,u=n.shouldHandleStateChanges,l=u===void 0?!0:u,d=n.storeKey,f=d===void 0?"store":d;n.withRef;var p=n.forwardRef,g=p===void 0?!1:p,m=n.context,x=m===void 0?md:m,w=oi(n,a0),y=x;return function(S){var b=S.displayName||S.name||"Component",I=o(b),P=de({},w,{getDisplayName:o,methodName:a,renderCountProp:c,shouldHandleStateChanges:l,storeKey:f,displayName:I,wrappedComponentName:b,WrappedComponent:S}),A=w.pure;function D(C){return e(C.dispatch,P)}var k=A?v.useMemo:function(C){return C()};function L(C){var O=v.useMemo(function(){var se=C.reactReduxForwardedRef,me=oi(C,s0);return[C.context,se,me]},[C]),N=O[0],_=O[1],W=O[2],T=v.useMemo(function(){return N&&N.Consumer&&i0.isContextConsumer(z.createElement(N.Consumer,null))?N:y},[N,y]),H=v.useContext(T),R=!!C.store&&!!C.store.getState&&!!C.store.dispatch;H&&H.store;var M=R?C.store:H.store,U=v.useMemo(function(){return D(M)},[M]),G=v.useMemo(function(){if(!l)return c0;var se=yd(M,R?null:H.subscription),me=se.notifyNestedSubs.bind(se);return[se,me]},[M,R,H]),q=G[0],Z=G[1],Q=v.useMemo(function(){return R?H:de({},H,{subscription:q})},[R,H,q]),$=v.useReducer(u0,l0,p0),V=$[0],X=V[0],ne=$[1];if(X&&X.error)throw X.error;var F=v.useRef(),Y=v.useRef(W),ee=v.useRef(),oe=v.useRef(!1),ae=k(function(){return ee.current&&W===Y.current?ee.current:U(M.getState(),W)},[M,X,W]);Xl(d0,[Y,F,oe,W,ae,ee,Z]),Xl(f0,[l,M,q,U,Y,F,oe,ee,Z,ne],[M,q,U]);var re=v.useMemo(function(){return z.createElement(S,de({},ae,{ref:_}))},[_,S,ae]),K=v.useMemo(function(){return l?z.createElement(T.Provider,{value:Q},re):re},[T,re,Q]);return K}var B=A?z.memo(L):L;if(B.WrappedComponent=S,B.displayName=L.displayName=I,g){var j=z.forwardRef(function(O,N){return z.createElement(B,de({},O,{reactReduxForwardedRef:N}))});return j.displayName=I,j.WrappedComponent=S,kl(j,S)}return kl(B,S)}}function Yl(e,t){return e===t?e!==0||t!==0||1/e===1/t:e!==e&&t!==t}function ta(e,t){if(Yl(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(var o=0;o<n.length;o++)if(!Object.prototype.hasOwnProperty.call(t,n[o])||!Yl(e[n[o]],t[n[o]]))return!1;return!0}function g0(e,t){var n={},r=function(a){var s=e[a];typeof s=="function"&&(n[a]=function(){return t(s.apply(void 0,arguments))})};for(var o in e)r(o);return n}function Ds(e){return function(n,r){var o=e(n,r);function i(){return o}return i.dependsOnOwnProps=!1,i}}function Kl(e){return e.dependsOnOwnProps!==null&&e.dependsOnOwnProps!==void 0?!!e.dependsOnOwnProps:e.length!==1}function Dd(e,t){return function(r,o){o.displayName;var i=function(s,c){return i.dependsOnOwnProps?i.mapToProps(s,c):i.mapToProps(s)};return i.dependsOnOwnProps=!0,i.mapToProps=function(s,c){i.mapToProps=e,i.dependsOnOwnProps=Kl(e);var u=i(s,c);return typeof u=="function"&&(i.mapToProps=u,i.dependsOnOwnProps=Kl(u),u=i(s,c)),u},i}}function m0(e){return typeof e=="function"?Dd(e):void 0}function v0(e){return e?void 0:Ds(function(t){return{dispatch:t}})}function y0(e){return e&&typeof e=="object"?Ds(function(t){return g0(e,t)}):void 0}const b0=[m0,v0,y0];function x0(e){return typeof e=="function"?Dd(e):void 0}function w0(e){return e?void 0:Ds(function(){return{}})}const S0=[x0,w0];function C0(e,t,n){return de({},n,e,t)}function E0(e){return function(n,r){r.displayName;var o=r.pure,i=r.areMergedPropsEqual,a=!1,s;return function(u,l,d){var f=e(u,l,d);return a?(!o||!i(f,s))&&(s=f):(a=!0,s=f),s}}}function I0(e){return typeof e=="function"?E0(e):void 0}function D0(e){return e?void 0:function(){return C0}}const P0=[I0,D0];var A0=["initMapStateToProps","initMapDispatchToProps","initMergeProps"];function N0(e,t,n,r){return function(i,a){return n(e(i,a),t(r,a),a)}}function O0(e,t,n,r,o){var i=o.areStatesEqual,a=o.areOwnPropsEqual,s=o.areStatePropsEqual,c=!1,u,l,d,f,p;function g(E,S){return u=E,l=S,d=e(u,l),f=t(r,l),p=n(d,f,l),c=!0,p}function m(){return d=e(u,l),t.dependsOnOwnProps&&(f=t(r,l)),p=n(d,f,l),p}function x(){return e.dependsOnOwnProps&&(d=e(u,l)),t.dependsOnOwnProps&&(f=t(r,l)),p=n(d,f,l),p}function w(){var E=e(u,l),S=!s(E,d);return d=E,S&&(p=n(d,f,l)),p}function y(E,S){var b=!a(S,l),I=!i(E,u,S,l);return u=E,l=S,b&&I?m():b?x():I?w():p}return function(S,b){return c?y(S,b):g(S,b)}}function R0(e,t){var n=t.initMapStateToProps,r=t.initMapDispatchToProps,o=t.initMergeProps,i=oi(t,A0),a=n(e,i),s=r(e,i),c=o(e,i),u=i.pure?O0:N0;return u(a,s,c,e,i)}var T0=["pure","areStatesEqual","areOwnPropsEqual","areStatePropsEqual","areMergedPropsEqual"];function na(e,t,n){for(var r=t.length-1;r>=0;r--){var o=t[r](e);if(o)return o}return function(i,a){throw new Error("Invalid value of type "+typeof e+" for "+n+" argument when connecting component "+a.wrappedComponentName+".")}}function j0(e,t){return e===t}function M0(e){var t={},n=t.connectHOC,r=n===void 0?h0:n,o=t.mapStateToPropsFactories,i=o===void 0?S0:o,a=t.mapDispatchToPropsFactories,s=a===void 0?b0:a,c=t.mergePropsFactories,u=c===void 0?P0:c,l=t.selectorFactory,d=l===void 0?R0:l;return function(p,g,m,x){x===void 0&&(x={});var w=x,y=w.pure,E=y===void 0?!0:y,S=w.areStatesEqual,b=S===void 0?j0:S,I=w.areOwnPropsEqual,P=I===void 0?ta:I,A=w.areStatePropsEqual,D=A===void 0?ta:A,k=w.areMergedPropsEqual,L=k===void 0?ta:k,B=oi(w,T0),j=na(p,i,"mapStateToProps"),C=na(g,s,"mapDispatchToProps"),O=na(m,u,"mergeProps");return r(d,de({methodName:"connect",getDisplayName:function(_){return"Connect("+_+")"},shouldHandleStateChanges:!!p,initMapStateToProps:j,initMapDispatchToProps:C,initMergeProps:O,pure:E,areStatesEqual:b,areOwnPropsEqual:P,areStatePropsEqual:D,areMergedPropsEqual:L},B))}}const Pd=M0();Uv(cg.unstable_batchedUpdates);function k0(e,t){if(e.length!==t.length)return!1;for(var n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}function Ad(e,t){var n=v.useState(function(){return{inputs:t,result:e()}})[0],r=v.useRef(!0),o=v.useRef(n),i=r.current||!!(t&&o.current.inputs&&k0(t,o.current.inputs)),a=i?o.current:{inputs:t,result:e()};return v.useEffect(function(){r.current=!1,o.current=a},[a]),a.result}function $0(e,t){return Ad(function(){return e},t)}var ye=Ad,le=$0,_0="Invariant failed";function B0(e,t){throw new Error(_0)}var Vt=function(t){var n=t.top,r=t.right,o=t.bottom,i=t.left,a=r-i,s=o-n,c={top:n,right:r,bottom:o,left:i,width:a,height:s,x:i,y:n,center:{x:(r+i)/2,y:(o+n)/2}};return c},Ps=function(t,n){return{top:t.top-n.top,left:t.left-n.left,bottom:t.bottom+n.bottom,right:t.right+n.right}},Zl=function(t,n){return{top:t.top+n.top,left:t.left+n.left,bottom:t.bottom-n.bottom,right:t.right-n.right}},L0=function(t,n){return{top:t.top+n.y,left:t.left+n.x,bottom:t.bottom+n.y,right:t.right+n.x}},ra={top:0,right:0,bottom:0,left:0},As=function(t){var n=t.borderBox,r=t.margin,o=r===void 0?ra:r,i=t.border,a=i===void 0?ra:i,s=t.padding,c=s===void 0?ra:s,u=Vt(Ps(n,o)),l=Vt(Zl(n,a)),d=Vt(Zl(l,c));return{marginBox:u,borderBox:Vt(n),paddingBox:l,contentBox:d,margin:o,border:a,padding:c}},Mt=function(t){var n=t.slice(0,-2),r=t.slice(-2);if(r!=="px")return 0;var o=Number(n);return isNaN(o)&&B0(),o},z0=function(){return{x:window.pageXOffset,y:window.pageYOffset}},ii=function(t,n){var r=t.borderBox,o=t.border,i=t.margin,a=t.padding,s=L0(r,n);return As({borderBox:s,border:o,margin:i,padding:a})},ai=function(t,n){return n===void 0&&(n=z0()),ii(t,n)},Nd=function(t,n){var r={top:Mt(n.marginTop),right:Mt(n.marginRight),bottom:Mt(n.marginBottom),left:Mt(n.marginLeft)},o={top:Mt(n.paddingTop),right:Mt(n.paddingRight),bottom:Mt(n.paddingBottom),left:Mt(n.paddingLeft)},i={top:Mt(n.borderTopWidth),right:Mt(n.borderRightWidth),bottom:Mt(n.borderBottomWidth),left:Mt(n.borderLeftWidth)};return As({borderBox:t,margin:r,padding:o,border:i})},Od=function(t){var n=t.getBoundingClientRect(),r=window.getComputedStyle(t);return Nd(n,r)},Jl=Number.isNaN||function(t){return typeof t=="number"&&t!==t};function F0(e,t){return!!(e===t||Jl(e)&&Jl(t))}function H0(e,t){if(e.length!==t.length)return!1;for(var n=0;n<e.length;n++)if(!F0(e[n],t[n]))return!1;return!0}function Ue(e,t){t===void 0&&(t=H0);var n,r=[],o,i=!1;function a(){for(var s=[],c=0;c<arguments.length;c++)s[c]=arguments[c];return i&&n===this&&t(s,r)||(o=e.apply(this,s),i=!0,n=this,r=s),o}return a}var Wr=function(t){var n=[],r=null,o=function(){for(var a=arguments.length,s=new Array(a),c=0;c<a;c++)s[c]=arguments[c];n=s,!r&&(r=requestAnimationFrame(function(){r=null,t.apply(void 0,n)}))};return o.cancel=function(){r&&(cancelAnimationFrame(r),r=null)},o};function Rd(e,t){}Rd.bind(null,"warn");Rd.bind(null,"error");function vn(){}function W0(e,t){return de({},e,{},t)}function $t(e,t,n){var r=t.map(function(o){var i=W0(n,o.options);return e.addEventListener(o.eventName,o.fn,i),function(){e.removeEventListener(o.eventName,o.fn,i)}});return function(){r.forEach(function(i){i()})}}var V0="Invariant failed";function si(e){this.message=e}si.prototype.toString=function(){return this.message};function te(e,t){throw new si(V0)}var U0=function(e){gd(t,e);function t(){for(var r,o=arguments.length,i=new Array(o),a=0;a<o;a++)i[a]=arguments[a];return r=e.call.apply(e,[this].concat(i))||this,r.callbacks=null,r.unbind=vn,r.onWindowError=function(s){var c=r.getCallbacks();c.isDragging()&&c.tryAbort();var u=s.error;u instanceof si&&s.preventDefault()},r.getCallbacks=function(){if(!r.callbacks)throw new Error("Unable to find AppCallbacks in <ErrorBoundary/>");return r.callbacks},r.setCallbacks=function(s){r.callbacks=s},r}var n=t.prototype;return n.componentDidMount=function(){this.unbind=$t(window,[{eventName:"error",fn:this.onWindowError}])},n.componentDidCatch=function(o){if(o instanceof si){this.setState({});return}throw o},n.componentWillUnmount=function(){this.unbind()},n.render=function(){return this.props.children(this.setCallbacks)},t}(z.Component),q0=`
  Press space bar to start a drag.
  When dragging you can use the arrow keys to move the item around and escape to cancel.
  Some screen readers may require you to be in focus mode or to use your pass through key
`,li=function(t){return t+1},G0=function(t){return`
  You have lifted an item in position `+li(t.source.index)+`
`},Td=function(t,n){var r=t.droppableId===n.droppableId,o=li(t.index),i=li(n.index);return r?`
      You have moved the item from position `+o+`
      to position `+i+`
    `:`
    You have moved the item from position `+o+`
    in list `+t.droppableId+`
    to list `+n.droppableId+`
    in position `+i+`
  `},jd=function(t,n,r){var o=n.droppableId===r.droppableId;return o?`
      The item `+t+`
      has been combined with `+r.draggableId:`
      The item `+t+`
      in list `+n.droppableId+`
      has been combined with `+r.draggableId+`
      in list `+r.droppableId+`
    `},X0=function(t){var n=t.destination;if(n)return Td(t.source,n);var r=t.combine;return r?jd(t.draggableId,t.source,r):"You are over an area that cannot be dropped on"},Ql=function(t){return`
  The item has returned to its starting position
  of `+li(t.index)+`
`},Y0=function(t){if(t.reason==="CANCEL")return`
      Movement cancelled.
      `+Ql(t.source)+`
    `;var n=t.destination,r=t.combine;return n?`
      You have dropped the item.
      `+Td(t.source,n)+`
    `:r?`
      You have dropped the item.
      `+jd(t.draggableId,t.source,r)+`
    `:`
    The item has been dropped while not over a drop area.
    `+Ql(t.source)+`
  `},Ko={dragHandleUsageInstructions:q0,onDragStart:G0,onDragUpdate:X0,onDragEnd:Y0},qe={x:0,y:0},Ze=function(t,n){return{x:t.x+n.x,y:t.y+n.y}},It=function(t,n){return{x:t.x-n.x,y:t.y-n.y}},yn=function(t,n){return t.x===n.x&&t.y===n.y},pr=function(t){return{x:t.x!==0?-t.x:0,y:t.y!==0?-t.y:0}},Bn=function(t,n,r){var o;return r===void 0&&(r=0),o={},o[t]=n,o[t==="x"?"y":"x"]=r,o},Vr=function(t,n){return Math.sqrt(Math.pow(n.x-t.x,2)+Math.pow(n.y-t.y,2))},ec=function(t,n){return Math.min.apply(Math,n.map(function(r){return Vr(t,r)}))},Md=function(t){return function(n){return{x:t(n.x),y:t(n.y)}}},K0=function(e,t){var n=Vt({top:Math.max(t.top,e.top),right:Math.min(t.right,e.right),bottom:Math.min(t.bottom,e.bottom),left:Math.max(t.left,e.left)});return n.width<=0||n.height<=0?null:n},vo=function(t,n){return{top:t.top+n.y,left:t.left+n.x,bottom:t.bottom+n.y,right:t.right+n.x}},tc=function(t){return[{x:t.left,y:t.top},{x:t.right,y:t.top},{x:t.left,y:t.bottom},{x:t.right,y:t.bottom}]},Z0={top:0,right:0,bottom:0,left:0},J0=function(t,n){return n?vo(t,n.scroll.diff.displacement):t},Q0=function(t,n,r){if(r&&r.increasedBy){var o;return de({},t,(o={},o[n.end]=t[n.end]+r.increasedBy[n.line],o))}return t},ey=function(t,n){return n&&n.shouldClipSubject?K0(n.pageMarginBox,t):Vt(t)},nr=function(e){var t=e.page,n=e.withPlaceholder,r=e.axis,o=e.frame,i=J0(t.marginBox,o),a=Q0(i,r,n),s=ey(a,o);return{page:t,withPlaceholder:n,active:s}},Ns=function(e,t){e.frame||te();var n=e.frame,r=It(t,n.scroll.initial),o=pr(r),i=de({},n,{scroll:{initial:n.scroll.initial,current:t,diff:{value:r,displacement:o},max:n.scroll.max}}),a=nr({page:e.subject.page,withPlaceholder:e.subject.withPlaceholder,axis:e.axis,frame:i}),s=de({},e,{frame:i,subject:a});return s};function ci(e){return Object.values?Object.values(e):Object.keys(e).map(function(t){return e[t]})}function Os(e,t){if(e.findIndex)return e.findIndex(t);for(var n=0;n<e.length;n++)if(t(e[n]))return n;return-1}function En(e,t){if(e.find)return e.find(t);var n=Os(e,t);if(n!==-1)return e[n]}function kd(e){return Array.prototype.slice.call(e)}var $d=Ue(function(e){return e.reduce(function(t,n){return t[n.descriptor.id]=n,t},{})}),_d=Ue(function(e){return e.reduce(function(t,n){return t[n.descriptor.id]=n,t},{})}),Di=Ue(function(e){return ci(e)}),ty=Ue(function(e){return ci(e)}),hr=Ue(function(e,t){var n=ty(t).filter(function(r){return e===r.descriptor.droppableId}).sort(function(r,o){return r.descriptor.index-o.descriptor.index});return n});function Rs(e){return e.at&&e.at.type==="REORDER"?e.at.destination:null}function Pi(e){return e.at&&e.at.type==="COMBINE"?e.at.combine:null}var Ai=Ue(function(e,t){return t.filter(function(n){return n.descriptor.id!==e.descriptor.id})}),ny=function(e){var t=e.isMovingForward,n=e.draggable,r=e.destination,o=e.insideDestination,i=e.previousImpact;if(!r.isCombineEnabled)return null;var a=Rs(i);if(!a)return null;function s(m){var x={type:"COMBINE",combine:{draggableId:m,droppableId:r.descriptor.id}};return de({},i,{at:x})}var c=i.displaced.all,u=c.length?c[0]:null;if(t)return u?s(u):null;var l=Ai(n,o);if(!u){if(!l.length)return null;var d=l[l.length-1];return s(d.descriptor.id)}var f=Os(l,function(m){return m.descriptor.id===u});f===-1&&te();var p=f-1;if(p<0)return null;var g=l[p];return s(g.descriptor.id)},gr=function(e,t){return e.descriptor.droppableId===t.descriptor.id},Bd={point:qe,value:0},Ur={invisible:{},visible:{},all:[]},ry={displaced:Ur,displacedBy:Bd,at:null},Bt=function(e,t){return function(n){return e<=n&&n<=t}},Ld=function(e){var t=Bt(e.top,e.bottom),n=Bt(e.left,e.right);return function(r){var o=t(r.top)&&t(r.bottom)&&n(r.left)&&n(r.right);if(o)return!0;var i=t(r.top)||t(r.bottom),a=n(r.left)||n(r.right),s=i&&a;if(s)return!0;var c=r.top<e.top&&r.bottom>e.bottom,u=r.left<e.left&&r.right>e.right,l=c&&u;if(l)return!0;var d=c&&a||u&&i;return d}},oy=function(e){var t=Bt(e.top,e.bottom),n=Bt(e.left,e.right);return function(r){var o=t(r.top)&&t(r.bottom)&&n(r.left)&&n(r.right);return o}},Ts={direction:"vertical",line:"y",crossAxisLine:"x",start:"top",end:"bottom",size:"height",crossAxisStart:"left",crossAxisEnd:"right",crossAxisSize:"width"},zd={direction:"horizontal",line:"x",crossAxisLine:"y",start:"left",end:"right",size:"width",crossAxisStart:"top",crossAxisEnd:"bottom",crossAxisSize:"height"},iy=function(e){return function(t){var n=Bt(t.top,t.bottom),r=Bt(t.left,t.right);return function(o){return e===Ts?n(o.top)&&n(o.bottom):r(o.left)&&r(o.right)}}},ay=function(t,n){var r=n.frame?n.frame.scroll.diff.displacement:qe;return vo(t,r)},sy=function(t,n,r){return n.subject.active?r(n.subject.active)(t):!1},ly=function(t,n,r){return r(n)(t)},js=function(t){var n=t.target,r=t.destination,o=t.viewport,i=t.withDroppableDisplacement,a=t.isVisibleThroughFrameFn,s=i?ay(n,r):n;return sy(s,r,a)&&ly(s,o,a)},cy=function(t){return js(de({},t,{isVisibleThroughFrameFn:Ld}))},Fd=function(t){return js(de({},t,{isVisibleThroughFrameFn:oy}))},uy=function(t){return js(de({},t,{isVisibleThroughFrameFn:iy(t.destination.axis)}))},dy=function(t,n,r){if(typeof r=="boolean")return r;if(!n)return!0;var o=n.invisible,i=n.visible;if(o[t])return!1;var a=i[t];return a?a.shouldAnimate:!0};function fy(e,t){var n=e.page.marginBox,r={top:t.point.y,right:0,bottom:0,left:t.point.x};return Vt(Ps(n,r))}function qr(e){var t=e.afterDragging,n=e.destination,r=e.displacedBy,o=e.viewport,i=e.forceShouldAnimate,a=e.last;return t.reduce(function(c,u){var l=fy(u,r),d=u.descriptor.id;c.all.push(d);var f=cy({target:l,destination:n,viewport:o,withDroppableDisplacement:!0});if(!f)return c.invisible[u.descriptor.id]=!0,c;var p=dy(d,a,i),g={draggableId:d,shouldAnimate:p};return c.visible[d]=g,c},{all:[],visible:{},invisible:{}})}function py(e,t){if(!e.length)return 0;var n=e[e.length-1].descriptor.index;return t.inHomeList?n:n+1}function nc(e){var t=e.insideDestination,n=e.inHomeList,r=e.displacedBy,o=e.destination,i=py(t,{inHomeList:n});return{displaced:Ur,displacedBy:r,at:{type:"REORDER",destination:{droppableId:o.descriptor.id,index:i}}}}function ui(e){var t=e.draggable,n=e.insideDestination,r=e.destination,o=e.viewport,i=e.displacedBy,a=e.last,s=e.index,c=e.forceShouldAnimate,u=gr(t,r);if(s==null)return nc({insideDestination:n,inHomeList:u,displacedBy:i,destination:r});var l=En(n,function(m){return m.descriptor.index===s});if(!l)return nc({insideDestination:n,inHomeList:u,displacedBy:i,destination:r});var d=Ai(t,n),f=n.indexOf(l),p=d.slice(f),g=qr({afterDragging:p,destination:r,displacedBy:i,last:a,viewport:o.frame,forceShouldAnimate:c});return{displaced:g,displacedBy:i,at:{type:"REORDER",destination:{droppableId:r.descriptor.id,index:s}}}}function Cn(e,t){return!!t.effected[e]}var hy=function(e){var t=e.isMovingForward,n=e.destination,r=e.draggables,o=e.combine,i=e.afterCritical;if(!n.isCombineEnabled)return null;var a=o.draggableId,s=r[a],c=s.descriptor.index,u=Cn(a,i);return u?t?c:c-1:t?c+1:c},gy=function(e){var t=e.isMovingForward,n=e.isInHomeList,r=e.insideDestination,o=e.location;if(!r.length)return null;var i=o.index,a=t?i+1:i-1,s=r[0].descriptor.index,c=r[r.length-1].descriptor.index,u=n?c:c+1;return a<s||a>u?null:a},my=function(e){var t=e.isMovingForward,n=e.isInHomeList,r=e.draggable,o=e.draggables,i=e.destination,a=e.insideDestination,s=e.previousImpact,c=e.viewport,u=e.afterCritical,l=s.at;if(l||te(),l.type==="REORDER"){var d=gy({isMovingForward:t,isInHomeList:n,location:l.destination,insideDestination:a});return d==null?null:ui({draggable:r,insideDestination:a,destination:i,viewport:c,last:s.displaced,displacedBy:s.displacedBy,index:d})}var f=hy({isMovingForward:t,destination:i,displaced:s.displaced,draggables:o,combine:l.combine,afterCritical:u});return f==null?null:ui({draggable:r,insideDestination:a,destination:i,viewport:c,last:s.displaced,displacedBy:s.displacedBy,index:f})},vy=function(e){var t=e.displaced,n=e.afterCritical,r=e.combineWith,o=e.displacedBy,i=!!(t.visible[r]||t.invisible[r]);return Cn(r,n)?i?qe:pr(o.point):i?o.point:qe},yy=function(e){var t=e.afterCritical,n=e.impact,r=e.draggables,o=Pi(n);o||te();var i=o.draggableId,a=r[i].page.borderBox.center,s=vy({displaced:n.displaced,afterCritical:t,combineWith:i,displacedBy:n.displacedBy});return Ze(a,s)},Hd=function(t,n){return n.margin[t.start]+n.borderBox[t.size]/2},by=function(t,n){return n.margin[t.end]+n.borderBox[t.size]/2},Ms=function(t,n,r){return n[t.crossAxisStart]+r.margin[t.crossAxisStart]+r.borderBox[t.crossAxisSize]/2},rc=function(t){var n=t.axis,r=t.moveRelativeTo,o=t.isMoving;return Bn(n.line,r.marginBox[n.end]+Hd(n,o),Ms(n,r.marginBox,o))},oc=function(t){var n=t.axis,r=t.moveRelativeTo,o=t.isMoving;return Bn(n.line,r.marginBox[n.start]-by(n,o),Ms(n,r.marginBox,o))},xy=function(t){var n=t.axis,r=t.moveInto,o=t.isMoving;return Bn(n.line,r.contentBox[n.start]+Hd(n,o),Ms(n,r.contentBox,o))},wy=function(e){var t=e.impact,n=e.draggable,r=e.draggables,o=e.droppable,i=e.afterCritical,a=hr(o.descriptor.id,r),s=n.page,c=o.axis;if(!a.length)return xy({axis:c,moveInto:o.page,isMoving:s});var u=t.displaced,l=t.displacedBy,d=u.all[0];if(d){var f=r[d];if(Cn(d,i))return oc({axis:c,moveRelativeTo:f.page,isMoving:s});var p=ii(f.page,l.point);return oc({axis:c,moveRelativeTo:p,isMoving:s})}var g=a[a.length-1];if(g.descriptor.id===n.descriptor.id)return s.borderBox.center;if(Cn(g.descriptor.id,i)){var m=ii(g.page,pr(i.displacedBy.point));return rc({axis:c,moveRelativeTo:m,isMoving:s})}return rc({axis:c,moveRelativeTo:g.page,isMoving:s})},Wa=function(e,t){var n=e.frame;return n?Ze(t,n.scroll.diff.displacement):t},Sy=function(t){var n=t.impact,r=t.draggable,o=t.droppable,i=t.draggables,a=t.afterCritical,s=r.page.borderBox.center,c=n.at;return!o||!c?s:c.type==="REORDER"?wy({impact:n,draggable:r,draggables:i,droppable:o,afterCritical:a}):yy({impact:n,draggables:i,afterCritical:a})},Ni=function(e){var t=Sy(e),n=e.droppable,r=n?Wa(n,t):t;return r},Wd=function(e,t){var n=It(t,e.scroll.initial),r=pr(n),o=Vt({top:t.y,bottom:t.y+e.frame.height,left:t.x,right:t.x+e.frame.width}),i={frame:o,scroll:{initial:e.scroll.initial,max:e.scroll.max,current:t,diff:{value:n,displacement:r}}};return i};function ic(e,t){return e.map(function(n){return t[n]})}function Cy(e,t){for(var n=0;n<t.length;n++){var r=t[n].visible[e];if(r)return r}return null}var Ey=function(e){var t=e.impact,n=e.viewport,r=e.destination,o=e.draggables,i=e.maxScrollChange,a=Wd(n,Ze(n.scroll.current,i)),s=r.frame?Ns(r,Ze(r.frame.scroll.current,i)):r,c=t.displaced,u=qr({afterDragging:ic(c.all,o),destination:r,displacedBy:t.displacedBy,viewport:a.frame,last:c,forceShouldAnimate:!1}),l=qr({afterDragging:ic(c.all,o),destination:s,displacedBy:t.displacedBy,viewport:n.frame,last:c,forceShouldAnimate:!1}),d={},f={},p=[c,u,l];c.all.forEach(function(m){var x=Cy(m,p);if(x){f[m]=x;return}d[m]=!0});var g=de({},t,{displaced:{all:c.all,invisible:d,visible:f}});return g},Iy=function(e,t){return Ze(e.scroll.diff.displacement,t)},ks=function(e){var t=e.pageBorderBoxCenter,n=e.draggable,r=e.viewport,o=Iy(r,t),i=It(o,n.page.borderBox.center);return Ze(n.client.borderBox.center,i)},Vd=function(e){var t=e.draggable,n=e.destination,r=e.newPageBorderBoxCenter,o=e.viewport,i=e.withDroppableDisplacement,a=e.onlyOnMainAxis,s=a===void 0?!1:a,c=It(r,t.page.borderBox.center),u=vo(t.page.borderBox,c),l={target:u,destination:n,withDroppableDisplacement:i,viewport:o};return s?uy(l):Fd(l)},Dy=function(e){var t=e.isMovingForward,n=e.draggable,r=e.destination,o=e.draggables,i=e.previousImpact,a=e.viewport,s=e.previousPageBorderBoxCenter,c=e.previousClientSelection,u=e.afterCritical;if(!r.isEnabled)return null;var l=hr(r.descriptor.id,o),d=gr(n,r),f=ny({isMovingForward:t,draggable:n,destination:r,insideDestination:l,previousImpact:i})||my({isMovingForward:t,isInHomeList:d,draggable:n,draggables:o,destination:r,insideDestination:l,previousImpact:i,viewport:a,afterCritical:u});if(!f)return null;var p=Ni({impact:f,draggable:n,droppable:r,draggables:o,afterCritical:u}),g=Vd({draggable:n,destination:r,newPageBorderBoxCenter:p,viewport:a.frame,withDroppableDisplacement:!1,onlyOnMainAxis:!0});if(g){var m=ks({pageBorderBoxCenter:p,draggable:n,viewport:a});return{clientSelection:m,impact:f,scrollJumpRequest:null}}var x=It(p,s),w=Ey({impact:f,viewport:a,destination:r,draggables:o,maxScrollChange:x});return{clientSelection:c,impact:w,scrollJumpRequest:x}},at=function(t){var n=t.subject.active;return n||te(),n},Py=function(e){var t=e.isMovingForward,n=e.pageBorderBoxCenter,r=e.source,o=e.droppables,i=e.viewport,a=r.subject.active;if(!a)return null;var s=r.axis,c=Bt(a[s.start],a[s.end]),u=Di(o).filter(function(d){return d!==r}).filter(function(d){return d.isEnabled}).filter(function(d){return!!d.subject.active}).filter(function(d){return Ld(i.frame)(at(d))}).filter(function(d){var f=at(d);return t?a[s.crossAxisEnd]<f[s.crossAxisEnd]:f[s.crossAxisStart]<a[s.crossAxisStart]}).filter(function(d){var f=at(d),p=Bt(f[s.start],f[s.end]);return c(f[s.start])||c(f[s.end])||p(a[s.start])||p(a[s.end])}).sort(function(d,f){var p=at(d)[s.crossAxisStart],g=at(f)[s.crossAxisStart];return t?p-g:g-p}).filter(function(d,f,p){return at(d)[s.crossAxisStart]===at(p[0])[s.crossAxisStart]});if(!u.length)return null;if(u.length===1)return u[0];var l=u.filter(function(d){var f=Bt(at(d)[s.start],at(d)[s.end]);return f(n[s.line])});return l.length===1?l[0]:l.length>1?l.sort(function(d,f){return at(d)[s.start]-at(f)[s.start]})[0]:u.sort(function(d,f){var p=ec(n,tc(at(d))),g=ec(n,tc(at(f)));return p!==g?p-g:at(d)[s.start]-at(f)[s.start]})[0]},ac=function(t,n){var r=t.page.borderBox.center;return Cn(t.descriptor.id,n)?It(r,n.displacedBy.point):r},Ay=function(t,n){var r=t.page.borderBox;return Cn(t.descriptor.id,n)?vo(r,pr(n.displacedBy.point)):r},Ny=function(e){var t=e.pageBorderBoxCenter,n=e.viewport,r=e.destination,o=e.insideDestination,i=e.afterCritical,a=o.filter(function(s){return Fd({target:Ay(s,i),destination:r,viewport:n.frame,withDroppableDisplacement:!0})}).sort(function(s,c){var u=Vr(t,Wa(r,ac(s,i))),l=Vr(t,Wa(r,ac(c,i)));return u<l?-1:l<u?1:s.descriptor.index-c.descriptor.index});return a[0]||null},yo=Ue(function(t,n){var r=n[t.line];return{value:r,point:Bn(t.line,r)}}),Oy=function(t,n,r){var o=t.axis;if(t.descriptor.mode==="virtual")return Bn(o.line,n[o.line]);var i=t.subject.page.contentBox[o.size],a=hr(t.descriptor.id,r),s=a.reduce(function(l,d){return l+d.client.marginBox[o.size]},0),c=s+n[o.line],u=c-i;return u<=0?null:Bn(o.line,u)},Ud=function(t,n){return de({},t,{scroll:de({},t.scroll,{max:n})})},qd=function(t,n,r){var o=t.frame;gr(n,t)&&te(),t.subject.withPlaceholder&&te();var i=yo(t.axis,n.displaceBy).point,a=Oy(t,i,r),s={placeholderSize:i,increasedBy:a,oldFrameMaxScroll:t.frame?t.frame.scroll.max:null};if(!o){var c=nr({page:t.subject.page,withPlaceholder:s,axis:t.axis,frame:t.frame});return de({},t,{subject:c})}var u=a?Ze(o.scroll.max,a):o.scroll.max,l=Ud(o,u),d=nr({page:t.subject.page,withPlaceholder:s,axis:t.axis,frame:l});return de({},t,{subject:d,frame:l})},Ry=function(t){var n=t.subject.withPlaceholder;n||te();var r=t.frame;if(!r){var o=nr({page:t.subject.page,axis:t.axis,frame:null,withPlaceholder:null});return de({},t,{subject:o})}var i=n.oldFrameMaxScroll;i||te();var a=Ud(r,i),s=nr({page:t.subject.page,axis:t.axis,frame:a,withPlaceholder:null});return de({},t,{subject:s,frame:a})},Ty=function(e){var t=e.previousPageBorderBoxCenter,n=e.moveRelativeTo,r=e.insideDestination,o=e.draggable,i=e.draggables,a=e.destination,s=e.viewport,c=e.afterCritical;if(!n){if(r.length)return null;var u={displaced:Ur,displacedBy:Bd,at:{type:"REORDER",destination:{droppableId:a.descriptor.id,index:0}}},l=Ni({impact:u,draggable:o,droppable:a,draggables:i,afterCritical:c}),d=gr(o,a)?a:qd(a,o,i),f=Vd({draggable:o,destination:d,newPageBorderBoxCenter:l,viewport:s.frame,withDroppableDisplacement:!1,onlyOnMainAxis:!0});return f?u:null}var p=t[a.axis.line]<=n.page.borderBox.center[a.axis.line],g=function(){var x=n.descriptor.index;return n.descriptor.id===o.descriptor.id||p?x:x+1}(),m=yo(a.axis,o.displaceBy);return ui({draggable:o,insideDestination:r,destination:a,viewport:s,displacedBy:m,last:Ur,index:g})},jy=function(e){var t=e.isMovingForward,n=e.previousPageBorderBoxCenter,r=e.draggable,o=e.isOver,i=e.draggables,a=e.droppables,s=e.viewport,c=e.afterCritical,u=Py({isMovingForward:t,pageBorderBoxCenter:n,source:o,droppables:a,viewport:s});if(!u)return null;var l=hr(u.descriptor.id,i),d=Ny({pageBorderBoxCenter:n,viewport:s,destination:u,insideDestination:l,afterCritical:c}),f=Ty({previousPageBorderBoxCenter:n,destination:u,draggable:r,draggables:i,moveRelativeTo:d,insideDestination:l,viewport:s,afterCritical:c});if(!f)return null;var p=Ni({impact:f,draggable:r,droppable:u,draggables:i,afterCritical:c}),g=ks({pageBorderBoxCenter:p,draggable:r,viewport:s});return{clientSelection:g,impact:f,scrollJumpRequest:null}},Dt=function(e){var t=e.at;return t?t.type==="REORDER"?t.destination.droppableId:t.combine.droppableId:null},My=function(t,n){var r=Dt(t);return r?n[r]:null},ky=function(e){var t=e.state,n=e.type,r=My(t.impact,t.dimensions.droppables),o=!!r,i=t.dimensions.droppables[t.critical.droppable.id],a=r||i,s=a.axis.direction,c=s==="vertical"&&(n==="MOVE_UP"||n==="MOVE_DOWN")||s==="horizontal"&&(n==="MOVE_LEFT"||n==="MOVE_RIGHT");if(c&&!o)return null;var u=n==="MOVE_DOWN"||n==="MOVE_RIGHT",l=t.dimensions.draggables[t.critical.draggable.id],d=t.current.page.borderBoxCenter,f=t.dimensions,p=f.draggables,g=f.droppables;return c?Dy({isMovingForward:u,previousPageBorderBoxCenter:d,draggable:l,destination:a,draggables:p,viewport:t.viewport,previousClientSelection:t.current.client.selection,previousImpact:t.impact,afterCritical:t.afterCritical}):jy({isMovingForward:u,previousPageBorderBoxCenter:d,draggable:l,isOver:a,draggables:p,droppables:g,viewport:t.viewport,afterCritical:t.afterCritical})};function Pn(e){return e.phase==="DRAGGING"||e.phase==="COLLECTING"}function Gd(e){var t=Bt(e.top,e.bottom),n=Bt(e.left,e.right);return function(o){return t(o.y)&&n(o.x)}}function $y(e,t){return e.left<t.right&&e.right>t.left&&e.top<t.bottom&&e.bottom>t.top}function _y(e){var t=e.pageBorderBox,n=e.draggable,r=e.candidates,o=n.page.borderBox.center,i=r.map(function(a){var s=a.axis,c=Bn(a.axis.line,t.center[s.line],a.page.borderBox.center[s.crossAxisLine]);return{id:a.descriptor.id,distance:Vr(o,c)}}).sort(function(a,s){return s.distance-a.distance});return i[0]?i[0].id:null}function By(e){var t=e.pageBorderBox,n=e.draggable,r=e.droppables,o=Di(r).filter(function(i){if(!i.isEnabled)return!1;var a=i.subject.active;if(!a||!$y(t,a))return!1;if(Gd(a)(t.center))return!0;var s=i.axis,c=a.center[s.crossAxisLine],u=t[s.crossAxisStart],l=t[s.crossAxisEnd],d=Bt(a[s.crossAxisStart],a[s.crossAxisEnd]),f=d(u),p=d(l);return!f&&!p?!0:f?u<c:l>c});return o.length?o.length===1?o[0].descriptor.id:_y({pageBorderBox:t,draggable:n,candidates:o}):null}var Xd=function(t,n){return Vt(vo(t,n))},Ly=function(e,t){var n=e.frame;return n?Xd(t,n.scroll.diff.value):t};function Yd(e){var t=e.displaced,n=e.id;return!!(t.visible[n]||t.invisible[n])}function zy(e){var t=e.draggable,n=e.closest,r=e.inHomeList;return n?r&&n.descriptor.index>t.descriptor.index?n.descriptor.index-1:n.descriptor.index:null}var Fy=function(e){var t=e.pageBorderBoxWithDroppableScroll,n=e.draggable,r=e.destination,o=e.insideDestination,i=e.last,a=e.viewport,s=e.afterCritical,c=r.axis,u=yo(r.axis,n.displaceBy),l=u.value,d=t[c.start],f=t[c.end],p=Ai(n,o),g=En(p,function(x){var w=x.descriptor.id,y=x.page.borderBox.center[c.line],E=Cn(w,s),S=Yd({displaced:i,id:w});return E?S?f<=y:d<y-l:S?f<=y+l:d<y}),m=zy({draggable:n,closest:g,inHomeList:gr(n,r)});return ui({draggable:n,insideDestination:o,destination:r,viewport:a,last:i,displacedBy:u,index:m})},Hy=4,Wy=function(e){var t=e.draggable,n=e.pageBorderBoxWithDroppableScroll,r=e.previousImpact,o=e.destination,i=e.insideDestination,a=e.afterCritical;if(!o.isCombineEnabled)return null;var s=o.axis,c=yo(o.axis,t.displaceBy),u=c.value,l=n[s.start],d=n[s.end],f=Ai(t,i),p=En(f,function(m){var x=m.descriptor.id,w=m.page.borderBox,y=w[s.size],E=y/Hy,S=Cn(x,a),b=Yd({displaced:r.displaced,id:x});return S?b?d>w[s.start]+E&&d<w[s.end]-E:l>w[s.start]-u+E&&l<w[s.end]-u-E:b?d>w[s.start]+u+E&&d<w[s.end]+u-E:l>w[s.start]+E&&l<w[s.end]-E});if(!p)return null;var g={displacedBy:c,displaced:r.displaced,at:{type:"COMBINE",combine:{draggableId:p.descriptor.id,droppableId:o.descriptor.id}}};return g},Kd=function(e){var t=e.pageOffset,n=e.draggable,r=e.draggables,o=e.droppables,i=e.previousImpact,a=e.viewport,s=e.afterCritical,c=Xd(n.page.borderBox,t),u=By({pageBorderBox:c,draggable:n,droppables:o});if(!u)return ry;var l=o[u],d=hr(l.descriptor.id,r),f=Ly(l,c);return Wy({pageBorderBoxWithDroppableScroll:f,draggable:n,previousImpact:i,destination:l,insideDestination:d,afterCritical:s})||Fy({pageBorderBoxWithDroppableScroll:f,draggable:n,destination:l,insideDestination:d,last:i.displaced,viewport:a,afterCritical:s})},$s=function(e,t){var n;return de({},e,(n={},n[t.descriptor.id]=t,n))},Vy=function(t){var n=t.previousImpact,r=t.impact,o=t.droppables,i=Dt(n),a=Dt(r);if(!i||i===a)return o;var s=o[i];if(!s.subject.withPlaceholder)return o;var c=Ry(s);return $s(o,c)},Uy=function(e){var t=e.draggable,n=e.draggables,r=e.droppables,o=e.previousImpact,i=e.impact,a=Vy({previousImpact:o,impact:i,droppables:r}),s=Dt(i);if(!s)return a;var c=r[s];if(gr(t,c)||c.subject.withPlaceholder)return a;var u=qd(c,t,n);return $s(a,u)},_r=function(e){var t=e.state,n=e.clientSelection,r=e.dimensions,o=e.viewport,i=e.impact,a=e.scrollJumpRequest,s=o||t.viewport,c=r||t.dimensions,u=n||t.current.client.selection,l=It(u,t.initial.client.selection),d={offset:l,selection:u,borderBoxCenter:Ze(t.initial.client.borderBoxCenter,l)},f={selection:Ze(d.selection,s.scroll.current),borderBoxCenter:Ze(d.borderBoxCenter,s.scroll.current),offset:Ze(d.offset,s.scroll.diff.value)},p={client:d,page:f};if(t.phase==="COLLECTING")return de({phase:"COLLECTING"},t,{dimensions:c,viewport:s,current:p});var g=c.draggables[t.critical.draggable.id],m=i||Kd({pageOffset:f.offset,draggable:g,draggables:c.draggables,droppables:c.droppables,previousImpact:t.impact,viewport:s,afterCritical:t.afterCritical}),x=Uy({draggable:g,impact:m,previousImpact:t.impact,draggables:c.draggables,droppables:c.droppables}),w=de({},t,{current:p,dimensions:{draggables:c.draggables,droppables:x},impact:m,viewport:s,scrollJumpRequest:a||null,forceShouldAnimate:a?!1:null});return w};function qy(e,t){return e.map(function(n){return t[n]})}var Zd=function(e){var t=e.impact,n=e.viewport,r=e.draggables,o=e.destination,i=e.forceShouldAnimate,a=t.displaced,s=qy(a.all,r),c=qr({afterDragging:s,destination:o,displacedBy:t.displacedBy,viewport:n.frame,forceShouldAnimate:i,last:a});return de({},t,{displaced:c})},Jd=function(e){var t=e.impact,n=e.draggable,r=e.droppable,o=e.draggables,i=e.viewport,a=e.afterCritical,s=Ni({impact:t,draggable:n,draggables:o,droppable:r,afterCritical:a});return ks({pageBorderBoxCenter:s,draggable:n,viewport:i})},Qd=function(e){var t=e.state,n=e.dimensions,r=e.viewport;t.movementMode!=="SNAP"&&te();var o=t.impact,i=r||t.viewport,a=n||t.dimensions,s=a.draggables,c=a.droppables,u=s[t.critical.draggable.id],l=Dt(o);l||te();var d=c[l],f=Zd({impact:o,viewport:i,destination:d,draggables:s}),p=Jd({impact:f,draggable:u,droppable:d,draggables:s,viewport:i,afterCritical:t.afterCritical});return _r({impact:f,clientSelection:p,state:t,dimensions:a,viewport:i})},Gy=function(e){return{index:e.index,droppableId:e.droppableId}},ef=function(e){var t=e.draggable,n=e.home,r=e.draggables,o=e.viewport,i=yo(n.axis,t.displaceBy),a=hr(n.descriptor.id,r),s=a.indexOf(t);s===-1&&te();var c=a.slice(s+1),u=c.reduce(function(p,g){return p[g.descriptor.id]=!0,p},{}),l={inVirtualList:n.descriptor.mode==="virtual",displacedBy:i,effected:u},d=qr({afterDragging:c,destination:n,displacedBy:i,last:null,viewport:o.frame,forceShouldAnimate:!1}),f={displaced:d,displacedBy:i,at:{type:"REORDER",destination:Gy(t.descriptor)}};return{impact:f,afterCritical:l}},Xy=function(e,t){return{draggables:e.draggables,droppables:$s(e.droppables,t)}},Yy=function(e){var t=e.draggable,n=e.offset,r=e.initialWindowScroll,o=ii(t.client,n),i=ai(o,r),a=de({},t,{placeholder:de({},t.placeholder,{client:o}),client:o,page:i});return a},Ky=function(e){var t=e.frame;return t||te(),t},Zy=function(e){var t=e.additions,n=e.updatedDroppables,r=e.viewport,o=r.scroll.diff.value;return t.map(function(i){var a=i.descriptor.droppableId,s=n[a],c=Ky(s),u=c.scroll.diff.value,l=Ze(o,u),d=Yy({draggable:i,offset:l,initialWindowScroll:r.scroll.initial});return d})},Jy=function(e){var t=e.state,n=e.published,r=n.modified.map(function(E){var S=t.dimensions.droppables[E.droppableId],b=Ns(S,E.scroll);return b}),o=de({},t.dimensions.droppables,{},$d(r)),i=_d(Zy({additions:n.additions,updatedDroppables:o,viewport:t.viewport})),a=de({},t.dimensions.draggables,{},i);n.removals.forEach(function(E){delete a[E]});var s={droppables:o,draggables:a},c=Dt(t.impact),u=c?s.droppables[c]:null,l=s.draggables[t.critical.draggable.id],d=s.droppables[t.critical.droppable.id],f=ef({draggable:l,home:d,draggables:a,viewport:t.viewport}),p=f.impact,g=f.afterCritical,m=u&&u.isCombineEnabled?t.impact:p,x=Kd({pageOffset:t.current.page.offset,draggable:s.draggables[t.critical.draggable.id],draggables:s.draggables,droppables:s.droppables,previousImpact:m,viewport:t.viewport,afterCritical:g}),w=de({phase:"DRAGGING"},t,{phase:"DRAGGING",impact:x,onLiftImpact:p,dimensions:s,afterCritical:g,forceShouldAnimate:!1});if(t.phase==="COLLECTING")return w;var y=de({phase:"DROP_PENDING"},w,{phase:"DROP_PENDING",reason:t.reason,isWaiting:!1});return y},Va=function(t){return t.movementMode==="SNAP"},oa=function(t,n,r){var o=Xy(t.dimensions,n);return!Va(t)||r?_r({state:t,dimensions:o}):Qd({state:t,dimensions:o})};function ia(e){return e.isDragging&&e.movementMode==="SNAP"?de({phase:"DRAGGING"},e,{scrollJumpRequest:null}):e}var sc={phase:"IDLE",completed:null,shouldFlush:!1},Qy=function(e,t){if(e===void 0&&(e=sc),t.type==="FLUSH")return de({},sc,{shouldFlush:!0});if(t.type==="INITIAL_PUBLISH"){e.phase!=="IDLE"&&te();var n=t.payload,r=n.critical,o=n.clientSelection,i=n.viewport,a=n.dimensions,s=n.movementMode,c=a.draggables[r.draggable.id],u=a.droppables[r.droppable.id],l={selection:o,borderBoxCenter:c.client.borderBox.center,offset:qe},d={client:l,page:{selection:Ze(l.selection,i.scroll.initial),borderBoxCenter:Ze(l.selection,i.scroll.initial),offset:Ze(l.selection,i.scroll.diff.value)}},f=Di(a.droppables).every(function(ne){return!ne.isFixedOnPage}),p=ef({draggable:c,home:u,draggables:a.draggables,viewport:i}),g=p.impact,m=p.afterCritical,x={phase:"DRAGGING",isDragging:!0,critical:r,movementMode:s,dimensions:a,initial:d,current:d,isWindowScrollAllowed:f,impact:g,afterCritical:m,onLiftImpact:g,viewport:i,scrollJumpRequest:null,forceShouldAnimate:null};return x}if(t.type==="COLLECTION_STARTING"){if(e.phase==="COLLECTING"||e.phase==="DROP_PENDING")return e;e.phase!=="DRAGGING"&&te();var w=de({phase:"COLLECTING"},e,{phase:"COLLECTING"});return w}if(t.type==="PUBLISH_WHILE_DRAGGING")return e.phase==="COLLECTING"||e.phase==="DROP_PENDING"||te(),Jy({state:e,published:t.payload});if(t.type==="MOVE"){if(e.phase==="DROP_PENDING")return e;Pn(e)||te();var y=t.payload.client;return yn(y,e.current.client.selection)?e:_r({state:e,clientSelection:y,impact:Va(e)?e.impact:null})}if(t.type==="UPDATE_DROPPABLE_SCROLL"){if(e.phase==="DROP_PENDING"||e.phase==="COLLECTING")return ia(e);Pn(e)||te();var E=t.payload,S=E.id,b=E.newScroll,I=e.dimensions.droppables[S];if(!I)return e;var P=Ns(I,b);return oa(e,P,!1)}if(t.type==="UPDATE_DROPPABLE_IS_ENABLED"){if(e.phase==="DROP_PENDING")return e;Pn(e)||te();var A=t.payload,D=A.id,k=A.isEnabled,L=e.dimensions.droppables[D];L||te(),L.isEnabled===k&&te();var B=de({},L,{isEnabled:k});return oa(e,B,!0)}if(t.type==="UPDATE_DROPPABLE_IS_COMBINE_ENABLED"){if(e.phase==="DROP_PENDING")return e;Pn(e)||te();var j=t.payload,C=j.id,O=j.isCombineEnabled,N=e.dimensions.droppables[C];N||te(),N.isCombineEnabled===O&&te();var _=de({},N,{isCombineEnabled:O});return oa(e,_,!0)}if(t.type==="MOVE_BY_WINDOW_SCROLL"){if(e.phase==="DROP_PENDING"||e.phase==="DROP_ANIMATING")return e;Pn(e)||te(),e.isWindowScrollAllowed||te();var W=t.payload.newScroll;if(yn(e.viewport.scroll.current,W))return ia(e);var T=Wd(e.viewport,W);return Va(e)?Qd({state:e,viewport:T}):_r({state:e,viewport:T})}if(t.type==="UPDATE_VIEWPORT_MAX_SCROLL"){if(!Pn(e))return e;var H=t.payload.maxScroll;if(yn(H,e.viewport.scroll.max))return e;var R=de({},e.viewport,{scroll:de({},e.viewport.scroll,{max:H})});return de({phase:"DRAGGING"},e,{viewport:R})}if(t.type==="MOVE_UP"||t.type==="MOVE_DOWN"||t.type==="MOVE_LEFT"||t.type==="MOVE_RIGHT"){if(e.phase==="COLLECTING"||e.phase==="DROP_PENDING")return e;e.phase!=="DRAGGING"&&te();var M=ky({state:e,type:t.type});return M?_r({state:e,impact:M.impact,clientSelection:M.clientSelection,scrollJumpRequest:M.scrollJumpRequest}):e}if(t.type==="DROP_PENDING"){var U=t.payload.reason;e.phase!=="COLLECTING"&&te();var G=de({phase:"DROP_PENDING"},e,{phase:"DROP_PENDING",isWaiting:!0,reason:U});return G}if(t.type==="DROP_ANIMATE"){var q=t.payload,Z=q.completed,Q=q.dropDuration,$=q.newHomeClientOffset;e.phase==="DRAGGING"||e.phase==="DROP_PENDING"||te();var V={phase:"DROP_ANIMATING",completed:Z,dropDuration:Q,newHomeClientOffset:$,dimensions:e.dimensions};return V}if(t.type==="DROP_COMPLETE"){var X=t.payload.completed;return{phase:"IDLE",completed:X,shouldFlush:!1}}return e},eb=function(t){return{type:"BEFORE_INITIAL_CAPTURE",payload:t}},tb=function(t){return{type:"LIFT",payload:t}},nb=function(t){return{type:"INITIAL_PUBLISH",payload:t}},rb=function(t){return{type:"PUBLISH_WHILE_DRAGGING",payload:t}},ob=function(){return{type:"COLLECTION_STARTING",payload:null}},ib=function(t){return{type:"UPDATE_DROPPABLE_SCROLL",payload:t}},ab=function(t){return{type:"UPDATE_DROPPABLE_IS_ENABLED",payload:t}},sb=function(t){return{type:"UPDATE_DROPPABLE_IS_COMBINE_ENABLED",payload:t}},tf=function(t){return{type:"MOVE",payload:t}},lb=function(t){return{type:"MOVE_BY_WINDOW_SCROLL",payload:t}},cb=function(t){return{type:"UPDATE_VIEWPORT_MAX_SCROLL",payload:t}},ub=function(){return{type:"MOVE_UP",payload:null}},db=function(){return{type:"MOVE_DOWN",payload:null}},fb=function(){return{type:"MOVE_RIGHT",payload:null}},pb=function(){return{type:"MOVE_LEFT",payload:null}},_s=function(){return{type:"FLUSH",payload:null}},hb=function(t){return{type:"DROP_ANIMATE",payload:t}},Bs=function(t){return{type:"DROP_COMPLETE",payload:t}},nf=function(t){return{type:"DROP",payload:t}},gb=function(t){return{type:"DROP_PENDING",payload:t}},rf=function(){return{type:"DROP_ANIMATION_FINISHED",payload:null}},mb=function(e){return function(t){var n=t.getState,r=t.dispatch;return function(o){return function(i){if(i.type!=="LIFT"){o(i);return}var a=i.payload,s=a.id,c=a.clientSelection,u=a.movementMode,l=n();l.phase==="DROP_ANIMATING"&&r(Bs({completed:l.completed})),n().phase!=="IDLE"&&te(),r(_s()),r(eb({draggableId:s,movementMode:u}));var d={shouldPublishImmediately:u==="SNAP"},f={draggableId:s,scrollOptions:d},p=e.startPublishing(f),g=p.critical,m=p.dimensions,x=p.viewport;r(nb({critical:g,dimensions:m,clientSelection:c,movementMode:u,viewport:x}))}}}},vb=function(e){return function(){return function(t){return function(n){n.type==="INITIAL_PUBLISH"&&e.dragging(),n.type==="DROP_ANIMATE"&&e.dropping(n.payload.completed.result.reason),(n.type==="FLUSH"||n.type==="DROP_COMPLETE")&&e.resting(),t(n)}}}},Ls={outOfTheWay:"cubic-bezier(0.2, 0, 0, 1)",drop:"cubic-bezier(.2,1,.1,1)"},Gr={opacity:{drop:0,combining:.7},scale:{drop:.75}},zs={outOfTheWay:.2,minDropTime:.33,maxDropTime:.55},Dn=zs.outOfTheWay+"s "+Ls.outOfTheWay,Br={fluid:"opacity "+Dn,snap:"transform "+Dn+", opacity "+Dn,drop:function(t){var n=t+"s "+Ls.drop;return"transform "+n+", opacity "+n},outOfTheWay:"transform "+Dn,placeholder:"height "+Dn+", width "+Dn+", margin "+Dn},lc=function(t){return yn(t,qe)?null:"translate("+t.x+"px, "+t.y+"px)"},Ua={moveTo:lc,drop:function(t,n){var r=lc(t);return r?n?r+" scale("+Gr.scale.drop+")":r:null}},qa=zs.minDropTime,of=zs.maxDropTime,yb=of-qa,cc=1500,bb=.6,xb=function(e){var t=e.current,n=e.destination,r=e.reason,o=Vr(t,n);if(o<=0)return qa;if(o>=cc)return of;var i=o/cc,a=qa+yb*i,s=r==="CANCEL"?a*bb:a;return Number(s.toFixed(2))},wb=function(e){var t=e.impact,n=e.draggable,r=e.dimensions,o=e.viewport,i=e.afterCritical,a=r.draggables,s=r.droppables,c=Dt(t),u=c?s[c]:null,l=s[n.descriptor.droppableId],d=Jd({impact:t,draggable:n,draggables:a,afterCritical:i,droppable:u||l,viewport:o}),f=It(d,n.client.borderBox.center);return f},Sb=function(e){var t=e.draggables,n=e.reason,r=e.lastImpact,o=e.home,i=e.viewport,a=e.onLiftImpact;if(!r.at||n!=="DROP"){var s=Zd({draggables:t,impact:a,destination:o,viewport:i,forceShouldAnimate:!0});return{impact:s,didDropInsideDroppable:!1}}if(r.at.type==="REORDER")return{impact:r,didDropInsideDroppable:!0};var c=de({},r,{displaced:Ur});return{impact:c,didDropInsideDroppable:!0}},Cb=function(e){var t=e.getState,n=e.dispatch;return function(r){return function(o){if(o.type!=="DROP"){r(o);return}var i=t(),a=o.payload.reason;if(i.phase==="COLLECTING"){n(gb({reason:a}));return}if(i.phase!=="IDLE"){var s=i.phase==="DROP_PENDING"&&i.isWaiting;s&&te(),i.phase==="DRAGGING"||i.phase==="DROP_PENDING"||te();var c=i.critical,u=i.dimensions,l=u.draggables[i.critical.draggable.id],d=Sb({reason:a,lastImpact:i.impact,afterCritical:i.afterCritical,onLiftImpact:i.onLiftImpact,home:i.dimensions.droppables[i.critical.droppable.id],viewport:i.viewport,draggables:i.dimensions.draggables}),f=d.impact,p=d.didDropInsideDroppable,g=p?Rs(f):null,m=p?Pi(f):null,x={index:c.draggable.index,droppableId:c.droppable.id},w={draggableId:l.descriptor.id,type:l.descriptor.type,source:x,reason:a,mode:i.movementMode,destination:g,combine:m},y=wb({impact:f,draggable:l,dimensions:u,viewport:i.viewport,afterCritical:i.afterCritical}),E={critical:i.critical,afterCritical:i.afterCritical,result:w,impact:f},S=!yn(i.current.client.offset,y)||!!w.combine;if(!S){n(Bs({completed:E}));return}var b=xb({current:i.current.client.offset,destination:y,reason:a}),I={newHomeClientOffset:y,dropDuration:b,completed:E};n(hb(I))}}}},af=function(){return{x:window.pageXOffset,y:window.pageYOffset}};function Eb(e){return{eventName:"scroll",options:{passive:!0,capture:!1},fn:function(n){n.target!==window&&n.target!==window.document||e()}}}function Ib(e){var t=e.onWindowScroll;function n(){t(af())}var r=Wr(n),o=Eb(r),i=vn;function a(){return i!==vn}function s(){a()&&te(),i=$t(window,[o])}function c(){a()||te(),r.cancel(),i(),i=vn}return{start:s,stop:c,isActive:a}}var Db=function(t){return t.type==="DROP_COMPLETE"||t.type==="DROP_ANIMATE"||t.type==="FLUSH"},Pb=function(e){var t=Ib({onWindowScroll:function(r){e.dispatch(lb({newScroll:r}))}});return function(n){return function(r){!t.isActive()&&r.type==="INITIAL_PUBLISH"&&t.start(),t.isActive()&&Db(r)&&t.stop(),n(r)}}},Ab=function(e){var t=!1,n=!1,r=setTimeout(function(){n=!0}),o=function(a){t||n||(t=!0,e(a),clearTimeout(r))};return o.wasCalled=function(){return t},o},Nb=function(){var e=[],t=function(i){var a=Os(e,function(u){return u.timerId===i});a===-1&&te();var s=e.splice(a,1),c=s[0];c.callback()},n=function(i){var a=setTimeout(function(){return t(a)}),s={timerId:a,callback:i};e.push(s)},r=function(){if(e.length){var i=[].concat(e);e.length=0,i.forEach(function(a){clearTimeout(a.timerId),a.callback()})}};return{add:n,flush:r}},Ob=function(t,n){return t==null&&n==null?!0:t==null||n==null?!1:t.droppableId===n.droppableId&&t.index===n.index},Rb=function(t,n){return t==null&&n==null?!0:t==null||n==null?!1:t.draggableId===n.draggableId&&t.droppableId===n.droppableId},Tb=function(t,n){if(t===n)return!0;var r=t.draggable.id===n.draggable.id&&t.draggable.droppableId===n.draggable.droppableId&&t.draggable.type===n.draggable.type&&t.draggable.index===n.draggable.index,o=t.droppable.id===n.droppable.id&&t.droppable.type===n.droppable.type;return r&&o},Sr=function(t,n){n()},jo=function(t,n){return{draggableId:t.draggable.id,type:t.droppable.type,source:{droppableId:t.droppable.id,index:t.draggable.index},mode:n}},aa=function(t,n,r,o){if(!t){r(o(n));return}var i=Ab(r),a={announce:i};t(n,a),i.wasCalled()||r(o(n))},jb=function(e,t){var n=Nb(),r=null,o=function(f,p){r&&te(),Sr("onBeforeCapture",function(){var g=e().onBeforeCapture;if(g){var m={draggableId:f,mode:p};g(m)}})},i=function(f,p){r&&te(),Sr("onBeforeDragStart",function(){var g=e().onBeforeDragStart;g&&g(jo(f,p))})},a=function(f,p){r&&te();var g=jo(f,p);r={mode:p,lastCritical:f,lastLocation:g.source,lastCombine:null},n.add(function(){Sr("onDragStart",function(){return aa(e().onDragStart,g,t,Ko.onDragStart)})})},s=function(f,p){var g=Rs(p),m=Pi(p);r||te();var x=!Tb(f,r.lastCritical);x&&(r.lastCritical=f);var w=!Ob(r.lastLocation,g);w&&(r.lastLocation=g);var y=!Rb(r.lastCombine,m);if(y&&(r.lastCombine=m),!(!x&&!w&&!y)){var E=de({},jo(f,r.mode),{combine:m,destination:g});n.add(function(){Sr("onDragUpdate",function(){return aa(e().onDragUpdate,E,t,Ko.onDragUpdate)})})}},c=function(){r||te(),n.flush()},u=function(f){r||te(),r=null,Sr("onDragEnd",function(){return aa(e().onDragEnd,f,t,Ko.onDragEnd)})},l=function(){if(r){var f=de({},jo(r.lastCritical,r.mode),{combine:null,destination:null,reason:"CANCEL"});u(f)}};return{beforeCapture:o,beforeStart:i,start:a,update:s,flush:c,drop:u,abort:l}},Mb=function(e,t){var n=jb(e,t);return function(r){return function(o){return function(i){if(i.type==="BEFORE_INITIAL_CAPTURE"){n.beforeCapture(i.payload.draggableId,i.payload.movementMode);return}if(i.type==="INITIAL_PUBLISH"){var a=i.payload.critical;n.beforeStart(a,i.payload.movementMode),o(i),n.start(a,i.payload.movementMode);return}if(i.type==="DROP_COMPLETE"){var s=i.payload.completed.result;n.flush(),o(i),n.drop(s);return}if(o(i),i.type==="FLUSH"){n.abort();return}var c=r.getState();c.phase==="DRAGGING"&&n.update(c.critical,c.impact)}}}},kb=function(e){return function(t){return function(n){if(n.type!=="DROP_ANIMATION_FINISHED"){t(n);return}var r=e.getState();r.phase!=="DROP_ANIMATING"&&te(),e.dispatch(Bs({completed:r.completed}))}}},$b=function(e){var t=null,n=null;function r(){n&&(cancelAnimationFrame(n),n=null),t&&(t(),t=null)}return function(o){return function(i){if((i.type==="FLUSH"||i.type==="DROP_COMPLETE"||i.type==="DROP_ANIMATION_FINISHED")&&r(),o(i),i.type==="DROP_ANIMATE"){var a={eventName:"scroll",options:{capture:!0,passive:!1,once:!0},fn:function(){var c=e.getState();c.phase==="DROP_ANIMATING"&&e.dispatch(rf())}};n=requestAnimationFrame(function(){n=null,t=$t(window,[a])})}}}},_b=function(e){return function(){return function(t){return function(n){(n.type==="DROP_COMPLETE"||n.type==="FLUSH"||n.type==="DROP_ANIMATE")&&e.stopPublishing(),t(n)}}}},Bb=function(e){var t=!1;return function(){return function(n){return function(r){if(r.type==="INITIAL_PUBLISH"){t=!0,e.tryRecordFocus(r.payload.critical.draggable.id),n(r),e.tryRestoreFocusRecorded();return}if(n(r),!!t){if(r.type==="FLUSH"){t=!1,e.tryRestoreFocusRecorded();return}if(r.type==="DROP_COMPLETE"){t=!1;var o=r.payload.completed.result;o.combine&&e.tryShiftRecord(o.draggableId,o.combine.draggableId),e.tryRestoreFocusRecorded()}}}}}},Lb=function(t){return t.type==="DROP_COMPLETE"||t.type==="DROP_ANIMATE"||t.type==="FLUSH"},zb=function(e){return function(t){return function(n){return function(r){if(Lb(r)){e.stop(),n(r);return}if(r.type==="INITIAL_PUBLISH"){n(r);var o=t.getState();o.phase!=="DRAGGING"&&te(),e.start(o);return}n(r),e.scroll(t.getState())}}}},Fb=function(e){return function(t){return function(n){if(t(n),n.type==="PUBLISH_WHILE_DRAGGING"){var r=e.getState();r.phase==="DROP_PENDING"&&(r.isWaiting||e.dispatch(nf({reason:r.reason})))}}}},Hb=pg,Wb=function(e){var t=e.dimensionMarshal,n=e.focusMarshal,r=e.styleMarshal,o=e.getResponders,i=e.announce,a=e.autoScroller;return dg(Qy,Hb(fg(vb(r),_b(t),mb(t),Cb,kb,$b,Fb,zb(a),Pb,Bb(n),Mb(o,i))))},sa=function(){return{additions:{},removals:{},modified:{}}};function Vb(e){var t=e.registry,n=e.callbacks,r=sa(),o=null,i=function(){o||(n.collectionStarting(),o=requestAnimationFrame(function(){o=null;var l=r,d=l.additions,f=l.removals,p=l.modified,g=Object.keys(d).map(function(w){return t.draggable.getById(w).getDimension(qe)}).sort(function(w,y){return w.descriptor.index-y.descriptor.index}),m=Object.keys(p).map(function(w){var y=t.droppable.getById(w),E=y.callbacks.getScrollWhileDragging();return{droppableId:w,scroll:E}}),x={additions:g,removals:Object.keys(f),modified:m};r=sa(),n.publish(x)}))},a=function(l){var d=l.descriptor.id;r.additions[d]=l,r.modified[l.descriptor.droppableId]=!0,r.removals[d]&&delete r.removals[d],i()},s=function(l){var d=l.descriptor;r.removals[d.id]=!0,r.modified[d.droppableId]=!0,r.additions[d.id]&&delete r.additions[d.id],i()},c=function(){o&&(cancelAnimationFrame(o),o=null,r=sa())};return{add:a,remove:s,stop:c}}var sf=function(e){var t=e.scrollHeight,n=e.scrollWidth,r=e.height,o=e.width,i=It({x:n,y:t},{x:o,y:r}),a={x:Math.max(0,i.x),y:Math.max(0,i.y)};return a},lf=function(){var e=document.documentElement;return e||te(),e},cf=function(){var e=lf(),t=sf({scrollHeight:e.scrollHeight,scrollWidth:e.scrollWidth,width:e.clientWidth,height:e.clientHeight});return t},Ub=function(){var e=af(),t=cf(),n=e.y,r=e.x,o=lf(),i=o.clientWidth,a=o.clientHeight,s=r+i,c=n+a,u=Vt({top:n,left:r,right:s,bottom:c}),l={frame:u,scroll:{initial:e,current:e,max:t,diff:{value:qe,displacement:qe}}};return l},qb=function(e){var t=e.critical,n=e.scrollOptions,r=e.registry,o=Ub(),i=o.scroll.current,a=t.droppable,s=r.droppable.getAllByType(a.type).map(function(d){return d.callbacks.getDimensionAndWatchScroll(i,n)}),c=r.draggable.getAllByType(t.draggable.type).map(function(d){return d.getDimension(i)}),u={draggables:_d(c),droppables:$d(s)},l={dimensions:u,critical:t,viewport:o};return l};function uc(e,t,n){if(n.descriptor.id===t.id||n.descriptor.type!==t.type)return!1;var r=e.droppable.getById(n.descriptor.droppableId);return r.descriptor.mode==="virtual"}var Gb=function(e,t){var n=null,r=Vb({callbacks:{publish:t.publishWhileDragging,collectionStarting:t.collectionStarting},registry:e}),o=function(p,g){e.droppable.exists(p)||te(),n&&t.updateDroppableIsEnabled({id:p,isEnabled:g})},i=function(p,g){n&&(e.droppable.exists(p)||te(),t.updateDroppableIsCombineEnabled({id:p,isCombineEnabled:g}))},a=function(p,g){n&&(e.droppable.exists(p)||te(),t.updateDroppableScroll({id:p,newScroll:g}))},s=function(p,g){n&&e.droppable.getById(p).callbacks.scroll(g)},c=function(){if(n){r.stop();var p=n.critical.droppable;e.droppable.getAllByType(p.type).forEach(function(g){return g.callbacks.dragStopped()}),n.unsubscribe(),n=null}},u=function(p){n||te();var g=n.critical.draggable;p.type==="ADDITION"&&uc(e,g,p.value)&&r.add(p.value),p.type==="REMOVAL"&&uc(e,g,p.value)&&r.remove(p.value)},l=function(p){n&&te();var g=e.draggable.getById(p.draggableId),m=e.droppable.getById(g.descriptor.droppableId),x={draggable:g.descriptor,droppable:m.descriptor},w=e.subscribe(u);return n={critical:x,unsubscribe:w},qb({critical:x,registry:e,scrollOptions:p.scrollOptions})},d={updateDroppableIsEnabled:o,updateDroppableIsCombineEnabled:i,scrollDroppable:s,updateDroppableScroll:a,startPublishing:l,stopPublishing:c};return d},uf=function(e,t){return e.phase==="IDLE"?!0:e.phase!=="DROP_ANIMATING"||e.completed.result.draggableId===t?!1:e.completed.result.reason==="DROP"},Xb=function(e){window.scrollBy(e.x,e.y)},Yb=Ue(function(e){return Di(e).filter(function(t){return!(!t.isEnabled||!t.frame)})}),Kb=function(t,n){var r=En(Yb(n),function(o){return o.frame||te(),Gd(o.frame.pageMarginBox)(t)});return r},Zb=function(e){var t=e.center,n=e.destination,r=e.droppables;if(n){var o=r[n];return o.frame?o:null}var i=Kb(t,r);return i},bn={startFromPercentage:.25,maxScrollAtPercentage:.05,maxPixelScroll:28,ease:function(t){return Math.pow(t,2)},durationDampening:{stopDampeningAt:1200,accelerateAt:360}},Jb=function(e,t){var n=e[t.size]*bn.startFromPercentage,r=e[t.size]*bn.maxScrollAtPercentage,o={startScrollingFrom:n,maxScrollValueAt:r};return o},df=function(e){var t=e.startOfRange,n=e.endOfRange,r=e.current,o=n-t;if(o===0)return 0;var i=r-t,a=i/o;return a},Fs=1,Qb=function(e,t){if(e>t.startScrollingFrom)return 0;if(e<=t.maxScrollValueAt)return bn.maxPixelScroll;if(e===t.startScrollingFrom)return Fs;var n=df({startOfRange:t.maxScrollValueAt,endOfRange:t.startScrollingFrom,current:e}),r=1-n,o=bn.maxPixelScroll*bn.ease(r);return Math.ceil(o)},dc=bn.durationDampening.accelerateAt,fc=bn.durationDampening.stopDampeningAt,ex=function(e,t){var n=t,r=fc,o=Date.now(),i=o-n;if(i>=fc)return e;if(i<dc)return Fs;var a=df({startOfRange:dc,endOfRange:r,current:i}),s=e*bn.ease(a);return Math.ceil(s)},pc=function(e){var t=e.distanceToEdge,n=e.thresholds,r=e.dragStartTime,o=e.shouldUseTimeDampening,i=Qb(t,n);return i===0?0:o?Math.max(ex(i,r),Fs):i},hc=function(e){var t=e.container,n=e.distanceToEdges,r=e.dragStartTime,o=e.axis,i=e.shouldUseTimeDampening,a=Jb(t,o),s=n[o.end]<n[o.start];return s?pc({distanceToEdge:n[o.end],thresholds:a,dragStartTime:r,shouldUseTimeDampening:i}):-1*pc({distanceToEdge:n[o.start],thresholds:a,dragStartTime:r,shouldUseTimeDampening:i})},tx=function(e){var t=e.container,n=e.subject,r=e.proposedScroll,o=n.height>t.height,i=n.width>t.width;return!i&&!o?r:i&&o?null:{x:i?0:r.x,y:o?0:r.y}},nx=Md(function(e){return e===0?0:e}),ff=function(e){var t=e.dragStartTime,n=e.container,r=e.subject,o=e.center,i=e.shouldUseTimeDampening,a={top:o.y-n.top,right:n.right-o.x,bottom:n.bottom-o.y,left:o.x-n.left},s=hc({container:n,distanceToEdges:a,dragStartTime:t,axis:Ts,shouldUseTimeDampening:i}),c=hc({container:n,distanceToEdges:a,dragStartTime:t,axis:zd,shouldUseTimeDampening:i}),u=nx({x:c,y:s});if(yn(u,qe))return null;var l=tx({container:n,subject:r,proposedScroll:u});return l?yn(l,qe)?null:l:null},rx=Md(function(e){return e===0?0:e>0?1:-1}),Hs=function(){var e=function(n,r){return n<0?n:n>r?n-r:0};return function(t){var n=t.current,r=t.max,o=t.change,i=Ze(n,o),a={x:e(i.x,r.x),y:e(i.y,r.y)};return yn(a,qe)?null:a}}(),pf=function(t){var n=t.max,r=t.current,o=t.change,i={x:Math.max(r.x,n.x),y:Math.max(r.y,n.y)},a=rx(o),s=Hs({max:i,current:r,change:a});return!s||a.x!==0&&s.x===0||a.y!==0&&s.y===0},Ws=function(t,n){return pf({current:t.scroll.current,max:t.scroll.max,change:n})},ox=function(t,n){if(!Ws(t,n))return null;var r=t.scroll.max,o=t.scroll.current;return Hs({current:o,max:r,change:n})},Vs=function(t,n){var r=t.frame;return r?pf({current:r.scroll.current,max:r.scroll.max,change:n}):!1},ix=function(t,n){var r=t.frame;return!r||!Vs(t,n)?null:Hs({current:r.scroll.current,max:r.scroll.max,change:n})},ax=function(e){var t=e.viewport,n=e.subject,r=e.center,o=e.dragStartTime,i=e.shouldUseTimeDampening,a=ff({dragStartTime:o,container:t.frame,subject:n,center:r,shouldUseTimeDampening:i});return a&&Ws(t,a)?a:null},sx=function(e){var t=e.droppable,n=e.subject,r=e.center,o=e.dragStartTime,i=e.shouldUseTimeDampening,a=t.frame;if(!a)return null;var s=ff({dragStartTime:o,container:a.pageMarginBox,subject:n,center:r,shouldUseTimeDampening:i});return s&&Vs(t,s)?s:null},gc=function(e){var t=e.state,n=e.dragStartTime,r=e.shouldUseTimeDampening,o=e.scrollWindow,i=e.scrollDroppable,a=t.current.page.borderBoxCenter,s=t.dimensions.draggables[t.critical.draggable.id],c=s.page.marginBox;if(t.isWindowScrollAllowed){var u=t.viewport,l=ax({dragStartTime:n,viewport:u,subject:c,center:a,shouldUseTimeDampening:r});if(l){o(l);return}}var d=Zb({center:a,destination:Dt(t.impact),droppables:t.dimensions.droppables});if(d){var f=sx({dragStartTime:n,droppable:d,subject:c,center:a,shouldUseTimeDampening:r});f&&i(d.descriptor.id,f)}},lx=function(e){var t=e.scrollWindow,n=e.scrollDroppable,r=Wr(t),o=Wr(n),i=null,a=function(l){i||te();var d=i,f=d.shouldUseTimeDampening,p=d.dragStartTime;gc({state:l,scrollWindow:r,scrollDroppable:o,dragStartTime:p,shouldUseTimeDampening:f})},s=function(l){i&&te();var d=Date.now(),f=!1,p=function(){f=!0};gc({state:l,dragStartTime:0,shouldUseTimeDampening:!1,scrollWindow:p,scrollDroppable:p}),i={dragStartTime:d,shouldUseTimeDampening:f},f&&a(l)},c=function(){i&&(r.cancel(),o.cancel(),i=null)};return{start:s,stop:c,scroll:a}},cx=function(e){var t=e.move,n=e.scrollDroppable,r=e.scrollWindow,o=function(u,l){var d=Ze(u.current.client.selection,l);t({client:d})},i=function(u,l){if(!Vs(u,l))return l;var d=ix(u,l);if(!d)return n(u.descriptor.id,l),null;var f=It(l,d);n(u.descriptor.id,f);var p=It(l,f);return p},a=function(u,l,d){if(!u||!Ws(l,d))return d;var f=ox(l,d);if(!f)return r(d),null;var p=It(d,f);r(p);var g=It(d,p);return g},s=function(u){var l=u.scrollJumpRequest;if(l){var d=Dt(u.impact);d||te();var f=i(u.dimensions.droppables[d],l);if(f){var p=u.viewport,g=a(u.isWindowScrollAllowed,p,f);g&&o(u,g)}}};return s},ux=function(e){var t=e.scrollDroppable,n=e.scrollWindow,r=e.move,o=lx({scrollWindow:n,scrollDroppable:t}),i=cx({move:r,scrollWindow:n,scrollDroppable:t}),a=function(u){if(u.phase==="DRAGGING"){if(u.movementMode==="FLUID"){o.scroll(u);return}u.scrollJumpRequest&&i(u)}},s={scroll:a,start:o.start,stop:o.stop};return s},rr="data-rbd",or=function(){var e=rr+"-drag-handle";return{base:e,draggableId:e+"-draggable-id",contextId:e+"-context-id"}}(),Ga=function(){var e=rr+"-draggable";return{base:e,contextId:e+"-context-id",id:e+"-id"}}(),dx=function(){var e=rr+"-droppable";return{base:e,contextId:e+"-context-id",id:e+"-id"}}(),mc={contextId:rr+"-scroll-container-context-id"},fx=function(t){return function(n){return"["+n+'="'+t+'"]'}},Cr=function(t,n){return t.map(function(r){var o=r.styles[n];return o?r.selector+" { "+o+" }":""}).join(" ")},px="pointer-events: none;",hx=function(e){var t=fx(e),n=function(){var s=`
      cursor: -webkit-grab;
      cursor: grab;
    `;return{selector:t(or.contextId),styles:{always:`
          -webkit-touch-callout: none;
          -webkit-tap-highlight-color: rgba(0,0,0,0);
          touch-action: manipulation;
        `,resting:s,dragging:px,dropAnimating:s}}}(),r=function(){var s=`
      transition: `+Br.outOfTheWay+`;
    `;return{selector:t(Ga.contextId),styles:{dragging:s,dropAnimating:s,userCancel:s}}}(),o={selector:t(dx.contextId),styles:{always:"overflow-anchor: none;"}},i={selector:"body",styles:{dragging:`
        cursor: grabbing;
        cursor: -webkit-grabbing;
        user-select: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        overflow-anchor: none;
      `}},a=[r,n,o,i];return{always:Cr(a,"always"),resting:Cr(a,"resting"),dragging:Cr(a,"dragging"),dropAnimating:Cr(a,"dropAnimating"),userCancel:Cr(a,"userCancel")}},Pt=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u"?v.useLayoutEffect:v.useEffect,la=function(){var t=document.querySelector("head");return t||te(),t},vc=function(t){var n=document.createElement("style");return t&&n.setAttribute("nonce",t),n.type="text/css",n};function gx(e,t){var n=ye(function(){return hx(e)},[e]),r=v.useRef(null),o=v.useRef(null),i=le(Ue(function(d){var f=o.current;f||te(),f.textContent=d}),[]),a=le(function(d){var f=r.current;f||te(),f.textContent=d},[]);Pt(function(){!r.current&&!o.current||te();var d=vc(t),f=vc(t);return r.current=d,o.current=f,d.setAttribute(rr+"-always",e),f.setAttribute(rr+"-dynamic",e),la().appendChild(d),la().appendChild(f),a(n.always),i(n.resting),function(){var p=function(m){var x=m.current;x||te(),la().removeChild(x),m.current=null};p(r),p(o)}},[t,a,i,n.always,n.resting,e]);var s=le(function(){return i(n.dragging)},[i,n.dragging]),c=le(function(d){if(d==="DROP"){i(n.dropAnimating);return}i(n.userCancel)},[i,n.dropAnimating,n.userCancel]),u=le(function(){o.current&&i(n.resting)},[i,n.resting]),l=ye(function(){return{dragging:s,dropping:c,resting:u}},[s,c,u]);return l}var hf=function(e){return e&&e.ownerDocument?e.ownerDocument.defaultView:window};function Oi(e){return e instanceof hf(e).HTMLElement}function mx(e,t){var n="["+or.contextId+'="'+e+'"]',r=kd(document.querySelectorAll(n));if(!r.length)return null;var o=En(r,function(i){return i.getAttribute(or.draggableId)===t});return!o||!Oi(o)?null:o}function vx(e){var t=v.useRef({}),n=v.useRef(null),r=v.useRef(null),o=v.useRef(!1),i=le(function(f,p){var g={id:f,focus:p};return t.current[f]=g,function(){var x=t.current,w=x[f];w!==g&&delete x[f]}},[]),a=le(function(f){var p=mx(e,f);p&&p!==document.activeElement&&p.focus()},[e]),s=le(function(f,p){n.current===f&&(n.current=p)},[]),c=le(function(){r.current||o.current&&(r.current=requestAnimationFrame(function(){r.current=null;var f=n.current;f&&a(f)}))},[a]),u=le(function(f){n.current=null;var p=document.activeElement;p&&p.getAttribute(or.draggableId)===f&&(n.current=f)},[]);Pt(function(){return o.current=!0,function(){o.current=!1;var f=r.current;f&&cancelAnimationFrame(f)}},[]);var l=ye(function(){return{register:i,tryRecordFocus:u,tryRestoreFocusRecorded:c,tryShiftRecord:s}},[i,u,c,s]);return l}function yx(){var e={draggables:{},droppables:{}},t=[];function n(d){return t.push(d),function(){var p=t.indexOf(d);p!==-1&&t.splice(p,1)}}function r(d){t.length&&t.forEach(function(f){return f(d)})}function o(d){return e.draggables[d]||null}function i(d){var f=o(d);return f||te(),f}var a={register:function(f){e.draggables[f.descriptor.id]=f,r({type:"ADDITION",value:f})},update:function(f,p){var g=e.draggables[p.descriptor.id];g&&g.uniqueId===f.uniqueId&&(delete e.draggables[p.descriptor.id],e.draggables[f.descriptor.id]=f)},unregister:function(f){var p=f.descriptor.id,g=o(p);g&&f.uniqueId===g.uniqueId&&(delete e.draggables[p],r({type:"REMOVAL",value:f}))},getById:i,findById:o,exists:function(f){return!!o(f)},getAllByType:function(f){return ci(e.draggables).filter(function(p){return p.descriptor.type===f})}};function s(d){return e.droppables[d]||null}function c(d){var f=s(d);return f||te(),f}var u={register:function(f){e.droppables[f.descriptor.id]=f},unregister:function(f){var p=s(f.descriptor.id);p&&f.uniqueId===p.uniqueId&&delete e.droppables[f.descriptor.id]},getById:c,findById:s,exists:function(f){return!!s(f)},getAllByType:function(f){return ci(e.droppables).filter(function(p){return p.descriptor.type===f})}};function l(){e.draggables={},e.droppables={},t.length=0}return{draggable:a,droppable:u,subscribe:n,clean:l}}function bx(){var e=ye(yx,[]);return v.useEffect(function(){return function(){requestAnimationFrame(e.clean)}},[e]),e}var Us=z.createContext(null),di=function(){var e=document.body;return e||te(),e},xx={position:"absolute",width:"1px",height:"1px",margin:"-1px",border:"0",padding:"0",overflow:"hidden",clip:"rect(0 0 0 0)","clip-path":"inset(100%)"},wx=function(t){return"rbd-announcement-"+t};function Sx(e){var t=ye(function(){return wx(e)},[e]),n=v.useRef(null);v.useEffect(function(){var i=document.createElement("div");return n.current=i,i.id=t,i.setAttribute("aria-live","assertive"),i.setAttribute("aria-atomic","true"),de(i.style,xx),di().appendChild(i),function(){setTimeout(function(){var c=di();c.contains(i)&&c.removeChild(i),i===n.current&&(n.current=null)})}},[t]);var r=le(function(o){var i=n.current;if(i){i.textContent=o;return}},[]);return r}var Cx=0,Ex={separator:"::"};function qs(e,t){return t===void 0&&(t=Ex),ye(function(){return""+e+t.separator+Cx++},[t.separator,e])}function Ix(e){var t=e.contextId,n=e.uniqueId;return"rbd-hidden-text-"+t+"-"+n}function Dx(e){var t=e.contextId,n=e.text,r=qs("hidden-text",{separator:"-"}),o=ye(function(){return Ix({contextId:t,uniqueId:r})},[r,t]);return v.useEffect(function(){var a=document.createElement("div");return a.id=o,a.textContent=n,a.style.display="none",di().appendChild(a),function(){var c=di();c.contains(a)&&c.removeChild(a)}},[o,n]),o}var Ri=z.createContext(null);function gf(e){var t=v.useRef(e);return v.useEffect(function(){t.current=e}),t}function Px(){var e=null;function t(){return!!e}function n(a){return a===e}function r(a){e&&te();var s={abandon:a};return e=s,s}function o(){e||te(),e=null}function i(){e&&(e.abandon(),o())}return{isClaimed:t,isActive:n,claim:r,release:o,tryAbandon:i}}var Ax=9,Nx=13,Gs=27,mf=32,Ox=33,Rx=34,Tx=35,jx=36,Mx=37,kx=38,$x=39,_x=40,Mo,Bx=(Mo={},Mo[Nx]=!0,Mo[Ax]=!0,Mo),vf=function(e){Bx[e.keyCode]&&e.preventDefault()},Ti=function(){var e="visibilitychange";if(typeof document>"u")return e;var t=[e,"ms"+e,"webkit"+e,"moz"+e,"o"+e],n=En(t,function(r){return"on"+r in document});return n||e}(),yf=0,yc=5;function Lx(e,t){return Math.abs(t.x-e.x)>=yc||Math.abs(t.y-e.y)>=yc}var bc={type:"IDLE"};function zx(e){var t=e.cancel,n=e.completed,r=e.getPhase,o=e.setPhase;return[{eventName:"mousemove",fn:function(a){var s=a.button,c=a.clientX,u=a.clientY;if(s===yf){var l={x:c,y:u},d=r();if(d.type==="DRAGGING"){a.preventDefault(),d.actions.move(l);return}d.type!=="PENDING"&&te();var f=d.point;if(Lx(f,l)){a.preventDefault();var p=d.actions.fluidLift(l);o({type:"DRAGGING",actions:p})}}}},{eventName:"mouseup",fn:function(a){var s=r();if(s.type!=="DRAGGING"){t();return}a.preventDefault(),s.actions.drop({shouldBlockNextClick:!0}),n()}},{eventName:"mousedown",fn:function(a){r().type==="DRAGGING"&&a.preventDefault(),t()}},{eventName:"keydown",fn:function(a){var s=r();if(s.type==="PENDING"){t();return}if(a.keyCode===Gs){a.preventDefault(),t();return}vf(a)}},{eventName:"resize",fn:t},{eventName:"scroll",options:{passive:!0,capture:!1},fn:function(){r().type==="PENDING"&&t()}},{eventName:"webkitmouseforcedown",fn:function(a){var s=r();if(s.type==="IDLE"&&te(),s.actions.shouldRespectForcePress()){t();return}a.preventDefault()}},{eventName:Ti,fn:t}]}function Fx(e){var t=v.useRef(bc),n=v.useRef(vn),r=ye(function(){return{eventName:"mousedown",fn:function(d){if(!d.defaultPrevented&&d.button===yf&&!(d.ctrlKey||d.metaKey||d.shiftKey||d.altKey)){var f=e.findClosestDraggableId(d);if(f){var p=e.tryGetLock(f,a,{sourceEvent:d});if(p){d.preventDefault();var g={x:d.clientX,y:d.clientY};n.current(),u(p,g)}}}}}},[e]),o=ye(function(){return{eventName:"webkitmouseforcewillbegin",fn:function(d){if(!d.defaultPrevented){var f=e.findClosestDraggableId(d);if(f){var p=e.findOptionsForDraggable(f);p&&(p.shouldRespectForcePress||e.canGetLock(f)&&d.preventDefault())}}}}},[e]),i=le(function(){var d={passive:!1,capture:!0};n.current=$t(window,[o,r],d)},[o,r]),a=le(function(){var l=t.current;l.type!=="IDLE"&&(t.current=bc,n.current(),i())},[i]),s=le(function(){var l=t.current;a(),l.type==="DRAGGING"&&l.actions.cancel({shouldBlockNextClick:!0}),l.type==="PENDING"&&l.actions.abort()},[a]),c=le(function(){var d={capture:!0,passive:!1},f=zx({cancel:s,completed:a,getPhase:function(){return t.current},setPhase:function(g){t.current=g}});n.current=$t(window,f,d)},[s,a]),u=le(function(d,f){t.current.type!=="IDLE"&&te(),t.current={type:"PENDING",point:f,actions:d},c()},[c]);Pt(function(){return i(),function(){n.current()}},[i])}var Hn;function Hx(){}var Wx=(Hn={},Hn[Rx]=!0,Hn[Ox]=!0,Hn[jx]=!0,Hn[Tx]=!0,Hn);function Vx(e,t){function n(){t(),e.cancel()}function r(){t(),e.drop()}return[{eventName:"keydown",fn:function(i){if(i.keyCode===Gs){i.preventDefault(),n();return}if(i.keyCode===mf){i.preventDefault(),r();return}if(i.keyCode===_x){i.preventDefault(),e.moveDown();return}if(i.keyCode===kx){i.preventDefault(),e.moveUp();return}if(i.keyCode===$x){i.preventDefault(),e.moveRight();return}if(i.keyCode===Mx){i.preventDefault(),e.moveLeft();return}if(Wx[i.keyCode]){i.preventDefault();return}vf(i)}},{eventName:"mousedown",fn:n},{eventName:"mouseup",fn:n},{eventName:"click",fn:n},{eventName:"touchstart",fn:n},{eventName:"resize",fn:n},{eventName:"wheel",fn:n,options:{passive:!0}},{eventName:Ti,fn:n}]}function Ux(e){var t=v.useRef(Hx),n=ye(function(){return{eventName:"keydown",fn:function(i){if(i.defaultPrevented||i.keyCode!==mf)return;var a=e.findClosestDraggableId(i);if(!a)return;var s=e.tryGetLock(a,l,{sourceEvent:i});if(!s)return;i.preventDefault();var c=!0,u=s.snapLift();t.current();function l(){c||te(),c=!1,t.current(),r()}t.current=$t(window,Vx(u,l),{capture:!0,passive:!1})}}},[e]),r=le(function(){var i={passive:!1,capture:!0};t.current=$t(window,[n],i)},[n]);Pt(function(){return r(),function(){t.current()}},[r])}var ca={type:"IDLE"},qx=120,Gx=.15;function Xx(e){var t=e.cancel,n=e.getPhase;return[{eventName:"orientationchange",fn:t},{eventName:"resize",fn:t},{eventName:"contextmenu",fn:function(o){o.preventDefault()}},{eventName:"keydown",fn:function(o){if(n().type!=="DRAGGING"){t();return}o.keyCode===Gs&&o.preventDefault(),t()}},{eventName:Ti,fn:t}]}function Yx(e){var t=e.cancel,n=e.completed,r=e.getPhase;return[{eventName:"touchmove",options:{capture:!1},fn:function(i){var a=r();if(a.type!=="DRAGGING"){t();return}a.hasMoved=!0;var s=i.touches[0],c=s.clientX,u=s.clientY,l={x:c,y:u};i.preventDefault(),a.actions.move(l)}},{eventName:"touchend",fn:function(i){var a=r();if(a.type!=="DRAGGING"){t();return}i.preventDefault(),a.actions.drop({shouldBlockNextClick:!0}),n()}},{eventName:"touchcancel",fn:function(i){if(r().type!=="DRAGGING"){t();return}i.preventDefault(),t()}},{eventName:"touchforcechange",fn:function(i){var a=r();a.type==="IDLE"&&te();var s=i.touches[0];if(s){var c=s.force>=Gx;if(c){var u=a.actions.shouldRespectForcePress();if(a.type==="PENDING"){u&&t();return}if(u){if(a.hasMoved){i.preventDefault();return}t();return}i.preventDefault()}}}},{eventName:Ti,fn:t}]}function Kx(e){var t=v.useRef(ca),n=v.useRef(vn),r=le(function(){return t.current},[]),o=le(function(p){t.current=p},[]),i=ye(function(){return{eventName:"touchstart",fn:function(p){if(!p.defaultPrevented){var g=e.findClosestDraggableId(p);if(g){var m=e.tryGetLock(g,s,{sourceEvent:p});if(m){var x=p.touches[0],w=x.clientX,y=x.clientY,E={x:w,y};n.current(),d(m,E)}}}}}},[e]),a=le(function(){var p={capture:!0,passive:!1};n.current=$t(window,[i],p)},[i]),s=le(function(){var f=t.current;f.type!=="IDLE"&&(f.type==="PENDING"&&clearTimeout(f.longPressTimerId),o(ca),n.current(),a())},[a,o]),c=le(function(){var f=t.current;s(),f.type==="DRAGGING"&&f.actions.cancel({shouldBlockNextClick:!0}),f.type==="PENDING"&&f.actions.abort()},[s]),u=le(function(){var p={capture:!0,passive:!1},g={cancel:c,completed:s,getPhase:r},m=$t(window,Yx(g),p),x=$t(window,Xx(g),p);n.current=function(){m(),x()}},[c,r,s]),l=le(function(){var p=r();p.type!=="PENDING"&&te();var g=p.actions.fluidLift(p.point);o({type:"DRAGGING",actions:g,hasMoved:!1})},[r,o]),d=le(function(p,g){r().type!=="IDLE"&&te();var m=setTimeout(l,qx);o({type:"PENDING",point:g,actions:p,longPressTimerId:m}),u()},[u,r,o,l]);Pt(function(){return a(),function(){n.current();var g=r();g.type==="PENDING"&&(clearTimeout(g.longPressTimerId),o(ca))}},[r,a,o]),Pt(function(){var p=$t(window,[{eventName:"touchmove",fn:function(){},options:{capture:!1,passive:!1}}]);return p},[])}var Zx={input:!0,button:!0,textarea:!0,select:!0,option:!0,optgroup:!0,video:!0,audio:!0};function bf(e,t){if(t==null)return!1;var n=!!Zx[t.tagName.toLowerCase()];if(n)return!0;var r=t.getAttribute("contenteditable");return r==="true"||r===""?!0:t===e?!1:bf(e,t.parentElement)}function Jx(e,t){var n=t.target;return Oi(n)?bf(e,n):!1}var Qx=function(e){return Vt(e.getBoundingClientRect()).center};function e1(e){return e instanceof hf(e).Element}var t1=function(){var e="matches";if(typeof document>"u")return e;var t=[e,"msMatchesSelector","webkitMatchesSelector"],n=En(t,function(r){return r in Element.prototype});return n||e}();function xf(e,t){return e==null?null:e[t1](t)?e:xf(e.parentElement,t)}function n1(e,t){return e.closest?e.closest(t):xf(e,t)}function r1(e){return"["+or.contextId+'="'+e+'"]'}function o1(e,t){var n=t.target;if(!e1(n))return null;var r=r1(e),o=n1(n,r);return!o||!Oi(o)?null:o}function i1(e,t){var n=o1(e,t);return n?n.getAttribute(or.draggableId):null}function a1(e,t){var n="["+Ga.contextId+'="'+e+'"]',r=kd(document.querySelectorAll(n)),o=En(r,function(i){return i.getAttribute(Ga.id)===t});return!o||!Oi(o)?null:o}function s1(e){e.preventDefault()}function ko(e){var t=e.expected,n=e.phase,r=e.isLockActive;return e.shouldWarn,!(!r()||t!==n)}function wf(e){var t=e.lockAPI,n=e.store,r=e.registry,o=e.draggableId;if(t.isClaimed())return!1;var i=r.draggable.findById(o);return!(!i||!i.options.isEnabled||!uf(n.getState(),o))}function l1(e){var t=e.lockAPI,n=e.contextId,r=e.store,o=e.registry,i=e.draggableId,a=e.forceSensorStop,s=e.sourceEvent,c=wf({lockAPI:t,store:r,registry:o,draggableId:i});if(!c)return null;var u=o.draggable.getById(i),l=a1(n,u.descriptor.id);if(!l||s&&!u.options.canDragInteractiveElements&&Jx(l,s))return null;var d=t.claim(a||vn),f="PRE_DRAG";function p(){return u.options.shouldRespectForcePress}function g(){return t.isActive(d)}function m(I,P){ko({expected:I,phase:f,isLockActive:g,shouldWarn:!0})&&r.dispatch(P())}var x=m.bind(null,"DRAGGING");function w(I){function P(){t.release(),f="COMPLETED"}f!=="PRE_DRAG"&&(P(),f!=="PRE_DRAG"&&te()),r.dispatch(tb(I.liftActionArgs)),f="DRAGGING";function A(D,k){if(k===void 0&&(k={shouldBlockNextClick:!1}),I.cleanup(),k.shouldBlockNextClick){var L=$t(window,[{eventName:"click",fn:s1,options:{once:!0,passive:!1,capture:!0}}]);setTimeout(L)}P(),r.dispatch(nf({reason:D}))}return de({isActive:function(){return ko({expected:"DRAGGING",phase:f,isLockActive:g,shouldWarn:!1})},shouldRespectForcePress:p,drop:function(k){return A("DROP",k)},cancel:function(k){return A("CANCEL",k)}},I.actions)}function y(I){var P=Wr(function(D){x(function(){return tf({client:D})})}),A=w({liftActionArgs:{id:i,clientSelection:I,movementMode:"FLUID"},cleanup:function(){return P.cancel()},actions:{move:P}});return de({},A,{move:P})}function E(){var I={moveUp:function(){return x(ub)},moveRight:function(){return x(fb)},moveDown:function(){return x(db)},moveLeft:function(){return x(pb)}};return w({liftActionArgs:{id:i,clientSelection:Qx(l),movementMode:"SNAP"},cleanup:vn,actions:I})}function S(){var I=ko({expected:"PRE_DRAG",phase:f,isLockActive:g,shouldWarn:!0});I&&t.release()}var b={isActive:function(){return ko({expected:"PRE_DRAG",phase:f,isLockActive:g,shouldWarn:!1})},shouldRespectForcePress:p,fluidLift:y,snapLift:E,abort:S};return b}var c1=[Fx,Ux,Kx];function u1(e){var t=e.contextId,n=e.store,r=e.registry,o=e.customSensors,i=e.enableDefaultSensors,a=[].concat(i?c1:[],o||[]),s=v.useState(function(){return Px()})[0],c=le(function(y,E){y.isDragging&&!E.isDragging&&s.tryAbandon()},[s]);Pt(function(){var y=n.getState(),E=n.subscribe(function(){var S=n.getState();c(y,S),y=S});return E},[s,n,c]),Pt(function(){return s.tryAbandon},[s.tryAbandon]);for(var u=le(function(w){return wf({lockAPI:s,registry:r,store:n,draggableId:w})},[s,r,n]),l=le(function(w,y,E){return l1({lockAPI:s,registry:r,contextId:t,store:n,draggableId:w,forceSensorStop:y,sourceEvent:E&&E.sourceEvent?E.sourceEvent:null})},[t,s,r,n]),d=le(function(w){return i1(t,w)},[t]),f=le(function(w){var y=r.draggable.findById(w);return y?y.options:null},[r.draggable]),p=le(function(){s.isClaimed()&&(s.tryAbandon(),n.getState().phase!=="IDLE"&&n.dispatch(_s()))},[s,n]),g=le(s.isClaimed,[s]),m=ye(function(){return{canGetLock:u,tryGetLock:l,findClosestDraggableId:d,findOptionsForDraggable:f,tryReleaseLock:p,isLockClaimed:g}},[u,l,d,f,p,g]),x=0;x<a.length;x++)a[x](m)}var d1=function(t){return{onBeforeCapture:t.onBeforeCapture,onBeforeDragStart:t.onBeforeDragStart,onDragStart:t.onDragStart,onDragEnd:t.onDragEnd,onDragUpdate:t.onDragUpdate}};function Er(e){return e.current||te(),e.current}function f1(e){var t=e.contextId,n=e.setCallbacks,r=e.sensors,o=e.nonce,i=e.dragHandleUsageInstructions,a=v.useRef(null),s=gf(e),c=le(function(){return d1(s.current)},[s]),u=Sx(t),l=Dx({contextId:t,text:i}),d=gx(t,o),f=le(function(D){Er(a).dispatch(D)},[]),p=ye(function(){return $l({publishWhileDragging:rb,updateDroppableScroll:ib,updateDroppableIsEnabled:ab,updateDroppableIsCombineEnabled:sb,collectionStarting:ob},f)},[f]),g=bx(),m=ye(function(){return Gb(g,p)},[g,p]),x=ye(function(){return ux(de({scrollWindow:Xb,scrollDroppable:m.scrollDroppable},$l({move:tf},f)))},[m.scrollDroppable,f]),w=vx(t),y=ye(function(){return Wb({announce:u,autoScroller:x,dimensionMarshal:m,focusMarshal:w,getResponders:c,styleMarshal:d})},[u,x,m,w,c,d]);a.current=y;var E=le(function(){var D=Er(a),k=D.getState();k.phase!=="IDLE"&&D.dispatch(_s())},[]),S=le(function(){var D=Er(a).getState();return D.isDragging||D.phase==="DROP_ANIMATING"},[]),b=ye(function(){return{isDragging:S,tryAbort:E}},[S,E]);n(b);var I=le(function(D){return uf(Er(a).getState(),D)},[]),P=le(function(){return Pn(Er(a).getState())},[]),A=ye(function(){return{marshal:m,focus:w,contextId:t,canLift:I,isMovementAllowed:P,dragHandleUsageInstructionsId:l,registry:g}},[t,m,l,w,I,P,g]);return u1({contextId:t,store:y,registry:g,customSensors:r,enableDefaultSensors:e.enableDefaultSensors!==!1}),v.useEffect(function(){return E},[E]),z.createElement(Ri.Provider,{value:A},z.createElement(Xv,{context:Us,store:y},e.children))}var p1=0;function h1(){return ye(function(){return""+p1++},[])}function g1(e){var t=h1(),n=e.dragHandleUsageInstructions||Ko.dragHandleUsageInstructions;return z.createElement(U0,null,function(r){return z.createElement(f1,{nonce:e.nonce,contextId:t,setCallbacks:r,dragHandleUsageInstructions:n,enableDefaultSensors:e.enableDefaultSensors,sensors:e.sensors,onBeforeCapture:e.onBeforeCapture,onBeforeDragStart:e.onBeforeDragStart,onDragStart:e.onDragStart,onDragUpdate:e.onDragUpdate,onDragEnd:e.onDragEnd},e.children)})}var Sf=function(t){return function(n){return t===n}},m1=Sf("scroll"),v1=Sf("auto"),xc=function(t,n){return n(t.overflowX)||n(t.overflowY)},y1=function(t){var n=window.getComputedStyle(t),r={overflowX:n.overflowX,overflowY:n.overflowY};return xc(r,m1)||xc(r,v1)},b1=function(){return!1},x1=function e(t){return t==null?null:t===document.body?b1()?t:null:t===document.documentElement?null:y1(t)?t:e(t.parentElement)},Xa=function(e){return{x:e.scrollLeft,y:e.scrollTop}},w1=function e(t){if(!t)return!1;var n=window.getComputedStyle(t);return n.position==="fixed"?!0:e(t.parentElement)},S1=function(e){var t=x1(e),n=w1(e);return{closestScrollable:t,isFixedOnPage:n}},C1=function(e){var t=e.descriptor,n=e.isEnabled,r=e.isCombineEnabled,o=e.isFixedOnPage,i=e.direction,a=e.client,s=e.page,c=e.closest,u=function(){if(!c)return null;var p=c.scrollSize,g=c.client,m=sf({scrollHeight:p.scrollHeight,scrollWidth:p.scrollWidth,height:g.paddingBox.height,width:g.paddingBox.width});return{pageMarginBox:c.page.marginBox,frameClient:g,scrollSize:p,shouldClipSubject:c.shouldClipSubject,scroll:{initial:c.scroll,current:c.scroll,max:m,diff:{value:qe,displacement:qe}}}}(),l=i==="vertical"?Ts:zd,d=nr({page:s,withPlaceholder:null,axis:l,frame:u}),f={descriptor:t,isCombineEnabled:r,isFixedOnPage:o,axis:l,isEnabled:n,client:a,page:s,frame:u,subject:d};return f},E1=function(t,n){var r=Od(t);if(!n||t!==n)return r;var o=r.paddingBox.top-n.scrollTop,i=r.paddingBox.left-n.scrollLeft,a=o+n.scrollHeight,s=i+n.scrollWidth,c={top:o,right:s,bottom:a,left:i},u=Ps(c,r.border),l=As({borderBox:u,margin:r.margin,border:r.border,padding:r.padding});return l},I1=function(e){var t=e.ref,n=e.descriptor,r=e.env,o=e.windowScroll,i=e.direction,a=e.isDropDisabled,s=e.isCombineEnabled,c=e.shouldClipSubject,u=r.closestScrollable,l=E1(t,u),d=ai(l,o),f=function(){if(!u)return null;var g=Od(u),m={scrollHeight:u.scrollHeight,scrollWidth:u.scrollWidth};return{client:g,page:ai(g,o),scroll:Xa(u),scrollSize:m,shouldClipSubject:c}}(),p=C1({descriptor:n,isEnabled:!a,isCombineEnabled:s,isFixedOnPage:r.isFixedOnPage,direction:i,client:l,page:d,closest:f});return p},D1={passive:!1},P1={passive:!0},wc=function(e){return e.shouldPublishImmediately?D1:P1};function fi(e){var t=v.useContext(e);return t||te(),t}var $o=function(t){return t&&t.env.closestScrollable||null};function A1(e){var t=v.useRef(null),n=fi(Ri),r=qs("droppable"),o=n.registry,i=n.marshal,a=gf(e),s=ye(function(){return{id:e.droppableId,type:e.type,mode:e.mode}},[e.droppableId,e.mode,e.type]),c=v.useRef(s),u=ye(function(){return Ue(function(S,b){t.current||te();var I={x:S,y:b};i.updateDroppableScroll(s.id,I)})},[s.id,i]),l=le(function(){var S=t.current;return!S||!S.env.closestScrollable?qe:Xa(S.env.closestScrollable)},[]),d=le(function(){var S=l();u(S.x,S.y)},[l,u]),f=ye(function(){return Wr(d)},[d]),p=le(function(){var S=t.current,b=$o(S);S&&b||te();var I=S.scrollOptions;if(I.shouldPublishImmediately){d();return}f()},[f,d]),g=le(function(S,b){t.current&&te();var I=a.current,P=I.getDroppableRef();P||te();var A=S1(P),D={ref:P,descriptor:s,env:A,scrollOptions:b};t.current=D;var k=I1({ref:P,descriptor:s,env:A,windowScroll:S,direction:I.direction,isDropDisabled:I.isDropDisabled,isCombineEnabled:I.isCombineEnabled,shouldClipSubject:!I.ignoreContainerClipping}),L=A.closestScrollable;return L&&(L.setAttribute(mc.contextId,n.contextId),L.addEventListener("scroll",p,wc(D.scrollOptions))),k},[n.contextId,s,p,a]),m=le(function(){var S=t.current,b=$o(S);return S&&b||te(),Xa(b)},[]),x=le(function(){var S=t.current;S||te();var b=$o(S);t.current=null,b&&(f.cancel(),b.removeAttribute(mc.contextId),b.removeEventListener("scroll",p,wc(S.scrollOptions)))},[p,f]),w=le(function(S){var b=t.current;b||te();var I=$o(b);I||te(),I.scrollTop+=S.y,I.scrollLeft+=S.x},[]),y=ye(function(){return{getDimensionAndWatchScroll:g,getScrollWhileDragging:m,dragStopped:x,scroll:w}},[x,g,m,w]),E=ye(function(){return{uniqueId:r,descriptor:s,callbacks:y}},[y,s,r]);Pt(function(){return c.current=E.descriptor,o.droppable.register(E),function(){t.current&&x(),o.droppable.unregister(E)}},[y,s,x,E,i,o.droppable]),Pt(function(){t.current&&i.updateDroppableIsEnabled(c.current.id,!e.isDropDisabled)},[e.isDropDisabled,i]),Pt(function(){t.current&&i.updateDroppableIsCombineEnabled(c.current.id,e.isCombineEnabled)},[e.isCombineEnabled,i])}function ua(){}var Sc={width:0,height:0,margin:Z0},N1=function(t){var n=t.isAnimatingOpenOnMount,r=t.placeholder,o=t.animate;return n||o==="close"?Sc:{height:r.client.borderBox.height,width:r.client.borderBox.width,margin:r.client.margin}},O1=function(t){var n=t.isAnimatingOpenOnMount,r=t.placeholder,o=t.animate,i=N1({isAnimatingOpenOnMount:n,placeholder:r,animate:o});return{display:r.display,boxSizing:"border-box",width:i.width,height:i.height,marginTop:i.margin.top,marginRight:i.margin.right,marginBottom:i.margin.bottom,marginLeft:i.margin.left,flexShrink:"0",flexGrow:"0",pointerEvents:"none",transition:o!=="none"?Br.placeholder:null}};function R1(e){var t=v.useRef(null),n=le(function(){t.current&&(clearTimeout(t.current),t.current=null)},[]),r=e.animate,o=e.onTransitionEnd,i=e.onClose,a=e.contextId,s=v.useState(e.animate==="open"),c=s[0],u=s[1];v.useEffect(function(){return c?r!=="open"?(n(),u(!1),ua):t.current?ua:(t.current=setTimeout(function(){t.current=null,u(!1)}),n):ua},[r,c,n]);var l=le(function(f){f.propertyName==="height"&&(o(),r==="close"&&i())},[r,i,o]),d=O1({isAnimatingOpenOnMount:c,animate:e.animate,placeholder:e.placeholder});return z.createElement(e.placeholder.tagName,{style:d,"data-rbd-placeholder-context-id":a,onTransitionEnd:l,ref:e.innerRef})}var T1=z.memo(R1),Xs=z.createContext(null),j1=function(e){gd(t,e);function t(){for(var r,o=arguments.length,i=new Array(o),a=0;a<o;a++)i[a]=arguments[a];return r=e.call.apply(e,[this].concat(i))||this,r.state={isVisible:!!r.props.on,data:r.props.on,animate:r.props.shouldAnimate&&r.props.on?"open":"none"},r.onClose=function(){r.state.animate==="close"&&r.setState({isVisible:!1})},r}t.getDerivedStateFromProps=function(o,i){return o.shouldAnimate?o.on?{isVisible:!0,data:o.on,animate:"open"}:i.isVisible?{isVisible:!0,data:i.data,animate:"close"}:{isVisible:!1,animate:"close",data:null}:{isVisible:!!o.on,data:o.on,animate:"none"}};var n=t.prototype;return n.render=function(){if(!this.state.isVisible)return null;var o={onClose:this.onClose,data:this.state.data,animate:this.state.animate};return this.props.children(o)},t}(z.PureComponent),Cc={dragging:5e3,dropAnimating:4500},M1=function(t,n){return n?Br.drop(n.duration):t?Br.snap:Br.fluid},k1=function(t,n){return t?n?Gr.opacity.drop:Gr.opacity.combining:null},$1=function(t){return t.forceShouldAnimate!=null?t.forceShouldAnimate:t.mode==="SNAP"};function _1(e){var t=e.dimension,n=t.client,r=e.offset,o=e.combineWith,i=e.dropping,a=!!o,s=$1(e),c=!!i,u=c?Ua.drop(r,a):Ua.moveTo(r),l={position:"fixed",top:n.marginBox.top,left:n.marginBox.left,boxSizing:"border-box",width:n.borderBox.width,height:n.borderBox.height,transition:M1(s,i),transform:u,opacity:k1(a,c),zIndex:c?Cc.dropAnimating:Cc.dragging,pointerEvents:"none"};return l}function B1(e){return{transform:Ua.moveTo(e.offset),transition:e.shouldAnimateDisplacement?null:"none"}}function L1(e){return e.type==="DRAGGING"?_1(e):B1(e)}function z1(e,t,n){n===void 0&&(n=qe);var r=window.getComputedStyle(t),o=t.getBoundingClientRect(),i=Nd(o,r),a=ai(i,n),s={client:i,tagName:t.tagName.toLowerCase(),display:r.display},c={x:i.marginBox.width,y:i.marginBox.height},u={descriptor:e,placeholder:s,displaceBy:c,client:i,page:a};return u}function F1(e){var t=qs("draggable"),n=e.descriptor,r=e.registry,o=e.getDraggableRef,i=e.canDragInteractiveElements,a=e.shouldRespectForcePress,s=e.isEnabled,c=ye(function(){return{canDragInteractiveElements:i,shouldRespectForcePress:a,isEnabled:s}},[i,s,a]),u=le(function(p){var g=o();return g||te(),z1(n,g,p)},[n,o]),l=ye(function(){return{uniqueId:t,descriptor:n,options:c,getDimension:u}},[n,u,c,t]),d=v.useRef(l),f=v.useRef(!0);Pt(function(){return r.draggable.register(d.current),function(){return r.draggable.unregister(d.current)}},[r.draggable]),Pt(function(){if(f.current){f.current=!1;return}var p=d.current;d.current=l,r.draggable.update(l,p)},[l,r.draggable])}function H1(e){e.preventDefault()}function W1(e){var t=v.useRef(null),n=le(function(D){t.current=D},[]),r=le(function(){return t.current},[]),o=fi(Ri),i=o.contextId,a=o.dragHandleUsageInstructionsId,s=o.registry,c=fi(Xs),u=c.type,l=c.droppableId,d=ye(function(){return{id:e.draggableId,index:e.index,type:u,droppableId:l}},[e.draggableId,e.index,u,l]),f=e.children,p=e.draggableId,g=e.isEnabled,m=e.shouldRespectForcePress,x=e.canDragInteractiveElements,w=e.isClone,y=e.mapped,E=e.dropAnimationFinished;if(!w){var S=ye(function(){return{descriptor:d,registry:s,getDraggableRef:r,canDragInteractiveElements:x,shouldRespectForcePress:m,isEnabled:g}},[d,s,r,x,m,g]);F1(S)}var b=ye(function(){return g?{tabIndex:0,role:"button","aria-describedby":a,"data-rbd-drag-handle-draggable-id":p,"data-rbd-drag-handle-context-id":i,draggable:!1,onDragStart:H1}:null},[i,a,p,g]),I=le(function(D){y.type==="DRAGGING"&&y.dropping&&D.propertyName==="transform"&&E()},[E,y]),P=ye(function(){var D=L1(y),k=y.type==="DRAGGING"&&y.dropping?I:null,L={innerRef:n,draggableProps:{"data-rbd-draggable-context-id":i,"data-rbd-draggable-id":p,style:D,onTransitionEnd:k},dragHandleProps:b};return L},[i,b,p,y,I,n]),A=ye(function(){return{draggableId:d.id,type:d.type,source:{index:d.index,droppableId:d.droppableId}}},[d.droppableId,d.id,d.index,d.type]);return f(P,y.snapshot,A)}var Cf=function(e,t){return e===t},Ef=function(e){var t=e.combine,n=e.destination;return n?n.droppableId:t?t.droppableId:null},V1=function(t){return t.combine?t.combine.draggableId:null},U1=function(t){return t.at&&t.at.type==="COMBINE"?t.at.combine.draggableId:null};function q1(){var e=Ue(function(o,i){return{x:o,y:i}}),t=Ue(function(o,i,a,s,c){return{isDragging:!0,isClone:i,isDropAnimating:!!c,dropAnimation:c,mode:o,draggingOver:a,combineWith:s,combineTargetFor:null}}),n=Ue(function(o,i,a,s,c,u,l){return{mapped:{type:"DRAGGING",dropping:null,draggingOver:c,combineWith:u,mode:i,offset:o,dimension:a,forceShouldAnimate:l,snapshot:t(i,s,c,u,null)}}}),r=function(i,a){if(i.isDragging){if(i.critical.draggable.id!==a.draggableId)return null;var s=i.current.client.offset,c=i.dimensions.draggables[a.draggableId],u=Dt(i.impact),l=U1(i.impact),d=i.forceShouldAnimate;return n(e(s.x,s.y),i.movementMode,c,a.isClone,u,l,d)}if(i.phase==="DROP_ANIMATING"){var f=i.completed;if(f.result.draggableId!==a.draggableId)return null;var p=a.isClone,g=i.dimensions.draggables[a.draggableId],m=f.result,x=m.mode,w=Ef(m),y=V1(m),E=i.dropDuration,S={duration:E,curve:Ls.drop,moveTo:i.newHomeClientOffset,opacity:y?Gr.opacity.drop:null,scale:y?Gr.scale.drop:null};return{mapped:{type:"DRAGGING",offset:i.newHomeClientOffset,dimension:g,dropping:S,draggingOver:w,combineWith:y,mode:x,forceShouldAnimate:null,snapshot:t(x,p,w,y,S)}}}return null};return r}function If(e){return{isDragging:!1,isDropAnimating:!1,isClone:!1,dropAnimation:null,mode:null,draggingOver:null,combineTargetFor:e,combineWith:null}}var G1={mapped:{type:"SECONDARY",offset:qe,combineTargetFor:null,shouldAnimateDisplacement:!0,snapshot:If(null)}};function X1(){var e=Ue(function(a,s){return{x:a,y:s}}),t=Ue(If),n=Ue(function(a,s,c){return s===void 0&&(s=null),{mapped:{type:"SECONDARY",offset:a,combineTargetFor:s,shouldAnimateDisplacement:c,snapshot:t(s)}}}),r=function(s){return s?n(qe,s,!0):null},o=function(s,c,u,l){var d=u.displaced.visible[s],f=!!(l.inVirtualList&&l.effected[s]),p=Pi(u),g=p&&p.draggableId===s?c:null;if(!d){if(!f)return r(g);if(u.displaced.invisible[s])return null;var m=pr(l.displacedBy.point),x=e(m.x,m.y);return n(x,g,!0)}if(f)return r(g);var w=u.displacedBy.point,y=e(w.x,w.y);return n(y,g,d.shouldAnimate)},i=function(s,c){if(s.isDragging)return s.critical.draggable.id===c.draggableId?null:o(c.draggableId,s.critical.draggable.id,s.impact,s.afterCritical);if(s.phase==="DROP_ANIMATING"){var u=s.completed;return u.result.draggableId===c.draggableId?null:o(c.draggableId,u.result.draggableId,u.impact,u.afterCritical)}return null};return i}var Y1=function(){var t=q1(),n=X1(),r=function(i,a){return t(i,a)||n(i,a)||G1};return r},K1={dropAnimationFinished:rf},Z1=Pd(Y1,K1,null,{context:Us,pure:!0,areStatePropsEqual:Cf})(W1);function J1(e){var t=fi(Xs),n=t.isUsingCloneFor;return n===e.draggableId&&!e.isClone?null:z.createElement(Z1,e)}function Q1(e){var t=v.useContext(Ri);t||te();var n=t.contextId,r=t.isMovementAllowed,o=v.useRef(null),i=v.useRef(null),a=e.children,s=e.droppableId,c=e.type,u=e.mode,l=e.direction,d=e.ignoreContainerClipping,f=e.isDropDisabled,p=e.isCombineEnabled,g=e.snapshot,m=e.useClone,x=e.updateViewportMaxScroll,w=e.getContainerForClone,y=le(function(){return o.current},[]),E=le(function(L){o.current=L},[]);le(function(){return i.current},[]);var S=le(function(L){i.current=L},[]),b=le(function(){r()&&x({maxScroll:cf()})},[r,x]);A1({droppableId:s,type:c,mode:u,direction:l,isDropDisabled:f,isCombineEnabled:p,ignoreContainerClipping:d,getDroppableRef:y});var I=z.createElement(j1,{on:e.placeholder,shouldAnimate:e.shouldAnimatePlaceholder},function(L){var B=L.onClose,j=L.data,C=L.animate;return z.createElement(T1,{placeholder:j,onClose:B,innerRef:S,animate:C,contextId:n,onTransitionEnd:b})}),P=ye(function(){return{innerRef:E,placeholder:I,droppableProps:{"data-rbd-droppable-id":s,"data-rbd-droppable-context-id":n}}},[n,s,I,E]),A=m?m.dragging.draggableId:null,D=ye(function(){return{droppableId:s,type:c,isUsingCloneFor:A}},[s,A,c]);function k(){if(!m)return null;var L=m.dragging,B=m.render,j=z.createElement(J1,{draggableId:L.draggableId,index:L.source.index,isClone:!0,isEnabled:!0,shouldRespectForcePress:!1,canDragInteractiveElements:!0},function(C,O){return B(C,O,L)});return ug.createPortal(j,w())}return z.createElement(Xs.Provider,{value:D},a(P,g),k())}var da=function(t,n){return t===n.droppable.type},Ec=function(t,n){return n.draggables[t.draggable.id]},ew=function(){var t={placeholder:null,shouldAnimatePlaceholder:!0,snapshot:{isDraggingOver:!1,draggingOverWith:null,draggingFromThisWith:null,isUsingPlaceholder:!1},useClone:null},n=de({},t,{shouldAnimatePlaceholder:!1}),r=Ue(function(a){return{draggableId:a.id,type:a.type,source:{index:a.index,droppableId:a.droppableId}}}),o=Ue(function(a,s,c,u,l,d){var f=l.descriptor.id,p=l.descriptor.droppableId===a;if(p){var g=d?{render:d,dragging:r(l.descriptor)}:null,m={isDraggingOver:c,draggingOverWith:c?f:null,draggingFromThisWith:f,isUsingPlaceholder:!0};return{placeholder:l.placeholder,shouldAnimatePlaceholder:!1,snapshot:m,useClone:g}}if(!s)return n;if(!u)return t;var x={isDraggingOver:c,draggingOverWith:f,draggingFromThisWith:null,isUsingPlaceholder:!0};return{placeholder:l.placeholder,shouldAnimatePlaceholder:!0,snapshot:x,useClone:null}}),i=function(s,c){var u=c.droppableId,l=c.type,d=!c.isDropDisabled,f=c.renderClone;if(s.isDragging){var p=s.critical;if(!da(l,p))return n;var g=Ec(p,s.dimensions),m=Dt(s.impact)===u;return o(u,d,m,m,g,f)}if(s.phase==="DROP_ANIMATING"){var x=s.completed;if(!da(l,x.critical))return n;var w=Ec(x.critical,s.dimensions);return o(u,d,Ef(x.result)===u,Dt(x.impact)===u,w,f)}if(s.phase==="IDLE"&&s.completed&&!s.shouldFlush){var y=s.completed;if(!da(l,y.critical))return n;var E=Dt(y.impact)===u,S=!!(y.impact.at&&y.impact.at.type==="COMBINE"),b=y.critical.droppable.id===u;return E?S?t:n:b?t:n}return n};return i},tw={updateViewportMaxScroll:cb};function nw(){return document.body||te(),document.body}var rw={mode:"standard",type:"DEFAULT",direction:"vertical",isDropDisabled:!1,isCombineEnabled:!1,ignoreContainerClipping:!1,renderClone:null,getContainerForClone:nw},Df=Pd(ew,tw,null,{context:Us,pure:!0,areStatePropsEqual:Cf})(Q1);Df.defaultProps=rw;function ow({open:e,anchorEl:t,handlePopoverClose:n,handleSelect:r}){return h.jsx("div",{children:h.jsx(nd,{open:e,anchorEl:t,onClose:n,anchorOrigin:{vertical:"bottom",horizontal:"center"},paperProps:{sx:{backgroundColor:"#fff",borderRadius:"8px",boxShadow:"0 4px 8px rgba(0, 0, 0, 0.1)",padding:"10px"}},children:h.jsxs(rd,{children:[h.jsxs(un,{style:{cursor:"pointer"},onClick:()=>r("Empty Sequence"),children:[h.jsx(dn,{children:h.jsx(Se,{icon:"healthicons:communication",style:{fontSize:"20px",color:"#00C1FE"}})}),h.jsx(fn,{primary:"Empty Sequence",sx:{fontSize:"8px",color:"#00C1FE"}})]}),h.jsxs(un,{style:{cursor:"pointer"},onClick:()=>r("Default Sequence"),children:[h.jsx(dn,{children:h.jsx(Se,{icon:"healthicons:communication",style:{fontSize:"20px",color:"#60D669"}})}),h.jsx(fn,{primary:"Default Sequence",sx:{fontSize:"12px",color:"#60D669"}})]})]})})})}function iw(e,t){const n={};return(e[e.length-1]===""?[...e,""]:e).join((n.padRight?" ":"")+","+(n.padLeft===!1?"":" ")).trim()}const aw=/[ \t\n\f\r]/g;function sw(e){return typeof e=="object"?e.type==="text"?Ic(e.value):!1:Ic(e)}function Ic(e){return e.replace(aw,"")===""}class bo{constructor(t,n,r){this.normal=n,this.property=t,r&&(this.space=r)}}bo.prototype.normal={};bo.prototype.property={};bo.prototype.space=void 0;function Pf(e,t){const n={},r={};for(const o of e)Object.assign(n,o.property),Object.assign(r,o.normal);return new bo(n,r,t)}function Ya(e){return e.toLowerCase()}class ht{constructor(t,n){this.attribute=n,this.property=t}}ht.prototype.attribute="";ht.prototype.booleanish=!1;ht.prototype.boolean=!1;ht.prototype.commaOrSpaceSeparated=!1;ht.prototype.commaSeparated=!1;ht.prototype.defined=!1;ht.prototype.mustUseProperty=!1;ht.prototype.number=!1;ht.prototype.overloadedBoolean=!1;ht.prototype.property="";ht.prototype.spaceSeparated=!1;ht.prototype.space=void 0;let lw=0;const fe=Fn(),He=Fn(),Af=Fn(),J=Fn(),Ne=Fn(),Zn=Fn(),St=Fn();function Fn(){return 2**++lw}const Ka=Object.freeze(Object.defineProperty({__proto__:null,boolean:fe,booleanish:He,commaOrSpaceSeparated:St,commaSeparated:Zn,number:J,overloadedBoolean:Af,spaceSeparated:Ne},Symbol.toStringTag,{value:"Module"})),fa=Object.keys(Ka);class Ys extends ht{constructor(t,n,r,o){let i=-1;if(super(t,n),Dc(this,"space",o),typeof r=="number")for(;++i<fa.length;){const a=fa[i];Dc(this,fa[i],(r&Ka[a])===Ka[a])}}}Ys.prototype.defined=!0;function Dc(e,t,n){n&&(e[t]=n)}function mr(e){const t={},n={};for(const[r,o]of Object.entries(e.properties)){const i=new Ys(r,e.transform(e.attributes||{},r),o,e.space);e.mustUseProperty&&e.mustUseProperty.includes(r)&&(i.mustUseProperty=!0),t[r]=i,n[Ya(r)]=r,n[Ya(i.attribute)]=r}return new bo(t,n,e.space)}const Nf=mr({properties:{ariaActiveDescendant:null,ariaAtomic:He,ariaAutoComplete:null,ariaBusy:He,ariaChecked:He,ariaColCount:J,ariaColIndex:J,ariaColSpan:J,ariaControls:Ne,ariaCurrent:null,ariaDescribedBy:Ne,ariaDetails:null,ariaDisabled:He,ariaDropEffect:Ne,ariaErrorMessage:null,ariaExpanded:He,ariaFlowTo:Ne,ariaGrabbed:He,ariaHasPopup:null,ariaHidden:He,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:Ne,ariaLevel:J,ariaLive:null,ariaModal:He,ariaMultiLine:He,ariaMultiSelectable:He,ariaOrientation:null,ariaOwns:Ne,ariaPlaceholder:null,ariaPosInSet:J,ariaPressed:He,ariaReadOnly:He,ariaRelevant:null,ariaRequired:He,ariaRoleDescription:Ne,ariaRowCount:J,ariaRowIndex:J,ariaRowSpan:J,ariaSelected:He,ariaSetSize:J,ariaSort:null,ariaValueMax:J,ariaValueMin:J,ariaValueNow:J,ariaValueText:null,role:null},transform(e,t){return t==="role"?t:"aria-"+t.slice(4).toLowerCase()}});function Of(e,t){return t in e?e[t]:t}function Rf(e,t){return Of(e,t.toLowerCase())}const cw=mr({attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:Zn,acceptCharset:Ne,accessKey:Ne,action:null,allow:null,allowFullScreen:fe,allowPaymentRequest:fe,allowUserMedia:fe,alt:null,as:null,async:fe,autoCapitalize:null,autoComplete:Ne,autoFocus:fe,autoPlay:fe,blocking:Ne,capture:null,charSet:null,checked:fe,cite:null,className:Ne,cols:J,colSpan:null,content:null,contentEditable:He,controls:fe,controlsList:Ne,coords:J|Zn,crossOrigin:null,data:null,dateTime:null,decoding:null,default:fe,defer:fe,dir:null,dirName:null,disabled:fe,download:Af,draggable:He,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:fe,formTarget:null,headers:Ne,height:J,hidden:fe,high:J,href:null,hrefLang:null,htmlFor:Ne,httpEquiv:Ne,id:null,imageSizes:null,imageSrcSet:null,inert:fe,inputMode:null,integrity:null,is:null,isMap:fe,itemId:null,itemProp:Ne,itemRef:Ne,itemScope:fe,itemType:Ne,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:fe,low:J,manifest:null,max:null,maxLength:J,media:null,method:null,min:null,minLength:J,multiple:fe,muted:fe,name:null,nonce:null,noModule:fe,noValidate:fe,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:fe,optimum:J,pattern:null,ping:Ne,placeholder:null,playsInline:fe,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:fe,referrerPolicy:null,rel:Ne,required:fe,reversed:fe,rows:J,rowSpan:J,sandbox:Ne,scope:null,scoped:fe,seamless:fe,selected:fe,shadowRootClonable:fe,shadowRootDelegatesFocus:fe,shadowRootMode:null,shape:null,size:J,sizes:null,slot:null,span:J,spellCheck:He,src:null,srcDoc:null,srcLang:null,srcSet:null,start:J,step:null,style:null,tabIndex:J,target:null,title:null,translate:null,type:null,typeMustMatch:fe,useMap:null,value:He,width:J,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:Ne,axis:null,background:null,bgColor:null,border:J,borderColor:null,bottomMargin:J,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:fe,declare:fe,event:null,face:null,frame:null,frameBorder:null,hSpace:J,leftMargin:J,link:null,longDesc:null,lowSrc:null,marginHeight:J,marginWidth:J,noResize:fe,noHref:fe,noShade:fe,noWrap:fe,object:null,profile:null,prompt:null,rev:null,rightMargin:J,rules:null,scheme:null,scrolling:He,standby:null,summary:null,text:null,topMargin:J,valueType:null,version:null,vAlign:null,vLink:null,vSpace:J,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:fe,disableRemotePlayback:fe,prefix:null,property:null,results:J,security:null,unselectable:null},space:"html",transform:Rf}),uw=mr({attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},properties:{about:St,accentHeight:J,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:J,amplitude:J,arabicForm:null,ascent:J,attributeName:null,attributeType:null,azimuth:J,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:J,by:null,calcMode:null,capHeight:J,className:Ne,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:J,diffuseConstant:J,direction:null,display:null,dur:null,divisor:J,dominantBaseline:null,download:fe,dx:null,dy:null,edgeMode:null,editable:null,elevation:J,enableBackground:null,end:null,event:null,exponent:J,externalResourcesRequired:null,fill:null,fillOpacity:J,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:Zn,g2:Zn,glyphName:Zn,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:J,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:J,horizOriginX:J,horizOriginY:J,id:null,ideographic:J,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:J,k:J,k1:J,k2:J,k3:J,k4:J,kernelMatrix:St,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:J,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:J,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:J,overlineThickness:J,paintOrder:null,panose1:null,path:null,pathLength:J,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:Ne,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:J,pointsAtY:J,pointsAtZ:J,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:St,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:St,rev:St,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:St,requiredFeatures:St,requiredFonts:St,requiredFormats:St,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:J,specularExponent:J,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:J,strikethroughThickness:J,string:null,stroke:null,strokeDashArray:St,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:J,strokeOpacity:J,strokeWidth:null,style:null,surfaceScale:J,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:St,tabIndex:J,tableValues:null,target:null,targetX:J,targetY:J,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:St,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:J,underlineThickness:J,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:J,values:null,vAlphabetic:J,vMathematical:J,vectorEffect:null,vHanging:J,vIdeographic:J,version:null,vertAdvY:J,vertOriginX:J,vertOriginY:J,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:J,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null},space:"svg",transform:Of}),Tf=mr({properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null},space:"xlink",transform(e,t){return"xlink:"+t.slice(5).toLowerCase()}}),jf=mr({attributes:{xmlnsxlink:"xmlns:xlink"},properties:{xmlnsXLink:null,xmlns:null},space:"xmlns",transform:Rf}),Mf=mr({properties:{xmlBase:null,xmlLang:null,xmlSpace:null},space:"xml",transform(e,t){return"xml:"+t.slice(3).toLowerCase()}}),dw={classId:"classID",dataType:"datatype",itemId:"itemID",strokeDashArray:"strokeDasharray",strokeDashOffset:"strokeDashoffset",strokeLineCap:"strokeLinecap",strokeLineJoin:"strokeLinejoin",strokeMiterLimit:"strokeMiterlimit",typeOf:"typeof",xLinkActuate:"xlinkActuate",xLinkArcRole:"xlinkArcrole",xLinkHref:"xlinkHref",xLinkRole:"xlinkRole",xLinkShow:"xlinkShow",xLinkTitle:"xlinkTitle",xLinkType:"xlinkType",xmlnsXLink:"xmlnsXlink"},fw=/[A-Z]/g,Pc=/-[a-z]/g,pw=/^data[-\w.:]+$/i;function hw(e,t){const n=Ya(t);let r=t,o=ht;if(n in e.normal)return e.property[e.normal[n]];if(n.length>4&&n.slice(0,4)==="data"&&pw.test(t)){if(t.charAt(4)==="-"){const i=t.slice(5).replace(Pc,mw);r="data"+i.charAt(0).toUpperCase()+i.slice(1)}else{const i=t.slice(4);if(!Pc.test(i)){let a=i.replace(fw,gw);a.charAt(0)!=="-"&&(a="-"+a),t="data"+a}}o=Ys}return new o(r,t)}function gw(e){return"-"+e.toLowerCase()}function mw(e){return e.charAt(1).toUpperCase()}const vw=Pf([Nf,cw,Tf,jf,Mf],"html"),Ks=Pf([Nf,uw,Tf,jf,Mf],"svg");function yw(e){return e.join(" ").trim()}var Zs={},Ac=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,bw=/\n/g,xw=/^\s*/,ww=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,Sw=/^:\s*/,Cw=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,Ew=/^[;\s]*/,Iw=/^\s+|\s+$/g,Dw=`
`,Nc="/",Oc="*",Nn="",Pw="comment",Aw="declaration",Nw=function(e,t){if(typeof e!="string")throw new TypeError("First argument must be a string");if(!e)return[];t=t||{};var n=1,r=1;function o(g){var m=g.match(bw);m&&(n+=m.length);var x=g.lastIndexOf(Dw);r=~x?g.length-x:r+g.length}function i(){var g={line:n,column:r};return function(m){return m.position=new a(g),u(),m}}function a(g){this.start=g,this.end={line:n,column:r},this.source=t.source}a.prototype.content=e;function s(g){var m=new Error(t.source+":"+n+":"+r+": "+g);if(m.reason=g,m.filename=t.source,m.line=n,m.column=r,m.source=e,!t.silent)throw m}function c(g){var m=g.exec(e);if(m){var x=m[0];return o(x),e=e.slice(x.length),m}}function u(){c(xw)}function l(g){var m;for(g=g||[];m=d();)m!==!1&&g.push(m);return g}function d(){var g=i();if(!(Nc!=e.charAt(0)||Oc!=e.charAt(1))){for(var m=2;Nn!=e.charAt(m)&&(Oc!=e.charAt(m)||Nc!=e.charAt(m+1));)++m;if(m+=2,Nn===e.charAt(m-1))return s("End of comment missing");var x=e.slice(2,m-2);return r+=2,o(x),e=e.slice(m),r+=2,g({type:Pw,comment:x})}}function f(){var g=i(),m=c(ww);if(m){if(d(),!c(Sw))return s("property missing ':'");var x=c(Cw),w=g({type:Aw,property:Rc(m[0].replace(Ac,Nn)),value:x?Rc(x[0].replace(Ac,Nn)):Nn});return c(Ew),w}}function p(){var g=[];l(g);for(var m;m=f();)m!==!1&&(g.push(m),l(g));return g}return u(),p()};function Rc(e){return e?e.replace(Iw,Nn):Nn}var Ow=zr&&zr.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Zs,"__esModule",{value:!0});Zs.default=Tw;var Rw=Ow(Nw);function Tw(e,t){var n=null;if(!e||typeof e!="string")return n;var r=(0,Rw.default)(e),o=typeof t=="function";return r.forEach(function(i){if(i.type==="declaration"){var a=i.property,s=i.value;o?t(a,s,i):s&&(n=n||{},n[a]=s)}}),n}var ji={};Object.defineProperty(ji,"__esModule",{value:!0});ji.camelCase=void 0;var jw=/^--[a-zA-Z0-9_-]+$/,Mw=/-([a-z])/g,kw=/^[^-]+$/,$w=/^-(webkit|moz|ms|o|khtml)-/,_w=/^-(ms)-/,Bw=function(e){return!e||kw.test(e)||jw.test(e)},Lw=function(e,t){return t.toUpperCase()},Tc=function(e,t){return"".concat(t,"-")},zw=function(e,t){return t===void 0&&(t={}),Bw(e)?e:(e=e.toLowerCase(),t.reactCompat?e=e.replace(_w,Tc):e=e.replace($w,Tc),e.replace(Mw,Lw))};ji.camelCase=zw;var Fw=zr&&zr.__importDefault||function(e){return e&&e.__esModule?e:{default:e}},Hw=Fw(Zs),Ww=ji;function Za(e,t){var n={};return!e||typeof e!="string"||(0,Hw.default)(e,function(r,o){r&&o&&(n[(0,Ww.camelCase)(r,t)]=o)}),n}Za.default=Za;var Vw=Za;const Uw=bs(Vw),kf=$f("end"),Js=$f("start");function $f(e){return t;function t(n){const r=n&&n.position&&n.position[e]||{};if(typeof r.line=="number"&&r.line>0&&typeof r.column=="number"&&r.column>0)return{line:r.line,column:r.column,offset:typeof r.offset=="number"&&r.offset>-1?r.offset:void 0}}}function qw(e){const t=Js(e),n=kf(e);if(t&&n)return{start:t,end:n}}const Qs={}.hasOwnProperty,Gw=new Map,Xw=/[A-Z]/g,Yw=new Set(["table","tbody","thead","tfoot","tr"]),Kw=new Set(["td","th"]),_f="https://github.com/syntax-tree/hast-util-to-jsx-runtime";function Zw(e,t){if(!t||t.Fragment===void 0)throw new TypeError("Expected `Fragment` in options");const n=t.filePath||void 0;let r;if(t.development){if(typeof t.jsxDEV!="function")throw new TypeError("Expected `jsxDEV` in options when `development: true`");r=iS(n,t.jsxDEV)}else{if(typeof t.jsx!="function")throw new TypeError("Expected `jsx` in production options");if(typeof t.jsxs!="function")throw new TypeError("Expected `jsxs` in production options");r=oS(n,t.jsx,t.jsxs)}const o={Fragment:t.Fragment,ancestors:[],components:t.components||{},create:r,elementAttributeNameCase:t.elementAttributeNameCase||"react",evaluater:t.createEvaluater?t.createEvaluater():void 0,filePath:n,ignoreInvalidStyle:t.ignoreInvalidStyle||!1,passKeys:t.passKeys!==!1,passNode:t.passNode||!1,schema:t.space==="svg"?Ks:vw,stylePropertyNameCase:t.stylePropertyNameCase||"dom",tableCellAlignToStyle:t.tableCellAlignToStyle!==!1},i=Bf(o,e,void 0);return i&&typeof i!="string"?i:o.create(e,o.Fragment,{children:i||void 0},void 0)}function Bf(e,t,n){if(t.type==="element")return Jw(e,t,n);if(t.type==="mdxFlowExpression"||t.type==="mdxTextExpression")return Qw(e,t);if(t.type==="mdxJsxFlowElement"||t.type==="mdxJsxTextElement")return tS(e,t,n);if(t.type==="mdxjsEsm")return eS(e,t);if(t.type==="root")return nS(e,t,n);if(t.type==="text")return rS(e,t)}function Jw(e,t,n){const r=e.schema;let o=r;t.tagName.toLowerCase()==="svg"&&r.space==="html"&&(o=Ks,e.schema=o),e.ancestors.push(t);const i=zf(e,t.tagName,!1),a=aS(e,t);let s=tl(e,t);return Yw.has(t.tagName)&&(s=s.filter(function(c){return typeof c=="string"?!sw(c):!0})),Lf(e,a,i,t),el(a,s),e.ancestors.pop(),e.schema=r,e.create(t,i,a,n)}function Qw(e,t){if(t.data&&t.data.estree&&e.evaluater){const r=t.data.estree.body[0];return Tr(r.type==="ExpressionStatement"),e.evaluater.evaluateExpression(r.expression)}Xr(e,t.position)}function eS(e,t){if(t.data&&t.data.estree&&e.evaluater)return e.evaluater.evaluateProgram(t.data.estree);Xr(e,t.position)}function tS(e,t,n){const r=e.schema;let o=r;t.name==="svg"&&r.space==="html"&&(o=Ks,e.schema=o),e.ancestors.push(t);const i=t.name===null?e.Fragment:zf(e,t.name,!0),a=sS(e,t),s=tl(e,t);return Lf(e,a,i,t),el(a,s),e.ancestors.pop(),e.schema=r,e.create(t,i,a,n)}function nS(e,t,n){const r={};return el(r,tl(e,t)),e.create(t,e.Fragment,r,n)}function rS(e,t){return t.value}function Lf(e,t,n,r){typeof n!="string"&&n!==e.Fragment&&e.passNode&&(t.node=r)}function el(e,t){if(t.length>0){const n=t.length>1?t:t[0];n&&(e.children=n)}}function oS(e,t,n){return r;function r(o,i,a,s){const u=Array.isArray(a.children)?n:t;return s?u(i,a,s):u(i,a)}}function iS(e,t){return n;function n(r,o,i,a){const s=Array.isArray(i.children),c=Js(r);return t(o,i,a,s,{columnNumber:c?c.column-1:void 0,fileName:e,lineNumber:c?c.line:void 0},void 0)}}function aS(e,t){const n={};let r,o;for(o in t.properties)if(o!=="children"&&Qs.call(t.properties,o)){const i=lS(e,o,t.properties[o]);if(i){const[a,s]=i;e.tableCellAlignToStyle&&a==="align"&&typeof s=="string"&&Kw.has(t.tagName)?r=s:n[a]=s}}if(r){const i=n.style||(n.style={});i[e.stylePropertyNameCase==="css"?"text-align":"textAlign"]=r}return n}function sS(e,t){const n={};for(const r of t.attributes)if(r.type==="mdxJsxExpressionAttribute")if(r.data&&r.data.estree&&e.evaluater){const i=r.data.estree.body[0];Tr(i.type==="ExpressionStatement");const a=i.expression;Tr(a.type==="ObjectExpression");const s=a.properties[0];Tr(s.type==="SpreadElement"),Object.assign(n,e.evaluater.evaluateExpression(s.argument))}else Xr(e,t.position);else{const o=r.name;let i;if(r.value&&typeof r.value=="object")if(r.value.data&&r.value.data.estree&&e.evaluater){const s=r.value.data.estree.body[0];Tr(s.type==="ExpressionStatement"),i=e.evaluater.evaluateExpression(s.expression)}else Xr(e,t.position);else i=r.value===null?!0:r.value;n[o]=i}return n}function tl(e,t){const n=[];let r=-1;const o=e.passKeys?new Map:Gw;for(;++r<t.children.length;){const i=t.children[r];let a;if(e.passKeys){const c=i.type==="element"?i.tagName:i.type==="mdxJsxFlowElement"||i.type==="mdxJsxTextElement"?i.name:void 0;if(c){const u=o.get(c)||0;a=c+"-"+u,o.set(c,u+1)}}const s=Bf(e,i,a);s!==void 0&&n.push(s)}return n}function lS(e,t,n){const r=hw(e.schema,t);if(!(n==null||typeof n=="number"&&Number.isNaN(n))){if(Array.isArray(n)&&(n=r.commaSeparated?iw(n):yw(n)),r.property==="style"){let o=typeof n=="object"?n:cS(e,String(n));return e.stylePropertyNameCase==="css"&&(o=uS(o)),["style",o]}return[e.elementAttributeNameCase==="react"&&r.space?dw[r.property]||r.property:r.attribute,n]}}function cS(e,t){try{return Uw(t,{reactCompat:!0})}catch(n){if(e.ignoreInvalidStyle)return{};const r=n,o=new ws("Cannot parse `style` attribute",{ancestors:e.ancestors,cause:r,ruleId:"style",source:"hast-util-to-jsx-runtime"});throw o.file=e.filePath||void 0,o.url=_f+"#cannot-parse-style-attribute",o}}function zf(e,t,n){let r;if(!n)r={type:"Literal",value:t};else if(t.includes(".")){const o=t.split(".");let i=-1,a;for(;++i<o.length;){const s=Ll(o[i])?{type:"Identifier",name:o[i]}:{type:"Literal",value:o[i]};a=a?{type:"MemberExpression",object:a,property:s,computed:!!(i&&s.type==="Literal"),optional:!1}:s}r=a}else r=Ll(t)&&!/^[a-z]/.test(t)?{type:"Identifier",name:t}:{type:"Literal",value:t};if(r.type==="Literal"){const o=r.value;return Qs.call(e.components,o)?e.components[o]:o}if(e.evaluater)return e.evaluater.evaluateExpression(r);Xr(e)}function Xr(e,t){const n=new ws("Cannot handle MDX estrees without `createEvaluater`",{ancestors:e.ancestors,place:t,ruleId:"mdx-estree",source:"hast-util-to-jsx-runtime"});throw n.file=e.filePath||void 0,n.url=_f+"#cannot-handle-mdx-estrees-without-createevaluater",n}function uS(e){const t={};let n;for(n in e)Qs.call(e,n)&&(t[dS(n)]=e[n]);return t}function dS(e){let t=e.replace(Xw,fS);return t.slice(0,3)==="ms-"&&(t="-"+t),t}function fS(e){return"-"+e.toLowerCase()}const pa={action:["form"],cite:["blockquote","del","ins","q"],data:["object"],formAction:["button","input"],href:["a","area","base","link"],icon:["menuitem"],itemId:null,manifest:["html"],ping:["a","area"],poster:["video"],src:["audio","embed","iframe","img","input","script","source","track","video"]};function pS(e){const t=this;t.parser=n;function n(r){return Pg(r,{...t.data("settings"),...e,extensions:t.data("micromarkExtensions")||[],mdastExtensions:t.data("fromMarkdownExtensions")||[]})}}function hS(e,t){const n={type:"element",tagName:"blockquote",properties:{},children:e.wrap(e.all(t),!0)};return e.patch(t,n),e.applyData(t,n)}function gS(e,t){const n={type:"element",tagName:"br",properties:{},children:[]};return e.patch(t,n),[e.applyData(t,n),{type:"text",value:`
`}]}function mS(e,t){const n=t.value?t.value+`
`:"",r={};t.lang&&(r.className=["language-"+t.lang]);let o={type:"element",tagName:"code",properties:r,children:[{type:"text",value:n}]};return t.meta&&(o.data={meta:t.meta}),e.patch(t,o),o=e.applyData(t,o),o={type:"element",tagName:"pre",properties:{},children:[o]},e.patch(t,o),o}function vS(e,t){const n={type:"element",tagName:"del",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}function yS(e,t){const n={type:"element",tagName:"em",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}function bS(e,t){const n=typeof e.options.clobberPrefix=="string"?e.options.clobberPrefix:"user-content-",r=String(t.identifier).toUpperCase(),o=fr(r.toLowerCase()),i=e.footnoteOrder.indexOf(r);let a,s=e.footnoteCounts.get(r);s===void 0?(s=0,e.footnoteOrder.push(r),a=e.footnoteOrder.length):a=i+1,s+=1,e.footnoteCounts.set(r,s);const c={type:"element",tagName:"a",properties:{href:"#"+n+"fn-"+o,id:n+"fnref-"+o+(s>1?"-"+s:""),dataFootnoteRef:!0,ariaDescribedBy:["footnote-label"]},children:[{type:"text",value:String(a)}]};e.patch(t,c);const u={type:"element",tagName:"sup",properties:{},children:[c]};return e.patch(t,u),e.applyData(t,u)}function xS(e,t){const n={type:"element",tagName:"h"+t.depth,properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}function wS(e,t){if(e.options.allowDangerousHtml){const n={type:"raw",value:t.value};return e.patch(t,n),e.applyData(t,n)}}function Ff(e,t){const n=t.referenceType;let r="]";if(n==="collapsed"?r+="[]":n==="full"&&(r+="["+(t.label||t.identifier)+"]"),t.type==="imageReference")return[{type:"text",value:"!["+t.alt+r}];const o=e.all(t),i=o[0];i&&i.type==="text"?i.value="["+i.value:o.unshift({type:"text",value:"["});const a=o[o.length-1];return a&&a.type==="text"?a.value+=r:o.push({type:"text",value:r}),o}function SS(e,t){const n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return Ff(e,t);const o={src:fr(r.url||""),alt:t.alt};r.title!==null&&r.title!==void 0&&(o.title=r.title);const i={type:"element",tagName:"img",properties:o,children:[]};return e.patch(t,i),e.applyData(t,i)}function CS(e,t){const n={src:fr(t.url)};t.alt!==null&&t.alt!==void 0&&(n.alt=t.alt),t.title!==null&&t.title!==void 0&&(n.title=t.title);const r={type:"element",tagName:"img",properties:n,children:[]};return e.patch(t,r),e.applyData(t,r)}function ES(e,t){const n={type:"text",value:t.value.replace(/\r?\n|\r/g," ")};e.patch(t,n);const r={type:"element",tagName:"code",properties:{},children:[n]};return e.patch(t,r),e.applyData(t,r)}function IS(e,t){const n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return Ff(e,t);const o={href:fr(r.url||"")};r.title!==null&&r.title!==void 0&&(o.title=r.title);const i={type:"element",tagName:"a",properties:o,children:e.all(t)};return e.patch(t,i),e.applyData(t,i)}function DS(e,t){const n={href:fr(t.url)};t.title!==null&&t.title!==void 0&&(n.title=t.title);const r={type:"element",tagName:"a",properties:n,children:e.all(t)};return e.patch(t,r),e.applyData(t,r)}function PS(e,t,n){const r=e.all(t),o=n?AS(n):Hf(t),i={},a=[];if(typeof t.checked=="boolean"){const l=r[0];let d;l&&l.type==="element"&&l.tagName==="p"?d=l:(d={type:"element",tagName:"p",properties:{},children:[]},r.unshift(d)),d.children.length>0&&d.children.unshift({type:"text",value:" "}),d.children.unshift({type:"element",tagName:"input",properties:{type:"checkbox",checked:t.checked,disabled:!0},children:[]}),i.className=["task-list-item"]}let s=-1;for(;++s<r.length;){const l=r[s];(o||s!==0||l.type!=="element"||l.tagName!=="p")&&a.push({type:"text",value:`
`}),l.type==="element"&&l.tagName==="p"&&!o?a.push(...l.children):a.push(l)}const c=r[r.length-1];c&&(o||c.type!=="element"||c.tagName!=="p")&&a.push({type:"text",value:`
`});const u={type:"element",tagName:"li",properties:i,children:a};return e.patch(t,u),e.applyData(t,u)}function AS(e){let t=!1;if(e.type==="list"){t=e.spread||!1;const n=e.children;let r=-1;for(;!t&&++r<n.length;)t=Hf(n[r])}return t}function Hf(e){const t=e.spread;return t??e.children.length>1}function NS(e,t){const n={},r=e.all(t);let o=-1;for(typeof t.start=="number"&&t.start!==1&&(n.start=t.start);++o<r.length;){const a=r[o];if(a.type==="element"&&a.tagName==="li"&&a.properties&&Array.isArray(a.properties.className)&&a.properties.className.includes("task-list-item")){n.className=["contains-task-list"];break}}const i={type:"element",tagName:t.ordered?"ol":"ul",properties:n,children:e.wrap(r,!0)};return e.patch(t,i),e.applyData(t,i)}function OS(e,t){const n={type:"element",tagName:"p",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}function RS(e,t){const n={type:"root",children:e.wrap(e.all(t))};return e.patch(t,n),e.applyData(t,n)}function TS(e,t){const n={type:"element",tagName:"strong",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}function jS(e,t){const n=e.all(t),r=n.shift(),o=[];if(r){const a={type:"element",tagName:"thead",properties:{},children:e.wrap([r],!0)};e.patch(t.children[0],a),o.push(a)}if(n.length>0){const a={type:"element",tagName:"tbody",properties:{},children:e.wrap(n,!0)},s=Js(t.children[1]),c=kf(t.children[t.children.length-1]);s&&c&&(a.position={start:s,end:c}),o.push(a)}const i={type:"element",tagName:"table",properties:{},children:e.wrap(o,!0)};return e.patch(t,i),e.applyData(t,i)}function MS(e,t,n){const r=n?n.children:void 0,i=(r?r.indexOf(t):1)===0?"th":"td",a=n&&n.type==="table"?n.align:void 0,s=a?a.length:t.children.length;let c=-1;const u=[];for(;++c<s;){const d=t.children[c],f={},p=a?a[c]:void 0;p&&(f.align=p);let g={type:"element",tagName:i,properties:f,children:[]};d&&(g.children=e.all(d),e.patch(d,g),g=e.applyData(d,g)),u.push(g)}const l={type:"element",tagName:"tr",properties:{},children:e.wrap(u,!0)};return e.patch(t,l),e.applyData(t,l)}function kS(e,t){const n={type:"element",tagName:"td",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}const jc=9,Mc=32;function $S(e){const t=String(e),n=/\r?\n|\r/g;let r=n.exec(t),o=0;const i=[];for(;r;)i.push(kc(t.slice(o,r.index),o>0,!0),r[0]),o=r.index+r[0].length,r=n.exec(t);return i.push(kc(t.slice(o),o>0,!1)),i.join("")}function kc(e,t,n){let r=0,o=e.length;if(t){let i=e.codePointAt(r);for(;i===jc||i===Mc;)r++,i=e.codePointAt(r)}if(n){let i=e.codePointAt(o-1);for(;i===jc||i===Mc;)o--,i=e.codePointAt(o-1)}return o>r?e.slice(r,o):""}function _S(e,t){const n={type:"text",value:$S(String(t.value))};return e.patch(t,n),e.applyData(t,n)}function BS(e,t){const n={type:"element",tagName:"hr",properties:{},children:[]};return e.patch(t,n),e.applyData(t,n)}const LS={blockquote:hS,break:gS,code:mS,delete:vS,emphasis:yS,footnoteReference:bS,heading:xS,html:wS,imageReference:SS,image:CS,inlineCode:ES,linkReference:IS,link:DS,listItem:PS,list:NS,paragraph:OS,root:RS,strong:TS,table:jS,tableCell:kS,tableRow:MS,text:_S,thematicBreak:BS,toml:_o,yaml:_o,definition:_o,footnoteDefinition:_o};function _o(){}const Wf=-1,Mi=0,Lr=1,pi=2,nl=3,rl=4,ol=5,il=6,Vf=7,Uf=8,$c=typeof self=="object"?self:globalThis,zS=(e,t)=>{const n=(o,i)=>(e.set(i,o),o),r=o=>{if(e.has(o))return e.get(o);const[i,a]=t[o];switch(i){case Mi:case Wf:return n(a,o);case Lr:{const s=n([],o);for(const c of a)s.push(r(c));return s}case pi:{const s=n({},o);for(const[c,u]of a)s[r(c)]=r(u);return s}case nl:return n(new Date(a),o);case rl:{const{source:s,flags:c}=a;return n(new RegExp(s,c),o)}case ol:{const s=n(new Map,o);for(const[c,u]of a)s.set(r(c),r(u));return s}case il:{const s=n(new Set,o);for(const c of a)s.add(r(c));return s}case Vf:{const{name:s,message:c}=a;return n(new $c[s](c),o)}case Uf:return n(BigInt(a),o);case"BigInt":return n(Object(BigInt(a)),o);case"ArrayBuffer":return n(new Uint8Array(a).buffer,a);case"DataView":{const{buffer:s}=new Uint8Array(a);return n(new DataView(s),a)}}return n(new $c[i](a),o)};return r},_c=e=>zS(new Map,e)(0),Wn="",{toString:FS}={},{keys:HS}=Object,Ir=e=>{const t=typeof e;if(t!=="object"||!e)return[Mi,t];const n=FS.call(e).slice(8,-1);switch(n){case"Array":return[Lr,Wn];case"Object":return[pi,Wn];case"Date":return[nl,Wn];case"RegExp":return[rl,Wn];case"Map":return[ol,Wn];case"Set":return[il,Wn];case"DataView":return[Lr,n]}return n.includes("Array")?[Lr,n]:n.includes("Error")?[Vf,n]:[pi,n]},Bo=([e,t])=>e===Mi&&(t==="function"||t==="symbol"),WS=(e,t,n,r)=>{const o=(a,s)=>{const c=r.push(a)-1;return n.set(s,c),c},i=a=>{if(n.has(a))return n.get(a);let[s,c]=Ir(a);switch(s){case Mi:{let l=a;switch(c){case"bigint":s=Uf,l=a.toString();break;case"function":case"symbol":if(e)throw new TypeError("unable to serialize "+c);l=null;break;case"undefined":return o([Wf],a)}return o([s,l],a)}case Lr:{if(c){let f=a;return c==="DataView"?f=new Uint8Array(a.buffer):c==="ArrayBuffer"&&(f=new Uint8Array(a)),o([c,[...f]],a)}const l=[],d=o([s,l],a);for(const f of a)l.push(i(f));return d}case pi:{if(c)switch(c){case"BigInt":return o([c,a.toString()],a);case"Boolean":case"Number":case"String":return o([c,a.valueOf()],a)}if(t&&"toJSON"in a)return i(a.toJSON());const l=[],d=o([s,l],a);for(const f of HS(a))(e||!Bo(Ir(a[f])))&&l.push([i(f),i(a[f])]);return d}case nl:return o([s,a.toISOString()],a);case rl:{const{source:l,flags:d}=a;return o([s,{source:l,flags:d}],a)}case ol:{const l=[],d=o([s,l],a);for(const[f,p]of a)(e||!(Bo(Ir(f))||Bo(Ir(p))))&&l.push([i(f),i(p)]);return d}case il:{const l=[],d=o([s,l],a);for(const f of a)(e||!Bo(Ir(f)))&&l.push(i(f));return d}}const{message:u}=a;return o([s,{name:c,message:u}],a)};return i},Bc=(e,{json:t,lossy:n}={})=>{const r=[];return WS(!(t||n),!!t,new Map,r)(e),r},hi=typeof structuredClone=="function"?(e,t)=>t&&("json"in t||"lossy"in t)?_c(Bc(e,t)):structuredClone(e):(e,t)=>_c(Bc(e,t));function VS(e,t){const n=[{type:"text",value:"↩"}];return t>1&&n.push({type:"element",tagName:"sup",properties:{},children:[{type:"text",value:String(t)}]}),n}function US(e,t){return"Back to reference "+(e+1)+(t>1?"-"+t:"")}function qS(e){const t=typeof e.options.clobberPrefix=="string"?e.options.clobberPrefix:"user-content-",n=e.options.footnoteBackContent||VS,r=e.options.footnoteBackLabel||US,o=e.options.footnoteLabel||"Footnotes",i=e.options.footnoteLabelTagName||"h2",a=e.options.footnoteLabelProperties||{className:["sr-only"]},s=[];let c=-1;for(;++c<e.footnoteOrder.length;){const u=e.footnoteById.get(e.footnoteOrder[c]);if(!u)continue;const l=e.all(u),d=String(u.identifier).toUpperCase(),f=fr(d.toLowerCase());let p=0;const g=[],m=e.footnoteCounts.get(d);for(;m!==void 0&&++p<=m;){g.length>0&&g.push({type:"text",value:" "});let y=typeof n=="string"?n:n(c,p);typeof y=="string"&&(y={type:"text",value:y}),g.push({type:"element",tagName:"a",properties:{href:"#"+t+"fnref-"+f+(p>1?"-"+p:""),dataFootnoteBackref:"",ariaLabel:typeof r=="string"?r:r(c,p),className:["data-footnote-backref"]},children:Array.isArray(y)?y:[y]})}const x=l[l.length-1];if(x&&x.type==="element"&&x.tagName==="p"){const y=x.children[x.children.length-1];y&&y.type==="text"?y.value+=" ":x.children.push({type:"text",value:" "}),x.children.push(...g)}else l.push(...g);const w={type:"element",tagName:"li",properties:{id:t+"fn-"+f},children:e.wrap(l,!0)};e.patch(u,w),s.push(w)}if(s.length!==0)return{type:"element",tagName:"section",properties:{dataFootnotes:!0,className:["footnotes"]},children:[{type:"element",tagName:i,properties:{...hi(a),id:"footnote-label"},children:[{type:"text",value:o}]},{type:"text",value:`
`},{type:"element",tagName:"ol",properties:{},children:e.wrap(s,!0)},{type:"text",value:`
`}]}}const Ja={}.hasOwnProperty,GS={};function XS(e,t){const n=t||GS,r=new Map,o=new Map,i=new Map,a={...LS,...n.handlers},s={all:u,applyData:KS,definitionById:r,footnoteById:o,footnoteCounts:i,footnoteOrder:[],handlers:a,one:c,options:n,patch:YS,wrap:JS};return ad(e,function(l){if(l.type==="definition"||l.type==="footnoteDefinition"){const d=l.type==="definition"?r:o,f=String(l.identifier).toUpperCase();d.has(f)||d.set(f,l)}}),s;function c(l,d){const f=l.type,p=s.handlers[f];if(Ja.call(s.handlers,f)&&p)return p(s,l,d);if(s.options.passThrough&&s.options.passThrough.includes(f)){if("children"in l){const{children:m,...x}=l,w=hi(x);return w.children=s.all(l),w}return hi(l)}return(s.options.unknownHandler||ZS)(s,l,d)}function u(l){const d=[];if("children"in l){const f=l.children;let p=-1;for(;++p<f.length;){const g=s.one(f[p],l);if(g){if(p&&f[p-1].type==="break"&&(!Array.isArray(g)&&g.type==="text"&&(g.value=Lc(g.value)),!Array.isArray(g)&&g.type==="element")){const m=g.children[0];m&&m.type==="text"&&(m.value=Lc(m.value))}Array.isArray(g)?d.push(...g):d.push(g)}}}return d}}function YS(e,t){e.position&&(t.position=qw(e))}function KS(e,t){let n=t;if(e&&e.data){const r=e.data.hName,o=e.data.hChildren,i=e.data.hProperties;if(typeof r=="string")if(n.type==="element")n.tagName=r;else{const a="children"in n?n.children:[n];n={type:"element",tagName:r,properties:{},children:a}}n.type==="element"&&i&&Object.assign(n.properties,hi(i)),"children"in n&&n.children&&o!==null&&o!==void 0&&(n.children=o)}return n}function ZS(e,t){const n=t.data||{},r="value"in t&&!(Ja.call(n,"hProperties")||Ja.call(n,"hChildren"))?{type:"text",value:t.value}:{type:"element",tagName:"div",properties:{},children:e.all(t)};return e.patch(t,r),e.applyData(t,r)}function JS(e,t){const n=[];let r=-1;for(t&&n.push({type:"text",value:`
`});++r<e.length;)r&&n.push({type:"text",value:`
`}),n.push(e[r]);return t&&e.length>0&&n.push({type:"text",value:`
`}),n}function Lc(e){let t=0,n=e.charCodeAt(t);for(;n===9||n===32;)t++,n=e.charCodeAt(t);return e.slice(t)}function zc(e,t){const n=XS(e,t),r=n.one(e,void 0),o=qS(n),i=Array.isArray(r)?{type:"root",children:r}:r||{type:"root",children:[]};return o&&i.children.push({type:"text",value:`
`},o),i}function QS(e,t){return e&&"run"in e?async function(n,r){const o=zc(n,{file:r,...t});await e.run(o,r)}:function(n,r){return zc(n,{file:r,...e||t})}}function Fc(e){if(e)throw e}var Zo=Object.prototype.hasOwnProperty,qf=Object.prototype.toString,Hc=Object.defineProperty,Wc=Object.getOwnPropertyDescriptor,Vc=function(t){return typeof Array.isArray=="function"?Array.isArray(t):qf.call(t)==="[object Array]"},Uc=function(t){if(!t||qf.call(t)!=="[object Object]")return!1;var n=Zo.call(t,"constructor"),r=t.constructor&&t.constructor.prototype&&Zo.call(t.constructor.prototype,"isPrototypeOf");if(t.constructor&&!n&&!r)return!1;var o;for(o in t);return typeof o>"u"||Zo.call(t,o)},qc=function(t,n){Hc&&n.name==="__proto__"?Hc(t,n.name,{enumerable:!0,configurable:!0,value:n.newValue,writable:!0}):t[n.name]=n.newValue},Gc=function(t,n){if(n==="__proto__")if(Zo.call(t,n)){if(Wc)return Wc(t,n).value}else return;return t[n]},e2=function e(){var t,n,r,o,i,a,s=arguments[0],c=1,u=arguments.length,l=!1;for(typeof s=="boolean"&&(l=s,s=arguments[1]||{},c=2),(s==null||typeof s!="object"&&typeof s!="function")&&(s={});c<u;++c)if(t=arguments[c],t!=null)for(n in t)r=Gc(s,n),o=Gc(t,n),s!==o&&(l&&o&&(Uc(o)||(i=Vc(o)))?(i?(i=!1,a=r&&Vc(r)?r:[]):a=r&&Uc(r)?r:{},qc(s,{name:n,newValue:e(l,a,o)})):typeof o<"u"&&qc(s,{name:n,newValue:o}));return s};const ha=bs(e2);function Qa(e){if(typeof e!="object"||e===null)return!1;const t=Object.getPrototypeOf(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}function t2(){const e=[],t={run:n,use:r};return t;function n(...o){let i=-1;const a=o.pop();if(typeof a!="function")throw new TypeError("Expected function as last argument, not "+a);s(null,...o);function s(c,...u){const l=e[++i];let d=-1;if(c){a(c);return}for(;++d<o.length;)(u[d]===null||u[d]===void 0)&&(u[d]=o[d]);o=u,l?n2(l,s)(...u):a(null,...u)}}function r(o){if(typeof o!="function")throw new TypeError("Expected `middelware` to be a function, not "+o);return e.push(o),t}}function n2(e,t){let n;return r;function r(...a){const s=e.length>a.length;let c;s&&a.push(o);try{c=e.apply(this,a)}catch(u){const l=u;if(s&&n)throw l;return o(l)}s||(c&&c.then&&typeof c.then=="function"?c.then(i,o):c instanceof Error?o(c):i(c))}function o(a,...s){n||(n=!0,t(a,...s))}function i(a){o(null,a)}}const Zt={basename:r2,dirname:o2,extname:i2,join:a2,sep:"/"};function r2(e,t){if(t!==void 0&&typeof t!="string")throw new TypeError('"ext" argument must be a string');xo(e);let n=0,r=-1,o=e.length,i;if(t===void 0||t.length===0||t.length>e.length){for(;o--;)if(e.codePointAt(o)===47){if(i){n=o+1;break}}else r<0&&(i=!0,r=o+1);return r<0?"":e.slice(n,r)}if(t===e)return"";let a=-1,s=t.length-1;for(;o--;)if(e.codePointAt(o)===47){if(i){n=o+1;break}}else a<0&&(i=!0,a=o+1),s>-1&&(e.codePointAt(o)===t.codePointAt(s--)?s<0&&(r=o):(s=-1,r=a));return n===r?r=a:r<0&&(r=e.length),e.slice(n,r)}function o2(e){if(xo(e),e.length===0)return".";let t=-1,n=e.length,r;for(;--n;)if(e.codePointAt(n)===47){if(r){t=n;break}}else r||(r=!0);return t<0?e.codePointAt(0)===47?"/":".":t===1&&e.codePointAt(0)===47?"//":e.slice(0,t)}function i2(e){xo(e);let t=e.length,n=-1,r=0,o=-1,i=0,a;for(;t--;){const s=e.codePointAt(t);if(s===47){if(a){r=t+1;break}continue}n<0&&(a=!0,n=t+1),s===46?o<0?o=t:i!==1&&(i=1):o>-1&&(i=-1)}return o<0||n<0||i===0||i===1&&o===n-1&&o===r+1?"":e.slice(o,n)}function a2(...e){let t=-1,n;for(;++t<e.length;)xo(e[t]),e[t]&&(n=n===void 0?e[t]:n+"/"+e[t]);return n===void 0?".":s2(n)}function s2(e){xo(e);const t=e.codePointAt(0)===47;let n=l2(e,!t);return n.length===0&&!t&&(n="."),n.length>0&&e.codePointAt(e.length-1)===47&&(n+="/"),t?"/"+n:n}function l2(e,t){let n="",r=0,o=-1,i=0,a=-1,s,c;for(;++a<=e.length;){if(a<e.length)s=e.codePointAt(a);else{if(s===47)break;s=47}if(s===47){if(!(o===a-1||i===1))if(o!==a-1&&i===2){if(n.length<2||r!==2||n.codePointAt(n.length-1)!==46||n.codePointAt(n.length-2)!==46){if(n.length>2){if(c=n.lastIndexOf("/"),c!==n.length-1){c<0?(n="",r=0):(n=n.slice(0,c),r=n.length-1-n.lastIndexOf("/")),o=a,i=0;continue}}else if(n.length>0){n="",r=0,o=a,i=0;continue}}t&&(n=n.length>0?n+"/..":"..",r=2)}else n.length>0?n+="/"+e.slice(o+1,a):n=e.slice(o+1,a),r=a-o-1;o=a,i=0}else s===46&&i>-1?i++:i=-1}return n}function xo(e){if(typeof e!="string")throw new TypeError("Path must be a string. Received "+JSON.stringify(e))}const c2={cwd:u2};function u2(){return"/"}function es(e){return!!(e!==null&&typeof e=="object"&&"href"in e&&e.href&&"protocol"in e&&e.protocol&&e.auth===void 0)}function d2(e){if(typeof e=="string")e=new URL(e);else if(!es(e)){const t=new TypeError('The "path" argument must be of type string or an instance of URL. Received `'+e+"`");throw t.code="ERR_INVALID_ARG_TYPE",t}if(e.protocol!=="file:"){const t=new TypeError("The URL must be of scheme file");throw t.code="ERR_INVALID_URL_SCHEME",t}return f2(e)}function f2(e){if(e.hostname!==""){const r=new TypeError('File URL host must be "localhost" or empty on darwin');throw r.code="ERR_INVALID_FILE_URL_HOST",r}const t=e.pathname;let n=-1;for(;++n<t.length;)if(t.codePointAt(n)===37&&t.codePointAt(n+1)===50){const r=t.codePointAt(n+2);if(r===70||r===102){const o=new TypeError("File URL path must not include encoded / characters");throw o.code="ERR_INVALID_FILE_URL_PATH",o}}return decodeURIComponent(t)}const ga=["history","path","basename","stem","extname","dirname"];class Gf{constructor(t){let n;t?es(t)?n={path:t}:typeof t=="string"||p2(t)?n={value:t}:n=t:n={},this.cwd="cwd"in n?"":c2.cwd(),this.data={},this.history=[],this.messages=[],this.value,this.map,this.result,this.stored;let r=-1;for(;++r<ga.length;){const i=ga[r];i in n&&n[i]!==void 0&&n[i]!==null&&(this[i]=i==="history"?[...n[i]]:n[i])}let o;for(o in n)ga.includes(o)||(this[o]=n[o])}get basename(){return typeof this.path=="string"?Zt.basename(this.path):void 0}set basename(t){va(t,"basename"),ma(t,"basename"),this.path=Zt.join(this.dirname||"",t)}get dirname(){return typeof this.path=="string"?Zt.dirname(this.path):void 0}set dirname(t){Xc(this.basename,"dirname"),this.path=Zt.join(t||"",this.basename)}get extname(){return typeof this.path=="string"?Zt.extname(this.path):void 0}set extname(t){if(ma(t,"extname"),Xc(this.dirname,"extname"),t){if(t.codePointAt(0)!==46)throw new Error("`extname` must start with `.`");if(t.includes(".",1))throw new Error("`extname` cannot contain multiple dots")}this.path=Zt.join(this.dirname,this.stem+(t||""))}get path(){return this.history[this.history.length-1]}set path(t){es(t)&&(t=d2(t)),va(t,"path"),this.path!==t&&this.history.push(t)}get stem(){return typeof this.path=="string"?Zt.basename(this.path,this.extname):void 0}set stem(t){va(t,"stem"),ma(t,"stem"),this.path=Zt.join(this.dirname||"",t+(this.extname||""))}fail(t,n,r){const o=this.message(t,n,r);throw o.fatal=!0,o}info(t,n,r){const o=this.message(t,n,r);return o.fatal=void 0,o}message(t,n,r){const o=new ws(t,n,r);return this.path&&(o.name=this.path+":"+o.name,o.file=this.path),o.fatal=!1,this.messages.push(o),o}toString(t){return this.value===void 0?"":typeof this.value=="string"?this.value:new TextDecoder(t||void 0).decode(this.value)}}function ma(e,t){if(e&&e.includes(Zt.sep))throw new Error("`"+t+"` cannot be a path: did not expect `"+Zt.sep+"`")}function va(e,t){if(!e)throw new Error("`"+t+"` cannot be empty")}function Xc(e,t){if(!e)throw new Error("Setting `"+t+"` requires `path` to be set too")}function p2(e){return!!(e&&typeof e=="object"&&"byteLength"in e&&"byteOffset"in e)}const h2=function(e){const r=this.constructor.prototype,o=r[e],i=function(){return o.apply(i,arguments)};return Object.setPrototypeOf(i,r),i},g2={}.hasOwnProperty;class al extends h2{constructor(){super("copy"),this.Compiler=void 0,this.Parser=void 0,this.attachers=[],this.compiler=void 0,this.freezeIndex=-1,this.frozen=void 0,this.namespace={},this.parser=void 0,this.transformers=t2()}copy(){const t=new al;let n=-1;for(;++n<this.attachers.length;){const r=this.attachers[n];t.use(...r)}return t.data(ha(!0,{},this.namespace)),t}data(t,n){return typeof t=="string"?arguments.length===2?(xa("data",this.frozen),this.namespace[t]=n,this):g2.call(this.namespace,t)&&this.namespace[t]||void 0:t?(xa("data",this.frozen),this.namespace=t,this):this.namespace}freeze(){if(this.frozen)return this;const t=this;for(;++this.freezeIndex<this.attachers.length;){const[n,...r]=this.attachers[this.freezeIndex];if(r[0]===!1)continue;r[0]===!0&&(r[0]=void 0);const o=n.call(t,...r);typeof o=="function"&&this.transformers.use(o)}return this.frozen=!0,this.freezeIndex=Number.POSITIVE_INFINITY,this}parse(t){this.freeze();const n=Lo(t),r=this.parser||this.Parser;return ya("parse",r),r(String(n),n)}process(t,n){const r=this;return this.freeze(),ya("process",this.parser||this.Parser),ba("process",this.compiler||this.Compiler),n?o(void 0,n):new Promise(o);function o(i,a){const s=Lo(t),c=r.parse(s);r.run(c,s,function(l,d,f){if(l||!d||!f)return u(l);const p=d,g=r.stringify(p,f);y2(g)?f.value=g:f.result=g,u(l,f)});function u(l,d){l||!d?a(l):i?i(d):n(void 0,d)}}}processSync(t){let n=!1,r;return this.freeze(),ya("processSync",this.parser||this.Parser),ba("processSync",this.compiler||this.Compiler),this.process(t,o),Kc("processSync","process",n),r;function o(i,a){n=!0,Fc(i),r=a}}run(t,n,r){Yc(t),this.freeze();const o=this.transformers;return!r&&typeof n=="function"&&(r=n,n=void 0),r?i(void 0,r):new Promise(i);function i(a,s){const c=Lo(n);o.run(t,c,u);function u(l,d,f){const p=d||t;l?s(l):a?a(p):r(void 0,p,f)}}}runSync(t,n){let r=!1,o;return this.run(t,n,i),Kc("runSync","run",r),o;function i(a,s){Fc(a),o=s,r=!0}}stringify(t,n){this.freeze();const r=Lo(n),o=this.compiler||this.Compiler;return ba("stringify",o),Yc(t),o(t,r)}use(t,...n){const r=this.attachers,o=this.namespace;if(xa("use",this.frozen),t!=null)if(typeof t=="function")c(t,n);else if(typeof t=="object")Array.isArray(t)?s(t):a(t);else throw new TypeError("Expected usable value, not `"+t+"`");return this;function i(u){if(typeof u=="function")c(u,[]);else if(typeof u=="object")if(Array.isArray(u)){const[l,...d]=u;c(l,d)}else a(u);else throw new TypeError("Expected usable value, not `"+u+"`")}function a(u){if(!("plugins"in u)&&!("settings"in u))throw new Error("Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither");s(u.plugins),u.settings&&(o.settings=ha(!0,o.settings,u.settings))}function s(u){let l=-1;if(u!=null)if(Array.isArray(u))for(;++l<u.length;){const d=u[l];i(d)}else throw new TypeError("Expected a list of plugins, not `"+u+"`")}function c(u,l){let d=-1,f=-1;for(;++d<r.length;)if(r[d][0]===u){f=d;break}if(f===-1)r.push([u,...l]);else if(l.length>0){let[p,...g]=l;const m=r[f][1];Qa(m)&&Qa(p)&&(p=ha(!0,m,p)),r[f]=[u,p,...g]}}}}const m2=new al().freeze();function ya(e,t){if(typeof t!="function")throw new TypeError("Cannot `"+e+"` without `parser`")}function ba(e,t){if(typeof t!="function")throw new TypeError("Cannot `"+e+"` without `compiler`")}function xa(e,t){if(t)throw new Error("Cannot call `"+e+"` on a frozen processor.\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.")}function Yc(e){if(!Qa(e)||typeof e.type!="string")throw new TypeError("Expected node, got `"+e+"`")}function Kc(e,t,n){if(!n)throw new Error("`"+e+"` finished async. Use `"+t+"` instead")}function Lo(e){return v2(e)?e:new Gf(e)}function v2(e){return!!(e&&typeof e=="object"&&"message"in e&&"messages"in e)}function y2(e){return typeof e=="string"||b2(e)}function b2(e){return!!(e&&typeof e=="object"&&"byteLength"in e&&"byteOffset"in e)}const x2="https://github.com/remarkjs/react-markdown/blob/main/changelog.md",Zc=[],Jc={allowDangerousHtml:!0},w2=/^(https?|ircs?|mailto|xmpp)$/i,S2=[{from:"astPlugins",id:"remove-buggy-html-in-markdown-parser"},{from:"allowDangerousHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"allowNode",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowElement"},{from:"allowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowedElements"},{from:"className",id:"remove-classname"},{from:"disallowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"disallowedElements"},{from:"escapeHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"includeElementIndex",id:"#remove-includeelementindex"},{from:"includeNodeIndex",id:"change-includenodeindex-to-includeelementindex"},{from:"linkTarget",id:"remove-linktarget"},{from:"plugins",id:"change-plugins-to-remarkplugins",to:"remarkPlugins"},{from:"rawSourcePos",id:"#remove-rawsourcepos"},{from:"renderers",id:"change-renderers-to-components",to:"components"},{from:"source",id:"change-source-to-children",to:"children"},{from:"sourcePos",id:"#remove-sourcepos"},{from:"transformImageUri",id:"#add-urltransform",to:"urlTransform"},{from:"transformLinkUri",id:"#add-urltransform",to:"urlTransform"}];function C2(e){const t=E2(e),n=I2(e);return D2(t.runSync(t.parse(n),n),e)}function E2(e){const t=e.rehypePlugins||Zc,n=e.remarkPlugins||Zc,r=e.remarkRehypeOptions?{...e.remarkRehypeOptions,...Jc}:Jc;return m2().use(pS).use(n).use(QS,r).use(t)}function I2(e){const t=e.children||"",n=new Gf;return typeof t=="string"&&(n.value=t),n}function D2(e,t){const n=t.allowedElements,r=t.allowElement,o=t.components,i=t.disallowedElements,a=t.skipHtml,s=t.unwrapDisallowed,c=t.urlTransform||P2;for(const l of S2)Object.hasOwn(t,l.from)&&Ag("Unexpected `"+l.from+"` prop, "+(l.to?"use `"+l.to+"` instead":"remove it")+" (see <"+x2+"#"+l.id+"> for more info)");return ad(e,u),Zw(e,{Fragment:h.Fragment,components:o,ignoreInvalidStyle:!0,jsx:h.jsx,jsxs:h.jsxs,passKeys:!0,passNode:!0});function u(l,d,f){if(l.type==="raw"&&f&&typeof d=="number")return a?f.children.splice(d,1):f.children[d]={type:"text",value:l.value},d;if(l.type==="element"){let p;for(p in pa)if(Object.hasOwn(pa,p)&&Object.hasOwn(l.properties,p)){const g=l.properties[p],m=pa[p];(m===null||m.includes(l.tagName))&&(l.properties[p]=c(String(g||""),p,l))}}if(l.type==="element"){let p=n?!n.includes(l.tagName):i?i.includes(l.tagName):!1;if(!p&&r&&typeof d=="number"&&(p=!r(l,d,f)),p&&f&&typeof d=="number")return s&&l.children?f.children.splice(d,1,...l.children):f.children.splice(d,1),d}}}function P2(e){const t=e.indexOf(":"),n=e.indexOf("?"),r=e.indexOf("#"),o=e.indexOf("/");return t===-1||o!==-1&&t>o||n!==-1&&t>n||r!==-1&&t>r||w2.test(e.slice(0,t))?e:""}const wa=od(_n)`
  padding: 15px;
  margin-top: 20px;
  width: 100%;
  border-radius: 8px;
  background-color: white;
  border: 1px solid #d9d9d9;
`,A2=z.forwardRef(function(t,n){return h.jsx(hg,{direction:"left",ref:n,...t,timeout:{enter:1e3,exit:500},easing:{enter:"cubic-bezier(0.4, 0, 0.2, 1)",exit:"cubic-bezier(0.4, 0, 0.2, 1)"}})}),Sa=[{id:1,name:"Template 1",content:"This is the template for sending email to candidates"},{id:2,name:"Template 2",content:"This is the template for sending email to candidates"}];function Xf({open:e,onClose:t,step:n}){var y,E;console.log("sdsadtep",n);const r=["name","role","salary","location"],[o,i]=v.useState("Preview"),[a,s]=v.useState(!1),[c,u]=v.useState(""),[l,d]=v.useState(""),[f,p]=v.useState(),[g,m]=v.useState((n==null?void 0:n.delay)||0),x=()=>{navigator.clipboard.writeText("{{ name }}").then(()=>{s(!0),setTimeout(()=>s(!1),2e3)})},w=(S,b)=>{b!==null&&i(b)};return console.log("templateContent",c),h.jsx("div",{children:h.jsxs(gg,{open:e,onClose:()=>t(),TransitionComponent:A2,keepMounted:!0,fullScreen:!0,PaperProps:{sx:{ml:"auto",mt:0,mr:0,width:"1200px",height:"100vh",overflowY:"hidden",borderRadius:0}},BackdropProps:{sx:{backgroundColor:"rgba(0, 0, 0, 0.5)"}},children:[h.jsx(mg,{children:h.jsxs(_l,{display:"flex",justifyContent:"space-between",children:[h.jsx(vg,{variant:"h4",className:"ml-8",children:"Message Template Editor"}),h.jsx(Se,{icon:"material-symbols:close-rounded",width:"24",height:"24",style:{cursor:"pointer"},onClick:()=>t()})]})}),h.jsx(yg,{sx:{width:"100%",overflowY:"auto","&::-webkit-scrollbar":{width:"5px",height:"5px"},"&::-webkit-scrollbar-thumb":{backgroundColor:"#d9d9d9",borderRadius:"4px"},"&::-webkit-scrollbar-thumb:hover":{backgroundColor:"#cccccc"},"&::-webkit-scrollbar-track":{backgroundColor:"#f0f0f0"}},children:h.jsxs("div",{style:{display:"flex"},children:[h.jsxs("div",{style:{width:"55%"},children:[((y=n==null?void 0:n.title)==null?void 0:y.includes("Email"))&&h.jsxs(h.Fragment,{children:[h.jsx("p",{style:{fontSize:"16px",color:"#424242",fontFamily:"Poppins",fontWeight:500,margin:0},children:"Subject"}),h.jsx(wa,{placeholder:"",variant:"borderless",style:{width:"100%",marginTop:"5px"},onChange:S=>d(S.target.value),value:l})]}),((E=n==null?void 0:n.title)==null?void 0:E.includes("Follow up"))&&h.jsxs(h.Fragment,{children:[h.jsx("p",{style:{fontSize:"16px",color:"#424242",fontFamily:"Poppins",fontWeight:500,marginTop:"15px"},children:"Select Date & Time"}),h.jsx(bg,{getPopupContainer:S=>S.parentNode,id:"date_picker",showTime:!0,readOnly:!0,onChange:(S,b)=>{},style:{width:"100%",borderRadius:"10px",backgroundColor:"white",border:"1px solid #ccc",marginTop:0,padding:"13px 10px",marginBottom:"10px"},className:"ant-picker-custom"})]}),h.jsx("p",{style:{fontSize:"16px",color:"#424242",fontFamily:"Poppins",fontWeight:500},children:"Variables"}),h.jsx("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",flexDirection:"row"},children:r==null?void 0:r.map((S,b)=>h.jsx("p",{style:{fontSize:"14px",color:"#1a84de",fontFamily:"Poppins",fontWeight:400,backgroundColor:"#edeff8",border:"2px solid #1a84de",borderRadius:"4px",padding:"4px 15px",cursor:"pointer"},onClick:x,children:`{${S}}`}))}),a&&h.jsx("p",{style:{fontSize:"14px",color:"green",fontFamily:"Poppins",fontWeight:400,margin:0},children:"Copied to clipboard!"}),h.jsx("p",{style:{fontSize:"16px",color:"#424242",fontFamily:"Poppins",fontWeight:500,marginTop:"20px"},children:"Delay Settings"}),h.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"10px",marginBottom:"15px"},children:[h.jsx("span",{style:{fontSize:"14px",color:"#424242",fontFamily:"Poppins",fontWeight:400},children:"Wait"}),h.jsx(wa,{type:"number",placeholder:"0",value:g,onChange:S=>m(parseInt(S.target.value)||0),style:{width:"100px",marginTop:0},min:0}),h.jsx("span",{style:{fontSize:"14px",color:"#424242",fontFamily:"Poppins",fontWeight:400},children:"minutes before sending this message"})]}),h.jsx("p",{style:{fontSize:"16px",color:"#424242",fontFamily:"Poppins",fontWeight:500},children:"Message Content"}),h.jsx(Ng,{initialContent:c,onContentChange:S=>u(S),onHtmlChange:S=>p(S),height:"500px"}),h.jsxs("div",{style:{display:"flex",marginTop:"20px"},children:[h.jsx(he,{type:"primary",icon:h.jsx(Se,{icon:"material-symbols:close-rounded",width:"18",height:"18"}),style:{borderRadius:"10px",height:"45px",width:"200px",fontSize:"16px",backgroundColor:"#ff3b30",fontWeight:500},onClick:()=>t(),children:"Close without saving"}),h.jsx(he,{type:"primary",icon:h.jsx(Se,{icon:"mdi:tick",width:"18",height:"18"}),style:{borderRadius:"10px",height:"45px",width:"150px",fontSize:"16px",marginLeft:"15px",backgroundColor:"#007aff",fontWeight:500},onClick:()=>t({delay:g,subject:l,templateContent:c}),children:"Save & Close"})]})]}),h.jsx("div",{style:{width:"3%"}}),h.jsxs("div",{style:{width:"42%"},children:[h.jsx("div",{style:{display:"flex",justifyContent:"center"},children:h.jsxs(Ug,{value:o,exclusive:!0,onChange:w,sx:{width:"85%",color:"#fff",margin:"auto","& .MuiToggleButtonGroup-grouped":{flex:1,borderRadius:0},"& .Mui-selected":{backgroundColor:"#1E88E5 !important",color:"#fff"},"& .MuiToggleButton-root":{backgroundColor:"#2E3B55",color:"#fff","&:hover":{backgroundColor:"#3A475E"}}},children:[h.jsx(zl,{value:"Preview",children:"Preview"}),h.jsx(zl,{value:"Template Gallery",children:"Template Gallery"})]})}),o==="Template Gallery"&&h.jsxs("div",{style:{padding:"10px 30px"},children:[h.jsx("p",{style:{fontSize:"16px",color:"#424242",fontFamily:"Poppins",fontWeight:400},children:"Templates Gallery"}),h.jsx("p",{style:{fontSize:"14px",color:"#424242",fontFamily:"Poppins",fontWeight:400},children:"Use template you have already created , or create new"}),h.jsx(he,{type:"primary",style:{borderRadius:"10px",height:"50px",width:"100%",fontSize:"16px",backgroundColor:"#1A84DE",fontWeight:500},onClick:()=>{},children:"Save Template to Gallery"}),h.jsx(wa,{placeholder:"Search templates",value:null,variant:"borderless",suffix:h.jsx(Se,{icon:"ant-design:search-outlined",width:"18",height:"18"})}),Sa==null?void 0:Sa.map(S=>h.jsxs("div",{style:{display:"flex",flexDirection:"column",backgroundColor:"#f2f5fa",padding:"5px 10px",margin:"15px 0",cursor:"pointer"},onClick:()=>{u(S==null?void 0:S.content)},children:[h.jsx("p",{style:{fontSize:"16px",color:"#424242",fontFamily:"Poppins",fontWeight:400},children:S==null?void 0:S.name}),h.jsx("div",{style:{fontSize:"14px",color:"#424242",fontFamily:"Poppins",fontWeight:400,margin:0},children:S==null?void 0:S.content})]}))]}),o==="Preview"&&h.jsxs("div",{style:{padding:"10px 30px"},children:[h.jsx("p",{style:{fontSize:"16px",color:"#424242",fontFamily:"Poppins",fontWeight:400},children:"Message Preview"}),h.jsx(_l,{sx:{padding:"10px",border:"1px solid #ccc",borderRadius:"4px",backgroundColor:"#f2f5fa",width:"100%",height:"455px",overflowY:"scroll","&::-webkit-scrollbar":{width:"5px",height:"5px"},"&::-webkit-scrollbar-thumb":{backgroundColor:"#d9d9d9",borderRadius:"4px"},"&::-webkit-scrollbar-thumb:hover":{backgroundColor:"#cccccc"},"&::-webkit-scrollbar-track":{backgroundColor:"#f0f0f0"}},children:h.jsx(C2,{children:c})})]})]})]})})]})})}const Ke=[];for(let e=0;e<256;++e)Ke.push((e+256).toString(16).slice(1));function N2(e,t=0){return(Ke[e[t+0]]+Ke[e[t+1]]+Ke[e[t+2]]+Ke[e[t+3]]+"-"+Ke[e[t+4]]+Ke[e[t+5]]+"-"+Ke[e[t+6]]+Ke[e[t+7]]+"-"+Ke[e[t+8]]+Ke[e[t+9]]+"-"+Ke[e[t+10]]+Ke[e[t+11]]+Ke[e[t+12]]+Ke[e[t+13]]+Ke[e[t+14]]+Ke[e[t+15]]).toLowerCase()}let Ca;const O2=new Uint8Array(16);function R2(){if(!Ca){if(typeof crypto>"u"||!crypto.getRandomValues)throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");Ca=crypto.getRandomValues.bind(crypto)}return Ca(O2)}const T2=typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto),Qc={randomUUID:T2};function Ea(e,t,n){var o;if(Qc.randomUUID&&!t&&!e)return Qc.randomUUID();e=e||{};const r=e.random??((o=e.rng)==null?void 0:o.call(e))??R2();if(r.length<16)throw new Error("Random bytes length must be >= 16");return r[6]=r[6]&15|64,r[8]=r[8]&63|128,N2(r)}const j2=["st","nd","rd","th"],M2=od.div`
  width: 50%;
  padding-right: 16px;
  margin-right: auto;
  margin-left: auto;

  &::-webkit-scrollbar {
    width: 5px;
    height: 5px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #d9d9d9;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: #cccccc;
  }

  &::-webkit-scrollbar-track {
    background-color: #f0f0f0;
  }
`;function k2(e){const[t,n]=v.useState([]),[r,o]=v.useState(null),[i,a]=v.useState(null),[s,c]=v.useState(),u=O=>o(O.currentTarget),[l,d]=v.useState(!1),f=O=>{i||a(O.currentTarget)},[p,g]=v.useState(""),m=()=>a(null),x=()=>o(null),[w,y]=v.useState(0),{roleId:E,roleService:S}=ao(),b=O=>{const N=j2[(O-1)%10]||"th";return`${O}${N}`},I=O=>O.map((N,_)=>({...N,alias:b(_+1)})),P=(O,N=null)=>{n(N?t.map(_=>_.id===N&&_.type==="parallel"?{..._,steps:_.steps.filter(W=>W.id!==O)}:_):t.filter(_=>_.id!==O))},A=O=>({Email:"mdi:email",WhatsApp:"logos:whatsapp-icon",SMS:"material-symbols:sms","Connection Request":"flowbite:linkedin-solid",Call:"mdi:phone",InMail:"flowbite:linkedin-solid"})[O]||"mdi:email",D=O=>({Email:[{id:1,title:"Email Sent",value:0,icon:"mdi:tick",color:"green"},{id:2,title:"Email Failed",value:0,icon:"mdi:close",color:"red"},{id:3,title:"Auto Reply",value:0,icon:"mdi:auto-mode",color:"blue"},{id:4,title:"Potential Reply",value:0,icon:"tabler:message-up",color:"green"}],WhatsApp:[{id:1,title:"WhatsApp Sent",value:0,icon:"mdi:tick",color:"green"},{id:2,title:"WhatsApp Replied",value:0,icon:"hugeicons:message-02",color:"blue"},{id:3,title:"WhatsApp Failed",value:0,icon:"mdi:close",color:"red"}],SMS:[{id:1,title:"SMS Sent",value:0,icon:"mdi:tick",color:"green"},{id:2,title:"SMS Replied",value:0,icon:"hugeicons:message-02",color:"blue"},{id:3,title:"SMS Failed",value:0,icon:"bitcoin-icons:cross-filled",color:"red"}],"Connection Request":[{id:1,title:"Connection Request Sent",value:0,icon:"mdi:tick",color:"green"},{id:2,title:"Connection Request Replied",value:0,icon:"codicon:verified-filled",color:"#E5B137"},{id:3,title:"Connection Request Failed",value:0,icon:"lucide:refresh-cw-off",color:"red"}],Call:[{id:1,title:"Called",value:0,icon:"mdi:tick",color:"green"},{id:2,title:"Call Replied",value:0,icon:"solar:outgoing-call-bold",color:"#E5B137"},{id:3,title:"Call Failed",value:0,icon:"subway:call-3",color:"red"}],InMail:[{id:1,title:"InMail Sent",value:0,icon:"mdi:tick",color:"green"},{id:2,title:"InMail Replied",value:0,icon:"codicon:verified-filled",color:"#E5B137"},{id:3,title:"InMail Failed",value:0,icon:"lucide:refresh-cw-off",color:"red"}]})[O]||[],k=O=>{const N={id:Ea(),alias:b(t.length+1),total_candidates:0,title:O,is_active:!1,icon:A(O),stats:D(O)};n([...t,N]),x()},L=()=>{n([...t,{id:Ea(),type:"parallel",steps:[]}])},B=(O,N)=>{n(t.map(_=>_.id===O&&_.type==="parallel"?{..._,steps:[..._.steps,{id:Ea(),alias:b(_.steps.length+1),total_candidates:0,title:N,is_active:!1,icon:A(N),stats:D(N)}]}:_))},j=O=>{if(!O.destination)return;const N=Array.from(t),[_]=N.splice(O.source.index,1);N.splice(O.destination.index,0,_),n(I(N))},C=v.useCallback(()=>{try{Fr({},Pe.getRoleCandidateCount.replace(":roleId",E),O=>{y(O)},O=>{})}catch{}},[E]);return v.useEffect(()=>{C()},[C]),v.useEffect(()=>{p==="Empty Sequence"?n([]):p==="Default Sequence"&&n(Hv)},[p]),console.log("stepdsdsf",t),h.jsxs("div",{children:[h.jsx("div",{style:{display:"flex",height:"100vh"},children:h.jsx(M2,{children:h.jsx(Et,{gutter:32,children:h.jsxs(be,{span:24,className:"gutter-row",style:{display:"flex",flexDirection:"column",justifyContent:"center"},children:[h.jsx("div",{style:{backgroundColor:"white",height:"60px",borderRadius:"4px",marginBottom:"20px"},children:h.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"10px 20px"},children:[h.jsxs("p",{style:{fontSize:"16px",color:"#424242",fontFamily:"Poppins",fontWeight:500},children:[w!=null&&w.linkedin_count?w==null?void 0:w.linkedin_count:" + "+(w==null?void 0:w.cv_count)?w==null?void 0:w.cv_count:" = "+(w==null?void 0:w.total_count)," ",w!=null&&w.linkedin_count||w!=null&&w.cv_count?"Candidates":""]}),h.jsx(he,{type:"primary",icon:h.jsx(Se,{icon:"material-symbols:add-rounded",width:"18",height:"18"}),style:{borderRadius:"4px",height:"38px",width:"165px",fontSize:"16px",marginLeft:"10px",backgroundColor:"#1A84DE",fontFamily:"Poppins",fontWeight:500},onClick:f,children:"Add Sequence"})]})}),p==="Empty Sequence"&&h.jsxs("div",{style:{display:"flex",justifyContent:"center",flexDirection:"column",alignItems:"center"},children:[h.jsx(On,{type:"vertical",variant:"dashed",style:{borderColor:"#CED0DA",height:"30px"}}),h.jsx("p",{style:{backgroundColor:"#dadaf9",padding:"5px",width:"34px",height:"34px",display:"inline-flex",justifyContent:"center",alignItems:"center",borderRadius:"4px",cursor:"pointer"},onClick:u,children:h.jsx(Se,{icon:"material-symbols:add-rounded",width:"24",height:"24"})}),h.jsx(On,{type:"vertical",variant:"dashed",style:{borderColor:"#CED0DA",height:"30px"}})]}),p&&h.jsxs(h.Fragment,{children:[h.jsxs("div",{style:{marginBottom:16},children:[h.jsx(he,{type:"primary",onClick:L,style:{marginRight:8},children:"+ Add Parallel Group"}),h.jsx(he,{type:"primary",onClick:()=>k("Email"),children:"+ Add Sequential Step"})]}),h.jsx(g1,{onDragEnd:j,children:h.jsx(Df,{droppableId:"steps",children:O=>h.jsxs("div",{...O.droppableProps,ref:O.innerRef,children:[t==null?void 0:t.map((N,_)=>N.type==="parallel"?h.jsxs("div",{style:{border:"2px dashed #1A84DE",margin:"16px 0",padding:"12px",borderRadius:8},children:[h.jsx("div",{style:{fontWeight:600,color:"#1A84DE",marginBottom:8},children:"Parallel Group"}),h.jsx(he,{size:"small",onClick:()=>B(N.id,"Email"),children:"+ Email"}),h.jsx(he,{size:"small",onClick:()=>B(N.id,"WhatsApp"),style:{marginLeft:8},children:"+ WhatsApp"}),h.jsx("div",{style:{display:"flex",gap:16,marginTop:12},children:N.steps.map(W=>h.jsx(eu,{step:W,onDelete:()=>P(W.id,N.id),onAddClick:u,onSetActive:()=>{},onEditClick:()=>{}},W.id))}),h.jsx(he,{danger:!0,size:"small",style:{marginTop:8},onClick:()=>P(N.id),children:"Delete Parallel Group"})]},N.id):h.jsx("div",{style:{margin:"16px 0"},children:h.jsx(eu,{step:N,onDelete:()=>P(N.id),onAddClick:u,onSetActive:()=>{},onEditClick:()=>{}})},N.id)),O.placeholder]})})})]})]})})})}),l&&h.jsx(Xf,{open:l,onClose:()=>d(!1),step:s}),h.jsx(Wv,{open:!!r,anchorEl:r,handlePopoverClose:x,handleAddStep:O=>{!(t==null?void 0:t.find(_=>_.title==="Outreach Email"))&&O==="Follow up Email"?addStep("Outreach Email"):addStep(O)}}),h.jsx(ow,{open:!!i,anchorEl:i,handlePopoverClose:m,handleSelect:O=>{console.log("typdsdsae",O),g(O),a(null)}})]})}function $2(e){const[t,n]=v.useState(),{roleId:r}=ao(),o=()=>{try{const i=Pe.getRoleById.replace(":id",r);Fr({},i,a=>{n(a)},a=>{var s;console.log(a),Ee.error(((s=a==null?void 0:a.response)==null?void 0:s.message)||"Failed to get role details. Try refreshing the page!")})}catch{Ee.error("Failed to get role details. Try refreshing the page!")}};return v.useEffect(()=>{o()},[]),h.jsxs(h.Fragment,{children:[h.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[h.jsx(_n,{value:t==null?void 0:t.title,bordered:!0,style:{width:"85%",borderRadius:"6px",backgroundColor:"transparent",height:"50px"},readOnly:!0}),h.jsxs(nt,{size:"small",children:[h.jsx("p",{style:{fontSize:"16px",color:"#424242",fontFamily:"Poppins",fontWeight:400},children:"Created"}),h.jsx(Se,{icon:"solar:calendar-broken",width:"20",height:"20"}),h.jsx("p",{style:{fontSize:"16px",color:"#424242",fontFamily:"Poppins",fontWeight:400},children:"05/10/2024"})]})]}),h.jsx(Og,{roleDetails:t})]})}function eu({step:e,onDelete:t,onAddClick:n,onSetActive:r,onEditClick:o}){return h.jsxs("div",{onClick:()=>{r(e)},children:[h.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",marginTop:"5px"},children:h.jsxs("p",{style:{fontSize:"12px",color:"#424242",fontFamily:"Poppins",fontWeight:400},children:[e==null?void 0:e.alias," Action"]})}),h.jsxs("div",{style:{backgroundColor:"white",height:"auto",marginBottom:"20px"},children:[h.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"0px 20px"},children:[h.jsxs("p",{style:{fontSize:"16px",color:"#424242",fontFamily:"Poppins",fontWeight:400},children:["Queue : ",e==null?void 0:e.total_candidates]}),h.jsxs("p",{style:{fontSize:"16px",color:"#424242",fontFamily:"Poppins",fontWeight:400,display:"flex",alignItems:"center",gap:"10px",cursor:"pointer"},onClick:()=>t(e==null?void 0:e.id),children:[h.jsx(Se,{icon:"material-symbols:delete-outline-rounded",width:"18",height:"18"})," Delete"]})]}),h.jsx(On,{style:{marginTop:0,borderColor:"#CED0DA",borderWidth:"2px",marginBottom:"0px"}}),h.jsxs("div",{style:{display:"flex",padding:"0px 20px",gap:"20px",alignItems:"center",backgroundColor:(e==null?void 0:e.is_active)===!0?"#1A84DE":"white",display:"flex",justifyContent:"space-between"},children:[h.jsxs("div",{style:{display:"flex",gap:"20px",alignItems:"center"},children:[h.jsx("p",{style:{backgroundColor:(e==null?void 0:e.is_active)===!0?"white":"#1A84DE",padding:"5px",width:"45px",height:"45px",display:"inline-flex",justifyContent:"center",alignItems:"center",borderRadius:"8px",color:(e==null?void 0:e.is_active)===!0?"#1A84DE":"white"},children:h.jsx(Se,{icon:e==null?void 0:e.icon,width:"24",height:"24"})}),h.jsx("p",{style:{fontSize:"18px",color:(e==null?void 0:e.is_active)===!0?"white":"#1A84DE",fontFamily:"Poppins",fontWeight:500},children:e==null?void 0:e.title})]}),h.jsxs("p",{style:{fontSize:"16px",color:"white",fontFamily:"Poppins",fontWeight:500,display:"flex",alignItems:"center",gap:"10px",cursor:"pointer",backgroundColor:"#1a84de",padding:"9px 20px",borderRadius:"2px"},onClick:()=>o(e),children:[h.jsx(Se,{icon:"mdi:edit-outline",width:"18",height:"18"})," Edit"]})]}),h.jsx(On,{style:{marginTop:0,borderColor:"#CED0DA",borderWidth:"2px",marginBottom:"10px"}}),h.jsx("div",{style:{display:"flex",padding:"0px 20px",gap:"20px",alignItems:"center",justifyContent:"space-between"},children:e==null?void 0:e.stats.map(i=>h.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"10px"},children:[h.jsx("p",{children:h.jsx(Se,{icon:i==null?void 0:i.icon,width:"24",height:"24",style:{color:i==null?void 0:i.color}})}),h.jsx("p",{style:{fontSize:"17px",color:"#424242",fontFamily:"Poppins",fontWeight:500},children:i.value})]},i.id))})]}),h.jsxs("div",{style:{display:"flex",justifyContent:"center",flexDirection:"column",alignItems:"center"},children:[h.jsx(On,{type:"vertical",variant:"dashed",style:{borderColor:"#CED0DA",height:"30px"}}),h.jsx("p",{style:{backgroundColor:"#dadaf9",padding:"5px",width:"34px",height:"34px",display:"inline-flex",justifyContent:"center",alignItems:"center",borderRadius:"4px",cursor:"pointer"},onClick:i=>n(i),children:h.jsx(Se,{icon:"material-symbols:add-rounded",width:"24",height:"24"})}),h.jsx(On,{type:"vertical",variant:"dashed",style:{borderColor:"#CED0DA",height:"30px"}})]})]})}const Gt=e=>new Promise((t,n)=>{Fr(null,e,t,n)}),ln=(e,t)=>new Promise((n,r)=>{xg(t,e,n,r)}),Yf=(e,t)=>new Promise((n,r)=>{wg(t,e,n,r)}),Kf=(e,t=null)=>new Promise((n,r)=>{Sg(t,e,()=>n(),r)}),sl=async e=>{console.log("Creating sequence with URL:",Pe.createSequence),console.log("Sequence data:",e);try{const t=await ln(Pe.createSequence,e);return console.log("Create sequence result:",t),t}catch(t){throw console.error("Error in createSequence:",t),t}},ll=async()=>{try{return await Gt(Pe.getAllSequences)||[]}catch(e){return console.error("Error fetching sequences:",e),[]}},Zf=async e=>await Gt(Pe.getSequenceById.replace(":id",e)),Jf=async(e,t)=>await Yf(Pe.updateSequence.replace(":id",e),t),Qf=async e=>await Kf(Pe.deleteSequence.replace(":id",e)),cl=async(e,t)=>await ln(Pe.startSequence.replace(":id",e),{candidateIds:t}),ep=async e=>await ln(`/sequence/${e}/start-with-test-candidates`),tp=async(e,t,n=10)=>await ln(`/sequence/${e}/start-with-role-candidates`,{roleId:t,limit:n}),_2=async(e,t)=>await Gt({roleId:e,userId:t}),ul=async e=>await Gt(Pe.getSequenceWithSteps.replace(":id",e)),ki=async e=>await Gt(Pe.getSequenceStats.replace(":id",e)),dl=async e=>{console.log("Creating sequence step with URL:",Pe.createSequenceStep),console.log("Step data:",e);try{const t=await ln(Pe.createSequenceStep,e);return console.log("Create sequence step result:",t),t}catch(t){throw console.error("Error in createSequenceStep:",t),t}},np=async()=>await Gt(Pe.getAllSequenceSteps),rp=async e=>await Gt(Pe.getSequenceStepById.replace(":id",e)),op=async(e,t)=>await Yf(Pe.updateSequenceStep.replace(":id",e),t),ip=async e=>await Kf(Pe.deleteSequenceStep.replace(":id",e)),$i=async()=>{try{return await Gt(Pe.getQueueStats)||{}}catch(e){return console.error("Error fetching queue stats:",e),{}}},ap=async e=>await Gt(Pe.getQueueStatsByName.replace(":queueName",e)),fl=async e=>await ln(Pe.pauseQueue.replace(":queueName",e)),pl=async e=>await ln(Pe.resumeQueue.replace(":queueName",e)),hl=async(e,t)=>await ln(Pe.addTestJob.replace(":queueName",e),t),gl=async(e,t,n)=>await ln(Pe.testWebhook.replace(":candidateId",e).replace(":stepId",t),n),sp=async(e,t)=>await Gt(Pe.getCandidateSequenceStatus.replace(":candidateId",e).replace(":sequenceId",t)),ml=async(e,t=100)=>await Gt(`${Pe.getStatusByType.replace(":status",e)}?limit=${t}`),lp=async(e,t,n,r)=>{try{const o=B2(t,n);if(!r)throw new Error("User ID is required for sequence creation");const i={name:`Sequence for Role ${e}`,description:"Auto-generated from visual flow",status:"ACTIVE",userId:r,roleId:e},a=await sl(i),s=[];for(const c of o){const u={...c,roleSequenceId:a.id},l=await dl(u);s.push(l)}return{sequence:a,steps:s}}catch(o){throw console.error("Error saving sequence:",o),o}},B2=(e,t)=>{const n=[],r=["EMAIL","EMAIL_STEP","SMS","SMS_STEP","WHATSAPP","WHATSAPP_STEP","CALL","CALL_STEP","LI_CONNECTION","LI_INMAIL","LINKEDIN","LINKEDIN_STEP"],o=e.filter(s=>{const c=s.data.label.toUpperCase().replace(/\s+/g,"_");return r.includes(c)}),i={};o.forEach(s=>{const c=Math.floor(s.position.y/150);i[c]||(i[c]=[]),i[c].push(s)});const a=new Set;return Object.keys(i).forEach((s,c)=>{i[s].forEach(l=>{const d=l.data.label.toUpperCase().replace(/\s+/g,"_"),f=`${d}_${c}`;if(a.has(f)){console.warn(`Duplicate step detected: ${f}, skipping`);return}const g={EMAIL:"EMAIL",EMAIL_STEP:"EMAIL",SMS:"SMS",SMS_STEP:"SMS",WHATSAPP:"WHATSAPP",WHATSAPP_STEP:"WHATSAPP",CALL:"CALL",CALL_STEP:"CALL",LI_CONNECTION:"LINKEDIN",LI_INMAIL:"LINKEDIN",LINKEDIN:"LINKEDIN",LINKEDIN_STEP:"LINKEDIN"}[d];if(!g){console.warn(`Unknown medium type: ${d}, skipping step`);return}a.add(f);const m={name:l.data.label,order:c,type:"OUTREACH",medium:g,templateId:1,roleSequenceId:null};n.push(m)})}),n},L2={createSequence:sl,getAllSequences:ll,getSequenceById:Zf,updateSequence:Jf,deleteSequence:Qf,startSequence:cl,getSequenceWithSteps:ul,getSequenceStats:ki,createSequenceStep:dl,getAllSequenceSteps:np,getSequenceStepById:rp,updateSequenceStep:op,deleteSequenceStep:ip,getQueueStats:$i,getQueueStatsByName:ap,pauseQueue:fl,resumeQueue:pl,addTestJob:hl,sendTestWebhook:gl,getCandidateSequenceStatus:sp,getStatusByType:ml,saveSequenceFromFlow:lp},z2=Object.freeze(Object.defineProperty({__proto__:null,addTestJob:hl,createSequence:sl,createSequenceStep:dl,default:L2,deleteSequence:Qf,deleteSequenceStep:ip,getAllSequenceSteps:np,getAllSequences:ll,getCandidateSequenceStatus:sp,getQueueStats:$i,getQueueStatsByName:ap,getRoleCandidates:_2,getSequenceById:Zf,getSequenceStats:ki,getSequenceStepById:rp,getSequenceWithSteps:ul,getStatusByType:ml,pauseQueue:fl,resumeQueue:pl,saveSequenceFromFlow:lp,sendTestWebhook:gl,startSequence:cl,startSequenceWithRoleCandidates:tp,startSequenceWithTestCandidates:ep,updateSequence:Jf,updateSequenceStep:op},Symbol.toStringTag,{value:"Module"})),{Option:Dr}=wn,{confirm:F2}=Hr,H2=()=>{const[e,t]=v.useState({}),[n,r]=v.useState(!1),[o,i]=v.useState(null),[a,s]=v.useState(!0),[c,u]=v.useState(!1),[l]=Ht.useForm();v.useEffect(()=>{if(d(),a){const b=setInterval(d,3e3);i(b)}return()=>{o&&clearInterval(o)}},[a]);const d=async()=>{try{r(!0);const b=await $i();let I={};Array.isArray(b)?I=b.reduce((P,A)=>(A&&A.name&&(P[A.name]=A),P),{}):b&&typeof b=="object"&&(I=b),t(I)}catch(b){console.error("Error loading queue stats:",b),Ee.error("Failed to load queue statistics"),t({})}finally{r(!1)}},f=async b=>{F2({title:`Pause ${b}?`,icon:h.jsx(ea,{}),content:"This will pause all job processing for this queue.",onOk:async()=>{try{await fl(b),Ee.success(`${b} paused successfully`),d()}catch{Ee.error(`Failed to pause ${b}`)}}})},p=async b=>{try{await pl(b),Ee.success(`${b} resumed successfully`),d()}catch{Ee.error(`Failed to resume ${b}`)}},g=async b=>{try{await hl(b.queueName,{candidateId:b.candidateId,stepId:b.stepId}),Ee.success("Test job added successfully"),u(!1),l.resetFields(),d()}catch{Ee.error("Failed to add test job")}},m=b=>b?(b.active||0)>0?"processing":(b.waiting||0)>0?"waiting":(b.failed||0)>0?"error":"idle":"idle",x=b=>{switch(b){case"processing":return"#1890ff";case"waiting":return"#faad14";case"error":return"#ff4d4f";case"idle":return"#52c41a";default:return"#d9d9d9"}},w=b=>{switch(b){case"processing":return h.jsx(Kn,{});case"waiting":return h.jsx(ea,{});case"error":return h.jsx(ri,{});case"idle":return h.jsx(tr,{});default:return h.jsx(Kn,{})}},y=[{title:"Queue Name",dataIndex:"name",key:"name",render:b=>h.jsx(nt,{children:h.jsx("strong",{children:b.replace("-queue","").toUpperCase()})})},{title:"Status",dataIndex:"status",key:"status",render:b=>h.jsx(nn,{color:x(b),text:b.toUpperCase()})},{title:"Active Jobs",dataIndex:"active",key:"active",render:b=>h.jsx(Jt,{color:b>0?"blue":"default",children:b})},{title:"Waiting Jobs",dataIndex:"waiting",key:"waiting",render:b=>h.jsx(Jt,{color:b>0?"orange":"default",children:b})},{title:"Completed",dataIndex:"completed",key:"completed",render:b=>h.jsx(Jt,{color:"green",children:b})},{title:"Failed",dataIndex:"failed",key:"failed",render:b=>h.jsx(Jt,{color:b>0?"red":"default",children:b})},{title:"Actions",key:"actions",render:(b,I)=>h.jsxs(nt,{children:[h.jsx(Sn,{title:"Pause Queue",children:h.jsx(he,{size:"small",icon:h.jsx(Av,{}),onClick:()=>f(I.name)})}),h.jsx(Sn,{title:"Resume Queue",children:h.jsx(he,{size:"small",icon:h.jsx(Yo,{}),onClick:()=>p(I.name)})})]})}],E=Object.entries(e||{}).map(([b,I])=>({key:b,name:b,status:m(I||{}),active:I&&I.active||0,waiting:I&&I.waiting||0,completed:I&&I.completed||0,failed:I&&I.failed||0,delayed:I&&I.delayed||0})),S=E.reduce((b,I)=>({active:b.active+I.active,waiting:b.waiting+I.waiting,completed:b.completed+I.completed,failed:b.failed+I.failed}),{active:0,waiting:0,completed:0,failed:0});return h.jsxs("div",{children:[h.jsx(Ie,{style:{marginBottom:16},children:h.jsxs(Et,{gutter:16,align:"middle",children:[h.jsx(be,{children:h.jsx("h3",{style:{margin:0},children:"Queue Management Dashboard"})}),h.jsx(be,{flex:"auto"}),h.jsx(be,{children:h.jsxs(nt,{children:[h.jsx(he,{icon:h.jsx(jn,{}),onClick:d,loading:n,children:"Refresh"}),h.jsxs(he,{type:a?"primary":"default",onClick:()=>s(!a),children:["Auto Refresh: ",a?"ON":"OFF"]}),h.jsx(he,{icon:h.jsx(hd,{}),onClick:()=>u(!0),children:"Add Test Job"})]})})]})}),h.jsxs(Et,{gutter:16,style:{marginBottom:16},children:[h.jsx(be,{span:6,children:h.jsx(Ie,{children:h.jsx(Fe,{title:"Active Jobs",value:S.active,valueStyle:{color:"#1890ff"},prefix:h.jsx(Kn,{})})})}),h.jsx(be,{span:6,children:h.jsx(Ie,{children:h.jsx(Fe,{title:"Waiting Jobs",value:S.waiting,valueStyle:{color:"#faad14"},prefix:h.jsx(ea,{})})})}),h.jsx(be,{span:6,children:h.jsx(Ie,{children:h.jsx(Fe,{title:"Completed Jobs",value:S.completed,valueStyle:{color:"#52c41a"},prefix:h.jsx(tr,{})})})}),h.jsx(be,{span:6,children:h.jsx(Ie,{children:h.jsx(Fe,{title:"Failed Jobs",value:S.failed,valueStyle:{color:"#ff4d4f"},prefix:h.jsx(ri,{})})})})]}),h.jsx(Ie,{title:"Queue Details",children:E.length===0&&!n?h.jsx(zt,{message:"No Queue Data Available",description:"Unable to connect to the queue system. Please ensure the backend is running and try refreshing.",type:"warning",showIcon:!0,style:{marginBottom:16}}):h.jsx(Ss,{columns:y,dataSource:E,pagination:!1,loading:n})}),h.jsx(Et,{gutter:16,style:{marginTop:16},children:(E||[]).map(b=>h.jsx(be,{span:8,children:h.jsxs(Ie,{title:h.jsxs(nt,{children:[w(b.status),b.name.replace("-queue","").toUpperCase()]}),extra:h.jsx(nn,{color:x(b.status),text:b.status.toUpperCase()}),children:[h.jsxs(Et,{gutter:8,children:[h.jsx(be,{span:12,children:h.jsx(Fe,{title:"Active",value:b.active})}),h.jsx(be,{span:12,children:h.jsx(Fe,{title:"Waiting",value:b.waiting})})]}),h.jsxs(Et,{gutter:8,style:{marginTop:16},children:[h.jsx(be,{span:12,children:h.jsx(Fe,{title:"Completed",value:b.completed})}),h.jsx(be,{span:12,children:h.jsx(Fe,{title:"Failed",value:b.failed})})]}),b.active+b.waiting>0&&h.jsxs("div",{style:{marginTop:16},children:[h.jsx("div",{style:{marginBottom:8},children:"Processing Progress:"}),h.jsx(Cs,{percent:Math.round(b.completed/(b.completed+b.active+b.waiting)*100),status:b.failed>0?"exception":"active"})]})]})},b.name))}),h.jsx(Hr,{title:"Add Test Job",open:c,onOk:()=>l.submit(),onCancel:()=>u(!1),children:h.jsxs(Ht,{form:l,layout:"vertical",onFinish:g,children:[h.jsx(Ht.Item,{name:"queueName",label:"Queue",rules:[{required:!0,message:"Please select a queue"}],children:h.jsxs(wn,{placeholder:"Select queue",children:[h.jsx(Dr,{value:"email-queue",children:"Email Queue"}),h.jsx(Dr,{value:"whatsapp-queue",children:"WhatsApp Queue"}),h.jsx(Dr,{value:"sms-queue",children:"SMS Queue"}),h.jsx(Dr,{value:"call-queue",children:"Call Queue"}),h.jsx(Dr,{value:"linkedin-queue",children:"LinkedIn Queue"})]})}),h.jsx(Ht.Item,{name:"candidateId",label:"Candidate ID",rules:[{required:!0,message:"Please enter candidate ID"}],children:h.jsx(_n,{type:"number",placeholder:"Enter candidate ID"})}),h.jsx(Ht.Item,{name:"stepId",label:"Step ID",rules:[{required:!0,message:"Please enter step ID"}],children:h.jsx(_n,{type:"number",placeholder:"Enter step ID"})})]})})]})},{Option:Kt}=wn,W2=({sequenceId:e})=>{const[t,n]=v.useState(null),[r,o]=v.useState([]),[i,a]=v.useState(!1),[s,c]=v.useState(null),[u,l]=v.useState(!1),[d,f]=v.useState(!1),[p]=Ht.useForm();v.useEffect(()=>{e&&(g(),m())},[e]);const g=async()=>{try{a(!0);const b=await ki(e);n(b)}catch(b){console.error("Error loading sequence stats:",b)}finally{a(!1)}},m=async()=>{try{const b=["pending","queued","sent","delivered","opened","replied","completed","failed"],I=[];for(const P of b)try{const D=(await ml(P)).filter(k=>k.sequenceId===parseInt(e));I.push(...D)}catch(A){console.error(`Error loading ${P} statuses:`,A)}o(I)}catch(b){console.error("Error loading candidate statuses:",b)}},x=async b=>{try{await gl(b.candidateId,b.stepId,{event:b.event,medium:b.medium}),Ee.success("Test webhook sent successfully"),f(!1),p.resetFields(),setTimeout(m,1e3)}catch{Ee.error("Failed to send test webhook")}},w=b=>({pending:"default",queued:"processing",sent:"blue",delivered:"cyan",opened:"geekblue",clicked:"purple",replied:"green",completed:"success",failed:"error",skipped:"warning"})[b.toLowerCase()]||"default",y=b=>({EMAIL:h.jsx($r,{}),WHATSAPP:h.jsx(Fa,{}),SMS:h.jsx(La,{}),CALL:h.jsx(za,{}),LINKEDIN:h.jsx(Ba,{})})[b==null?void 0:b.toUpperCase()]||h.jsx($r,{}),E=[{title:"Candidate",dataIndex:"candidateId",key:"candidateId",render:b=>`Candidate ${b}`},{title:"Step",dataIndex:"step",key:"step",render:b=>h.jsxs(nt,{children:[y(b==null?void 0:b.medium),(b==null?void 0:b.name)||"Unknown Step"]})},{title:"Order",dataIndex:"stepOrder",key:"stepOrder",sorter:(b,I)=>b.stepOrder-I.stepOrder},{title:"Status",dataIndex:"status",key:"status",render:b=>h.jsx(Jt,{color:w(b),children:b.toUpperCase()}),filters:[{text:"Pending",value:"PENDING"},{text:"Queued",value:"QUEUED"},{text:"Sent",value:"SENT"},{text:"Delivered",value:"DELIVERED"},{text:"Opened",value:"OPENED"},{text:"Replied",value:"REPLIED"},{text:"Completed",value:"COMPLETED"},{text:"Failed",value:"FAILED"}],onFilter:(b,I)=>I.status===b},{title:"Attempts",dataIndex:"attemptCount",key:"attemptCount",render:b=>h.jsx(nn,{count:b,showZero:!0,color:b>1?"orange":"blue"})},{title:"Last Updated",dataIndex:"updatedAt",key:"updatedAt",render:b=>new Date(b).toLocaleString(),sorter:(b,I)=>new Date(b.updatedAt)-new Date(I.updatedAt)},{title:"Actions",key:"actions",render:(b,I)=>h.jsxs(nt,{children:[h.jsx(Sn,{title:"View Details",children:h.jsx(he,{size:"small",icon:h.jsx(sd,{}),onClick:()=>{c(I),l(!0)}})}),h.jsx(Sn,{title:"Send Test Webhook",children:h.jsx(he,{size:"small",icon:h.jsx(ql,{}),onClick:()=>{var P,A;p.setFieldsValue({candidateId:I.candidateId,stepId:I.stepId,medium:((A=(P=I.step)==null?void 0:P.medium)==null?void 0:A.toLowerCase())||"email"}),f(!0)}})})]})}],S=b=>{if(!b||typeof b!="object")return 0;const I=Object.values(b).reduce((A,D)=>A+(D||0),0),P=(b.completed||0)+(b.replied||0);return I>0?Math.round(P/I*100):0};return h.jsxs("div",{children:[t?t&&t.statusBreakdown?h.jsxs(Ie,{title:"Sequence Overview",style:{marginBottom:16},children:[h.jsxs(Et,{gutter:16,children:[h.jsx(be,{span:6,children:h.jsx(Fe,{title:"Total Candidates",value:t.totalCandidates||0,prefix:h.jsx(tr,{})})}),h.jsx(be,{span:6,children:h.jsx(Fe,{title:"Total Steps",value:t.totalSteps||0,prefix:h.jsx(Kn,{})})}),h.jsx(be,{span:6,children:h.jsx(Fe,{title:"Completion Rate",value:S(t.statusBreakdown),suffix:"%",prefix:h.jsx(tr,{})})}),h.jsx(be,{span:6,children:h.jsx(Fe,{title:"Failed Steps",value:t.statusBreakdown.failed||0,valueStyle:{color:"#ff4d4f"},prefix:h.jsx(bv,{})})})]}),h.jsxs(Et,{gutter:16,style:{marginTop:16},children:[h.jsx(be,{span:12,children:h.jsx(Ie,{size:"small",title:"Status Breakdown",children:h.jsx(nt,{direction:"vertical",style:{width:"100%"},children:t.statusBreakdown&&Object.entries(t.statusBreakdown).map(([b,I])=>h.jsxs("div",{style:{display:"flex",justifyContent:"space-between"},children:[h.jsx(Jt,{color:w(b),children:b.toUpperCase()}),h.jsx("span",{children:I||0})]},b))})})}),h.jsx(be,{span:12,children:h.jsx(Ie,{size:"small",title:"Overall Progress",children:h.jsx(Cs,{type:"circle",percent:S(t.statusBreakdown),format:b=>`${b}%`})})})]})]}):h.jsx(Ie,{title:"Sequence Overview",style:{marginBottom:16},children:h.jsx("div",{style:{textAlign:"center",padding:20},children:h.jsx("p",{children:"No sequence statistics available. Please start the sequence first."})})}):h.jsx(Ie,{title:"Sequence Overview",style:{marginBottom:16},children:h.jsxs("div",{style:{textAlign:"center",padding:40},children:[h.jsx(jn,{spin:!0,style:{fontSize:24}}),h.jsx("p",{children:"Loading sequence statistics..."})]})}),h.jsx(Ie,{title:"Candidate Execution Status",extra:h.jsxs(nt,{children:[h.jsx(he,{icon:h.jsx(jn,{}),onClick:m,children:"Refresh"}),h.jsx(he,{type:"primary",icon:h.jsx(ql,{}),onClick:()=>f(!0),children:"Test Webhook"})]}),children:h.jsx(Ss,{columns:E,dataSource:r,rowKey:b=>`${b.candidateId}-${b.stepId}`,pagination:{pageSize:10},loading:i})}),h.jsx(Hr,{title:"Candidate Execution Details",open:u,onCancel:()=>l(!1),footer:null,width:800,children:s&&h.jsxs("div",{children:[h.jsxs(Ct,{bordered:!0,column:2,children:[h.jsx(Ct.Item,{label:"Candidate ID",children:s.candidateId}),h.jsx(Ct.Item,{label:"Step ID",children:s.stepId}),h.jsx(Ct.Item,{label:"Step Order",children:s.stepOrder}),h.jsx(Ct.Item,{label:"Status",children:h.jsx(Jt,{color:w(s.status),children:s.status})}),h.jsx(Ct.Item,{label:"Attempt Count",children:s.attemptCount}),h.jsx(Ct.Item,{label:"Created At",children:new Date(s.createdAt).toLocaleString()})]}),s.metadata&&h.jsx(Ie,{title:"Metadata",size:"small",style:{marginTop:16},children:h.jsx("pre",{children:JSON.stringify(s.metadata,null,2)})}),s.responseData&&h.jsx(Ie,{title:"Response Data",size:"small",style:{marginTop:16},children:h.jsx("pre",{children:s.responseData})}),s.errorMessage&&h.jsx(Ie,{title:"Error Message",size:"small",style:{marginTop:16},children:h.jsx("div",{style:{color:"#ff4d4f"},children:s.errorMessage})})]})}),h.jsx(Hr,{title:"Send Test Webhook",open:d,onOk:()=>p.submit(),onCancel:()=>f(!1),children:h.jsxs(Ht,{form:p,layout:"vertical",onFinish:x,children:[h.jsx(Ht.Item,{name:"candidateId",label:"Candidate ID",rules:[{required:!0,message:"Please enter candidate ID"}],children:h.jsx(_n,{type:"number",placeholder:"Enter candidate ID"})}),h.jsx(Ht.Item,{name:"stepId",label:"Step ID",rules:[{required:!0,message:"Please enter step ID"}],children:h.jsx(_n,{type:"number",placeholder:"Enter step ID"})}),h.jsx(Ht.Item,{name:"medium",label:"Medium",rules:[{required:!0,message:"Please select medium"}],children:h.jsxs(wn,{placeholder:"Select medium",children:[h.jsx(Kt,{value:"email",children:"Email"}),h.jsx(Kt,{value:"whatsapp",children:"WhatsApp"}),h.jsx(Kt,{value:"sms",children:"SMS"}),h.jsx(Kt,{value:"call",children:"Call"}),h.jsx(Kt,{value:"linkedin",children:"LinkedIn"})]})}),h.jsx(Ht.Item,{name:"event",label:"Event",rules:[{required:!0,message:"Please select event"}],children:h.jsxs(wn,{placeholder:"Select event",children:[h.jsx(Kt,{value:"delivered",children:"Delivered"}),h.jsx(Kt,{value:"opened",children:"Opened"}),h.jsx(Kt,{value:"clicked",children:"Clicked"}),h.jsx(Kt,{value:"replied",children:"Replied"}),h.jsx(Kt,{value:"failed",children:"Failed"})]})})]})})]})};function Je(e){if(typeof e=="string"||typeof e=="number")return""+e;let t="";if(Array.isArray(e))for(let n=0,r;n<e.length;n++)(r=Je(e[n]))!==""&&(t+=(t&&" ")+r);else for(let n in e)e[n]&&(t+=(t&&" ")+n);return t}var cp={exports:{}},up={},dp={exports:{}},fp={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ir=v;function V2(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var U2=typeof Object.is=="function"?Object.is:V2,q2=ir.useState,G2=ir.useEffect,X2=ir.useLayoutEffect,Y2=ir.useDebugValue;function K2(e,t){var n=t(),r=q2({inst:{value:n,getSnapshot:t}}),o=r[0].inst,i=r[1];return X2(function(){o.value=n,o.getSnapshot=t,Ia(o)&&i({inst:o})},[e,n,t]),G2(function(){return Ia(o)&&i({inst:o}),e(function(){Ia(o)&&i({inst:o})})},[e]),Y2(n),n}function Ia(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!U2(e,n)}catch{return!0}}function Z2(e,t){return t()}var J2=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?Z2:K2;fp.useSyncExternalStore=ir.useSyncExternalStore!==void 0?ir.useSyncExternalStore:J2;dp.exports=fp;var Q2=dp.exports;/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var _i=v,eC=Q2;function tC(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var nC=typeof Object.is=="function"?Object.is:tC,rC=eC.useSyncExternalStore,oC=_i.useRef,iC=_i.useEffect,aC=_i.useMemo,sC=_i.useDebugValue;up.useSyncExternalStoreWithSelector=function(e,t,n,r,o){var i=oC(null);if(i.current===null){var a={hasValue:!1,value:null};i.current=a}else a=i.current;i=aC(function(){function c(p){if(!u){if(u=!0,l=p,p=r(p),o!==void 0&&a.hasValue){var g=a.value;if(o(g,p))return d=g}return d=p}if(g=d,nC(l,p))return g;var m=r(p);return o!==void 0&&o(g,m)?(l=p,g):(l=p,d=m)}var u=!1,l,d,f=n===void 0?null:n;return[function(){return c(t())},f===null?void 0:function(){return c(f())}]},[t,n,r,o]);var s=rC(e,i[0],i[1]);return iC(function(){a.hasValue=!0,a.value=s},[s]),sC(s),s};cp.exports=up;var lC=cp.exports;const cC=bs(lC);var uC={VITE_APP_VERSION:"v1.3.0",VITE_APP_BASE_NAME:"/",VITE_AWS_ACCESS_KEY:"********************",VITE_AWS_SECRET_KEY:"KEUkIwReevdHJJHQhXw8wofN1OgManJ44RSipOl1",VITE_AWS_REGION:"eu-west-2",VITE_WS_URL:"https://x74v19wh-5001.inc1.devtunnels.ms",BASE_URL:"/",MODE:"production",DEV:!1,PROD:!0,SSR:!1};const tu=e=>{let t;const n=new Set,r=(l,d)=>{const f=typeof l=="function"?l(t):l;if(!Object.is(f,t)){const p=t;t=d??(typeof f!="object"||f===null)?f:Object.assign({},t,f),n.forEach(g=>g(t,p))}},o=()=>t,c={setState:r,getState:o,getInitialState:()=>u,subscribe:l=>(n.add(l),()=>n.delete(l)),destroy:()=>{(uC?"production":void 0)!=="production"&&console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),n.clear()}},u=t=e(r,o,c);return c},dC=e=>e?tu(e):tu,{useDebugValue:fC}=z,{useSyncExternalStoreWithSelector:pC}=cC,hC=e=>e;function pp(e,t=hC,n){const r=pC(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,n);return fC(r),r}const nu=(e,t)=>{const n=dC(e),r=(o,i=t)=>pp(n,o,i);return Object.assign(r,n),r},gC=(e,t)=>e?nu(e,t):nu;function Ge(e,t){if(Object.is(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;if(e instanceof Map&&t instanceof Map){if(e.size!==t.size)return!1;for(const[r,o]of e)if(!Object.is(o,t.get(r)))return!1;return!0}if(e instanceof Set&&t instanceof Set){if(e.size!==t.size)return!1;for(const r of e)if(!t.has(r))return!1;return!0}const n=Object.keys(e);if(n.length!==Object.keys(t).length)return!1;for(const r of n)if(!Object.prototype.hasOwnProperty.call(t,r)||!Object.is(e[r],t[r]))return!1;return!0}var mC={value:()=>{}};function Bi(){for(var e=0,t=arguments.length,n={},r;e<t;++e){if(!(r=arguments[e]+"")||r in n||/[\s.]/.test(r))throw new Error("illegal type: "+r);n[r]=[]}return new Jo(n)}function Jo(e){this._=e}function vC(e,t){return e.trim().split(/^|\s+/).map(function(n){var r="",o=n.indexOf(".");if(o>=0&&(r=n.slice(o+1),n=n.slice(0,o)),n&&!t.hasOwnProperty(n))throw new Error("unknown type: "+n);return{type:n,name:r}})}Jo.prototype=Bi.prototype={constructor:Jo,on:function(e,t){var n=this._,r=vC(e+"",n),o,i=-1,a=r.length;if(arguments.length<2){for(;++i<a;)if((o=(e=r[i]).type)&&(o=yC(n[o],e.name)))return o;return}if(t!=null&&typeof t!="function")throw new Error("invalid callback: "+t);for(;++i<a;)if(o=(e=r[i]).type)n[o]=ru(n[o],e.name,t);else if(t==null)for(o in n)n[o]=ru(n[o],e.name,null);return this},copy:function(){var e={},t=this._;for(var n in t)e[n]=t[n].slice();return new Jo(e)},call:function(e,t){if((o=arguments.length-2)>0)for(var n=new Array(o),r=0,o,i;r<o;++r)n[r]=arguments[r+2];if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(i=this._[e],r=0,o=i.length;r<o;++r)i[r].value.apply(t,n)},apply:function(e,t,n){if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(var r=this._[e],o=0,i=r.length;o<i;++o)r[o].value.apply(t,n)}};function yC(e,t){for(var n=0,r=e.length,o;n<r;++n)if((o=e[n]).name===t)return o.value}function ru(e,t,n){for(var r=0,o=e.length;r<o;++r)if(e[r].name===t){e[r]=mC,e=e.slice(0,r).concat(e.slice(r+1));break}return n!=null&&e.push({name:t,value:n}),e}var ts="http://www.w3.org/1999/xhtml";const ou={svg:"http://www.w3.org/2000/svg",xhtml:ts,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function Li(e){var t=e+="",n=t.indexOf(":");return n>=0&&(t=e.slice(0,n))!=="xmlns"&&(e=e.slice(n+1)),ou.hasOwnProperty(t)?{space:ou[t],local:e}:e}function bC(e){return function(){var t=this.ownerDocument,n=this.namespaceURI;return n===ts&&t.documentElement.namespaceURI===ts?t.createElement(e):t.createElementNS(n,e)}}function xC(e){return function(){return this.ownerDocument.createElementNS(e.space,e.local)}}function hp(e){var t=Li(e);return(t.local?xC:bC)(t)}function wC(){}function vl(e){return e==null?wC:function(){return this.querySelector(e)}}function SC(e){typeof e!="function"&&(e=vl(e));for(var t=this._groups,n=t.length,r=new Array(n),o=0;o<n;++o)for(var i=t[o],a=i.length,s=r[o]=new Array(a),c,u,l=0;l<a;++l)(c=i[l])&&(u=e.call(c,c.__data__,l,i))&&("__data__"in c&&(u.__data__=c.__data__),s[l]=u);return new At(r,this._parents)}function CC(e){return e==null?[]:Array.isArray(e)?e:Array.from(e)}function EC(){return[]}function gp(e){return e==null?EC:function(){return this.querySelectorAll(e)}}function IC(e){return function(){return CC(e.apply(this,arguments))}}function DC(e){typeof e=="function"?e=IC(e):e=gp(e);for(var t=this._groups,n=t.length,r=[],o=[],i=0;i<n;++i)for(var a=t[i],s=a.length,c,u=0;u<s;++u)(c=a[u])&&(r.push(e.call(c,c.__data__,u,a)),o.push(c));return new At(r,o)}function mp(e){return function(){return this.matches(e)}}function vp(e){return function(t){return t.matches(e)}}var PC=Array.prototype.find;function AC(e){return function(){return PC.call(this.children,e)}}function NC(){return this.firstElementChild}function OC(e){return this.select(e==null?NC:AC(typeof e=="function"?e:vp(e)))}var RC=Array.prototype.filter;function TC(){return Array.from(this.children)}function jC(e){return function(){return RC.call(this.children,e)}}function MC(e){return this.selectAll(e==null?TC:jC(typeof e=="function"?e:vp(e)))}function kC(e){typeof e!="function"&&(e=mp(e));for(var t=this._groups,n=t.length,r=new Array(n),o=0;o<n;++o)for(var i=t[o],a=i.length,s=r[o]=[],c,u=0;u<a;++u)(c=i[u])&&e.call(c,c.__data__,u,i)&&s.push(c);return new At(r,this._parents)}function yp(e){return new Array(e.length)}function $C(){return new At(this._enter||this._groups.map(yp),this._parents)}function gi(e,t){this.ownerDocument=e.ownerDocument,this.namespaceURI=e.namespaceURI,this._next=null,this._parent=e,this.__data__=t}gi.prototype={constructor:gi,appendChild:function(e){return this._parent.insertBefore(e,this._next)},insertBefore:function(e,t){return this._parent.insertBefore(e,t)},querySelector:function(e){return this._parent.querySelector(e)},querySelectorAll:function(e){return this._parent.querySelectorAll(e)}};function _C(e){return function(){return e}}function BC(e,t,n,r,o,i){for(var a=0,s,c=t.length,u=i.length;a<u;++a)(s=t[a])?(s.__data__=i[a],r[a]=s):n[a]=new gi(e,i[a]);for(;a<c;++a)(s=t[a])&&(o[a]=s)}function LC(e,t,n,r,o,i,a){var s,c,u=new Map,l=t.length,d=i.length,f=new Array(l),p;for(s=0;s<l;++s)(c=t[s])&&(f[s]=p=a.call(c,c.__data__,s,t)+"",u.has(p)?o[s]=c:u.set(p,c));for(s=0;s<d;++s)p=a.call(e,i[s],s,i)+"",(c=u.get(p))?(r[s]=c,c.__data__=i[s],u.delete(p)):n[s]=new gi(e,i[s]);for(s=0;s<l;++s)(c=t[s])&&u.get(f[s])===c&&(o[s]=c)}function zC(e){return e.__data__}function FC(e,t){if(!arguments.length)return Array.from(this,zC);var n=t?LC:BC,r=this._parents,o=this._groups;typeof e!="function"&&(e=_C(e));for(var i=o.length,a=new Array(i),s=new Array(i),c=new Array(i),u=0;u<i;++u){var l=r[u],d=o[u],f=d.length,p=HC(e.call(l,l&&l.__data__,u,r)),g=p.length,m=s[u]=new Array(g),x=a[u]=new Array(g),w=c[u]=new Array(f);n(l,d,m,x,w,p,t);for(var y=0,E=0,S,b;y<g;++y)if(S=m[y]){for(y>=E&&(E=y+1);!(b=x[E])&&++E<g;);S._next=b||null}}return a=new At(a,r),a._enter=s,a._exit=c,a}function HC(e){return typeof e=="object"&&"length"in e?e:Array.from(e)}function WC(){return new At(this._exit||this._groups.map(yp),this._parents)}function VC(e,t,n){var r=this.enter(),o=this,i=this.exit();return typeof e=="function"?(r=e(r),r&&(r=r.selection())):r=r.append(e+""),t!=null&&(o=t(o),o&&(o=o.selection())),n==null?i.remove():n(i),r&&o?r.merge(o).order():o}function UC(e){for(var t=e.selection?e.selection():e,n=this._groups,r=t._groups,o=n.length,i=r.length,a=Math.min(o,i),s=new Array(o),c=0;c<a;++c)for(var u=n[c],l=r[c],d=u.length,f=s[c]=new Array(d),p,g=0;g<d;++g)(p=u[g]||l[g])&&(f[g]=p);for(;c<o;++c)s[c]=n[c];return new At(s,this._parents)}function qC(){for(var e=this._groups,t=-1,n=e.length;++t<n;)for(var r=e[t],o=r.length-1,i=r[o],a;--o>=0;)(a=r[o])&&(i&&a.compareDocumentPosition(i)^4&&i.parentNode.insertBefore(a,i),i=a);return this}function GC(e){e||(e=XC);function t(d,f){return d&&f?e(d.__data__,f.__data__):!d-!f}for(var n=this._groups,r=n.length,o=new Array(r),i=0;i<r;++i){for(var a=n[i],s=a.length,c=o[i]=new Array(s),u,l=0;l<s;++l)(u=a[l])&&(c[l]=u);c.sort(t)}return new At(o,this._parents).order()}function XC(e,t){return e<t?-1:e>t?1:e>=t?0:NaN}function YC(){var e=arguments[0];return arguments[0]=this,e.apply(null,arguments),this}function KC(){return Array.from(this)}function ZC(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var r=e[t],o=0,i=r.length;o<i;++o){var a=r[o];if(a)return a}return null}function JC(){let e=0;for(const t of this)++e;return e}function QC(){return!this.node()}function eE(e){for(var t=this._groups,n=0,r=t.length;n<r;++n)for(var o=t[n],i=0,a=o.length,s;i<a;++i)(s=o[i])&&e.call(s,s.__data__,i,o);return this}function tE(e){return function(){this.removeAttribute(e)}}function nE(e){return function(){this.removeAttributeNS(e.space,e.local)}}function rE(e,t){return function(){this.setAttribute(e,t)}}function oE(e,t){return function(){this.setAttributeNS(e.space,e.local,t)}}function iE(e,t){return function(){var n=t.apply(this,arguments);n==null?this.removeAttribute(e):this.setAttribute(e,n)}}function aE(e,t){return function(){var n=t.apply(this,arguments);n==null?this.removeAttributeNS(e.space,e.local):this.setAttributeNS(e.space,e.local,n)}}function sE(e,t){var n=Li(e);if(arguments.length<2){var r=this.node();return n.local?r.getAttributeNS(n.space,n.local):r.getAttribute(n)}return this.each((t==null?n.local?nE:tE:typeof t=="function"?n.local?aE:iE:n.local?oE:rE)(n,t))}function bp(e){return e.ownerDocument&&e.ownerDocument.defaultView||e.document&&e||e.defaultView}function lE(e){return function(){this.style.removeProperty(e)}}function cE(e,t,n){return function(){this.style.setProperty(e,t,n)}}function uE(e,t,n){return function(){var r=t.apply(this,arguments);r==null?this.style.removeProperty(e):this.style.setProperty(e,r,n)}}function dE(e,t,n){return arguments.length>1?this.each((t==null?lE:typeof t=="function"?uE:cE)(e,t,n??"")):ar(this.node(),e)}function ar(e,t){return e.style.getPropertyValue(t)||bp(e).getComputedStyle(e,null).getPropertyValue(t)}function fE(e){return function(){delete this[e]}}function pE(e,t){return function(){this[e]=t}}function hE(e,t){return function(){var n=t.apply(this,arguments);n==null?delete this[e]:this[e]=n}}function gE(e,t){return arguments.length>1?this.each((t==null?fE:typeof t=="function"?hE:pE)(e,t)):this.node()[e]}function xp(e){return e.trim().split(/^|\s+/)}function yl(e){return e.classList||new wp(e)}function wp(e){this._node=e,this._names=xp(e.getAttribute("class")||"")}wp.prototype={add:function(e){var t=this._names.indexOf(e);t<0&&(this._names.push(e),this._node.setAttribute("class",this._names.join(" ")))},remove:function(e){var t=this._names.indexOf(e);t>=0&&(this._names.splice(t,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(e){return this._names.indexOf(e)>=0}};function Sp(e,t){for(var n=yl(e),r=-1,o=t.length;++r<o;)n.add(t[r])}function Cp(e,t){for(var n=yl(e),r=-1,o=t.length;++r<o;)n.remove(t[r])}function mE(e){return function(){Sp(this,e)}}function vE(e){return function(){Cp(this,e)}}function yE(e,t){return function(){(t.apply(this,arguments)?Sp:Cp)(this,e)}}function bE(e,t){var n=xp(e+"");if(arguments.length<2){for(var r=yl(this.node()),o=-1,i=n.length;++o<i;)if(!r.contains(n[o]))return!1;return!0}return this.each((typeof t=="function"?yE:t?mE:vE)(n,t))}function xE(){this.textContent=""}function wE(e){return function(){this.textContent=e}}function SE(e){return function(){var t=e.apply(this,arguments);this.textContent=t??""}}function CE(e){return arguments.length?this.each(e==null?xE:(typeof e=="function"?SE:wE)(e)):this.node().textContent}function EE(){this.innerHTML=""}function IE(e){return function(){this.innerHTML=e}}function DE(e){return function(){var t=e.apply(this,arguments);this.innerHTML=t??""}}function PE(e){return arguments.length?this.each(e==null?EE:(typeof e=="function"?DE:IE)(e)):this.node().innerHTML}function AE(){this.nextSibling&&this.parentNode.appendChild(this)}function NE(){return this.each(AE)}function OE(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function RE(){return this.each(OE)}function TE(e){var t=typeof e=="function"?e:hp(e);return this.select(function(){return this.appendChild(t.apply(this,arguments))})}function jE(){return null}function ME(e,t){var n=typeof e=="function"?e:hp(e),r=t==null?jE:typeof t=="function"?t:vl(t);return this.select(function(){return this.insertBefore(n.apply(this,arguments),r.apply(this,arguments)||null)})}function kE(){var e=this.parentNode;e&&e.removeChild(this)}function $E(){return this.each(kE)}function _E(){var e=this.cloneNode(!1),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function BE(){var e=this.cloneNode(!0),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function LE(e){return this.select(e?BE:_E)}function zE(e){return arguments.length?this.property("__data__",e):this.node().__data__}function FE(e){return function(t){e.call(this,t,this.__data__)}}function HE(e){return e.trim().split(/^|\s+/).map(function(t){var n="",r=t.indexOf(".");return r>=0&&(n=t.slice(r+1),t=t.slice(0,r)),{type:t,name:n}})}function WE(e){return function(){var t=this.__on;if(t){for(var n=0,r=-1,o=t.length,i;n<o;++n)i=t[n],(!e.type||i.type===e.type)&&i.name===e.name?this.removeEventListener(i.type,i.listener,i.options):t[++r]=i;++r?t.length=r:delete this.__on}}}function VE(e,t,n){return function(){var r=this.__on,o,i=FE(t);if(r){for(var a=0,s=r.length;a<s;++a)if((o=r[a]).type===e.type&&o.name===e.name){this.removeEventListener(o.type,o.listener,o.options),this.addEventListener(o.type,o.listener=i,o.options=n),o.value=t;return}}this.addEventListener(e.type,i,n),o={type:e.type,name:e.name,value:t,listener:i,options:n},r?r.push(o):this.__on=[o]}}function UE(e,t,n){var r=HE(e+""),o,i=r.length,a;if(arguments.length<2){var s=this.node().__on;if(s){for(var c=0,u=s.length,l;c<u;++c)for(o=0,l=s[c];o<i;++o)if((a=r[o]).type===l.type&&a.name===l.name)return l.value}return}for(s=t?VE:WE,o=0;o<i;++o)this.each(s(r[o],t,n));return this}function Ep(e,t,n){var r=bp(e),o=r.CustomEvent;typeof o=="function"?o=new o(t,n):(o=r.document.createEvent("Event"),n?(o.initEvent(t,n.bubbles,n.cancelable),o.detail=n.detail):o.initEvent(t,!1,!1)),e.dispatchEvent(o)}function qE(e,t){return function(){return Ep(this,e,t)}}function GE(e,t){return function(){return Ep(this,e,t.apply(this,arguments))}}function XE(e,t){return this.each((typeof t=="function"?GE:qE)(e,t))}function*YE(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var r=e[t],o=0,i=r.length,a;o<i;++o)(a=r[o])&&(yield a)}var Ip=[null];function At(e,t){this._groups=e,this._parents=t}function wo(){return new At([[document.documentElement]],Ip)}function KE(){return this}At.prototype=wo.prototype={constructor:At,select:SC,selectAll:DC,selectChild:OC,selectChildren:MC,filter:kC,data:FC,enter:$C,exit:WC,join:VC,merge:UC,selection:KE,order:qC,sort:GC,call:YC,nodes:KC,node:ZC,size:JC,empty:QC,each:eE,attr:sE,style:dE,property:gE,classed:bE,text:CE,html:PE,raise:NE,lower:RE,append:TE,insert:ME,remove:$E,clone:LE,datum:zE,on:UE,dispatch:XE,[Symbol.iterator]:YE};function kt(e){return typeof e=="string"?new At([[document.querySelector(e)]],[document.documentElement]):new At([[e]],Ip)}function ZE(e){let t;for(;t=e.sourceEvent;)e=t;return e}function Ft(e,t){if(e=ZE(e),t===void 0&&(t=e.currentTarget),t){var n=t.ownerSVGElement||t;if(n.createSVGPoint){var r=n.createSVGPoint();return r.x=e.clientX,r.y=e.clientY,r=r.matrixTransform(t.getScreenCTM().inverse()),[r.x,r.y]}if(t.getBoundingClientRect){var o=t.getBoundingClientRect();return[e.clientX-o.left-t.clientLeft,e.clientY-o.top-t.clientTop]}}return[e.pageX,e.pageY]}const JE={passive:!1},Yr={capture:!0,passive:!1};function Da(e){e.stopImmediatePropagation()}function Jn(e){e.preventDefault(),e.stopImmediatePropagation()}function Dp(e){var t=e.document.documentElement,n=kt(e).on("dragstart.drag",Jn,Yr);"onselectstart"in t?n.on("selectstart.drag",Jn,Yr):(t.__noselect=t.style.MozUserSelect,t.style.MozUserSelect="none")}function Pp(e,t){var n=e.document.documentElement,r=kt(e).on("dragstart.drag",null);t&&(r.on("click.drag",Jn,Yr),setTimeout(function(){r.on("click.drag",null)},0)),"onselectstart"in n?r.on("selectstart.drag",null):(n.style.MozUserSelect=n.__noselect,delete n.__noselect)}const zo=e=>()=>e;function ns(e,{sourceEvent:t,subject:n,target:r,identifier:o,active:i,x:a,y:s,dx:c,dy:u,dispatch:l}){Object.defineProperties(this,{type:{value:e,enumerable:!0,configurable:!0},sourceEvent:{value:t,enumerable:!0,configurable:!0},subject:{value:n,enumerable:!0,configurable:!0},target:{value:r,enumerable:!0,configurable:!0},identifier:{value:o,enumerable:!0,configurable:!0},active:{value:i,enumerable:!0,configurable:!0},x:{value:a,enumerable:!0,configurable:!0},y:{value:s,enumerable:!0,configurable:!0},dx:{value:c,enumerable:!0,configurable:!0},dy:{value:u,enumerable:!0,configurable:!0},_:{value:l}})}ns.prototype.on=function(){var e=this._.on.apply(this._,arguments);return e===this._?this:e};function QE(e){return!e.ctrlKey&&!e.button}function e4(){return this.parentNode}function t4(e,t){return t??{x:e.x,y:e.y}}function n4(){return navigator.maxTouchPoints||"ontouchstart"in this}function r4(){var e=QE,t=e4,n=t4,r=n4,o={},i=Bi("start","drag","end"),a=0,s,c,u,l,d=0;function f(S){S.on("mousedown.drag",p).filter(r).on("touchstart.drag",x).on("touchmove.drag",w,JE).on("touchend.drag touchcancel.drag",y).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function p(S,b){if(!(l||!e.call(this,S,b))){var I=E(this,t.call(this,S,b),S,b,"mouse");I&&(kt(S.view).on("mousemove.drag",g,Yr).on("mouseup.drag",m,Yr),Dp(S.view),Da(S),u=!1,s=S.clientX,c=S.clientY,I("start",S))}}function g(S){if(Jn(S),!u){var b=S.clientX-s,I=S.clientY-c;u=b*b+I*I>d}o.mouse("drag",S)}function m(S){kt(S.view).on("mousemove.drag mouseup.drag",null),Pp(S.view,u),Jn(S),o.mouse("end",S)}function x(S,b){if(e.call(this,S,b)){var I=S.changedTouches,P=t.call(this,S,b),A=I.length,D,k;for(D=0;D<A;++D)(k=E(this,P,S,b,I[D].identifier,I[D]))&&(Da(S),k("start",S,I[D]))}}function w(S){var b=S.changedTouches,I=b.length,P,A;for(P=0;P<I;++P)(A=o[b[P].identifier])&&(Jn(S),A("drag",S,b[P]))}function y(S){var b=S.changedTouches,I=b.length,P,A;for(l&&clearTimeout(l),l=setTimeout(function(){l=null},500),P=0;P<I;++P)(A=o[b[P].identifier])&&(Da(S),A("end",S,b[P]))}function E(S,b,I,P,A,D){var k=i.copy(),L=Ft(D||I,b),B,j,C;if((C=n.call(S,new ns("beforestart",{sourceEvent:I,target:f,identifier:A,active:a,x:L[0],y:L[1],dx:0,dy:0,dispatch:k}),P))!=null)return B=C.x-L[0]||0,j=C.y-L[1]||0,function O(N,_,W){var T=L,H;switch(N){case"start":o[A]=O,H=a++;break;case"end":delete o[A],--a;case"drag":L=Ft(W||_,b),H=a;break}k.call(N,S,new ns(N,{sourceEvent:_,subject:C,target:f,identifier:A,active:H,x:L[0]+B,y:L[1]+j,dx:L[0]-T[0],dy:L[1]-T[1],dispatch:k}),P)}}return f.filter=function(S){return arguments.length?(e=typeof S=="function"?S:zo(!!S),f):e},f.container=function(S){return arguments.length?(t=typeof S=="function"?S:zo(S),f):t},f.subject=function(S){return arguments.length?(n=typeof S=="function"?S:zo(S),f):n},f.touchable=function(S){return arguments.length?(r=typeof S=="function"?S:zo(!!S),f):r},f.on=function(){var S=i.on.apply(i,arguments);return S===i?f:S},f.clickDistance=function(S){return arguments.length?(d=(S=+S)*S,f):Math.sqrt(d)},f}function bl(e,t,n){e.prototype=t.prototype=n,n.constructor=e}function Ap(e,t){var n=Object.create(e.prototype);for(var r in t)n[r]=t[r];return n}function So(){}var Kr=.7,mi=1/Kr,Qn="\\s*([+-]?\\d+)\\s*",Zr="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",Qt="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",o4=/^#([0-9a-f]{3,8})$/,i4=new RegExp(`^rgb\\(${Qn},${Qn},${Qn}\\)$`),a4=new RegExp(`^rgb\\(${Qt},${Qt},${Qt}\\)$`),s4=new RegExp(`^rgba\\(${Qn},${Qn},${Qn},${Zr}\\)$`),l4=new RegExp(`^rgba\\(${Qt},${Qt},${Qt},${Zr}\\)$`),c4=new RegExp(`^hsl\\(${Zr},${Qt},${Qt}\\)$`),u4=new RegExp(`^hsla\\(${Zr},${Qt},${Qt},${Zr}\\)$`),iu={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};bl(So,Jr,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:au,formatHex:au,formatHex8:d4,formatHsl:f4,formatRgb:su,toString:su});function au(){return this.rgb().formatHex()}function d4(){return this.rgb().formatHex8()}function f4(){return Np(this).formatHsl()}function su(){return this.rgb().formatRgb()}function Jr(e){var t,n;return e=(e+"").trim().toLowerCase(),(t=o4.exec(e))?(n=t[1].length,t=parseInt(t[1],16),n===6?lu(t):n===3?new pt(t>>8&15|t>>4&240,t>>4&15|t&240,(t&15)<<4|t&15,1):n===8?Fo(t>>24&255,t>>16&255,t>>8&255,(t&255)/255):n===4?Fo(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|t&240,((t&15)<<4|t&15)/255):null):(t=i4.exec(e))?new pt(t[1],t[2],t[3],1):(t=a4.exec(e))?new pt(t[1]*255/100,t[2]*255/100,t[3]*255/100,1):(t=s4.exec(e))?Fo(t[1],t[2],t[3],t[4]):(t=l4.exec(e))?Fo(t[1]*255/100,t[2]*255/100,t[3]*255/100,t[4]):(t=c4.exec(e))?du(t[1],t[2]/100,t[3]/100,1):(t=u4.exec(e))?du(t[1],t[2]/100,t[3]/100,t[4]):iu.hasOwnProperty(e)?lu(iu[e]):e==="transparent"?new pt(NaN,NaN,NaN,0):null}function lu(e){return new pt(e>>16&255,e>>8&255,e&255,1)}function Fo(e,t,n,r){return r<=0&&(e=t=n=NaN),new pt(e,t,n,r)}function p4(e){return e instanceof So||(e=Jr(e)),e?(e=e.rgb(),new pt(e.r,e.g,e.b,e.opacity)):new pt}function rs(e,t,n,r){return arguments.length===1?p4(e):new pt(e,t,n,r??1)}function pt(e,t,n,r){this.r=+e,this.g=+t,this.b=+n,this.opacity=+r}bl(pt,rs,Ap(So,{brighter(e){return e=e==null?mi:Math.pow(mi,e),new pt(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=e==null?Kr:Math.pow(Kr,e),new pt(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new pt(Mn(this.r),Mn(this.g),Mn(this.b),vi(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:cu,formatHex:cu,formatHex8:h4,formatRgb:uu,toString:uu}));function cu(){return`#${Rn(this.r)}${Rn(this.g)}${Rn(this.b)}`}function h4(){return`#${Rn(this.r)}${Rn(this.g)}${Rn(this.b)}${Rn((isNaN(this.opacity)?1:this.opacity)*255)}`}function uu(){const e=vi(this.opacity);return`${e===1?"rgb(":"rgba("}${Mn(this.r)}, ${Mn(this.g)}, ${Mn(this.b)}${e===1?")":`, ${e})`}`}function vi(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function Mn(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function Rn(e){return e=Mn(e),(e<16?"0":"")+e.toString(16)}function du(e,t,n,r){return r<=0?e=t=n=NaN:n<=0||n>=1?e=t=NaN:t<=0&&(e=NaN),new Wt(e,t,n,r)}function Np(e){if(e instanceof Wt)return new Wt(e.h,e.s,e.l,e.opacity);if(e instanceof So||(e=Jr(e)),!e)return new Wt;if(e instanceof Wt)return e;e=e.rgb();var t=e.r/255,n=e.g/255,r=e.b/255,o=Math.min(t,n,r),i=Math.max(t,n,r),a=NaN,s=i-o,c=(i+o)/2;return s?(t===i?a=(n-r)/s+(n<r)*6:n===i?a=(r-t)/s+2:a=(t-n)/s+4,s/=c<.5?i+o:2-i-o,a*=60):s=c>0&&c<1?0:a,new Wt(a,s,c,e.opacity)}function g4(e,t,n,r){return arguments.length===1?Np(e):new Wt(e,t,n,r??1)}function Wt(e,t,n,r){this.h=+e,this.s=+t,this.l=+n,this.opacity=+r}bl(Wt,g4,Ap(So,{brighter(e){return e=e==null?mi:Math.pow(mi,e),new Wt(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=e==null?Kr:Math.pow(Kr,e),new Wt(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,n=this.l,r=n+(n<.5?n:1-n)*t,o=2*n-r;return new pt(Pa(e>=240?e-240:e+120,o,r),Pa(e,o,r),Pa(e<120?e+240:e-120,o,r),this.opacity)},clamp(){return new Wt(fu(this.h),Ho(this.s),Ho(this.l),vi(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=vi(this.opacity);return`${e===1?"hsl(":"hsla("}${fu(this.h)}, ${Ho(this.s)*100}%, ${Ho(this.l)*100}%${e===1?")":`, ${e})`}`}}));function fu(e){return e=(e||0)%360,e<0?e+360:e}function Ho(e){return Math.max(0,Math.min(1,e||0))}function Pa(e,t,n){return(e<60?t+(n-t)*e/60:e<180?n:e<240?t+(n-t)*(240-e)/60:t)*255}const Op=e=>()=>e;function m4(e,t){return function(n){return e+n*t}}function v4(e,t,n){return e=Math.pow(e,n),t=Math.pow(t,n)-e,n=1/n,function(r){return Math.pow(e+r*t,n)}}function y4(e){return(e=+e)==1?Rp:function(t,n){return n-t?v4(t,n,e):Op(isNaN(t)?n:t)}}function Rp(e,t){var n=t-e;return n?m4(e,n):Op(isNaN(e)?t:e)}const pu=function e(t){var n=y4(t);function r(o,i){var a=n((o=rs(o)).r,(i=rs(i)).r),s=n(o.g,i.g),c=n(o.b,i.b),u=Rp(o.opacity,i.opacity);return function(l){return o.r=a(l),o.g=s(l),o.b=c(l),o.opacity=u(l),o+""}}return r.gamma=e,r}(1);function pn(e,t){return e=+e,t=+t,function(n){return e*(1-n)+t*n}}var os=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,Aa=new RegExp(os.source,"g");function b4(e){return function(){return e}}function x4(e){return function(t){return e(t)+""}}function w4(e,t){var n=os.lastIndex=Aa.lastIndex=0,r,o,i,a=-1,s=[],c=[];for(e=e+"",t=t+"";(r=os.exec(e))&&(o=Aa.exec(t));)(i=o.index)>n&&(i=t.slice(n,i),s[a]?s[a]+=i:s[++a]=i),(r=r[0])===(o=o[0])?s[a]?s[a]+=o:s[++a]=o:(s[++a]=null,c.push({i:a,x:pn(r,o)})),n=Aa.lastIndex;return n<t.length&&(i=t.slice(n),s[a]?s[a]+=i:s[++a]=i),s.length<2?c[0]?x4(c[0].x):b4(t):(t=c.length,function(u){for(var l=0,d;l<t;++l)s[(d=c[l]).i]=d.x(u);return s.join("")})}var hu=180/Math.PI,is={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function Tp(e,t,n,r,o,i){var a,s,c;return(a=Math.sqrt(e*e+t*t))&&(e/=a,t/=a),(c=e*n+t*r)&&(n-=e*c,r-=t*c),(s=Math.sqrt(n*n+r*r))&&(n/=s,r/=s,c/=s),e*r<t*n&&(e=-e,t=-t,c=-c,a=-a),{translateX:o,translateY:i,rotate:Math.atan2(t,e)*hu,skewX:Math.atan(c)*hu,scaleX:a,scaleY:s}}var Wo;function S4(e){const t=new(typeof DOMMatrix=="function"?DOMMatrix:WebKitCSSMatrix)(e+"");return t.isIdentity?is:Tp(t.a,t.b,t.c,t.d,t.e,t.f)}function C4(e){return e==null||(Wo||(Wo=document.createElementNS("http://www.w3.org/2000/svg","g")),Wo.setAttribute("transform",e),!(e=Wo.transform.baseVal.consolidate()))?is:(e=e.matrix,Tp(e.a,e.b,e.c,e.d,e.e,e.f))}function jp(e,t,n,r){function o(u){return u.length?u.pop()+" ":""}function i(u,l,d,f,p,g){if(u!==d||l!==f){var m=p.push("translate(",null,t,null,n);g.push({i:m-4,x:pn(u,d)},{i:m-2,x:pn(l,f)})}else(d||f)&&p.push("translate("+d+t+f+n)}function a(u,l,d,f){u!==l?(u-l>180?l+=360:l-u>180&&(u+=360),f.push({i:d.push(o(d)+"rotate(",null,r)-2,x:pn(u,l)})):l&&d.push(o(d)+"rotate("+l+r)}function s(u,l,d,f){u!==l?f.push({i:d.push(o(d)+"skewX(",null,r)-2,x:pn(u,l)}):l&&d.push(o(d)+"skewX("+l+r)}function c(u,l,d,f,p,g){if(u!==d||l!==f){var m=p.push(o(p)+"scale(",null,",",null,")");g.push({i:m-4,x:pn(u,d)},{i:m-2,x:pn(l,f)})}else(d!==1||f!==1)&&p.push(o(p)+"scale("+d+","+f+")")}return function(u,l){var d=[],f=[];return u=e(u),l=e(l),i(u.translateX,u.translateY,l.translateX,l.translateY,d,f),a(u.rotate,l.rotate,d,f),s(u.skewX,l.skewX,d,f),c(u.scaleX,u.scaleY,l.scaleX,l.scaleY,d,f),u=l=null,function(p){for(var g=-1,m=f.length,x;++g<m;)d[(x=f[g]).i]=x.x(p);return d.join("")}}}var E4=jp(S4,"px, ","px)","deg)"),I4=jp(C4,", ",")",")"),D4=1e-12;function gu(e){return((e=Math.exp(e))+1/e)/2}function P4(e){return((e=Math.exp(e))-1/e)/2}function A4(e){return((e=Math.exp(2*e))-1)/(e+1)}const N4=function e(t,n,r){function o(i,a){var s=i[0],c=i[1],u=i[2],l=a[0],d=a[1],f=a[2],p=l-s,g=d-c,m=p*p+g*g,x,w;if(m<D4)w=Math.log(f/u)/t,x=function(P){return[s+P*p,c+P*g,u*Math.exp(t*P*w)]};else{var y=Math.sqrt(m),E=(f*f-u*u+r*m)/(2*u*n*y),S=(f*f-u*u-r*m)/(2*f*n*y),b=Math.log(Math.sqrt(E*E+1)-E),I=Math.log(Math.sqrt(S*S+1)-S);w=(I-b)/t,x=function(P){var A=P*w,D=gu(b),k=u/(n*y)*(D*A4(t*A+b)-P4(b));return[s+k*p,c+k*g,u*D/gu(t*A+b)]}}return x.duration=w*1e3*t/Math.SQRT2,x}return o.rho=function(i){var a=Math.max(.001,+i),s=a*a,c=s*s;return e(a,s,c)},o}(Math.SQRT2,2,4);var sr=0,jr=0,Pr=0,Mp=1e3,yi,Mr,bi=0,Ln=0,zi=0,Qr=typeof performance=="object"&&performance.now?performance:Date,kp=typeof window=="object"&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(e){setTimeout(e,17)};function xl(){return Ln||(kp(O4),Ln=Qr.now()+zi)}function O4(){Ln=0}function xi(){this._call=this._time=this._next=null}xi.prototype=$p.prototype={constructor:xi,restart:function(e,t,n){if(typeof e!="function")throw new TypeError("callback is not a function");n=(n==null?xl():+n)+(t==null?0:+t),!this._next&&Mr!==this&&(Mr?Mr._next=this:yi=this,Mr=this),this._call=e,this._time=n,as()},stop:function(){this._call&&(this._call=null,this._time=1/0,as())}};function $p(e,t,n){var r=new xi;return r.restart(e,t,n),r}function R4(){xl(),++sr;for(var e=yi,t;e;)(t=Ln-e._time)>=0&&e._call.call(void 0,t),e=e._next;--sr}function mu(){Ln=(bi=Qr.now())+zi,sr=jr=0;try{R4()}finally{sr=0,j4(),Ln=0}}function T4(){var e=Qr.now(),t=e-bi;t>Mp&&(zi-=t,bi=e)}function j4(){for(var e,t=yi,n,r=1/0;t;)t._call?(r>t._time&&(r=t._time),e=t,t=t._next):(n=t._next,t._next=null,t=e?e._next=n:yi=n);Mr=e,as(r)}function as(e){if(!sr){jr&&(jr=clearTimeout(jr));var t=e-Ln;t>24?(e<1/0&&(jr=setTimeout(mu,e-Qr.now()-zi)),Pr&&(Pr=clearInterval(Pr))):(Pr||(bi=Qr.now(),Pr=setInterval(T4,Mp)),sr=1,kp(mu))}}function vu(e,t,n){var r=new xi;return t=t==null?0:+t,r.restart(o=>{r.stop(),e(o+t)},t,n),r}var M4=Bi("start","end","cancel","interrupt"),k4=[],_p=0,yu=1,ss=2,Qo=3,bu=4,ls=5,ei=6;function Fi(e,t,n,r,o,i){var a=e.__transition;if(!a)e.__transition={};else if(n in a)return;$4(e,n,{name:t,index:r,group:o,on:M4,tween:k4,time:i.time,delay:i.delay,duration:i.duration,ease:i.ease,timer:null,state:_p})}function wl(e,t){var n=Xt(e,t);if(n.state>_p)throw new Error("too late; already scheduled");return n}function en(e,t){var n=Xt(e,t);if(n.state>Qo)throw new Error("too late; already running");return n}function Xt(e,t){var n=e.__transition;if(!n||!(n=n[t]))throw new Error("transition not found");return n}function $4(e,t,n){var r=e.__transition,o;r[t]=n,n.timer=$p(i,0,n.time);function i(u){n.state=yu,n.timer.restart(a,n.delay,n.time),n.delay<=u&&a(u-n.delay)}function a(u){var l,d,f,p;if(n.state!==yu)return c();for(l in r)if(p=r[l],p.name===n.name){if(p.state===Qo)return vu(a);p.state===bu?(p.state=ei,p.timer.stop(),p.on.call("interrupt",e,e.__data__,p.index,p.group),delete r[l]):+l<t&&(p.state=ei,p.timer.stop(),p.on.call("cancel",e,e.__data__,p.index,p.group),delete r[l])}if(vu(function(){n.state===Qo&&(n.state=bu,n.timer.restart(s,n.delay,n.time),s(u))}),n.state=ss,n.on.call("start",e,e.__data__,n.index,n.group),n.state===ss){for(n.state=Qo,o=new Array(f=n.tween.length),l=0,d=-1;l<f;++l)(p=n.tween[l].value.call(e,e.__data__,n.index,n.group))&&(o[++d]=p);o.length=d+1}}function s(u){for(var l=u<n.duration?n.ease.call(null,u/n.duration):(n.timer.restart(c),n.state=ls,1),d=-1,f=o.length;++d<f;)o[d].call(e,l);n.state===ls&&(n.on.call("end",e,e.__data__,n.index,n.group),c())}function c(){n.state=ei,n.timer.stop(),delete r[t];for(var u in r)return;delete e.__transition}}function ti(e,t){var n=e.__transition,r,o,i=!0,a;if(n){t=t==null?null:t+"";for(a in n){if((r=n[a]).name!==t){i=!1;continue}o=r.state>ss&&r.state<ls,r.state=ei,r.timer.stop(),r.on.call(o?"interrupt":"cancel",e,e.__data__,r.index,r.group),delete n[a]}i&&delete e.__transition}}function _4(e){return this.each(function(){ti(this,e)})}function B4(e,t){var n,r;return function(){var o=en(this,e),i=o.tween;if(i!==n){r=n=i;for(var a=0,s=r.length;a<s;++a)if(r[a].name===t){r=r.slice(),r.splice(a,1);break}}o.tween=r}}function L4(e,t,n){var r,o;if(typeof n!="function")throw new Error;return function(){var i=en(this,e),a=i.tween;if(a!==r){o=(r=a).slice();for(var s={name:t,value:n},c=0,u=o.length;c<u;++c)if(o[c].name===t){o[c]=s;break}c===u&&o.push(s)}i.tween=o}}function z4(e,t){var n=this._id;if(e+="",arguments.length<2){for(var r=Xt(this.node(),n).tween,o=0,i=r.length,a;o<i;++o)if((a=r[o]).name===e)return a.value;return null}return this.each((t==null?B4:L4)(n,e,t))}function Sl(e,t,n){var r=e._id;return e.each(function(){var o=en(this,r);(o.value||(o.value={}))[t]=n.apply(this,arguments)}),function(o){return Xt(o,r).value[t]}}function Bp(e,t){var n;return(typeof t=="number"?pn:t instanceof Jr?pu:(n=Jr(t))?(t=n,pu):w4)(e,t)}function F4(e){return function(){this.removeAttribute(e)}}function H4(e){return function(){this.removeAttributeNS(e.space,e.local)}}function W4(e,t,n){var r,o=n+"",i;return function(){var a=this.getAttribute(e);return a===o?null:a===r?i:i=t(r=a,n)}}function V4(e,t,n){var r,o=n+"",i;return function(){var a=this.getAttributeNS(e.space,e.local);return a===o?null:a===r?i:i=t(r=a,n)}}function U4(e,t,n){var r,o,i;return function(){var a,s=n(this),c;return s==null?void this.removeAttribute(e):(a=this.getAttribute(e),c=s+"",a===c?null:a===r&&c===o?i:(o=c,i=t(r=a,s)))}}function q4(e,t,n){var r,o,i;return function(){var a,s=n(this),c;return s==null?void this.removeAttributeNS(e.space,e.local):(a=this.getAttributeNS(e.space,e.local),c=s+"",a===c?null:a===r&&c===o?i:(o=c,i=t(r=a,s)))}}function G4(e,t){var n=Li(e),r=n==="transform"?I4:Bp;return this.attrTween(e,typeof t=="function"?(n.local?q4:U4)(n,r,Sl(this,"attr."+e,t)):t==null?(n.local?H4:F4)(n):(n.local?V4:W4)(n,r,t))}function X4(e,t){return function(n){this.setAttribute(e,t.call(this,n))}}function Y4(e,t){return function(n){this.setAttributeNS(e.space,e.local,t.call(this,n))}}function K4(e,t){var n,r;function o(){var i=t.apply(this,arguments);return i!==r&&(n=(r=i)&&Y4(e,i)),n}return o._value=t,o}function Z4(e,t){var n,r;function o(){var i=t.apply(this,arguments);return i!==r&&(n=(r=i)&&X4(e,i)),n}return o._value=t,o}function J4(e,t){var n="attr."+e;if(arguments.length<2)return(n=this.tween(n))&&n._value;if(t==null)return this.tween(n,null);if(typeof t!="function")throw new Error;var r=Li(e);return this.tween(n,(r.local?K4:Z4)(r,t))}function Q4(e,t){return function(){wl(this,e).delay=+t.apply(this,arguments)}}function e3(e,t){return t=+t,function(){wl(this,e).delay=t}}function t3(e){var t=this._id;return arguments.length?this.each((typeof e=="function"?Q4:e3)(t,e)):Xt(this.node(),t).delay}function n3(e,t){return function(){en(this,e).duration=+t.apply(this,arguments)}}function r3(e,t){return t=+t,function(){en(this,e).duration=t}}function o3(e){var t=this._id;return arguments.length?this.each((typeof e=="function"?n3:r3)(t,e)):Xt(this.node(),t).duration}function i3(e,t){if(typeof t!="function")throw new Error;return function(){en(this,e).ease=t}}function a3(e){var t=this._id;return arguments.length?this.each(i3(t,e)):Xt(this.node(),t).ease}function s3(e,t){return function(){var n=t.apply(this,arguments);if(typeof n!="function")throw new Error;en(this,e).ease=n}}function l3(e){if(typeof e!="function")throw new Error;return this.each(s3(this._id,e))}function c3(e){typeof e!="function"&&(e=mp(e));for(var t=this._groups,n=t.length,r=new Array(n),o=0;o<n;++o)for(var i=t[o],a=i.length,s=r[o]=[],c,u=0;u<a;++u)(c=i[u])&&e.call(c,c.__data__,u,i)&&s.push(c);return new an(r,this._parents,this._name,this._id)}function u3(e){if(e._id!==this._id)throw new Error;for(var t=this._groups,n=e._groups,r=t.length,o=n.length,i=Math.min(r,o),a=new Array(r),s=0;s<i;++s)for(var c=t[s],u=n[s],l=c.length,d=a[s]=new Array(l),f,p=0;p<l;++p)(f=c[p]||u[p])&&(d[p]=f);for(;s<r;++s)a[s]=t[s];return new an(a,this._parents,this._name,this._id)}function d3(e){return(e+"").trim().split(/^|\s+/).every(function(t){var n=t.indexOf(".");return n>=0&&(t=t.slice(0,n)),!t||t==="start"})}function f3(e,t,n){var r,o,i=d3(t)?wl:en;return function(){var a=i(this,e),s=a.on;s!==r&&(o=(r=s).copy()).on(t,n),a.on=o}}function p3(e,t){var n=this._id;return arguments.length<2?Xt(this.node(),n).on.on(e):this.each(f3(n,e,t))}function h3(e){return function(){var t=this.parentNode;for(var n in this.__transition)if(+n!==e)return;t&&t.removeChild(this)}}function g3(){return this.on("end.remove",h3(this._id))}function m3(e){var t=this._name,n=this._id;typeof e!="function"&&(e=vl(e));for(var r=this._groups,o=r.length,i=new Array(o),a=0;a<o;++a)for(var s=r[a],c=s.length,u=i[a]=new Array(c),l,d,f=0;f<c;++f)(l=s[f])&&(d=e.call(l,l.__data__,f,s))&&("__data__"in l&&(d.__data__=l.__data__),u[f]=d,Fi(u[f],t,n,f,u,Xt(l,n)));return new an(i,this._parents,t,n)}function v3(e){var t=this._name,n=this._id;typeof e!="function"&&(e=gp(e));for(var r=this._groups,o=r.length,i=[],a=[],s=0;s<o;++s)for(var c=r[s],u=c.length,l,d=0;d<u;++d)if(l=c[d]){for(var f=e.call(l,l.__data__,d,c),p,g=Xt(l,n),m=0,x=f.length;m<x;++m)(p=f[m])&&Fi(p,t,n,m,f,g);i.push(f),a.push(l)}return new an(i,a,t,n)}var y3=wo.prototype.constructor;function b3(){return new y3(this._groups,this._parents)}function x3(e,t){var n,r,o;return function(){var i=ar(this,e),a=(this.style.removeProperty(e),ar(this,e));return i===a?null:i===n&&a===r?o:o=t(n=i,r=a)}}function Lp(e){return function(){this.style.removeProperty(e)}}function w3(e,t,n){var r,o=n+"",i;return function(){var a=ar(this,e);return a===o?null:a===r?i:i=t(r=a,n)}}function S3(e,t,n){var r,o,i;return function(){var a=ar(this,e),s=n(this),c=s+"";return s==null&&(c=s=(this.style.removeProperty(e),ar(this,e))),a===c?null:a===r&&c===o?i:(o=c,i=t(r=a,s))}}function C3(e,t){var n,r,o,i="style."+t,a="end."+i,s;return function(){var c=en(this,e),u=c.on,l=c.value[i]==null?s||(s=Lp(t)):void 0;(u!==n||o!==l)&&(r=(n=u).copy()).on(a,o=l),c.on=r}}function E3(e,t,n){var r=(e+="")=="transform"?E4:Bp;return t==null?this.styleTween(e,x3(e,r)).on("end.style."+e,Lp(e)):typeof t=="function"?this.styleTween(e,S3(e,r,Sl(this,"style."+e,t))).each(C3(this._id,e)):this.styleTween(e,w3(e,r,t),n).on("end.style."+e,null)}function I3(e,t,n){return function(r){this.style.setProperty(e,t.call(this,r),n)}}function D3(e,t,n){var r,o;function i(){var a=t.apply(this,arguments);return a!==o&&(r=(o=a)&&I3(e,a,n)),r}return i._value=t,i}function P3(e,t,n){var r="style."+(e+="");if(arguments.length<2)return(r=this.tween(r))&&r._value;if(t==null)return this.tween(r,null);if(typeof t!="function")throw new Error;return this.tween(r,D3(e,t,n??""))}function A3(e){return function(){this.textContent=e}}function N3(e){return function(){var t=e(this);this.textContent=t??""}}function O3(e){return this.tween("text",typeof e=="function"?N3(Sl(this,"text",e)):A3(e==null?"":e+""))}function R3(e){return function(t){this.textContent=e.call(this,t)}}function T3(e){var t,n;function r(){var o=e.apply(this,arguments);return o!==n&&(t=(n=o)&&R3(o)),t}return r._value=e,r}function j3(e){var t="text";if(arguments.length<1)return(t=this.tween(t))&&t._value;if(e==null)return this.tween(t,null);if(typeof e!="function")throw new Error;return this.tween(t,T3(e))}function M3(){for(var e=this._name,t=this._id,n=zp(),r=this._groups,o=r.length,i=0;i<o;++i)for(var a=r[i],s=a.length,c,u=0;u<s;++u)if(c=a[u]){var l=Xt(c,t);Fi(c,e,n,u,a,{time:l.time+l.delay+l.duration,delay:0,duration:l.duration,ease:l.ease})}return new an(r,this._parents,e,n)}function k3(){var e,t,n=this,r=n._id,o=n.size();return new Promise(function(i,a){var s={value:a},c={value:function(){--o===0&&i()}};n.each(function(){var u=en(this,r),l=u.on;l!==e&&(t=(e=l).copy(),t._.cancel.push(s),t._.interrupt.push(s),t._.end.push(c)),u.on=t}),o===0&&i()})}var $3=0;function an(e,t,n,r){this._groups=e,this._parents=t,this._name=n,this._id=r}function zp(){return++$3}var tn=wo.prototype;an.prototype={constructor:an,select:m3,selectAll:v3,selectChild:tn.selectChild,selectChildren:tn.selectChildren,filter:c3,merge:u3,selection:b3,transition:M3,call:tn.call,nodes:tn.nodes,node:tn.node,size:tn.size,empty:tn.empty,each:tn.each,on:p3,attr:G4,attrTween:J4,style:E3,styleTween:P3,text:O3,textTween:j3,remove:g3,tween:z4,delay:t3,duration:o3,ease:a3,easeVarying:l3,end:k3,[Symbol.iterator]:tn[Symbol.iterator]};function _3(e){return((e*=2)<=1?e*e*e:(e-=2)*e*e+2)/2}var B3={time:null,delay:0,duration:250,ease:_3};function L3(e,t){for(var n;!(n=e.__transition)||!(n=n[t]);)if(!(e=e.parentNode))throw new Error(`transition ${t} not found`);return n}function z3(e){var t,n;e instanceof an?(t=e._id,e=e._name):(t=zp(),(n=B3).time=xl(),e=e==null?null:e+"");for(var r=this._groups,o=r.length,i=0;i<o;++i)for(var a=r[i],s=a.length,c,u=0;u<s;++u)(c=a[u])&&Fi(c,e,t,u,a,n||L3(c,t));return new an(r,this._parents,e,t)}wo.prototype.interrupt=_4;wo.prototype.transition=z3;const Vo=e=>()=>e;function F3(e,{sourceEvent:t,target:n,transform:r,dispatch:o}){Object.defineProperties(this,{type:{value:e,enumerable:!0,configurable:!0},sourceEvent:{value:t,enumerable:!0,configurable:!0},target:{value:n,enumerable:!0,configurable:!0},transform:{value:r,enumerable:!0,configurable:!0},_:{value:o}})}function rn(e,t,n){this.k=e,this.x=t,this.y=n}rn.prototype={constructor:rn,scale:function(e){return e===1?this:new rn(this.k*e,this.x,this.y)},translate:function(e,t){return e===0&t===0?this:new rn(this.k,this.x+this.k*e,this.y+this.k*t)},apply:function(e){return[e[0]*this.k+this.x,e[1]*this.k+this.y]},applyX:function(e){return e*this.k+this.x},applyY:function(e){return e*this.k+this.y},invert:function(e){return[(e[0]-this.x)/this.k,(e[1]-this.y)/this.k]},invertX:function(e){return(e-this.x)/this.k},invertY:function(e){return(e-this.y)/this.k},rescaleX:function(e){return e.copy().domain(e.range().map(this.invertX,this).map(e.invert,e))},rescaleY:function(e){return e.copy().domain(e.range().map(this.invertY,this).map(e.invert,e))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var on=new rn(1,0,0);rn.prototype;function Na(e){e.stopImmediatePropagation()}function Ar(e){e.preventDefault(),e.stopImmediatePropagation()}function H3(e){return(!e.ctrlKey||e.type==="wheel")&&!e.button}function W3(){var e=this;return e instanceof SVGElement?(e=e.ownerSVGElement||e,e.hasAttribute("viewBox")?(e=e.viewBox.baseVal,[[e.x,e.y],[e.x+e.width,e.y+e.height]]):[[0,0],[e.width.baseVal.value,e.height.baseVal.value]]):[[0,0],[e.clientWidth,e.clientHeight]]}function xu(){return this.__zoom||on}function V3(e){return-e.deltaY*(e.deltaMode===1?.05:e.deltaMode?1:.002)*(e.ctrlKey?10:1)}function U3(){return navigator.maxTouchPoints||"ontouchstart"in this}function q3(e,t,n){var r=e.invertX(t[0][0])-n[0][0],o=e.invertX(t[1][0])-n[1][0],i=e.invertY(t[0][1])-n[0][1],a=e.invertY(t[1][1])-n[1][1];return e.translate(o>r?(r+o)/2:Math.min(0,r)||Math.max(0,o),a>i?(i+a)/2:Math.min(0,i)||Math.max(0,a))}function Fp(){var e=H3,t=W3,n=q3,r=V3,o=U3,i=[0,1/0],a=[[-1/0,-1/0],[1/0,1/0]],s=250,c=N4,u=Bi("start","zoom","end"),l,d,f,p=500,g=150,m=0,x=10;function w(C){C.property("__zoom",xu).on("wheel.zoom",A,{passive:!1}).on("mousedown.zoom",D).on("dblclick.zoom",k).filter(o).on("touchstart.zoom",L).on("touchmove.zoom",B).on("touchend.zoom touchcancel.zoom",j).style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}w.transform=function(C,O,N,_){var W=C.selection?C.selection():C;W.property("__zoom",xu),C!==W?b(C,O,N,_):W.interrupt().each(function(){I(this,arguments).event(_).start().zoom(null,typeof O=="function"?O.apply(this,arguments):O).end()})},w.scaleBy=function(C,O,N,_){w.scaleTo(C,function(){var W=this.__zoom.k,T=typeof O=="function"?O.apply(this,arguments):O;return W*T},N,_)},w.scaleTo=function(C,O,N,_){w.transform(C,function(){var W=t.apply(this,arguments),T=this.__zoom,H=N==null?S(W):typeof N=="function"?N.apply(this,arguments):N,R=T.invert(H),M=typeof O=="function"?O.apply(this,arguments):O;return n(E(y(T,M),H,R),W,a)},N,_)},w.translateBy=function(C,O,N,_){w.transform(C,function(){return n(this.__zoom.translate(typeof O=="function"?O.apply(this,arguments):O,typeof N=="function"?N.apply(this,arguments):N),t.apply(this,arguments),a)},null,_)},w.translateTo=function(C,O,N,_,W){w.transform(C,function(){var T=t.apply(this,arguments),H=this.__zoom,R=_==null?S(T):typeof _=="function"?_.apply(this,arguments):_;return n(on.translate(R[0],R[1]).scale(H.k).translate(typeof O=="function"?-O.apply(this,arguments):-O,typeof N=="function"?-N.apply(this,arguments):-N),T,a)},_,W)};function y(C,O){return O=Math.max(i[0],Math.min(i[1],O)),O===C.k?C:new rn(O,C.x,C.y)}function E(C,O,N){var _=O[0]-N[0]*C.k,W=O[1]-N[1]*C.k;return _===C.x&&W===C.y?C:new rn(C.k,_,W)}function S(C){return[(+C[0][0]+ +C[1][0])/2,(+C[0][1]+ +C[1][1])/2]}function b(C,O,N,_){C.on("start.zoom",function(){I(this,arguments).event(_).start()}).on("interrupt.zoom end.zoom",function(){I(this,arguments).event(_).end()}).tween("zoom",function(){var W=this,T=arguments,H=I(W,T).event(_),R=t.apply(W,T),M=N==null?S(R):typeof N=="function"?N.apply(W,T):N,U=Math.max(R[1][0]-R[0][0],R[1][1]-R[0][1]),G=W.__zoom,q=typeof O=="function"?O.apply(W,T):O,Z=c(G.invert(M).concat(U/G.k),q.invert(M).concat(U/q.k));return function(Q){if(Q===1)Q=q;else{var $=Z(Q),V=U/$[2];Q=new rn(V,M[0]-$[0]*V,M[1]-$[1]*V)}H.zoom(null,Q)}})}function I(C,O,N){return!N&&C.__zooming||new P(C,O)}function P(C,O){this.that=C,this.args=O,this.active=0,this.sourceEvent=null,this.extent=t.apply(C,O),this.taps=0}P.prototype={event:function(C){return C&&(this.sourceEvent=C),this},start:function(){return++this.active===1&&(this.that.__zooming=this,this.emit("start")),this},zoom:function(C,O){return this.mouse&&C!=="mouse"&&(this.mouse[1]=O.invert(this.mouse[0])),this.touch0&&C!=="touch"&&(this.touch0[1]=O.invert(this.touch0[0])),this.touch1&&C!=="touch"&&(this.touch1[1]=O.invert(this.touch1[0])),this.that.__zoom=O,this.emit("zoom"),this},end:function(){return--this.active===0&&(delete this.that.__zooming,this.emit("end")),this},emit:function(C){var O=kt(this.that).datum();u.call(C,this.that,new F3(C,{sourceEvent:this.sourceEvent,target:w,type:C,transform:this.that.__zoom,dispatch:u}),O)}};function A(C,...O){if(!e.apply(this,arguments))return;var N=I(this,O).event(C),_=this.__zoom,W=Math.max(i[0],Math.min(i[1],_.k*Math.pow(2,r.apply(this,arguments)))),T=Ft(C);if(N.wheel)(N.mouse[0][0]!==T[0]||N.mouse[0][1]!==T[1])&&(N.mouse[1]=_.invert(N.mouse[0]=T)),clearTimeout(N.wheel);else{if(_.k===W)return;N.mouse=[T,_.invert(T)],ti(this),N.start()}Ar(C),N.wheel=setTimeout(H,g),N.zoom("mouse",n(E(y(_,W),N.mouse[0],N.mouse[1]),N.extent,a));function H(){N.wheel=null,N.end()}}function D(C,...O){if(f||!e.apply(this,arguments))return;var N=C.currentTarget,_=I(this,O,!0).event(C),W=kt(C.view).on("mousemove.zoom",M,!0).on("mouseup.zoom",U,!0),T=Ft(C,N),H=C.clientX,R=C.clientY;Dp(C.view),Na(C),_.mouse=[T,this.__zoom.invert(T)],ti(this),_.start();function M(G){if(Ar(G),!_.moved){var q=G.clientX-H,Z=G.clientY-R;_.moved=q*q+Z*Z>m}_.event(G).zoom("mouse",n(E(_.that.__zoom,_.mouse[0]=Ft(G,N),_.mouse[1]),_.extent,a))}function U(G){W.on("mousemove.zoom mouseup.zoom",null),Pp(G.view,_.moved),Ar(G),_.event(G).end()}}function k(C,...O){if(e.apply(this,arguments)){var N=this.__zoom,_=Ft(C.changedTouches?C.changedTouches[0]:C,this),W=N.invert(_),T=N.k*(C.shiftKey?.5:2),H=n(E(y(N,T),_,W),t.apply(this,O),a);Ar(C),s>0?kt(this).transition().duration(s).call(b,H,_,C):kt(this).call(w.transform,H,_,C)}}function L(C,...O){if(e.apply(this,arguments)){var N=C.touches,_=N.length,W=I(this,O,C.changedTouches.length===_).event(C),T,H,R,M;for(Na(C),H=0;H<_;++H)R=N[H],M=Ft(R,this),M=[M,this.__zoom.invert(M),R.identifier],W.touch0?!W.touch1&&W.touch0[2]!==M[2]&&(W.touch1=M,W.taps=0):(W.touch0=M,T=!0,W.taps=1+!!l);l&&(l=clearTimeout(l)),T&&(W.taps<2&&(d=M[0],l=setTimeout(function(){l=null},p)),ti(this),W.start())}}function B(C,...O){if(this.__zooming){var N=I(this,O).event(C),_=C.changedTouches,W=_.length,T,H,R,M;for(Ar(C),T=0;T<W;++T)H=_[T],R=Ft(H,this),N.touch0&&N.touch0[2]===H.identifier?N.touch0[0]=R:N.touch1&&N.touch1[2]===H.identifier&&(N.touch1[0]=R);if(H=N.that.__zoom,N.touch1){var U=N.touch0[0],G=N.touch0[1],q=N.touch1[0],Z=N.touch1[1],Q=(Q=q[0]-U[0])*Q+(Q=q[1]-U[1])*Q,$=($=Z[0]-G[0])*$+($=Z[1]-G[1])*$;H=y(H,Math.sqrt(Q/$)),R=[(U[0]+q[0])/2,(U[1]+q[1])/2],M=[(G[0]+Z[0])/2,(G[1]+Z[1])/2]}else if(N.touch0)R=N.touch0[0],M=N.touch0[1];else return;N.zoom("touch",n(E(H,R,M),N.extent,a))}}function j(C,...O){if(this.__zooming){var N=I(this,O).event(C),_=C.changedTouches,W=_.length,T,H;for(Na(C),f&&clearTimeout(f),f=setTimeout(function(){f=null},p),T=0;T<W;++T)H=_[T],N.touch0&&N.touch0[2]===H.identifier?delete N.touch0:N.touch1&&N.touch1[2]===H.identifier&&delete N.touch1;if(N.touch1&&!N.touch0&&(N.touch0=N.touch1,delete N.touch1),N.touch0)N.touch0[1]=this.__zoom.invert(N.touch0[0]);else if(N.end(),N.taps===2&&(H=Ft(H,this),Math.hypot(d[0]-H[0],d[1]-H[1])<x)){var R=kt(this).on("dblclick.zoom");R&&R.apply(this,arguments)}}}return w.wheelDelta=function(C){return arguments.length?(r=typeof C=="function"?C:Vo(+C),w):r},w.filter=function(C){return arguments.length?(e=typeof C=="function"?C:Vo(!!C),w):e},w.touchable=function(C){return arguments.length?(o=typeof C=="function"?C:Vo(!!C),w):o},w.extent=function(C){return arguments.length?(t=typeof C=="function"?C:Vo([[+C[0][0],+C[0][1]],[+C[1][0],+C[1][1]]]),w):t},w.scaleExtent=function(C){return arguments.length?(i[0]=+C[0],i[1]=+C[1],w):[i[0],i[1]]},w.translateExtent=function(C){return arguments.length?(a[0][0]=+C[0][0],a[1][0]=+C[1][0],a[0][1]=+C[0][1],a[1][1]=+C[1][1],w):[[a[0][0],a[0][1]],[a[1][0],a[1][1]]]},w.constrain=function(C){return arguments.length?(n=C,w):n},w.duration=function(C){return arguments.length?(s=+C,w):s},w.interpolate=function(C){return arguments.length?(c=C,w):c},w.on=function(){var C=u.on.apply(u,arguments);return C===u?w:C},w.clickDistance=function(C){return arguments.length?(m=(C=+C)*C,w):Math.sqrt(m)},w.tapDistance=function(C){return arguments.length?(x=+C,w):x},w}const Hi=v.createContext(null),G3=Hi.Provider,sn={error001:()=>"[React Flow]: Seems like you have not used zustand provider as an ancestor. Help: https://reactflow.dev/error#001",error002:()=>"It looks like you've created a new nodeTypes or edgeTypes object. If this wasn't on purpose please define the nodeTypes/edgeTypes outside of the component or memoize them.",error003:e=>`Node type "${e}" not found. Using fallback type "default".`,error004:()=>"The React Flow parent container needs a width and a height to render the graph.",error005:()=>"Only child nodes can use a parent extent.",error006:()=>"Can't create edge. An edge needs a source and a target.",error007:e=>`The old edge with id=${e} does not exist.`,error009:e=>`Marker type "${e}" doesn't exist.`,error008:(e,t)=>`Couldn't create edge for ${e?"target":"source"} handle id: "${e?t.targetHandle:t.sourceHandle}", edge id: ${t.id}.`,error010:()=>"Handle: No node id found. Make sure to only use a Handle inside a custom Node.",error011:e=>`Edge type "${e}" not found. Using fallback type "default".`,error012:e=>`Node with id "${e}" does not exist, it may have been removed. This can happen when a node is deleted before the "onNodeClick" handler is called.`},Hp=sn.error001();function Oe(e,t){const n=v.useContext(Hi);if(n===null)throw new Error(Hp);return pp(n,e,t)}const We=()=>{const e=v.useContext(Hi);if(e===null)throw new Error(Hp);return v.useMemo(()=>({getState:e.getState,setState:e.setState,subscribe:e.subscribe,destroy:e.destroy}),[e])},X3=e=>e.userSelectionActive?"none":"all";function eo({position:e,children:t,className:n,style:r,...o}){const i=Oe(X3),a=`${e}`.split("-");return z.createElement("div",{className:Je(["react-flow__panel",n,...a]),style:{...r,pointerEvents:i},...o},t)}function Y3({proOptions:e,position:t="bottom-right"}){return e!=null&&e.hideAttribution?null:z.createElement(eo,{position:t,className:"react-flow__attribution","data-message":"Please only hide this attribution when you are subscribed to React Flow Pro: https://reactflow.dev/pro"},z.createElement("a",{href:"https://reactflow.dev",target:"_blank",rel:"noopener noreferrer","aria-label":"React Flow attribution"},"React Flow"))}const K3=({x:e,y:t,label:n,labelStyle:r={},labelShowBg:o=!0,labelBgStyle:i={},labelBgPadding:a=[2,4],labelBgBorderRadius:s=2,children:c,className:u,...l})=>{const d=v.useRef(null),[f,p]=v.useState({x:0,y:0,width:0,height:0}),g=Je(["react-flow__edge-textwrapper",u]);return v.useEffect(()=>{if(d.current){const m=d.current.getBBox();p({x:m.x,y:m.y,width:m.width,height:m.height})}},[n]),typeof n>"u"||!n?null:z.createElement("g",{transform:`translate(${e-f.width/2} ${t-f.height/2})`,className:g,visibility:f.width?"visible":"hidden",...l},o&&z.createElement("rect",{width:f.width+2*a[0],x:-a[0],y:-a[1],height:f.height+2*a[1],className:"react-flow__edge-textbg",style:i,rx:s,ry:s}),z.createElement("text",{className:"react-flow__edge-text",y:f.height/2,dy:"0.3em",ref:d,style:r},n),c)};var Z3=v.memo(K3);const Cl=e=>({width:e.offsetWidth,height:e.offsetHeight}),lr=(e,t=0,n=1)=>Math.min(Math.max(e,t),n),El=(e={x:0,y:0},t)=>({x:lr(e.x,t[0][0],t[1][0]),y:lr(e.y,t[0][1],t[1][1])}),wu=(e,t,n)=>e<t?lr(Math.abs(e-t),1,50)/50:e>n?-lr(Math.abs(e-n),1,50)/50:0,Wp=(e,t)=>{const n=wu(e.x,35,t.width-35)*20,r=wu(e.y,35,t.height-35)*20;return[n,r]},Vp=e=>{var t;return((t=e.getRootNode)==null?void 0:t.call(e))||(window==null?void 0:window.document)},Up=(e,t)=>({x:Math.min(e.x,t.x),y:Math.min(e.y,t.y),x2:Math.max(e.x2,t.x2),y2:Math.max(e.y2,t.y2)}),to=({x:e,y:t,width:n,height:r})=>({x:e,y:t,x2:e+n,y2:t+r}),qp=({x:e,y:t,x2:n,y2:r})=>({x:e,y:t,width:n-e,height:r-t}),Su=e=>({...e.positionAbsolute||{x:0,y:0},width:e.width||0,height:e.height||0}),J3=(e,t)=>qp(Up(to(e),to(t))),cs=(e,t)=>{const n=Math.max(0,Math.min(e.x+e.width,t.x+t.width)-Math.max(e.x,t.x)),r=Math.max(0,Math.min(e.y+e.height,t.y+t.height)-Math.max(e.y,t.y));return Math.ceil(n*r)},Q3=e=>_t(e.width)&&_t(e.height)&&_t(e.x)&&_t(e.y),_t=e=>!isNaN(e)&&isFinite(e),ke=Symbol.for("internals"),Gp=["Enter"," ","Escape"],eI=(e,t)=>{},tI=e=>"nativeEvent"in e;function us(e){var o,i;const t=tI(e)?e.nativeEvent:e,n=((i=(o=t.composedPath)==null?void 0:o.call(t))==null?void 0:i[0])||e.target;return["INPUT","SELECT","TEXTAREA"].includes(n==null?void 0:n.nodeName)||(n==null?void 0:n.hasAttribute("contenteditable"))||!!(n!=null&&n.closest(".nokey"))}const Xp=e=>"clientX"in e,xn=(e,t)=>{var i,a;const n=Xp(e),r=n?e.clientX:(i=e.touches)==null?void 0:i[0].clientX,o=n?e.clientY:(a=e.touches)==null?void 0:a[0].clientY;return{x:r-((t==null?void 0:t.left)??0),y:o-((t==null?void 0:t.top)??0)}},wi=()=>{var e;return typeof navigator<"u"&&((e=navigator==null?void 0:navigator.userAgent)==null?void 0:e.indexOf("Mac"))>=0},Co=({id:e,path:t,labelX:n,labelY:r,label:o,labelStyle:i,labelShowBg:a,labelBgStyle:s,labelBgPadding:c,labelBgBorderRadius:u,style:l,markerEnd:d,markerStart:f,interactionWidth:p=20})=>z.createElement(z.Fragment,null,z.createElement("path",{id:e,style:l,d:t,fill:"none",className:"react-flow__edge-path",markerEnd:d,markerStart:f}),p&&z.createElement("path",{d:t,fill:"none",strokeOpacity:0,strokeWidth:p,className:"react-flow__edge-interaction"}),o&&_t(n)&&_t(r)?z.createElement(Z3,{x:n,y:r,label:o,labelStyle:i,labelShowBg:a,labelBgStyle:s,labelBgPadding:c,labelBgBorderRadius:u}):null);Co.displayName="BaseEdge";function Nr(e,t,n){return n===void 0?n:r=>{const o=t().edges.find(i=>i.id===e);o&&n(r,{...o})}}function Yp({sourceX:e,sourceY:t,targetX:n,targetY:r}){const o=Math.abs(n-e)/2,i=n<e?n+o:n-o,a=Math.abs(r-t)/2,s=r<t?r+a:r-a;return[i,s,o,a]}function Kp({sourceX:e,sourceY:t,targetX:n,targetY:r,sourceControlX:o,sourceControlY:i,targetControlX:a,targetControlY:s}){const c=e*.125+o*.375+a*.375+n*.125,u=t*.125+i*.375+s*.375+r*.125,l=Math.abs(c-e),d=Math.abs(u-t);return[c,u,l,d]}var zn;(function(e){e.Strict="strict",e.Loose="loose"})(zn||(zn={}));var Tn;(function(e){e.Free="free",e.Vertical="vertical",e.Horizontal="horizontal"})(Tn||(Tn={}));var no;(function(e){e.Partial="partial",e.Full="full"})(no||(no={}));var gn;(function(e){e.Bezier="default",e.Straight="straight",e.Step="step",e.SmoothStep="smoothstep",e.SimpleBezier="simplebezier"})(gn||(gn={}));var kn;(function(e){e.Arrow="arrow",e.ArrowClosed="arrowclosed"})(kn||(kn={}));var ce;(function(e){e.Left="left",e.Top="top",e.Right="right",e.Bottom="bottom"})(ce||(ce={}));function Cu({pos:e,x1:t,y1:n,x2:r,y2:o}){return e===ce.Left||e===ce.Right?[.5*(t+r),n]:[t,.5*(n+o)]}function Zp({sourceX:e,sourceY:t,sourcePosition:n=ce.Bottom,targetX:r,targetY:o,targetPosition:i=ce.Top}){const[a,s]=Cu({pos:n,x1:e,y1:t,x2:r,y2:o}),[c,u]=Cu({pos:i,x1:r,y1:o,x2:e,y2:t}),[l,d,f,p]=Kp({sourceX:e,sourceY:t,targetX:r,targetY:o,sourceControlX:a,sourceControlY:s,targetControlX:c,targetControlY:u});return[`M${e},${t} C${a},${s} ${c},${u} ${r},${o}`,l,d,f,p]}const Il=v.memo(({sourceX:e,sourceY:t,targetX:n,targetY:r,sourcePosition:o=ce.Bottom,targetPosition:i=ce.Top,label:a,labelStyle:s,labelShowBg:c,labelBgStyle:u,labelBgPadding:l,labelBgBorderRadius:d,style:f,markerEnd:p,markerStart:g,interactionWidth:m})=>{const[x,w,y]=Zp({sourceX:e,sourceY:t,sourcePosition:o,targetX:n,targetY:r,targetPosition:i});return z.createElement(Co,{path:x,labelX:w,labelY:y,label:a,labelStyle:s,labelShowBg:c,labelBgStyle:u,labelBgPadding:l,labelBgBorderRadius:d,style:f,markerEnd:p,markerStart:g,interactionWidth:m})});Il.displayName="SimpleBezierEdge";const Eu={[ce.Left]:{x:-1,y:0},[ce.Right]:{x:1,y:0},[ce.Top]:{x:0,y:-1},[ce.Bottom]:{x:0,y:1}},nI=({source:e,sourcePosition:t=ce.Bottom,target:n})=>t===ce.Left||t===ce.Right?e.x<n.x?{x:1,y:0}:{x:-1,y:0}:e.y<n.y?{x:0,y:1}:{x:0,y:-1},Iu=(e,t)=>Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2));function rI({source:e,sourcePosition:t=ce.Bottom,target:n,targetPosition:r=ce.Top,center:o,offset:i}){const a=Eu[t],s=Eu[r],c={x:e.x+a.x*i,y:e.y+a.y*i},u={x:n.x+s.x*i,y:n.y+s.y*i},l=nI({source:c,sourcePosition:t,target:u}),d=l.x!==0?"x":"y",f=l[d];let p=[],g,m;const x={x:0,y:0},w={x:0,y:0},[y,E,S,b]=Yp({sourceX:e.x,sourceY:e.y,targetX:n.x,targetY:n.y});if(a[d]*s[d]===-1){g=o.x??y,m=o.y??E;const P=[{x:g,y:c.y},{x:g,y:u.y}],A=[{x:c.x,y:m},{x:u.x,y:m}];a[d]===f?p=d==="x"?P:A:p=d==="x"?A:P}else{const P=[{x:c.x,y:u.y}],A=[{x:u.x,y:c.y}];if(d==="x"?p=a.x===f?A:P:p=a.y===f?P:A,t===r){const j=Math.abs(e[d]-n[d]);if(j<=i){const C=Math.min(i-1,i-j);a[d]===f?x[d]=(c[d]>e[d]?-1:1)*C:w[d]=(u[d]>n[d]?-1:1)*C}}if(t!==r){const j=d==="x"?"y":"x",C=a[d]===s[j],O=c[j]>u[j],N=c[j]<u[j];(a[d]===1&&(!C&&O||C&&N)||a[d]!==1&&(!C&&N||C&&O))&&(p=d==="x"?P:A)}const D={x:c.x+x.x,y:c.y+x.y},k={x:u.x+w.x,y:u.y+w.y},L=Math.max(Math.abs(D.x-p[0].x),Math.abs(k.x-p[0].x)),B=Math.max(Math.abs(D.y-p[0].y),Math.abs(k.y-p[0].y));L>=B?(g=(D.x+k.x)/2,m=p[0].y):(g=p[0].x,m=(D.y+k.y)/2)}return[[e,{x:c.x+x.x,y:c.y+x.y},...p,{x:u.x+w.x,y:u.y+w.y},n],g,m,S,b]}function oI(e,t,n,r){const o=Math.min(Iu(e,t)/2,Iu(t,n)/2,r),{x:i,y:a}=t;if(e.x===i&&i===n.x||e.y===a&&a===n.y)return`L${i} ${a}`;if(e.y===a){const u=e.x<n.x?-1:1,l=e.y<n.y?1:-1;return`L ${i+o*u},${a}Q ${i},${a} ${i},${a+o*l}`}const s=e.x<n.x?1:-1,c=e.y<n.y?-1:1;return`L ${i},${a+o*c}Q ${i},${a} ${i+o*s},${a}`}function ds({sourceX:e,sourceY:t,sourcePosition:n=ce.Bottom,targetX:r,targetY:o,targetPosition:i=ce.Top,borderRadius:a=5,centerX:s,centerY:c,offset:u=20}){const[l,d,f,p,g]=rI({source:{x:e,y:t},sourcePosition:n,target:{x:r,y:o},targetPosition:i,center:{x:s,y:c},offset:u});return[l.reduce((x,w,y)=>{let E="";return y>0&&y<l.length-1?E=oI(l[y-1],w,l[y+1],a):E=`${y===0?"M":"L"}${w.x} ${w.y}`,x+=E,x},""),d,f,p,g]}const Wi=v.memo(({sourceX:e,sourceY:t,targetX:n,targetY:r,label:o,labelStyle:i,labelShowBg:a,labelBgStyle:s,labelBgPadding:c,labelBgBorderRadius:u,style:l,sourcePosition:d=ce.Bottom,targetPosition:f=ce.Top,markerEnd:p,markerStart:g,pathOptions:m,interactionWidth:x})=>{const[w,y,E]=ds({sourceX:e,sourceY:t,sourcePosition:d,targetX:n,targetY:r,targetPosition:f,borderRadius:m==null?void 0:m.borderRadius,offset:m==null?void 0:m.offset});return z.createElement(Co,{path:w,labelX:y,labelY:E,label:o,labelStyle:i,labelShowBg:a,labelBgStyle:s,labelBgPadding:c,labelBgBorderRadius:u,style:l,markerEnd:p,markerStart:g,interactionWidth:x})});Wi.displayName="SmoothStepEdge";const Dl=v.memo(e=>{var t;return z.createElement(Wi,{...e,pathOptions:v.useMemo(()=>{var n;return{borderRadius:0,offset:(n=e.pathOptions)==null?void 0:n.offset}},[(t=e.pathOptions)==null?void 0:t.offset])})});Dl.displayName="StepEdge";function iI({sourceX:e,sourceY:t,targetX:n,targetY:r}){const[o,i,a,s]=Yp({sourceX:e,sourceY:t,targetX:n,targetY:r});return[`M ${e},${t}L ${n},${r}`,o,i,a,s]}const Pl=v.memo(({sourceX:e,sourceY:t,targetX:n,targetY:r,label:o,labelStyle:i,labelShowBg:a,labelBgStyle:s,labelBgPadding:c,labelBgBorderRadius:u,style:l,markerEnd:d,markerStart:f,interactionWidth:p})=>{const[g,m,x]=iI({sourceX:e,sourceY:t,targetX:n,targetY:r});return z.createElement(Co,{path:g,labelX:m,labelY:x,label:o,labelStyle:i,labelShowBg:a,labelBgStyle:s,labelBgPadding:c,labelBgBorderRadius:u,style:l,markerEnd:d,markerStart:f,interactionWidth:p})});Pl.displayName="StraightEdge";function Uo(e,t){return e>=0?.5*e:t*25*Math.sqrt(-e)}function Du({pos:e,x1:t,y1:n,x2:r,y2:o,c:i}){switch(e){case ce.Left:return[t-Uo(t-r,i),n];case ce.Right:return[t+Uo(r-t,i),n];case ce.Top:return[t,n-Uo(n-o,i)];case ce.Bottom:return[t,n+Uo(o-n,i)]}}function Jp({sourceX:e,sourceY:t,sourcePosition:n=ce.Bottom,targetX:r,targetY:o,targetPosition:i=ce.Top,curvature:a=.25}){const[s,c]=Du({pos:n,x1:e,y1:t,x2:r,y2:o,c:a}),[u,l]=Du({pos:i,x1:r,y1:o,x2:e,y2:t,c:a}),[d,f,p,g]=Kp({sourceX:e,sourceY:t,targetX:r,targetY:o,sourceControlX:s,sourceControlY:c,targetControlX:u,targetControlY:l});return[`M${e},${t} C${s},${c} ${u},${l} ${r},${o}`,d,f,p,g]}const Si=v.memo(({sourceX:e,sourceY:t,targetX:n,targetY:r,sourcePosition:o=ce.Bottom,targetPosition:i=ce.Top,label:a,labelStyle:s,labelShowBg:c,labelBgStyle:u,labelBgPadding:l,labelBgBorderRadius:d,style:f,markerEnd:p,markerStart:g,pathOptions:m,interactionWidth:x})=>{const[w,y,E]=Jp({sourceX:e,sourceY:t,sourcePosition:o,targetX:n,targetY:r,targetPosition:i,curvature:m==null?void 0:m.curvature});return z.createElement(Co,{path:w,labelX:y,labelY:E,label:a,labelStyle:s,labelShowBg:c,labelBgStyle:u,labelBgPadding:l,labelBgBorderRadius:d,style:f,markerEnd:p,markerStart:g,interactionWidth:x})});Si.displayName="BezierEdge";const Al=v.createContext(null),aI=Al.Provider;Al.Consumer;const sI=()=>v.useContext(Al),lI=e=>"id"in e&&"source"in e&&"target"in e,cI=({source:e,sourceHandle:t,target:n,targetHandle:r})=>`reactflow__edge-${e}${t||""}-${n}${r||""}`,fs=(e,t)=>typeof e>"u"?"":typeof e=="string"?e:`${t?`${t}__`:""}${Object.keys(e).sort().map(r=>`${r}=${e[r]}`).join("&")}`,uI=(e,t)=>t.some(n=>n.source===e.source&&n.target===e.target&&(n.sourceHandle===e.sourceHandle||!n.sourceHandle&&!e.sourceHandle)&&(n.targetHandle===e.targetHandle||!n.targetHandle&&!e.targetHandle)),ni=(e,t)=>{if(!e.source||!e.target)return t;let n;return lI(e)?n={...e}:n={...e,id:cI(e)},uI(n,t)?t:t.concat(n)},ps=({x:e,y:t},[n,r,o],i,[a,s])=>{const c={x:(e-n)/o,y:(t-r)/o};return i?{x:a*Math.round(c.x/a),y:s*Math.round(c.y/s)}:c},Qp=({x:e,y:t},[n,r,o])=>({x:e*o+n,y:t*o+r}),$n=(e,t=[0,0])=>{if(!e)return{x:0,y:0,positionAbsolute:{x:0,y:0}};const n=(e.width??0)*t[0],r=(e.height??0)*t[1],o={x:e.position.x-n,y:e.position.y-r};return{...o,positionAbsolute:e.positionAbsolute?{x:e.positionAbsolute.x-n,y:e.positionAbsolute.y-r}:o}},Vi=(e,t=[0,0])=>{if(e.length===0)return{x:0,y:0,width:0,height:0};const n=e.reduce((r,o)=>{const{x:i,y:a}=$n(o,t).positionAbsolute;return Up(r,to({x:i,y:a,width:o.width||0,height:o.height||0}))},{x:1/0,y:1/0,x2:-1/0,y2:-1/0});return qp(n)},eh=(e,t,[n,r,o]=[0,0,1],i=!1,a=!1,s=[0,0])=>{const c={x:(t.x-n)/o,y:(t.y-r)/o,width:t.width/o,height:t.height/o},u=[];return e.forEach(l=>{const{width:d,height:f,selectable:p=!0,hidden:g=!1}=l;if(a&&!p||g)return!1;const{positionAbsolute:m}=$n(l,s),x={x:m.x,y:m.y,width:d||0,height:f||0},w=cs(c,x),y=typeof d>"u"||typeof f>"u"||d===null||f===null,E=i&&w>0,S=(d||0)*(f||0);(y||E||w>=S||l.dragging)&&u.push(l)}),u},th=(e,t)=>{const n=e.map(r=>r.id);return t.filter(r=>n.includes(r.source)||n.includes(r.target))},nh=(e,t,n,r,o,i=.1)=>{const a=t/(e.width*(1+i)),s=n/(e.height*(1+i)),c=Math.min(a,s),u=lr(c,r,o),l=e.x+e.width/2,d=e.y+e.height/2,f=t/2-l*u,p=n/2-d*u;return{x:f,y:p,zoom:u}},An=(e,t=0)=>e.transition().duration(t);function Pu(e,t,n,r){return(t[n]||[]).reduce((o,i)=>{var a,s;return`${e.id}-${i.id}-${n}`!==r&&o.push({id:i.id||null,type:n,nodeId:e.id,x:(((a=e.positionAbsolute)==null?void 0:a.x)??0)+i.x+i.width/2,y:(((s=e.positionAbsolute)==null?void 0:s.y)??0)+i.y+i.height/2}),o},[])}function dI(e,t,n,r,o,i){const{x:a,y:s}=xn(e),u=t.elementsFromPoint(a,s).find(g=>g.classList.contains("react-flow__handle"));if(u){const g=u.getAttribute("data-nodeid");if(g){const m=Nl(void 0,u),x=u.getAttribute("data-handleid"),w=i({nodeId:g,id:x,type:m});if(w){const y=o.find(E=>E.nodeId===g&&E.type===m&&E.id===x);return{handle:{id:x,type:m,nodeId:g,x:(y==null?void 0:y.x)||n.x,y:(y==null?void 0:y.y)||n.y},validHandleResult:w}}}}let l=[],d=1/0;if(o.forEach(g=>{const m=Math.sqrt((g.x-n.x)**2+(g.y-n.y)**2);if(m<=r){const x=i(g);m<=d&&(m<d?l=[{handle:g,validHandleResult:x}]:m===d&&l.push({handle:g,validHandleResult:x}),d=m)}}),!l.length)return{handle:null,validHandleResult:rh()};if(l.length===1)return l[0];const f=l.some(({validHandleResult:g})=>g.isValid),p=l.some(({handle:g})=>g.type==="target");return l.find(({handle:g,validHandleResult:m})=>p?g.type==="target":f?m.isValid:!0)||l[0]}const fI={source:null,target:null,sourceHandle:null,targetHandle:null},rh=()=>({handleDomNode:null,isValid:!1,connection:fI,endHandle:null});function oh(e,t,n,r,o,i,a){const s=o==="target",c=a.querySelector(`.react-flow__handle[data-id="${e==null?void 0:e.nodeId}-${e==null?void 0:e.id}-${e==null?void 0:e.type}"]`),u={...rh(),handleDomNode:c};if(c){const l=Nl(void 0,c),d=c.getAttribute("data-nodeid"),f=c.getAttribute("data-handleid"),p=c.classList.contains("connectable"),g=c.classList.contains("connectableend"),m={source:s?d:n,sourceHandle:s?f:r,target:s?n:d,targetHandle:s?r:f};u.connection=m,p&&g&&(t===zn.Strict?s&&l==="source"||!s&&l==="target":d!==n||f!==r)&&(u.endHandle={nodeId:d,handleId:f,type:l},u.isValid=i(m))}return u}function pI({nodes:e,nodeId:t,handleId:n,handleType:r}){return e.reduce((o,i)=>{if(i[ke]){const{handleBounds:a}=i[ke];let s=[],c=[];a&&(s=Pu(i,a,"source",`${t}-${n}-${r}`),c=Pu(i,a,"target",`${t}-${n}-${r}`)),o.push(...s,...c)}return o},[])}function Nl(e,t){return e||(t!=null&&t.classList.contains("target")?"target":t!=null&&t.classList.contains("source")?"source":null)}function Oa(e){e==null||e.classList.remove("valid","connecting","react-flow__handle-valid","react-flow__handle-connecting")}function hI(e,t){let n=null;return t?n="valid":e&&!t&&(n="invalid"),n}function ih({event:e,handleId:t,nodeId:n,onConnect:r,isTarget:o,getState:i,setState:a,isValidConnection:s,edgeUpdaterType:c,onReconnectEnd:u}){const l=Vp(e.target),{connectionMode:d,domNode:f,autoPanOnConnect:p,connectionRadius:g,onConnectStart:m,panBy:x,getNodes:w,cancelConnection:y}=i();let E=0,S;const{x:b,y:I}=xn(e),P=l==null?void 0:l.elementFromPoint(b,I),A=Nl(c,P),D=f==null?void 0:f.getBoundingClientRect();if(!D||!A)return;let k,L=xn(e,D),B=!1,j=null,C=!1,O=null;const N=pI({nodes:w(),nodeId:n,handleId:t,handleType:A}),_=()=>{if(!p)return;const[H,R]=Wp(L,D);x({x:H,y:R}),E=requestAnimationFrame(_)};a({connectionPosition:L,connectionStatus:null,connectionNodeId:n,connectionHandleId:t,connectionHandleType:A,connectionStartHandle:{nodeId:n,handleId:t,type:A},connectionEndHandle:null}),m==null||m(e,{nodeId:n,handleId:t,handleType:A});function W(H){const{transform:R}=i();L=xn(H,D);const{handle:M,validHandleResult:U}=dI(H,l,ps(L,R,!1,[1,1]),g,N,G=>oh(G,d,n,t,o?"target":"source",s,l));if(S=M,B||(_(),B=!0),O=U.handleDomNode,j=U.connection,C=U.isValid,a({connectionPosition:S&&C?Qp({x:S.x,y:S.y},R):L,connectionStatus:hI(!!S,C),connectionEndHandle:U.endHandle}),!S&&!C&&!O)return Oa(k);j.source!==j.target&&O&&(Oa(k),k=O,O.classList.add("connecting","react-flow__handle-connecting"),O.classList.toggle("valid",C),O.classList.toggle("react-flow__handle-valid",C))}function T(H){var R,M;(S||O)&&j&&C&&(r==null||r(j)),(M=(R=i()).onConnectEnd)==null||M.call(R,H),c&&(u==null||u(H)),Oa(k),y(),cancelAnimationFrame(E),B=!1,C=!1,j=null,O=null,l.removeEventListener("mousemove",W),l.removeEventListener("mouseup",T),l.removeEventListener("touchmove",W),l.removeEventListener("touchend",T)}l.addEventListener("mousemove",W),l.addEventListener("mouseup",T),l.addEventListener("touchmove",W),l.addEventListener("touchend",T)}const Au=()=>!0,gI=e=>({connectionStartHandle:e.connectionStartHandle,connectOnClick:e.connectOnClick,noPanClassName:e.noPanClassName}),mI=(e,t,n)=>r=>{const{connectionStartHandle:o,connectionEndHandle:i,connectionClickStartHandle:a}=r;return{connecting:(o==null?void 0:o.nodeId)===e&&(o==null?void 0:o.handleId)===t&&(o==null?void 0:o.type)===n||(i==null?void 0:i.nodeId)===e&&(i==null?void 0:i.handleId)===t&&(i==null?void 0:i.type)===n,clickConnecting:(a==null?void 0:a.nodeId)===e&&(a==null?void 0:a.handleId)===t&&(a==null?void 0:a.type)===n}},ah=v.forwardRef(({type:e="source",position:t=ce.Top,isValidConnection:n,isConnectable:r=!0,isConnectableStart:o=!0,isConnectableEnd:i=!0,id:a,onConnect:s,children:c,className:u,onMouseDown:l,onTouchStart:d,...f},p)=>{var D,k;const g=a||null,m=e==="target",x=We(),w=sI(),{connectOnClick:y,noPanClassName:E}=Oe(gI,Ge),{connecting:S,clickConnecting:b}=Oe(mI(w,g,e),Ge);w||(k=(D=x.getState()).onError)==null||k.call(D,"010",sn.error010());const I=L=>{const{defaultEdgeOptions:B,onConnect:j,hasDefaultEdges:C}=x.getState(),O={...B,...L};if(C){const{edges:N,setEdges:_}=x.getState();_(ni(O,N))}j==null||j(O),s==null||s(O)},P=L=>{if(!w)return;const B=Xp(L);o&&(B&&L.button===0||!B)&&ih({event:L,handleId:g,nodeId:w,onConnect:I,isTarget:m,getState:x.getState,setState:x.setState,isValidConnection:n||x.getState().isValidConnection||Au}),B?l==null||l(L):d==null||d(L)},A=L=>{const{onClickConnectStart:B,onClickConnectEnd:j,connectionClickStartHandle:C,connectionMode:O,isValidConnection:N}=x.getState();if(!w||!C&&!o)return;if(!C){B==null||B(L,{nodeId:w,handleId:g,handleType:e}),x.setState({connectionClickStartHandle:{nodeId:w,type:e,handleId:g}});return}const _=Vp(L.target),W=n||N||Au,{connection:T,isValid:H}=oh({nodeId:w,id:g,type:e},O,C.nodeId,C.handleId||null,C.type,W,_);H&&I(T),j==null||j(L),x.setState({connectionClickStartHandle:null})};return z.createElement("div",{"data-handleid":g,"data-nodeid":w,"data-handlepos":t,"data-id":`${w}-${g}-${e}`,className:Je(["react-flow__handle",`react-flow__handle-${t}`,"nodrag",E,u,{source:!m,target:m,connectable:r,connectablestart:o,connectableend:i,connecting:b,connectionindicator:r&&(o&&!S||i&&S)}]),onMouseDown:P,onTouchStart:P,onClick:y?A:void 0,ref:p,...f},c)});ah.displayName="Handle";var cr=v.memo(ah);const sh=({data:e,isConnectable:t,targetPosition:n=ce.Top,sourcePosition:r=ce.Bottom})=>z.createElement(z.Fragment,null,z.createElement(cr,{type:"target",position:n,isConnectable:t}),e==null?void 0:e.label,z.createElement(cr,{type:"source",position:r,isConnectable:t}));sh.displayName="DefaultNode";var hs=v.memo(sh);const lh=({data:e,isConnectable:t,sourcePosition:n=ce.Bottom})=>z.createElement(z.Fragment,null,e==null?void 0:e.label,z.createElement(cr,{type:"source",position:n,isConnectable:t}));lh.displayName="InputNode";var ch=v.memo(lh);const uh=({data:e,isConnectable:t,targetPosition:n=ce.Top})=>z.createElement(z.Fragment,null,z.createElement(cr,{type:"target",position:n,isConnectable:t}),e==null?void 0:e.label);uh.displayName="OutputNode";var dh=v.memo(uh);const Ol=()=>null;Ol.displayName="GroupNode";const vI=e=>({selectedNodes:e.getNodes().filter(t=>t.selected),selectedEdges:e.edges.filter(t=>t.selected).map(t=>({...t}))}),qo=e=>e.id;function yI(e,t){return Ge(e.selectedNodes.map(qo),t.selectedNodes.map(qo))&&Ge(e.selectedEdges.map(qo),t.selectedEdges.map(qo))}const fh=v.memo(({onSelectionChange:e})=>{const t=We(),{selectedNodes:n,selectedEdges:r}=Oe(vI,yI);return v.useEffect(()=>{const o={nodes:n,edges:r};e==null||e(o),t.getState().onSelectionChange.forEach(i=>i(o))},[n,r,e]),null});fh.displayName="SelectionListener";const bI=e=>!!e.onSelectionChange;function xI({onSelectionChange:e}){const t=Oe(bI);return e||t?z.createElement(fh,{onSelectionChange:e}):null}const wI=e=>({setNodes:e.setNodes,setEdges:e.setEdges,setDefaultNodesAndEdges:e.setDefaultNodesAndEdges,setMinZoom:e.setMinZoom,setMaxZoom:e.setMaxZoom,setTranslateExtent:e.setTranslateExtent,setNodeExtent:e.setNodeExtent,reset:e.reset});function Vn(e,t){v.useEffect(()=>{typeof e<"u"&&t(e)},[e])}function ge(e,t,n){v.useEffect(()=>{typeof t<"u"&&n({[e]:t})},[t])}const SI=({nodes:e,edges:t,defaultNodes:n,defaultEdges:r,onConnect:o,onConnectStart:i,onConnectEnd:a,onClickConnectStart:s,onClickConnectEnd:c,nodesDraggable:u,nodesConnectable:l,nodesFocusable:d,edgesFocusable:f,edgesUpdatable:p,elevateNodesOnSelect:g,minZoom:m,maxZoom:x,nodeExtent:w,onNodesChange:y,onEdgesChange:E,elementsSelectable:S,connectionMode:b,snapGrid:I,snapToGrid:P,translateExtent:A,connectOnClick:D,defaultEdgeOptions:k,fitView:L,fitViewOptions:B,onNodesDelete:j,onEdgesDelete:C,onNodeDrag:O,onNodeDragStart:N,onNodeDragStop:_,onSelectionDrag:W,onSelectionDragStart:T,onSelectionDragStop:H,noPanClassName:R,nodeOrigin:M,rfId:U,autoPanOnConnect:G,autoPanOnNodeDrag:q,onError:Z,connectionRadius:Q,isValidConnection:$,nodeDragThreshold:V})=>{const{setNodes:X,setEdges:ne,setDefaultNodesAndEdges:F,setMinZoom:Y,setMaxZoom:ee,setTranslateExtent:oe,setNodeExtent:ae,reset:re}=Oe(wI,Ge),K=We();return v.useEffect(()=>{const se=r==null?void 0:r.map(me=>({...me,...k}));return F(n,se),()=>{re()}},[]),ge("defaultEdgeOptions",k,K.setState),ge("connectionMode",b,K.setState),ge("onConnect",o,K.setState),ge("onConnectStart",i,K.setState),ge("onConnectEnd",a,K.setState),ge("onClickConnectStart",s,K.setState),ge("onClickConnectEnd",c,K.setState),ge("nodesDraggable",u,K.setState),ge("nodesConnectable",l,K.setState),ge("nodesFocusable",d,K.setState),ge("edgesFocusable",f,K.setState),ge("edgesUpdatable",p,K.setState),ge("elementsSelectable",S,K.setState),ge("elevateNodesOnSelect",g,K.setState),ge("snapToGrid",P,K.setState),ge("snapGrid",I,K.setState),ge("onNodesChange",y,K.setState),ge("onEdgesChange",E,K.setState),ge("connectOnClick",D,K.setState),ge("fitViewOnInit",L,K.setState),ge("fitViewOnInitOptions",B,K.setState),ge("onNodesDelete",j,K.setState),ge("onEdgesDelete",C,K.setState),ge("onNodeDrag",O,K.setState),ge("onNodeDragStart",N,K.setState),ge("onNodeDragStop",_,K.setState),ge("onSelectionDrag",W,K.setState),ge("onSelectionDragStart",T,K.setState),ge("onSelectionDragStop",H,K.setState),ge("noPanClassName",R,K.setState),ge("nodeOrigin",M,K.setState),ge("rfId",U,K.setState),ge("autoPanOnConnect",G,K.setState),ge("autoPanOnNodeDrag",q,K.setState),ge("onError",Z,K.setState),ge("connectionRadius",Q,K.setState),ge("isValidConnection",$,K.setState),ge("nodeDragThreshold",V,K.setState),Vn(e,X),Vn(t,ne),Vn(m,Y),Vn(x,ee),Vn(A,oe),Vn(w,ae),null},Nu={display:"none"},CI={position:"absolute",width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0px, 0px, 0px, 0px)",clipPath:"inset(100%)"},ph="react-flow__node-desc",hh="react-flow__edge-desc",EI="react-flow__aria-live",II=e=>e.ariaLiveMessage;function DI({rfId:e}){const t=Oe(II);return z.createElement("div",{id:`${EI}-${e}`,"aria-live":"assertive","aria-atomic":"true",style:CI},t)}function PI({rfId:e,disableKeyboardA11y:t}){return z.createElement(z.Fragment,null,z.createElement("div",{id:`${ph}-${e}`,style:Nu},"Press enter or space to select a node.",!t&&"You can then use the arrow keys to move the node around."," Press delete to remove it and escape to cancel."," "),z.createElement("div",{id:`${hh}-${e}`,style:Nu},"Press enter or space to select an edge. You can then press delete to remove it or escape to cancel."),!t&&z.createElement(DI,{rfId:e}))}var ro=(e=null,t={actInsideInputWithModifier:!0})=>{const[n,r]=v.useState(!1),o=v.useRef(!1),i=v.useRef(new Set([])),[a,s]=v.useMemo(()=>{if(e!==null){const u=(Array.isArray(e)?e:[e]).filter(d=>typeof d=="string").map(d=>d.split("+")),l=u.reduce((d,f)=>d.concat(...f),[]);return[u,l]}return[[],[]]},[e]);return v.useEffect(()=>{const c=typeof document<"u"?document:null,u=(t==null?void 0:t.target)||c;if(e!==null){const l=p=>{if(o.current=p.ctrlKey||p.metaKey||p.shiftKey,(!o.current||o.current&&!t.actInsideInputWithModifier)&&us(p))return!1;const m=Ru(p.code,s);i.current.add(p[m]),Ou(a,i.current,!1)&&(p.preventDefault(),r(!0))},d=p=>{if((!o.current||o.current&&!t.actInsideInputWithModifier)&&us(p))return!1;const m=Ru(p.code,s);Ou(a,i.current,!0)?(r(!1),i.current.clear()):i.current.delete(p[m]),p.key==="Meta"&&i.current.clear(),o.current=!1},f=()=>{i.current.clear(),r(!1)};return u==null||u.addEventListener("keydown",l),u==null||u.addEventListener("keyup",d),window.addEventListener("blur",f),()=>{u==null||u.removeEventListener("keydown",l),u==null||u.removeEventListener("keyup",d),window.removeEventListener("blur",f)}}},[e,r]),n};function Ou(e,t,n){return e.filter(r=>n||r.length===t.size).some(r=>r.every(o=>t.has(o)))}function Ru(e,t){return t.includes(e)?"code":"key"}function gh(e,t,n,r){var s,c;const o=e.parentNode||e.parentId;if(!o)return n;const i=t.get(o),a=$n(i,r);return gh(i,t,{x:(n.x??0)+a.x,y:(n.y??0)+a.y,z:(((s=i[ke])==null?void 0:s.z)??0)>(n.z??0)?((c=i[ke])==null?void 0:c.z)??0:n.z??0},r)}function mh(e,t,n){e.forEach(r=>{var i;const o=r.parentNode||r.parentId;if(o&&!e.has(o))throw new Error(`Parent node ${o} not found`);if(o||n!=null&&n[r.id]){const{x:a,y:s,z:c}=gh(r,e,{...r.position,z:((i=r[ke])==null?void 0:i.z)??0},t);r.positionAbsolute={x:a,y:s},r[ke].z=c,n!=null&&n[r.id]&&(r[ke].isParent=!0)}})}function Ra(e,t,n,r){const o=new Map,i={},a=r?1e3:0;return e.forEach(s=>{var p;const c=(_t(s.zIndex)?s.zIndex:0)+(s.selected?a:0),u=t.get(s.id),l={...s,positionAbsolute:{x:s.position.x,y:s.position.y}},d=s.parentNode||s.parentId;d&&(i[d]=!0);const f=(u==null?void 0:u.type)&&(u==null?void 0:u.type)!==s.type;Object.defineProperty(l,ke,{enumerable:!1,value:{handleBounds:f||(p=u==null?void 0:u[ke])==null?void 0:p.handleBounds,z:c}}),o.set(s.id,l)}),mh(o,n,i),o}function vh(e,t={}){const{getNodes:n,width:r,height:o,minZoom:i,maxZoom:a,d3Zoom:s,d3Selection:c,fitViewOnInitDone:u,fitViewOnInit:l,nodeOrigin:d}=e(),f=t.initial&&!u&&l;if(s&&c&&(f||!t.initial)){const g=n().filter(x=>{var y;const w=t.includeHiddenNodes?x.width&&x.height:!x.hidden;return(y=t.nodes)!=null&&y.length?w&&t.nodes.some(E=>E.id===x.id):w}),m=g.every(x=>x.width&&x.height);if(g.length>0&&m){const x=Vi(g,d),{x:w,y,zoom:E}=nh(x,r,o,t.minZoom??i,t.maxZoom??a,t.padding??.1),S=on.translate(w,y).scale(E);return typeof t.duration=="number"&&t.duration>0?s.transform(An(c,t.duration),S):s.transform(c,S),!0}}return!1}function AI(e,t){return e.forEach(n=>{const r=t.get(n.id);r&&t.set(r.id,{...r,[ke]:r[ke],selected:n.selected})}),new Map(t)}function NI(e,t){return t.map(n=>{const r=e.find(o=>o.id===n.id);return r&&(n.selected=r.selected),n})}function Go({changedNodes:e,changedEdges:t,get:n,set:r}){const{nodeInternals:o,edges:i,onNodesChange:a,onEdgesChange:s,hasDefaultNodes:c,hasDefaultEdges:u}=n();e!=null&&e.length&&(c&&r({nodeInternals:AI(e,o)}),a==null||a(e)),t!=null&&t.length&&(u&&r({edges:NI(t,i)}),s==null||s(t))}const Un=()=>{},OI={zoomIn:Un,zoomOut:Un,zoomTo:Un,getZoom:()=>1,setViewport:Un,getViewport:()=>({x:0,y:0,zoom:1}),fitView:()=>!1,setCenter:Un,fitBounds:Un,project:e=>e,screenToFlowPosition:e=>e,flowToScreenPosition:e=>e,viewportInitialized:!1},RI=e=>({d3Zoom:e.d3Zoom,d3Selection:e.d3Selection}),TI=()=>{const e=We(),{d3Zoom:t,d3Selection:n}=Oe(RI,Ge);return v.useMemo(()=>n&&t?{zoomIn:o=>t.scaleBy(An(n,o==null?void 0:o.duration),1.2),zoomOut:o=>t.scaleBy(An(n,o==null?void 0:o.duration),1/1.2),zoomTo:(o,i)=>t.scaleTo(An(n,i==null?void 0:i.duration),o),getZoom:()=>e.getState().transform[2],setViewport:(o,i)=>{const[a,s,c]=e.getState().transform,u=on.translate(o.x??a,o.y??s).scale(o.zoom??c);t.transform(An(n,i==null?void 0:i.duration),u)},getViewport:()=>{const[o,i,a]=e.getState().transform;return{x:o,y:i,zoom:a}},fitView:o=>vh(e.getState,o),setCenter:(o,i,a)=>{const{width:s,height:c,maxZoom:u}=e.getState(),l=typeof(a==null?void 0:a.zoom)<"u"?a.zoom:u,d=s/2-o*l,f=c/2-i*l,p=on.translate(d,f).scale(l);t.transform(An(n,a==null?void 0:a.duration),p)},fitBounds:(o,i)=>{const{width:a,height:s,minZoom:c,maxZoom:u}=e.getState(),{x:l,y:d,zoom:f}=nh(o,a,s,c,u,(i==null?void 0:i.padding)??.1),p=on.translate(l,d).scale(f);t.transform(An(n,i==null?void 0:i.duration),p)},project:o=>{const{transform:i,snapToGrid:a,snapGrid:s}=e.getState();return console.warn("[DEPRECATED] `project` is deprecated. Instead use `screenToFlowPosition`. There is no need to subtract the react flow bounds anymore! https://reactflow.dev/api-reference/types/react-flow-instance#screen-to-flow-position"),ps(o,i,a,s)},screenToFlowPosition:o=>{const{transform:i,snapToGrid:a,snapGrid:s,domNode:c}=e.getState();if(!c)return o;const{x:u,y:l}=c.getBoundingClientRect(),d={x:o.x-u,y:o.y-l};return ps(d,i,a,s)},flowToScreenPosition:o=>{const{transform:i,domNode:a}=e.getState();if(!a)return o;const{x:s,y:c}=a.getBoundingClientRect(),u=Qp(o,i);return{x:u.x+s,y:u.y+c}},viewportInitialized:!0}:OI,[t,n])};function Rl(){const e=TI(),t=We(),n=v.useCallback(()=>t.getState().getNodes().map(m=>({...m})),[]),r=v.useCallback(m=>t.getState().nodeInternals.get(m),[]),o=v.useCallback(()=>{const{edges:m=[]}=t.getState();return m.map(x=>({...x}))},[]),i=v.useCallback(m=>{const{edges:x=[]}=t.getState();return x.find(w=>w.id===m)},[]),a=v.useCallback(m=>{const{getNodes:x,setNodes:w,hasDefaultNodes:y,onNodesChange:E}=t.getState(),S=x(),b=typeof m=="function"?m(S):m;if(y)w(b);else if(E){const I=b.length===0?S.map(P=>({type:"remove",id:P.id})):b.map(P=>({item:P,type:"reset"}));E(I)}},[]),s=v.useCallback(m=>{const{edges:x=[],setEdges:w,hasDefaultEdges:y,onEdgesChange:E}=t.getState(),S=typeof m=="function"?m(x):m;if(y)w(S);else if(E){const b=S.length===0?x.map(I=>({type:"remove",id:I.id})):S.map(I=>({item:I,type:"reset"}));E(b)}},[]),c=v.useCallback(m=>{const x=Array.isArray(m)?m:[m],{getNodes:w,setNodes:y,hasDefaultNodes:E,onNodesChange:S}=t.getState();if(E){const I=[...w(),...x];y(I)}else if(S){const b=x.map(I=>({item:I,type:"add"}));S(b)}},[]),u=v.useCallback(m=>{const x=Array.isArray(m)?m:[m],{edges:w=[],setEdges:y,hasDefaultEdges:E,onEdgesChange:S}=t.getState();if(E)y([...w,...x]);else if(S){const b=x.map(I=>({item:I,type:"add"}));S(b)}},[]),l=v.useCallback(()=>{const{getNodes:m,edges:x=[],transform:w}=t.getState(),[y,E,S]=w;return{nodes:m().map(b=>({...b})),edges:x.map(b=>({...b})),viewport:{x:y,y:E,zoom:S}}},[]),d=v.useCallback(({nodes:m,edges:x})=>{const{nodeInternals:w,getNodes:y,edges:E,hasDefaultNodes:S,hasDefaultEdges:b,onNodesDelete:I,onEdgesDelete:P,onNodesChange:A,onEdgesChange:D}=t.getState(),k=(m||[]).map(O=>O.id),L=(x||[]).map(O=>O.id),B=y().reduce((O,N)=>{const _=N.parentNode||N.parentId,W=!k.includes(N.id)&&_&&O.find(H=>H.id===_);return(typeof N.deletable=="boolean"?N.deletable:!0)&&(k.includes(N.id)||W)&&O.push(N),O},[]),j=E.filter(O=>typeof O.deletable=="boolean"?O.deletable:!0),C=j.filter(O=>L.includes(O.id));if(B||C){const O=th(B,j),N=[...C,...O],_=N.reduce((W,T)=>(W.includes(T.id)||W.push(T.id),W),[]);if((b||S)&&(b&&t.setState({edges:E.filter(W=>!_.includes(W.id))}),S&&(B.forEach(W=>{w.delete(W.id)}),t.setState({nodeInternals:new Map(w)}))),_.length>0&&(P==null||P(N),D&&D(_.map(W=>({id:W,type:"remove"})))),B.length>0&&(I==null||I(B),A)){const W=B.map(T=>({id:T.id,type:"remove"}));A(W)}}},[]),f=v.useCallback(m=>{const x=Q3(m),w=x?null:t.getState().nodeInternals.get(m.id);return!x&&!w?[null,null,x]:[x?m:Su(w),w,x]},[]),p=v.useCallback((m,x=!0,w)=>{const[y,E,S]=f(m);return y?(w||t.getState().getNodes()).filter(b=>{if(!S&&(b.id===E.id||!b.positionAbsolute))return!1;const I=Su(b),P=cs(I,y);return x&&P>0||P>=y.width*y.height}):[]},[]),g=v.useCallback((m,x,w=!0)=>{const[y]=f(m);if(!y)return!1;const E=cs(y,x);return w&&E>0||E>=y.width*y.height},[]);return v.useMemo(()=>({...e,getNodes:n,getNode:r,getEdges:o,getEdge:i,setNodes:a,setEdges:s,addNodes:c,addEdges:u,toObject:l,deleteElements:d,getIntersectingNodes:p,isNodeIntersecting:g}),[e,n,r,o,i,a,s,c,u,l,d,p,g])}const jI={actInsideInputWithModifier:!1};var MI=({deleteKeyCode:e,multiSelectionKeyCode:t})=>{const n=We(),{deleteElements:r}=Rl(),o=ro(e,jI),i=ro(t);v.useEffect(()=>{if(o){const{edges:a,getNodes:s}=n.getState(),c=s().filter(l=>l.selected),u=a.filter(l=>l.selected);r({nodes:c,edges:u}),n.setState({nodesSelectionActive:!1})}},[o]),v.useEffect(()=>{n.setState({multiSelectionActive:i})},[i])};function kI(e){const t=We();v.useEffect(()=>{let n;const r=()=>{var i,a;if(!e.current)return;const o=Cl(e.current);(o.height===0||o.width===0)&&((a=(i=t.getState()).onError)==null||a.call(i,"004",sn.error004())),t.setState({width:o.width||500,height:o.height||500})};return r(),window.addEventListener("resize",r),e.current&&(n=new ResizeObserver(()=>r()),n.observe(e.current)),()=>{window.removeEventListener("resize",r),n&&e.current&&n.unobserve(e.current)}},[])}const Tl={position:"absolute",width:"100%",height:"100%",top:0,left:0},$I=(e,t)=>e.x!==t.x||e.y!==t.y||e.zoom!==t.k,Xo=e=>({x:e.x,y:e.y,zoom:e.k}),qn=(e,t)=>e.target.closest(`.${t}`),Tu=(e,t)=>t===2&&Array.isArray(e)&&e.includes(2),ju=e=>{const t=e.ctrlKey&&wi()?10:1;return-e.deltaY*(e.deltaMode===1?.05:e.deltaMode?1:.002)*t},_I=e=>({d3Zoom:e.d3Zoom,d3Selection:e.d3Selection,d3ZoomHandler:e.d3ZoomHandler,userSelectionActive:e.userSelectionActive}),BI=({onMove:e,onMoveStart:t,onMoveEnd:n,onPaneContextMenu:r,zoomOnScroll:o=!0,zoomOnPinch:i=!0,panOnScroll:a=!1,panOnScrollSpeed:s=.5,panOnScrollMode:c=Tn.Free,zoomOnDoubleClick:u=!0,elementsSelectable:l,panOnDrag:d=!0,defaultViewport:f,translateExtent:p,minZoom:g,maxZoom:m,zoomActivationKeyCode:x,preventScrolling:w=!0,children:y,noWheelClassName:E,noPanClassName:S})=>{const b=v.useRef(),I=We(),P=v.useRef(!1),A=v.useRef(!1),D=v.useRef(null),k=v.useRef({x:0,y:0,zoom:0}),{d3Zoom:L,d3Selection:B,d3ZoomHandler:j,userSelectionActive:C}=Oe(_I,Ge),O=ro(x),N=v.useRef(0),_=v.useRef(!1),W=v.useRef();return kI(D),v.useEffect(()=>{if(D.current){const T=D.current.getBoundingClientRect(),H=Fp().scaleExtent([g,m]).translateExtent(p),R=kt(D.current).call(H),M=on.translate(f.x,f.y).scale(lr(f.zoom,g,m)),U=[[0,0],[T.width,T.height]],G=H.constrain()(M,U,p);H.transform(R,G),H.wheelDelta(ju),I.setState({d3Zoom:H,d3Selection:R,d3ZoomHandler:R.on("wheel.zoom"),transform:[G.x,G.y,G.k],domNode:D.current.closest(".react-flow")})}},[]),v.useEffect(()=>{B&&L&&(a&&!O&&!C?B.on("wheel.zoom",T=>{if(qn(T,E))return!1;T.preventDefault(),T.stopImmediatePropagation();const H=B.property("__zoom").k||1;if(T.ctrlKey&&i){const $=Ft(T),V=ju(T),X=H*Math.pow(2,V);L.scaleTo(B,X,$,T);return}const R=T.deltaMode===1?20:1;let M=c===Tn.Vertical?0:T.deltaX*R,U=c===Tn.Horizontal?0:T.deltaY*R;!wi()&&T.shiftKey&&c!==Tn.Vertical&&(M=T.deltaY*R,U=0),L.translateBy(B,-(M/H)*s,-(U/H)*s,{internal:!0});const G=Xo(B.property("__zoom")),{onViewportChangeStart:q,onViewportChange:Z,onViewportChangeEnd:Q}=I.getState();clearTimeout(W.current),_.current||(_.current=!0,t==null||t(T,G),q==null||q(G)),_.current&&(e==null||e(T,G),Z==null||Z(G),W.current=setTimeout(()=>{n==null||n(T,G),Q==null||Q(G),_.current=!1},150))},{passive:!1}):typeof j<"u"&&B.on("wheel.zoom",function(T,H){if(!w&&T.type==="wheel"&&!T.ctrlKey||qn(T,E))return null;T.preventDefault(),j.call(this,T,H)},{passive:!1}))},[C,a,c,B,L,j,O,i,w,E,t,e,n]),v.useEffect(()=>{L&&L.on("start",T=>{var M,U;if(!T.sourceEvent||T.sourceEvent.internal)return null;N.current=(M=T.sourceEvent)==null?void 0:M.button;const{onViewportChangeStart:H}=I.getState(),R=Xo(T.transform);P.current=!0,k.current=R,((U=T.sourceEvent)==null?void 0:U.type)==="mousedown"&&I.setState({paneDragging:!0}),H==null||H(R),t==null||t(T.sourceEvent,R)})},[L,t]),v.useEffect(()=>{L&&(C&&!P.current?L.on("zoom",null):C||L.on("zoom",T=>{var R;const{onViewportChange:H}=I.getState();if(I.setState({transform:[T.transform.x,T.transform.y,T.transform.k]}),A.current=!!(r&&Tu(d,N.current??0)),(e||H)&&!((R=T.sourceEvent)!=null&&R.internal)){const M=Xo(T.transform);H==null||H(M),e==null||e(T.sourceEvent,M)}}))},[C,L,e,d,r]),v.useEffect(()=>{L&&L.on("end",T=>{if(!T.sourceEvent||T.sourceEvent.internal)return null;const{onViewportChangeEnd:H}=I.getState();if(P.current=!1,I.setState({paneDragging:!1}),r&&Tu(d,N.current??0)&&!A.current&&r(T.sourceEvent),A.current=!1,(n||H)&&$I(k.current,T.transform)){const R=Xo(T.transform);k.current=R,clearTimeout(b.current),b.current=setTimeout(()=>{H==null||H(R),n==null||n(T.sourceEvent,R)},a?150:0)}})},[L,a,d,n,r]),v.useEffect(()=>{L&&L.filter(T=>{const H=O||o,R=i&&T.ctrlKey;if((d===!0||Array.isArray(d)&&d.includes(1))&&T.button===1&&T.type==="mousedown"&&(qn(T,"react-flow__node")||qn(T,"react-flow__edge")))return!0;if(!d&&!H&&!a&&!u&&!i||C||!u&&T.type==="dblclick"||qn(T,E)&&T.type==="wheel"||qn(T,S)&&(T.type!=="wheel"||a&&T.type==="wheel"&&!O)||!i&&T.ctrlKey&&T.type==="wheel"||!H&&!a&&!R&&T.type==="wheel"||!d&&(T.type==="mousedown"||T.type==="touchstart")||Array.isArray(d)&&!d.includes(T.button)&&T.type==="mousedown")return!1;const M=Array.isArray(d)&&d.includes(T.button)||!T.button||T.button<=1;return(!T.ctrlKey||T.type==="wheel")&&M})},[C,L,o,i,a,u,d,l,O]),z.createElement("div",{className:"react-flow__renderer",ref:D,style:Tl},y)},LI=e=>({userSelectionActive:e.userSelectionActive,userSelectionRect:e.userSelectionRect});function zI(){const{userSelectionActive:e,userSelectionRect:t}=Oe(LI,Ge);return e&&t?z.createElement("div",{className:"react-flow__selection react-flow__container",style:{width:t.width,height:t.height,transform:`translate(${t.x}px, ${t.y}px)`}}):null}function Mu(e,t){const n=t.parentNode||t.parentId,r=e.find(o=>o.id===n);if(r){const o=t.position.x+t.width-r.width,i=t.position.y+t.height-r.height;if(o>0||i>0||t.position.x<0||t.position.y<0){if(r.style={...r.style},r.style.width=r.style.width??r.width,r.style.height=r.style.height??r.height,o>0&&(r.style.width+=o),i>0&&(r.style.height+=i),t.position.x<0){const a=Math.abs(t.position.x);r.position.x=r.position.x-a,r.style.width+=a,t.position.x=0}if(t.position.y<0){const a=Math.abs(t.position.y);r.position.y=r.position.y-a,r.style.height+=a,t.position.y=0}r.width=r.style.width,r.height=r.style.height}}}function yh(e,t){if(e.some(r=>r.type==="reset"))return e.filter(r=>r.type==="reset").map(r=>r.item);const n=e.filter(r=>r.type==="add").map(r=>r.item);return t.reduce((r,o)=>{const i=e.filter(s=>s.id===o.id);if(i.length===0)return r.push(o),r;const a={...o};for(const s of i)if(s)switch(s.type){case"select":{a.selected=s.selected;break}case"position":{typeof s.position<"u"&&(a.position=s.position),typeof s.positionAbsolute<"u"&&(a.positionAbsolute=s.positionAbsolute),typeof s.dragging<"u"&&(a.dragging=s.dragging),a.expandParent&&Mu(r,a);break}case"dimensions":{typeof s.dimensions<"u"&&(a.width=s.dimensions.width,a.height=s.dimensions.height),typeof s.updateStyle<"u"&&(a.style={...a.style||{},...s.dimensions}),typeof s.resizing=="boolean"&&(a.resizing=s.resizing),a.expandParent&&Mu(r,a);break}case"remove":return r}return r.push(a),r},n)}function bh(e,t){return yh(e,t)}function FI(e,t){return yh(e,t)}const hn=(e,t)=>({id:e,type:"select",selected:t});function Yn(e,t){return e.reduce((n,r)=>{const o=t.includes(r.id);return!r.selected&&o?(r.selected=!0,n.push(hn(r.id,!0))):r.selected&&!o&&(r.selected=!1,n.push(hn(r.id,!1))),n},[])}const Ta=(e,t)=>n=>{n.target===t.current&&(e==null||e(n))},HI=e=>({userSelectionActive:e.userSelectionActive,elementsSelectable:e.elementsSelectable,dragging:e.paneDragging}),xh=v.memo(({isSelecting:e,selectionMode:t=no.Full,panOnDrag:n,onSelectionStart:r,onSelectionEnd:o,onPaneClick:i,onPaneContextMenu:a,onPaneScroll:s,onPaneMouseEnter:c,onPaneMouseMove:u,onPaneMouseLeave:l,children:d})=>{const f=v.useRef(null),p=We(),g=v.useRef(0),m=v.useRef(0),x=v.useRef(),{userSelectionActive:w,elementsSelectable:y,dragging:E}=Oe(HI,Ge),S=()=>{p.setState({userSelectionActive:!1,userSelectionRect:null}),g.current=0,m.current=0},b=j=>{i==null||i(j),p.getState().resetSelectedElements(),p.setState({nodesSelectionActive:!1})},I=j=>{if(Array.isArray(n)&&(n!=null&&n.includes(2))){j.preventDefault();return}a==null||a(j)},P=s?j=>s(j):void 0,A=j=>{const{resetSelectedElements:C,domNode:O}=p.getState();if(x.current=O==null?void 0:O.getBoundingClientRect(),!y||!e||j.button!==0||j.target!==f.current||!x.current)return;const{x:N,y:_}=xn(j,x.current);C(),p.setState({userSelectionRect:{width:0,height:0,startX:N,startY:_,x:N,y:_}}),r==null||r(j)},D=j=>{const{userSelectionRect:C,nodeInternals:O,edges:N,transform:_,onNodesChange:W,onEdgesChange:T,nodeOrigin:H,getNodes:R}=p.getState();if(!e||!x.current||!C)return;p.setState({userSelectionActive:!0,nodesSelectionActive:!1});const M=xn(j,x.current),U=C.startX??0,G=C.startY??0,q={...C,x:M.x<U?M.x:U,y:M.y<G?M.y:G,width:Math.abs(M.x-U),height:Math.abs(M.y-G)},Z=R(),Q=eh(O,q,_,t===no.Partial,!0,H),$=th(Q,N).map(X=>X.id),V=Q.map(X=>X.id);if(g.current!==V.length){g.current=V.length;const X=Yn(Z,V);X.length&&(W==null||W(X))}if(m.current!==$.length){m.current=$.length;const X=Yn(N,$);X.length&&(T==null||T(X))}p.setState({userSelectionRect:q})},k=j=>{if(j.button!==0)return;const{userSelectionRect:C}=p.getState();!w&&C&&j.target===f.current&&(b==null||b(j)),p.setState({nodesSelectionActive:g.current>0}),S(),o==null||o(j)},L=j=>{w&&(p.setState({nodesSelectionActive:g.current>0}),o==null||o(j)),S()},B=y&&(e||w);return z.createElement("div",{className:Je(["react-flow__pane",{dragging:E,selection:e}]),onClick:B?void 0:Ta(b,f),onContextMenu:Ta(I,f),onWheel:Ta(P,f),onMouseEnter:B?void 0:c,onMouseDown:B?A:void 0,onMouseMove:B?D:u,onMouseUp:B?k:void 0,onMouseLeave:B?L:l,ref:f,style:Tl},d,z.createElement(zI,null))});xh.displayName="Pane";function wh(e,t){const n=e.parentNode||e.parentId;if(!n)return!1;const r=t.get(n);return r?r.selected?!0:wh(r,t):!1}function ku(e,t,n){let r=e;do{if(r!=null&&r.matches(t))return!0;if(r===n.current)return!1;r=r.parentElement}while(r);return!1}function WI(e,t,n,r){return Array.from(e.values()).filter(o=>(o.selected||o.id===r)&&(!o.parentNode||o.parentId||!wh(o,e))&&(o.draggable||t&&typeof o.draggable>"u")).map(o=>{var i,a;return{id:o.id,position:o.position||{x:0,y:0},positionAbsolute:o.positionAbsolute||{x:0,y:0},distance:{x:n.x-(((i=o.positionAbsolute)==null?void 0:i.x)??0),y:n.y-(((a=o.positionAbsolute)==null?void 0:a.y)??0)},delta:{x:0,y:0},extent:o.extent,parentNode:o.parentNode||o.parentId,parentId:o.parentNode||o.parentId,width:o.width,height:o.height,expandParent:o.expandParent}})}function VI(e,t){return!t||t==="parent"?t:[t[0],[t[1][0]-(e.width||0),t[1][1]-(e.height||0)]]}function Sh(e,t,n,r,o=[0,0],i){const a=VI(e,e.extent||r);let s=a;const c=e.parentNode||e.parentId;if(e.extent==="parent"&&!e.expandParent)if(c&&e.width&&e.height){const d=n.get(c),{x:f,y:p}=$n(d,o).positionAbsolute;s=d&&_t(f)&&_t(p)&&_t(d.width)&&_t(d.height)?[[f+e.width*o[0],p+e.height*o[1]],[f+d.width-e.width+e.width*o[0],p+d.height-e.height+e.height*o[1]]]:s}else i==null||i("005",sn.error005()),s=a;else if(e.extent&&c&&e.extent!=="parent"){const d=n.get(c),{x:f,y:p}=$n(d,o).positionAbsolute;s=[[e.extent[0][0]+f,e.extent[0][1]+p],[e.extent[1][0]+f,e.extent[1][1]+p]]}let u={x:0,y:0};if(c){const d=n.get(c);u=$n(d,o).positionAbsolute}const l=s&&s!=="parent"?El(t,s):t;return{position:{x:l.x-u.x,y:l.y-u.y},positionAbsolute:l}}function ja({nodeId:e,dragItems:t,nodeInternals:n}){const r=t.map(o=>({...n.get(o.id),position:o.position,positionAbsolute:o.positionAbsolute}));return[e?r.find(o=>o.id===e):r[0],r]}const $u=(e,t,n,r)=>{const o=t.querySelectorAll(e);if(!o||!o.length)return null;const i=Array.from(o),a=t.getBoundingClientRect(),s={x:a.width*r[0],y:a.height*r[1]};return i.map(c=>{const u=c.getBoundingClientRect();return{id:c.getAttribute("data-handleid"),position:c.getAttribute("data-handlepos"),x:(u.left-a.left-s.x)/n,y:(u.top-a.top-s.y)/n,...Cl(c)}})};function Or(e,t,n){return n===void 0?n:r=>{const o=t().nodeInternals.get(e);o&&n(r,{...o})}}function gs({id:e,store:t,unselect:n=!1,nodeRef:r}){const{addSelectedNodes:o,unselectNodesAndEdges:i,multiSelectionActive:a,nodeInternals:s,onError:c}=t.getState(),u=s.get(e);if(!u){c==null||c("012",sn.error012(e));return}t.setState({nodesSelectionActive:!1}),u.selected?(n||u.selected&&a)&&(i({nodes:[u],edges:[]}),requestAnimationFrame(()=>{var l;return(l=r==null?void 0:r.current)==null?void 0:l.blur()})):o([e])}function UI(){const e=We();return v.useCallback(({sourceEvent:n})=>{const{transform:r,snapGrid:o,snapToGrid:i}=e.getState(),a=n.touches?n.touches[0].clientX:n.clientX,s=n.touches?n.touches[0].clientY:n.clientY,c={x:(a-r[0])/r[2],y:(s-r[1])/r[2]};return{xSnapped:i?o[0]*Math.round(c.x/o[0]):c.x,ySnapped:i?o[1]*Math.round(c.y/o[1]):c.y,...c}},[])}function Ma(e){return(t,n,r)=>e==null?void 0:e(t,r)}function Ch({nodeRef:e,disabled:t=!1,noDragClassName:n,handleSelector:r,nodeId:o,isSelectable:i,selectNodesOnDrag:a}){const s=We(),[c,u]=v.useState(!1),l=v.useRef([]),d=v.useRef({x:null,y:null}),f=v.useRef(0),p=v.useRef(null),g=v.useRef({x:0,y:0}),m=v.useRef(null),x=v.useRef(!1),w=v.useRef(!1),y=v.useRef(!1),E=UI();return v.useEffect(()=>{if(e!=null&&e.current){const S=kt(e.current),b=({x:A,y:D})=>{const{nodeInternals:k,onNodeDrag:L,onSelectionDrag:B,updateNodePositions:j,nodeExtent:C,snapGrid:O,snapToGrid:N,nodeOrigin:_,onError:W}=s.getState();d.current={x:A,y:D};let T=!1,H={x:0,y:0,x2:0,y2:0};if(l.current.length>1&&C){const M=Vi(l.current,_);H=to(M)}if(l.current=l.current.map(M=>{const U={x:A-M.distance.x,y:D-M.distance.y};N&&(U.x=O[0]*Math.round(U.x/O[0]),U.y=O[1]*Math.round(U.y/O[1]));const G=[[C[0][0],C[0][1]],[C[1][0],C[1][1]]];l.current.length>1&&C&&!M.extent&&(G[0][0]=M.positionAbsolute.x-H.x+C[0][0],G[1][0]=M.positionAbsolute.x+(M.width??0)-H.x2+C[1][0],G[0][1]=M.positionAbsolute.y-H.y+C[0][1],G[1][1]=M.positionAbsolute.y+(M.height??0)-H.y2+C[1][1]);const q=Sh(M,U,k,G,_,W);return T=T||M.position.x!==q.position.x||M.position.y!==q.position.y,M.position=q.position,M.positionAbsolute=q.positionAbsolute,M}),!T)return;j(l.current,!0,!0),u(!0);const R=o?L:Ma(B);if(R&&m.current){const[M,U]=ja({nodeId:o,dragItems:l.current,nodeInternals:k});R(m.current,M,U)}},I=()=>{if(!p.current)return;const[A,D]=Wp(g.current,p.current);if(A!==0||D!==0){const{transform:k,panBy:L}=s.getState();d.current.x=(d.current.x??0)-A/k[2],d.current.y=(d.current.y??0)-D/k[2],L({x:A,y:D})&&b(d.current)}f.current=requestAnimationFrame(I)},P=A=>{var _;const{nodeInternals:D,multiSelectionActive:k,nodesDraggable:L,unselectNodesAndEdges:B,onNodeDragStart:j,onSelectionDragStart:C}=s.getState();w.current=!0;const O=o?j:Ma(C);(!a||!i)&&!k&&o&&((_=D.get(o))!=null&&_.selected||B()),o&&i&&a&&gs({id:o,store:s,nodeRef:e});const N=E(A);if(d.current=N,l.current=WI(D,L,N,o),O&&l.current){const[W,T]=ja({nodeId:o,dragItems:l.current,nodeInternals:D});O(A.sourceEvent,W,T)}};if(t)S.on(".drag",null);else{const A=r4().on("start",D=>{const{domNode:k,nodeDragThreshold:L}=s.getState();L===0&&P(D),y.current=!1;const B=E(D);d.current=B,p.current=(k==null?void 0:k.getBoundingClientRect())||null,g.current=xn(D.sourceEvent,p.current)}).on("drag",D=>{var j,C;const k=E(D),{autoPanOnNodeDrag:L,nodeDragThreshold:B}=s.getState();if(D.sourceEvent.type==="touchmove"&&D.sourceEvent.touches.length>1&&(y.current=!0),!y.current){if(!x.current&&w.current&&L&&(x.current=!0,I()),!w.current){const O=k.xSnapped-(((j=d==null?void 0:d.current)==null?void 0:j.x)??0),N=k.ySnapped-(((C=d==null?void 0:d.current)==null?void 0:C.y)??0);Math.sqrt(O*O+N*N)>B&&P(D)}(d.current.x!==k.xSnapped||d.current.y!==k.ySnapped)&&l.current&&w.current&&(m.current=D.sourceEvent,g.current=xn(D.sourceEvent,p.current),b(k))}}).on("end",D=>{if(!(!w.current||y.current)&&(u(!1),x.current=!1,w.current=!1,cancelAnimationFrame(f.current),l.current)){const{updateNodePositions:k,nodeInternals:L,onNodeDragStop:B,onSelectionDragStop:j}=s.getState(),C=o?B:Ma(j);if(k(l.current,!1,!1),C){const[O,N]=ja({nodeId:o,dragItems:l.current,nodeInternals:L});C(D.sourceEvent,O,N)}}}).filter(D=>{const k=D.target;return!D.button&&(!n||!ku(k,`.${n}`,e))&&(!r||ku(k,r,e))});return S.call(A),()=>{S.on(".drag",null)}}}},[e,t,n,r,i,s,o,a,E]),c}function Eh(){const e=We();return v.useCallback(n=>{const{nodeInternals:r,nodeExtent:o,updateNodePositions:i,getNodes:a,snapToGrid:s,snapGrid:c,onError:u,nodesDraggable:l}=e.getState(),d=a().filter(y=>y.selected&&(y.draggable||l&&typeof y.draggable>"u")),f=s?c[0]:5,p=s?c[1]:5,g=n.isShiftPressed?4:1,m=n.x*f*g,x=n.y*p*g,w=d.map(y=>{if(y.positionAbsolute){const E={x:y.positionAbsolute.x+m,y:y.positionAbsolute.y+x};s&&(E.x=c[0]*Math.round(E.x/c[0]),E.y=c[1]*Math.round(E.y/c[1]));const{positionAbsolute:S,position:b}=Sh(y,E,r,o,void 0,u);y.position=b,y.positionAbsolute=S}return y});i(w,!0,!1)},[])}const er={ArrowUp:{x:0,y:-1},ArrowDown:{x:0,y:1},ArrowLeft:{x:-1,y:0},ArrowRight:{x:1,y:0}};var Rr=e=>{const t=({id:n,type:r,data:o,xPos:i,yPos:a,xPosOrigin:s,yPosOrigin:c,selected:u,onClick:l,onMouseEnter:d,onMouseMove:f,onMouseLeave:p,onContextMenu:g,onDoubleClick:m,style:x,className:w,isDraggable:y,isSelectable:E,isConnectable:S,isFocusable:b,selectNodesOnDrag:I,sourcePosition:P,targetPosition:A,hidden:D,resizeObserver:k,dragHandle:L,zIndex:B,isParent:j,noDragClassName:C,noPanClassName:O,initialized:N,disableKeyboardA11y:_,ariaLabel:W,rfId:T,hasHandleBounds:H})=>{const R=We(),M=v.useRef(null),U=v.useRef(null),G=v.useRef(P),q=v.useRef(A),Z=v.useRef(r),Q=E||y||l||d||f||p,$=Eh(),V=Or(n,R.getState,d),X=Or(n,R.getState,f),ne=Or(n,R.getState,p),F=Or(n,R.getState,g),Y=Or(n,R.getState,m),ee=re=>{const{nodeDragThreshold:K}=R.getState();if(E&&(!I||!y||K>0)&&gs({id:n,store:R,nodeRef:M}),l){const se=R.getState().nodeInternals.get(n);se&&l(re,{...se})}},oe=re=>{if(!us(re)&&!_)if(Gp.includes(re.key)&&E){const K=re.key==="Escape";gs({id:n,store:R,unselect:K,nodeRef:M})}else y&&u&&Object.prototype.hasOwnProperty.call(er,re.key)&&(R.setState({ariaLiveMessage:`Moved selected node ${re.key.replace("Arrow","").toLowerCase()}. New position, x: ${~~i}, y: ${~~a}`}),$({x:er[re.key].x,y:er[re.key].y,isShiftPressed:re.shiftKey}))};v.useEffect(()=>()=>{U.current&&(k==null||k.unobserve(U.current),U.current=null)},[]),v.useEffect(()=>{if(M.current&&!D){const re=M.current;(!N||!H||U.current!==re)&&(U.current&&(k==null||k.unobserve(U.current)),k==null||k.observe(re),U.current=re)}},[D,N,H]),v.useEffect(()=>{const re=Z.current!==r,K=G.current!==P,se=q.current!==A;M.current&&(re||K||se)&&(re&&(Z.current=r),K&&(G.current=P),se&&(q.current=A),R.getState().updateNodeDimensions([{id:n,nodeElement:M.current,forceUpdate:!0}]))},[n,r,P,A]);const ae=Ch({nodeRef:M,disabled:D||!y,noDragClassName:C,handleSelector:L,nodeId:n,isSelectable:E,selectNodesOnDrag:I});return D?null:z.createElement("div",{className:Je(["react-flow__node",`react-flow__node-${r}`,{[O]:y},w,{selected:u,selectable:E,parent:j,dragging:ae}]),ref:M,style:{zIndex:B,transform:`translate(${s}px,${c}px)`,pointerEvents:Q?"all":"none",visibility:N?"visible":"hidden",...x},"data-id":n,"data-testid":`rf__node-${n}`,onMouseEnter:V,onMouseMove:X,onMouseLeave:ne,onContextMenu:F,onClick:ee,onDoubleClick:Y,onKeyDown:b?oe:void 0,tabIndex:b?0:void 0,role:b?"button":void 0,"aria-describedby":_?void 0:`${ph}-${T}`,"aria-label":W},z.createElement(aI,{value:n},z.createElement(e,{id:n,data:o,type:r,xPos:i,yPos:a,selected:u,isConnectable:S,sourcePosition:P,targetPosition:A,dragging:ae,dragHandle:L,zIndex:B})))};return t.displayName="NodeWrapper",v.memo(t)};const qI=e=>{const t=e.getNodes().filter(n=>n.selected);return{...Vi(t,e.nodeOrigin),transformString:`translate(${e.transform[0]}px,${e.transform[1]}px) scale(${e.transform[2]})`,userSelectionActive:e.userSelectionActive}};function GI({onSelectionContextMenu:e,noPanClassName:t,disableKeyboardA11y:n}){const r=We(),{width:o,height:i,x:a,y:s,transformString:c,userSelectionActive:u}=Oe(qI,Ge),l=Eh(),d=v.useRef(null);if(v.useEffect(()=>{var g;n||(g=d.current)==null||g.focus({preventScroll:!0})},[n]),Ch({nodeRef:d}),u||!o||!i)return null;const f=e?g=>{const m=r.getState().getNodes().filter(x=>x.selected);e(g,m)}:void 0,p=g=>{Object.prototype.hasOwnProperty.call(er,g.key)&&l({x:er[g.key].x,y:er[g.key].y,isShiftPressed:g.shiftKey})};return z.createElement("div",{className:Je(["react-flow__nodesselection","react-flow__container",t]),style:{transform:c}},z.createElement("div",{ref:d,className:"react-flow__nodesselection-rect",onContextMenu:f,tabIndex:n?void 0:-1,onKeyDown:n?void 0:p,style:{width:o,height:i,top:s,left:a}}))}var XI=v.memo(GI);const YI=e=>e.nodesSelectionActive,Ih=({children:e,onPaneClick:t,onPaneMouseEnter:n,onPaneMouseMove:r,onPaneMouseLeave:o,onPaneContextMenu:i,onPaneScroll:a,deleteKeyCode:s,onMove:c,onMoveStart:u,onMoveEnd:l,selectionKeyCode:d,selectionOnDrag:f,selectionMode:p,onSelectionStart:g,onSelectionEnd:m,multiSelectionKeyCode:x,panActivationKeyCode:w,zoomActivationKeyCode:y,elementsSelectable:E,zoomOnScroll:S,zoomOnPinch:b,panOnScroll:I,panOnScrollSpeed:P,panOnScrollMode:A,zoomOnDoubleClick:D,panOnDrag:k,defaultViewport:L,translateExtent:B,minZoom:j,maxZoom:C,preventScrolling:O,onSelectionContextMenu:N,noWheelClassName:_,noPanClassName:W,disableKeyboardA11y:T})=>{const H=Oe(YI),R=ro(d),M=ro(w),U=M||k,G=M||I,q=R||f&&U!==!0;return MI({deleteKeyCode:s,multiSelectionKeyCode:x}),z.createElement(BI,{onMove:c,onMoveStart:u,onMoveEnd:l,onPaneContextMenu:i,elementsSelectable:E,zoomOnScroll:S,zoomOnPinch:b,panOnScroll:G,panOnScrollSpeed:P,panOnScrollMode:A,zoomOnDoubleClick:D,panOnDrag:!R&&U,defaultViewport:L,translateExtent:B,minZoom:j,maxZoom:C,zoomActivationKeyCode:y,preventScrolling:O,noWheelClassName:_,noPanClassName:W},z.createElement(xh,{onSelectionStart:g,onSelectionEnd:m,onPaneClick:t,onPaneMouseEnter:n,onPaneMouseMove:r,onPaneMouseLeave:o,onPaneContextMenu:i,onPaneScroll:a,panOnDrag:U,isSelecting:!!q,selectionMode:p},e,H&&z.createElement(XI,{onSelectionContextMenu:N,noPanClassName:W,disableKeyboardA11y:T})))};Ih.displayName="FlowRenderer";var KI=v.memo(Ih);function ZI(e){return Oe(v.useCallback(n=>e?eh(n.nodeInternals,{x:0,y:0,width:n.width,height:n.height},n.transform,!0):n.getNodes(),[e]))}function JI(e){const t={input:Rr(e.input||ch),default:Rr(e.default||hs),output:Rr(e.output||dh),group:Rr(e.group||Ol)},n={},r=Object.keys(e).filter(o=>!["input","default","output","group"].includes(o)).reduce((o,i)=>(o[i]=Rr(e[i]||hs),o),n);return{...t,...r}}const QI=({x:e,y:t,width:n,height:r,origin:o})=>!n||!r?{x:e,y:t}:o[0]<0||o[1]<0||o[0]>1||o[1]>1?{x:e,y:t}:{x:e-n*o[0],y:t-r*o[1]},eD=e=>({nodesDraggable:e.nodesDraggable,nodesConnectable:e.nodesConnectable,nodesFocusable:e.nodesFocusable,elementsSelectable:e.elementsSelectable,updateNodeDimensions:e.updateNodeDimensions,onError:e.onError}),Dh=e=>{const{nodesDraggable:t,nodesConnectable:n,nodesFocusable:r,elementsSelectable:o,updateNodeDimensions:i,onError:a}=Oe(eD,Ge),s=ZI(e.onlyRenderVisibleElements),c=v.useRef(),u=v.useMemo(()=>{if(typeof ResizeObserver>"u")return null;const l=new ResizeObserver(d=>{const f=d.map(p=>({id:p.target.getAttribute("data-id"),nodeElement:p.target,forceUpdate:!0}));i(f)});return c.current=l,l},[]);return v.useEffect(()=>()=>{var l;(l=c==null?void 0:c.current)==null||l.disconnect()},[]),z.createElement("div",{className:"react-flow__nodes",style:Tl},s.map(l=>{var b,I,P;let d=l.type||"default";e.nodeTypes[d]||(a==null||a("003",sn.error003(d)),d="default");const f=e.nodeTypes[d]||e.nodeTypes.default,p=!!(l.draggable||t&&typeof l.draggable>"u"),g=!!(l.selectable||o&&typeof l.selectable>"u"),m=!!(l.connectable||n&&typeof l.connectable>"u"),x=!!(l.focusable||r&&typeof l.focusable>"u"),w=e.nodeExtent?El(l.positionAbsolute,e.nodeExtent):l.positionAbsolute,y=(w==null?void 0:w.x)??0,E=(w==null?void 0:w.y)??0,S=QI({x:y,y:E,width:l.width??0,height:l.height??0,origin:e.nodeOrigin});return z.createElement(f,{key:l.id,id:l.id,className:l.className,style:l.style,type:d,data:l.data,sourcePosition:l.sourcePosition||ce.Bottom,targetPosition:l.targetPosition||ce.Top,hidden:l.hidden,xPos:y,yPos:E,xPosOrigin:S.x,yPosOrigin:S.y,selectNodesOnDrag:e.selectNodesOnDrag,onClick:e.onNodeClick,onMouseEnter:e.onNodeMouseEnter,onMouseMove:e.onNodeMouseMove,onMouseLeave:e.onNodeMouseLeave,onContextMenu:e.onNodeContextMenu,onDoubleClick:e.onNodeDoubleClick,selected:!!l.selected,isDraggable:p,isSelectable:g,isConnectable:m,isFocusable:x,resizeObserver:u,dragHandle:l.dragHandle,zIndex:((b=l[ke])==null?void 0:b.z)??0,isParent:!!((I=l[ke])!=null&&I.isParent),noDragClassName:e.noDragClassName,noPanClassName:e.noPanClassName,initialized:!!l.width&&!!l.height,rfId:e.rfId,disableKeyboardA11y:e.disableKeyboardA11y,ariaLabel:l.ariaLabel,hasHandleBounds:!!((P=l[ke])!=null&&P.handleBounds)})}))};Dh.displayName="NodeRenderer";var tD=v.memo(Dh);const nD=(e,t,n)=>n===ce.Left?e-t:n===ce.Right?e+t:e,rD=(e,t,n)=>n===ce.Top?e-t:n===ce.Bottom?e+t:e,_u="react-flow__edgeupdater",Bu=({position:e,centerX:t,centerY:n,radius:r=10,onMouseDown:o,onMouseEnter:i,onMouseOut:a,type:s})=>z.createElement("circle",{onMouseDown:o,onMouseEnter:i,onMouseOut:a,className:Je([_u,`${_u}-${s}`]),cx:nD(t,r,e),cy:rD(n,r,e),r,stroke:"transparent",fill:"transparent"}),oD=()=>!0;var Gn=e=>{const t=({id:n,className:r,type:o,data:i,onClick:a,onEdgeDoubleClick:s,selected:c,animated:u,label:l,labelStyle:d,labelShowBg:f,labelBgStyle:p,labelBgPadding:g,labelBgBorderRadius:m,style:x,source:w,target:y,sourceX:E,sourceY:S,targetX:b,targetY:I,sourcePosition:P,targetPosition:A,elementsSelectable:D,hidden:k,sourceHandleId:L,targetHandleId:B,onContextMenu:j,onMouseEnter:C,onMouseMove:O,onMouseLeave:N,reconnectRadius:_,onReconnect:W,onReconnectStart:T,onReconnectEnd:H,markerEnd:R,markerStart:M,rfId:U,ariaLabel:G,isFocusable:q,isReconnectable:Z,pathOptions:Q,interactionWidth:$,disableKeyboardA11y:V})=>{const X=v.useRef(null),[ne,F]=v.useState(!1),[Y,ee]=v.useState(!1),oe=We(),ae=v.useMemo(()=>`url('#${fs(M,U)}')`,[M,U]),re=v.useMemo(()=>`url('#${fs(R,U)}')`,[R,U]);if(k)return null;const K=ue=>{var we;const{edges:pe,addSelectedEdges:Ot,unselectNodesAndEdges:Ve,multiSelectionActive:Xe}=oe.getState(),De=pe.find(st=>st.id===n);De&&(D&&(oe.setState({nodesSelectionActive:!1}),De.selected&&Xe?(Ve({nodes:[],edges:[De]}),(we=X.current)==null||we.blur()):Ot([n])),a&&a(ue,De))},se=Nr(n,oe.getState,s),me=Nr(n,oe.getState,j),ve=Nr(n,oe.getState,C),xe=Nr(n,oe.getState,O),Ce=Nr(n,oe.getState,N),je=(ue,pe)=>{if(ue.button!==0)return;const{edges:Ot,isValidConnection:Ve}=oe.getState(),Xe=pe?y:w,De=(pe?B:L)||null,we=pe?"target":"source",st=Ve||oD,lt=pe,rt=Ot.find(Be=>Be.id===n);ee(!0),T==null||T(ue,rt,we);const ot=Be=>{ee(!1),H==null||H(Be,rt,we)};ih({event:ue,handleId:De,nodeId:Xe,onConnect:Be=>W==null?void 0:W(rt,Be),isTarget:lt,getState:oe.getState,setState:oe.setState,isValidConnection:st,edgeUpdaterType:we,onReconnectEnd:ot})},Qe=ue=>je(ue,!0),Nt=ue=>je(ue,!1),gt=()=>F(!0),mt=()=>F(!1),Re=!D&&!a,ie=ue=>{var pe;if(!V&&Gp.includes(ue.key)&&D){const{unselectNodesAndEdges:Ot,addSelectedEdges:Ve,edges:Xe}=oe.getState();ue.key==="Escape"?((pe=X.current)==null||pe.blur(),Ot({edges:[Xe.find(we=>we.id===n)]})):Ve([n])}};return z.createElement("g",{className:Je(["react-flow__edge",`react-flow__edge-${o}`,r,{selected:c,animated:u,inactive:Re,updating:ne}]),onClick:K,onDoubleClick:se,onContextMenu:me,onMouseEnter:ve,onMouseMove:xe,onMouseLeave:Ce,onKeyDown:q?ie:void 0,tabIndex:q?0:void 0,role:q?"button":"img","data-testid":`rf__edge-${n}`,"aria-label":G===null?void 0:G||`Edge from ${w} to ${y}`,"aria-describedby":q?`${hh}-${U}`:void 0,ref:X},!Y&&z.createElement(e,{id:n,source:w,target:y,selected:c,animated:u,label:l,labelStyle:d,labelShowBg:f,labelBgStyle:p,labelBgPadding:g,labelBgBorderRadius:m,data:i,style:x,sourceX:E,sourceY:S,targetX:b,targetY:I,sourcePosition:P,targetPosition:A,sourceHandleId:L,targetHandleId:B,markerStart:ae,markerEnd:re,pathOptions:Q,interactionWidth:$}),Z&&z.createElement(z.Fragment,null,(Z==="source"||Z===!0)&&z.createElement(Bu,{position:P,centerX:E,centerY:S,radius:_,onMouseDown:Qe,onMouseEnter:gt,onMouseOut:mt,type:"source"}),(Z==="target"||Z===!0)&&z.createElement(Bu,{position:A,centerX:b,centerY:I,radius:_,onMouseDown:Nt,onMouseEnter:gt,onMouseOut:mt,type:"target"})))};return t.displayName="EdgeWrapper",v.memo(t)};function iD(e){const t={default:Gn(e.default||Si),straight:Gn(e.bezier||Pl),step:Gn(e.step||Dl),smoothstep:Gn(e.step||Wi),simplebezier:Gn(e.simplebezier||Il)},n={},r=Object.keys(e).filter(o=>!["default","bezier"].includes(o)).reduce((o,i)=>(o[i]=Gn(e[i]||Si),o),n);return{...t,...r}}function Lu(e,t,n=null){const r=((n==null?void 0:n.x)||0)+t.x,o=((n==null?void 0:n.y)||0)+t.y,i=(n==null?void 0:n.width)||t.width,a=(n==null?void 0:n.height)||t.height;switch(e){case ce.Top:return{x:r+i/2,y:o};case ce.Right:return{x:r+i,y:o+a/2};case ce.Bottom:return{x:r+i/2,y:o+a};case ce.Left:return{x:r,y:o+a/2}}}function zu(e,t){return e?e.length===1||!t?e[0]:t&&e.find(n=>n.id===t)||null:null}const aD=(e,t,n,r,o,i)=>{const a=Lu(n,e,t),s=Lu(i,r,o);return{sourceX:a.x,sourceY:a.y,targetX:s.x,targetY:s.y}};function sD({sourcePos:e,targetPos:t,sourceWidth:n,sourceHeight:r,targetWidth:o,targetHeight:i,width:a,height:s,transform:c}){const u={x:Math.min(e.x,t.x),y:Math.min(e.y,t.y),x2:Math.max(e.x+n,t.x+o),y2:Math.max(e.y+r,t.y+i)};u.x===u.x2&&(u.x2+=1),u.y===u.y2&&(u.y2+=1);const l=to({x:(0-c[0])/c[2],y:(0-c[1])/c[2],width:a/c[2],height:s/c[2]}),d=Math.max(0,Math.min(l.x2,u.x2)-Math.max(l.x,u.x)),f=Math.max(0,Math.min(l.y2,u.y2)-Math.max(l.y,u.y));return Math.ceil(d*f)>0}function Fu(e){var r,o,i,a,s;const t=((r=e==null?void 0:e[ke])==null?void 0:r.handleBounds)||null,n=t&&(e==null?void 0:e.width)&&(e==null?void 0:e.height)&&typeof((o=e==null?void 0:e.positionAbsolute)==null?void 0:o.x)<"u"&&typeof((i=e==null?void 0:e.positionAbsolute)==null?void 0:i.y)<"u";return[{x:((a=e==null?void 0:e.positionAbsolute)==null?void 0:a.x)||0,y:((s=e==null?void 0:e.positionAbsolute)==null?void 0:s.y)||0,width:(e==null?void 0:e.width)||0,height:(e==null?void 0:e.height)||0},t,!!n]}const lD=[{level:0,isMaxLevel:!0,edges:[]}];function cD(e,t,n=!1){let r=-1;const o=e.reduce((a,s)=>{var l,d;const c=_t(s.zIndex);let u=c?s.zIndex:0;if(n){const f=t.get(s.target),p=t.get(s.source),g=s.selected||(f==null?void 0:f.selected)||(p==null?void 0:p.selected),m=Math.max(((l=p==null?void 0:p[ke])==null?void 0:l.z)||0,((d=f==null?void 0:f[ke])==null?void 0:d.z)||0,1e3);u=(c?s.zIndex:0)+(g?m:0)}return a[u]?a[u].push(s):a[u]=[s],r=u>r?u:r,a},{}),i=Object.entries(o).map(([a,s])=>{const c=+a;return{edges:s,level:c,isMaxLevel:c===r}});return i.length===0?lD:i}function uD(e,t,n){const r=Oe(v.useCallback(o=>e?o.edges.filter(i=>{const a=t.get(i.source),s=t.get(i.target);return(a==null?void 0:a.width)&&(a==null?void 0:a.height)&&(s==null?void 0:s.width)&&(s==null?void 0:s.height)&&sD({sourcePos:a.positionAbsolute||{x:0,y:0},targetPos:s.positionAbsolute||{x:0,y:0},sourceWidth:a.width,sourceHeight:a.height,targetWidth:s.width,targetHeight:s.height,width:o.width,height:o.height,transform:o.transform})}):o.edges,[e,t]));return cD(r,t,n)}const dD=({color:e="none",strokeWidth:t=1})=>z.createElement("polyline",{style:{stroke:e,strokeWidth:t},strokeLinecap:"round",strokeLinejoin:"round",fill:"none",points:"-5,-4 0,0 -5,4"}),fD=({color:e="none",strokeWidth:t=1})=>z.createElement("polyline",{style:{stroke:e,fill:e,strokeWidth:t},strokeLinecap:"round",strokeLinejoin:"round",points:"-5,-4 0,0 -5,4 -5,-4"}),Hu={[kn.Arrow]:dD,[kn.ArrowClosed]:fD};function pD(e){const t=We();return v.useMemo(()=>{var o,i;return Object.prototype.hasOwnProperty.call(Hu,e)?Hu[e]:((i=(o=t.getState()).onError)==null||i.call(o,"009",sn.error009(e)),null)},[e])}const hD=({id:e,type:t,color:n,width:r=12.5,height:o=12.5,markerUnits:i="strokeWidth",strokeWidth:a,orient:s="auto-start-reverse"})=>{const c=pD(t);return c?z.createElement("marker",{className:"react-flow__arrowhead",id:e,markerWidth:`${r}`,markerHeight:`${o}`,viewBox:"-10 -10 20 20",markerUnits:i,orient:s,refX:"0",refY:"0"},z.createElement(c,{color:n,strokeWidth:a})):null},gD=({defaultColor:e,rfId:t})=>n=>{const r=[];return n.edges.reduce((o,i)=>([i.markerStart,i.markerEnd].forEach(a=>{if(a&&typeof a=="object"){const s=fs(a,t);r.includes(s)||(o.push({id:s,color:a.color||e,...a}),r.push(s))}}),o),[]).sort((o,i)=>o.id.localeCompare(i.id))},Ph=({defaultColor:e,rfId:t})=>{const n=Oe(v.useCallback(gD({defaultColor:e,rfId:t}),[e,t]),(r,o)=>!(r.length!==o.length||r.some((i,a)=>i.id!==o[a].id)));return z.createElement("defs",null,n.map(r=>z.createElement(hD,{id:r.id,key:r.id,type:r.type,color:r.color,width:r.width,height:r.height,markerUnits:r.markerUnits,strokeWidth:r.strokeWidth,orient:r.orient})))};Ph.displayName="MarkerDefinitions";var mD=v.memo(Ph);const vD=e=>({nodesConnectable:e.nodesConnectable,edgesFocusable:e.edgesFocusable,edgesUpdatable:e.edgesUpdatable,elementsSelectable:e.elementsSelectable,width:e.width,height:e.height,connectionMode:e.connectionMode,nodeInternals:e.nodeInternals,onError:e.onError}),Ah=({defaultMarkerColor:e,onlyRenderVisibleElements:t,elevateEdgesOnSelect:n,rfId:r,edgeTypes:o,noPanClassName:i,onEdgeContextMenu:a,onEdgeMouseEnter:s,onEdgeMouseMove:c,onEdgeMouseLeave:u,onEdgeClick:l,onEdgeDoubleClick:d,onReconnect:f,onReconnectStart:p,onReconnectEnd:g,reconnectRadius:m,children:x,disableKeyboardA11y:w})=>{const{edgesFocusable:y,edgesUpdatable:E,elementsSelectable:S,width:b,height:I,connectionMode:P,nodeInternals:A,onError:D}=Oe(vD,Ge),k=uD(t,A,n);return b?z.createElement(z.Fragment,null,k.map(({level:L,edges:B,isMaxLevel:j})=>z.createElement("svg",{key:L,style:{zIndex:L},width:b,height:I,className:"react-flow__edges react-flow__container"},j&&z.createElement(mD,{defaultColor:e,rfId:r}),z.createElement("g",null,B.map(C=>{const[O,N,_]=Fu(A.get(C.source)),[W,T,H]=Fu(A.get(C.target));if(!_||!H)return null;let R=C.type||"default";o[R]||(D==null||D("011",sn.error011(R)),R="default");const M=o[R]||o.default,U=P===zn.Strict?T.target:(T.target??[]).concat(T.source??[]),G=zu(N.source,C.sourceHandle),q=zu(U,C.targetHandle),Z=(G==null?void 0:G.position)||ce.Bottom,Q=(q==null?void 0:q.position)||ce.Top,$=!!(C.focusable||y&&typeof C.focusable>"u"),V=C.reconnectable||C.updatable,X=typeof f<"u"&&(V||E&&typeof V>"u");if(!G||!q)return D==null||D("008",sn.error008(G,C)),null;const{sourceX:ne,sourceY:F,targetX:Y,targetY:ee}=aD(O,G,Z,W,q,Q);return z.createElement(M,{key:C.id,id:C.id,className:Je([C.className,i]),type:R,data:C.data,selected:!!C.selected,animated:!!C.animated,hidden:!!C.hidden,label:C.label,labelStyle:C.labelStyle,labelShowBg:C.labelShowBg,labelBgStyle:C.labelBgStyle,labelBgPadding:C.labelBgPadding,labelBgBorderRadius:C.labelBgBorderRadius,style:C.style,source:C.source,target:C.target,sourceHandleId:C.sourceHandle,targetHandleId:C.targetHandle,markerEnd:C.markerEnd,markerStart:C.markerStart,sourceX:ne,sourceY:F,targetX:Y,targetY:ee,sourcePosition:Z,targetPosition:Q,elementsSelectable:S,onContextMenu:a,onMouseEnter:s,onMouseMove:c,onMouseLeave:u,onClick:l,onEdgeDoubleClick:d,onReconnect:f,onReconnectStart:p,onReconnectEnd:g,reconnectRadius:m,rfId:r,ariaLabel:C.ariaLabel,isFocusable:$,isReconnectable:X,pathOptions:"pathOptions"in C?C.pathOptions:void 0,interactionWidth:C.interactionWidth,disableKeyboardA11y:w})})))),x):null};Ah.displayName="EdgeRenderer";var yD=v.memo(Ah);const bD=e=>`translate(${e.transform[0]}px,${e.transform[1]}px) scale(${e.transform[2]})`;function xD({children:e}){const t=Oe(bD);return z.createElement("div",{className:"react-flow__viewport react-flow__container",style:{transform:t}},e)}function wD(e){const t=Rl(),n=v.useRef(!1);v.useEffect(()=>{!n.current&&t.viewportInitialized&&e&&(setTimeout(()=>e(t),1),n.current=!0)},[e,t.viewportInitialized])}const SD={[ce.Left]:ce.Right,[ce.Right]:ce.Left,[ce.Top]:ce.Bottom,[ce.Bottom]:ce.Top},Nh=({nodeId:e,handleType:t,style:n,type:r=gn.Bezier,CustomComponent:o,connectionStatus:i})=>{var I,P,A;const{fromNode:a,handleId:s,toX:c,toY:u,connectionMode:l}=Oe(v.useCallback(D=>({fromNode:D.nodeInternals.get(e),handleId:D.connectionHandleId,toX:(D.connectionPosition.x-D.transform[0])/D.transform[2],toY:(D.connectionPosition.y-D.transform[1])/D.transform[2],connectionMode:D.connectionMode}),[e]),Ge),d=(I=a==null?void 0:a[ke])==null?void 0:I.handleBounds;let f=d==null?void 0:d[t];if(l===zn.Loose&&(f=f||(d==null?void 0:d[t==="source"?"target":"source"])),!a||!f)return null;const p=s?f.find(D=>D.id===s):f[0],g=p?p.x+p.width/2:(a.width??0)/2,m=p?p.y+p.height/2:a.height??0,x=(((P=a.positionAbsolute)==null?void 0:P.x)??0)+g,w=(((A=a.positionAbsolute)==null?void 0:A.y)??0)+m,y=p==null?void 0:p.position,E=y?SD[y]:null;if(!y||!E)return null;if(o)return z.createElement(o,{connectionLineType:r,connectionLineStyle:n,fromNode:a,fromHandle:p,fromX:x,fromY:w,toX:c,toY:u,fromPosition:y,toPosition:E,connectionStatus:i});let S="";const b={sourceX:x,sourceY:w,sourcePosition:y,targetX:c,targetY:u,targetPosition:E};return r===gn.Bezier?[S]=Jp(b):r===gn.Step?[S]=ds({...b,borderRadius:0}):r===gn.SmoothStep?[S]=ds(b):r===gn.SimpleBezier?[S]=Zp(b):S=`M${x},${w} ${c},${u}`,z.createElement("path",{d:S,fill:"none",className:"react-flow__connection-path",style:n})};Nh.displayName="ConnectionLine";const CD=e=>({nodeId:e.connectionNodeId,handleType:e.connectionHandleType,nodesConnectable:e.nodesConnectable,connectionStatus:e.connectionStatus,width:e.width,height:e.height});function ED({containerStyle:e,style:t,type:n,component:r}){const{nodeId:o,handleType:i,nodesConnectable:a,width:s,height:c,connectionStatus:u}=Oe(CD,Ge);return!(o&&i&&s&&a)?null:z.createElement("svg",{style:e,width:s,height:c,className:"react-flow__edges react-flow__connectionline react-flow__container"},z.createElement("g",{className:Je(["react-flow__connection",u])},z.createElement(Nh,{nodeId:o,handleType:i,style:t,type:n,CustomComponent:r,connectionStatus:u})))}function Wu(e,t){return v.useRef(null),We(),v.useMemo(()=>t(e),[e])}const Oh=({nodeTypes:e,edgeTypes:t,onMove:n,onMoveStart:r,onMoveEnd:o,onInit:i,onNodeClick:a,onEdgeClick:s,onNodeDoubleClick:c,onEdgeDoubleClick:u,onNodeMouseEnter:l,onNodeMouseMove:d,onNodeMouseLeave:f,onNodeContextMenu:p,onSelectionContextMenu:g,onSelectionStart:m,onSelectionEnd:x,connectionLineType:w,connectionLineStyle:y,connectionLineComponent:E,connectionLineContainerStyle:S,selectionKeyCode:b,selectionOnDrag:I,selectionMode:P,multiSelectionKeyCode:A,panActivationKeyCode:D,zoomActivationKeyCode:k,deleteKeyCode:L,onlyRenderVisibleElements:B,elementsSelectable:j,selectNodesOnDrag:C,defaultViewport:O,translateExtent:N,minZoom:_,maxZoom:W,preventScrolling:T,defaultMarkerColor:H,zoomOnScroll:R,zoomOnPinch:M,panOnScroll:U,panOnScrollSpeed:G,panOnScrollMode:q,zoomOnDoubleClick:Z,panOnDrag:Q,onPaneClick:$,onPaneMouseEnter:V,onPaneMouseMove:X,onPaneMouseLeave:ne,onPaneScroll:F,onPaneContextMenu:Y,onEdgeContextMenu:ee,onEdgeMouseEnter:oe,onEdgeMouseMove:ae,onEdgeMouseLeave:re,onReconnect:K,onReconnectStart:se,onReconnectEnd:me,reconnectRadius:ve,noDragClassName:xe,noWheelClassName:Ce,noPanClassName:je,elevateEdgesOnSelect:Qe,disableKeyboardA11y:Nt,nodeOrigin:gt,nodeExtent:mt,rfId:Re})=>{const ie=Wu(e,JI),ue=Wu(t,iD);return wD(i),z.createElement(KI,{onPaneClick:$,onPaneMouseEnter:V,onPaneMouseMove:X,onPaneMouseLeave:ne,onPaneContextMenu:Y,onPaneScroll:F,deleteKeyCode:L,selectionKeyCode:b,selectionOnDrag:I,selectionMode:P,onSelectionStart:m,onSelectionEnd:x,multiSelectionKeyCode:A,panActivationKeyCode:D,zoomActivationKeyCode:k,elementsSelectable:j,onMove:n,onMoveStart:r,onMoveEnd:o,zoomOnScroll:R,zoomOnPinch:M,zoomOnDoubleClick:Z,panOnScroll:U,panOnScrollSpeed:G,panOnScrollMode:q,panOnDrag:Q,defaultViewport:O,translateExtent:N,minZoom:_,maxZoom:W,onSelectionContextMenu:g,preventScrolling:T,noDragClassName:xe,noWheelClassName:Ce,noPanClassName:je,disableKeyboardA11y:Nt},z.createElement(xD,null,z.createElement(yD,{edgeTypes:ue,onEdgeClick:s,onEdgeDoubleClick:u,onlyRenderVisibleElements:B,onEdgeContextMenu:ee,onEdgeMouseEnter:oe,onEdgeMouseMove:ae,onEdgeMouseLeave:re,onReconnect:K,onReconnectStart:se,onReconnectEnd:me,reconnectRadius:ve,defaultMarkerColor:H,noPanClassName:je,elevateEdgesOnSelect:!!Qe,disableKeyboardA11y:Nt,rfId:Re},z.createElement(ED,{style:y,type:w,component:E,containerStyle:S})),z.createElement("div",{className:"react-flow__edgelabel-renderer"}),z.createElement(tD,{nodeTypes:ie,onNodeClick:a,onNodeDoubleClick:c,onNodeMouseEnter:l,onNodeMouseMove:d,onNodeMouseLeave:f,onNodeContextMenu:p,selectNodesOnDrag:C,onlyRenderVisibleElements:B,noPanClassName:je,noDragClassName:xe,disableKeyboardA11y:Nt,nodeOrigin:gt,nodeExtent:mt,rfId:Re})))};Oh.displayName="GraphView";var ID=v.memo(Oh);const ms=[[Number.NEGATIVE_INFINITY,Number.NEGATIVE_INFINITY],[Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY]],cn={rfId:"1",width:0,height:0,transform:[0,0,1],nodeInternals:new Map,edges:[],onNodesChange:null,onEdgesChange:null,hasDefaultNodes:!1,hasDefaultEdges:!1,d3Zoom:null,d3Selection:null,d3ZoomHandler:void 0,minZoom:.5,maxZoom:2,translateExtent:ms,nodeExtent:ms,nodesSelectionActive:!1,userSelectionActive:!1,userSelectionRect:null,connectionNodeId:null,connectionHandleId:null,connectionHandleType:"source",connectionPosition:{x:0,y:0},connectionStatus:null,connectionMode:zn.Strict,domNode:null,paneDragging:!1,noPanClassName:"nopan",nodeOrigin:[0,0],nodeDragThreshold:0,snapGrid:[15,15],snapToGrid:!1,nodesDraggable:!0,nodesConnectable:!0,nodesFocusable:!0,edgesFocusable:!0,edgesUpdatable:!0,elementsSelectable:!0,elevateNodesOnSelect:!0,fitViewOnInit:!1,fitViewOnInitDone:!1,fitViewOnInitOptions:void 0,onSelectionChange:[],multiSelectionActive:!1,connectionStartHandle:null,connectionEndHandle:null,connectionClickStartHandle:null,connectOnClick:!0,ariaLiveMessage:"",autoPanOnConnect:!0,autoPanOnNodeDrag:!0,connectionRadius:20,onError:eI,isValidConnection:void 0},DD=()=>gC((e,t)=>({...cn,setNodes:n=>{const{nodeInternals:r,nodeOrigin:o,elevateNodesOnSelect:i}=t();e({nodeInternals:Ra(n,r,o,i)})},getNodes:()=>Array.from(t().nodeInternals.values()),setEdges:n=>{const{defaultEdgeOptions:r={}}=t();e({edges:n.map(o=>({...r,...o}))})},setDefaultNodesAndEdges:(n,r)=>{const o=typeof n<"u",i=typeof r<"u",a=o?Ra(n,new Map,t().nodeOrigin,t().elevateNodesOnSelect):new Map;e({nodeInternals:a,edges:i?r:[],hasDefaultNodes:o,hasDefaultEdges:i})},updateNodeDimensions:n=>{const{onNodesChange:r,nodeInternals:o,fitViewOnInit:i,fitViewOnInitDone:a,fitViewOnInitOptions:s,domNode:c,nodeOrigin:u}=t(),l=c==null?void 0:c.querySelector(".react-flow__viewport");if(!l)return;const d=window.getComputedStyle(l),{m22:f}=new window.DOMMatrixReadOnly(d.transform),p=n.reduce((m,x)=>{const w=o.get(x.id);if(w!=null&&w.hidden)o.set(w.id,{...w,[ke]:{...w[ke],handleBounds:void 0}});else if(w){const y=Cl(x.nodeElement);!!(y.width&&y.height&&(w.width!==y.width||w.height!==y.height||x.forceUpdate))&&(o.set(w.id,{...w,[ke]:{...w[ke],handleBounds:{source:$u(".source",x.nodeElement,f,u),target:$u(".target",x.nodeElement,f,u)}},...y}),m.push({id:w.id,type:"dimensions",dimensions:y}))}return m},[]);mh(o,u);const g=a||i&&!a&&vh(t,{initial:!0,...s});e({nodeInternals:new Map(o),fitViewOnInitDone:g}),(p==null?void 0:p.length)>0&&(r==null||r(p))},updateNodePositions:(n,r=!0,o=!1)=>{const{triggerNodeChanges:i}=t(),a=n.map(s=>{const c={id:s.id,type:"position",dragging:o};return r&&(c.positionAbsolute=s.positionAbsolute,c.position=s.position),c});i(a)},triggerNodeChanges:n=>{const{onNodesChange:r,nodeInternals:o,hasDefaultNodes:i,nodeOrigin:a,getNodes:s,elevateNodesOnSelect:c}=t();if(n!=null&&n.length){if(i){const u=bh(n,s()),l=Ra(u,o,a,c);e({nodeInternals:l})}r==null||r(n)}},addSelectedNodes:n=>{const{multiSelectionActive:r,edges:o,getNodes:i}=t();let a,s=null;r?a=n.map(c=>hn(c,!0)):(a=Yn(i(),n),s=Yn(o,[])),Go({changedNodes:a,changedEdges:s,get:t,set:e})},addSelectedEdges:n=>{const{multiSelectionActive:r,edges:o,getNodes:i}=t();let a,s=null;r?a=n.map(c=>hn(c,!0)):(a=Yn(o,n),s=Yn(i(),[])),Go({changedNodes:s,changedEdges:a,get:t,set:e})},unselectNodesAndEdges:({nodes:n,edges:r}={})=>{const{edges:o,getNodes:i}=t(),a=n||i(),s=r||o,c=a.map(l=>(l.selected=!1,hn(l.id,!1))),u=s.map(l=>hn(l.id,!1));Go({changedNodes:c,changedEdges:u,get:t,set:e})},setMinZoom:n=>{const{d3Zoom:r,maxZoom:o}=t();r==null||r.scaleExtent([n,o]),e({minZoom:n})},setMaxZoom:n=>{const{d3Zoom:r,minZoom:o}=t();r==null||r.scaleExtent([o,n]),e({maxZoom:n})},setTranslateExtent:n=>{var r;(r=t().d3Zoom)==null||r.translateExtent(n),e({translateExtent:n})},resetSelectedElements:()=>{const{edges:n,getNodes:r}=t(),i=r().filter(s=>s.selected).map(s=>hn(s.id,!1)),a=n.filter(s=>s.selected).map(s=>hn(s.id,!1));Go({changedNodes:i,changedEdges:a,get:t,set:e})},setNodeExtent:n=>{const{nodeInternals:r}=t();r.forEach(o=>{o.positionAbsolute=El(o.position,n)}),e({nodeExtent:n,nodeInternals:new Map(r)})},panBy:n=>{const{transform:r,width:o,height:i,d3Zoom:a,d3Selection:s,translateExtent:c}=t();if(!a||!s||!n.x&&!n.y)return!1;const u=on.translate(r[0]+n.x,r[1]+n.y).scale(r[2]),l=[[0,0],[o,i]],d=a==null?void 0:a.constrain()(u,l,c);return a.transform(s,d),r[0]!==d.x||r[1]!==d.y||r[2]!==d.k},cancelConnection:()=>e({connectionNodeId:cn.connectionNodeId,connectionHandleId:cn.connectionHandleId,connectionHandleType:cn.connectionHandleType,connectionStatus:cn.connectionStatus,connectionStartHandle:cn.connectionStartHandle,connectionEndHandle:cn.connectionEndHandle}),reset:()=>e({...cn})}),Object.is),Rh=({children:e})=>{const t=v.useRef(null);return t.current||(t.current=DD()),z.createElement(G3,{value:t.current},e)};Rh.displayName="ReactFlowProvider";const Th=({children:e})=>v.useContext(Hi)?z.createElement(z.Fragment,null,e):z.createElement(Rh,null,e);Th.displayName="ReactFlowWrapper";const PD={input:ch,default:hs,output:dh,group:Ol},AD={default:Si,straight:Pl,step:Dl,smoothstep:Wi,simplebezier:Il},ND=[0,0],OD=[15,15],RD={x:0,y:0,zoom:1},TD={width:"100%",height:"100%",overflow:"hidden",position:"relative",zIndex:0},jh=v.forwardRef(({nodes:e,edges:t,defaultNodes:n,defaultEdges:r,className:o,nodeTypes:i=PD,edgeTypes:a=AD,onNodeClick:s,onEdgeClick:c,onInit:u,onMove:l,onMoveStart:d,onMoveEnd:f,onConnect:p,onConnectStart:g,onConnectEnd:m,onClickConnectStart:x,onClickConnectEnd:w,onNodeMouseEnter:y,onNodeMouseMove:E,onNodeMouseLeave:S,onNodeContextMenu:b,onNodeDoubleClick:I,onNodeDragStart:P,onNodeDrag:A,onNodeDragStop:D,onNodesDelete:k,onEdgesDelete:L,onSelectionChange:B,onSelectionDragStart:j,onSelectionDrag:C,onSelectionDragStop:O,onSelectionContextMenu:N,onSelectionStart:_,onSelectionEnd:W,connectionMode:T=zn.Strict,connectionLineType:H=gn.Bezier,connectionLineStyle:R,connectionLineComponent:M,connectionLineContainerStyle:U,deleteKeyCode:G="Backspace",selectionKeyCode:q="Shift",selectionOnDrag:Z=!1,selectionMode:Q=no.Full,panActivationKeyCode:$="Space",multiSelectionKeyCode:V=wi()?"Meta":"Control",zoomActivationKeyCode:X=wi()?"Meta":"Control",snapToGrid:ne=!1,snapGrid:F=OD,onlyRenderVisibleElements:Y=!1,selectNodesOnDrag:ee=!0,nodesDraggable:oe,nodesConnectable:ae,nodesFocusable:re,nodeOrigin:K=ND,edgesFocusable:se,edgesUpdatable:me,elementsSelectable:ve,defaultViewport:xe=RD,minZoom:Ce=.5,maxZoom:je=2,translateExtent:Qe=ms,preventScrolling:Nt=!0,nodeExtent:gt,defaultMarkerColor:mt="#b1b1b7",zoomOnScroll:Re=!0,zoomOnPinch:ie=!0,panOnScroll:ue=!1,panOnScrollSpeed:pe=.5,panOnScrollMode:Ot=Tn.Free,zoomOnDoubleClick:Ve=!0,panOnDrag:Xe=!0,onPaneClick:De,onPaneMouseEnter:we,onPaneMouseMove:st,onPaneMouseLeave:lt,onPaneScroll:rt,onPaneContextMenu:ot,children:vt,onEdgeContextMenu:Be,onEdgeDoubleClick:Le,onEdgeMouseEnter:Yt,onEdgeMouseMove:yt,onEdgeMouseLeave:Lt,onEdgeUpdate:et,onEdgeUpdateStart:Rt,onEdgeUpdateEnd:In,onReconnect:bt,onReconnectStart:ct,onReconnectEnd:xt,reconnectRadius:ut=10,edgeUpdaterRadius:Tt=10,onNodesChange:jt,onEdgesChange:Ui,noDragClassName:qi="nodrag",noWheelClassName:it="nowheel",noPanClassName:tt="nopan",fitView:dt=!1,fitViewOptions:ft,connectOnClick:vr=!0,attributionPosition:wt,proOptions:Eo,defaultEdgeOptions:Io,elevateNodesOnSelect:Do=!0,elevateEdgesOnSelect:Po=!1,disableKeyboardA11y:yr=!1,autoPanOnConnect:Ao=!0,autoPanOnNodeDrag:Gi=!0,connectionRadius:Xi=20,isValidConnection:br,onError:xr,style:Yi,id:No,nodeDragThreshold:Ki,...wr},Lh)=>{const Zi=No||"1";return z.createElement("div",{...wr,style:{...Yi,...TD},ref:Lh,className:Je(["react-flow",o]),"data-testid":"rf__wrapper",id:No},z.createElement(Th,null,z.createElement(ID,{onInit:u,onMove:l,onMoveStart:d,onMoveEnd:f,onNodeClick:s,onEdgeClick:c,onNodeMouseEnter:y,onNodeMouseMove:E,onNodeMouseLeave:S,onNodeContextMenu:b,onNodeDoubleClick:I,nodeTypes:i,edgeTypes:a,connectionLineType:H,connectionLineStyle:R,connectionLineComponent:M,connectionLineContainerStyle:U,selectionKeyCode:q,selectionOnDrag:Z,selectionMode:Q,deleteKeyCode:G,multiSelectionKeyCode:V,panActivationKeyCode:$,zoomActivationKeyCode:X,onlyRenderVisibleElements:Y,selectNodesOnDrag:ee,defaultViewport:xe,translateExtent:Qe,minZoom:Ce,maxZoom:je,preventScrolling:Nt,zoomOnScroll:Re,zoomOnPinch:ie,zoomOnDoubleClick:Ve,panOnScroll:ue,panOnScrollSpeed:pe,panOnScrollMode:Ot,panOnDrag:Xe,onPaneClick:De,onPaneMouseEnter:we,onPaneMouseMove:st,onPaneMouseLeave:lt,onPaneScroll:rt,onPaneContextMenu:ot,onSelectionContextMenu:N,onSelectionStart:_,onSelectionEnd:W,onEdgeContextMenu:Be,onEdgeDoubleClick:Le,onEdgeMouseEnter:Yt,onEdgeMouseMove:yt,onEdgeMouseLeave:Lt,onReconnect:bt??et,onReconnectStart:ct??Rt,onReconnectEnd:xt??In,reconnectRadius:ut??Tt,defaultMarkerColor:mt,noDragClassName:qi,noWheelClassName:it,noPanClassName:tt,elevateEdgesOnSelect:Po,rfId:Zi,disableKeyboardA11y:yr,nodeOrigin:K,nodeExtent:gt}),z.createElement(SI,{nodes:e,edges:t,defaultNodes:n,defaultEdges:r,onConnect:p,onConnectStart:g,onConnectEnd:m,onClickConnectStart:x,onClickConnectEnd:w,nodesDraggable:oe,nodesConnectable:ae,nodesFocusable:re,edgesFocusable:se,edgesUpdatable:me,elementsSelectable:ve,elevateNodesOnSelect:Do,minZoom:Ce,maxZoom:je,nodeExtent:gt,onNodesChange:jt,onEdgesChange:Ui,snapToGrid:ne,snapGrid:F,connectionMode:T,translateExtent:Qe,connectOnClick:vr,defaultEdgeOptions:Io,fitView:dt,fitViewOptions:ft,onNodesDelete:k,onEdgesDelete:L,onNodeDragStart:P,onNodeDrag:A,onNodeDragStop:D,onSelectionDrag:C,onSelectionDragStart:j,onSelectionDragStop:O,noPanClassName:tt,nodeOrigin:K,rfId:Zi,autoPanOnConnect:Ao,autoPanOnNodeDrag:Gi,onError:xr,connectionRadius:Xi,isValidConnection:br,nodeDragThreshold:Ki}),z.createElement(xI,{onSelectionChange:B}),vt,z.createElement(Y3,{proOptions:Eo,position:wt}),z.createElement(PI,{rfId:Zi,disableKeyboardA11y:yr})))});jh.displayName="ReactFlow";function Mh(e){return t=>{const[n,r]=v.useState(t),o=v.useCallback(i=>r(a=>e(i,a)),[]);return[n,r,o]}}const jD=Mh(bh),MD=Mh(FI),kh=({id:e,x:t,y:n,width:r,height:o,style:i,color:a,strokeColor:s,strokeWidth:c,className:u,borderRadius:l,shapeRendering:d,onClick:f,selected:p})=>{const{background:g,backgroundColor:m}=i||{},x=a||g||m;return z.createElement("rect",{className:Je(["react-flow__minimap-node",{selected:p},u]),x:t,y:n,rx:l,ry:l,width:r,height:o,fill:x,stroke:s,strokeWidth:c,shapeRendering:d,onClick:f?w=>f(w,e):void 0})};kh.displayName="MiniMapNode";var kD=v.memo(kh);const $D=e=>e.nodeOrigin,_D=e=>e.getNodes().filter(t=>!t.hidden&&t.width&&t.height),ka=e=>e instanceof Function?e:()=>e;function BD({nodeStrokeColor:e="transparent",nodeColor:t="#e2e2e2",nodeClassName:n="",nodeBorderRadius:r=5,nodeStrokeWidth:o=2,nodeComponent:i=kD,onClick:a}){const s=Oe(_D,Ge),c=Oe($D),u=ka(t),l=ka(e),d=ka(n),f=typeof window>"u"||window.chrome?"crispEdges":"geometricPrecision";return z.createElement(z.Fragment,null,s.map(p=>{const{x:g,y:m}=$n(p,c).positionAbsolute;return z.createElement(i,{key:p.id,x:g,y:m,width:p.width,height:p.height,style:p.style,selected:p.selected,className:d(p),color:u(p),borderRadius:r,strokeColor:l(p),strokeWidth:o,shapeRendering:f,onClick:a,id:p.id})}))}var LD=v.memo(BD);const zD=200,FD=150,HD=e=>{const t=e.getNodes(),n={x:-e.transform[0]/e.transform[2],y:-e.transform[1]/e.transform[2],width:e.width/e.transform[2],height:e.height/e.transform[2]};return{viewBB:n,boundingRect:t.length>0?J3(Vi(t,e.nodeOrigin),n):n,rfId:e.rfId}},WD="react-flow__minimap-desc";function $h({style:e,className:t,nodeStrokeColor:n="transparent",nodeColor:r="#e2e2e2",nodeClassName:o="",nodeBorderRadius:i=5,nodeStrokeWidth:a=2,nodeComponent:s,maskColor:c="rgb(240, 240, 240, 0.6)",maskStrokeColor:u="none",maskStrokeWidth:l=1,position:d="bottom-right",onClick:f,onNodeClick:p,pannable:g=!1,zoomable:m=!1,ariaLabel:x="React Flow mini map",inversePan:w=!1,zoomStep:y=10,offsetScale:E=5}){const S=We(),b=v.useRef(null),{boundingRect:I,viewBB:P,rfId:A}=Oe(HD,Ge),D=(e==null?void 0:e.width)??zD,k=(e==null?void 0:e.height)??FD,L=I.width/D,B=I.height/k,j=Math.max(L,B),C=j*D,O=j*k,N=E*j,_=I.x-(C-I.width)/2-N,W=I.y-(O-I.height)/2-N,T=C+N*2,H=O+N*2,R=`${WD}-${A}`,M=v.useRef(0);M.current=j,v.useEffect(()=>{if(b.current){const q=kt(b.current),Z=V=>{const{transform:X,d3Selection:ne,d3Zoom:F}=S.getState();if(V.sourceEvent.type!=="wheel"||!ne||!F)return;const Y=-V.sourceEvent.deltaY*(V.sourceEvent.deltaMode===1?.05:V.sourceEvent.deltaMode?1:.002)*y,ee=X[2]*Math.pow(2,Y);F.scaleTo(ne,ee)},Q=V=>{const{transform:X,d3Selection:ne,d3Zoom:F,translateExtent:Y,width:ee,height:oe}=S.getState();if(V.sourceEvent.type!=="mousemove"||!ne||!F)return;const ae=M.current*Math.max(1,X[2])*(w?-1:1),re={x:X[0]-V.sourceEvent.movementX*ae,y:X[1]-V.sourceEvent.movementY*ae},K=[[0,0],[ee,oe]],se=on.translate(re.x,re.y).scale(X[2]),me=F.constrain()(se,K,Y);F.transform(ne,me)},$=Fp().on("zoom",g?Q:null).on("zoom.wheel",m?Z:null);return q.call($),()=>{q.on("zoom",null)}}},[g,m,w,y]);const U=f?q=>{const Z=Ft(q);f(q,{x:Z[0],y:Z[1]})}:void 0,G=p?(q,Z)=>{const Q=S.getState().nodeInternals.get(Z);p(q,Q)}:void 0;return z.createElement(eo,{position:d,style:e,className:Je(["react-flow__minimap",t]),"data-testid":"rf__minimap"},z.createElement("svg",{width:D,height:k,viewBox:`${_} ${W} ${T} ${H}`,role:"img","aria-labelledby":R,ref:b,onClick:U},x&&z.createElement("title",{id:R},x),z.createElement(LD,{onClick:G,nodeColor:r,nodeStrokeColor:n,nodeBorderRadius:i,nodeClassName:o,nodeStrokeWidth:a,nodeComponent:s}),z.createElement("path",{className:"react-flow__minimap-mask",d:`M${_-N},${W-N}h${T+N*2}v${H+N*2}h${-T-N*2}z
        M${P.x},${P.y}h${P.width}v${P.height}h${-P.width}z`,fill:c,fillRule:"evenodd",stroke:u,strokeWidth:l,pointerEvents:"none"})))}$h.displayName="MiniMap";var VD=v.memo($h);function UD(){return z.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32"},z.createElement("path",{d:"M32 18.133H18.133V32h-4.266V18.133H0v-4.266h13.867V0h4.266v13.867H32z"}))}function qD(){return z.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 5"},z.createElement("path",{d:"M0 0h32v4.2H0z"}))}function GD(){return z.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 30"},z.createElement("path",{d:"M3.692 4.63c0-.53.4-.938.939-.938h5.215V0H4.708C2.13 0 0 2.054 0 4.63v5.216h3.692V4.631zM27.354 0h-5.2v3.692h5.17c.53 0 .984.4.984.939v5.215H32V4.631A4.624 4.624 0 0027.354 0zm.954 24.83c0 .532-.4.94-.939.94h-5.215v3.768h5.215c2.577 0 4.631-2.13 4.631-4.707v-5.139h-3.692v5.139zm-23.677.94c-.531 0-.939-.4-.939-.94v-5.138H0v5.139c0 2.577 2.13 4.707 4.708 4.707h5.138V25.77H4.631z"}))}function XD(){return z.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 25 32"},z.createElement("path",{d:"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0 8 0 4.571 3.429 4.571 7.619v3.048H3.048A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047zm4.724-13.866H7.467V7.619c0-2.59 2.133-4.724 4.723-4.724 2.591 0 4.724 2.133 4.724 4.724v3.048z"}))}function YD(){return z.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 25 32"},z.createElement("path",{d:"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0c-4.114 1.828-1.37 2.133.305 2.438 1.676.305 4.42 2.59 4.42 5.181v3.048H3.047A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047z"}))}const kr=({children:e,className:t,...n})=>z.createElement("button",{type:"button",className:Je(["react-flow__controls-button",t]),...n},e);kr.displayName="ControlButton";const KD=e=>({isInteractive:e.nodesDraggable||e.nodesConnectable||e.elementsSelectable,minZoomReached:e.transform[2]<=e.minZoom,maxZoomReached:e.transform[2]>=e.maxZoom}),_h=({style:e,showZoom:t=!0,showFitView:n=!0,showInteractive:r=!0,fitViewOptions:o,onZoomIn:i,onZoomOut:a,onFitView:s,onInteractiveChange:c,className:u,children:l,position:d="bottom-left"})=>{const f=We(),[p,g]=v.useState(!1),{isInteractive:m,minZoomReached:x,maxZoomReached:w}=Oe(KD,Ge),{zoomIn:y,zoomOut:E,fitView:S}=Rl();if(v.useEffect(()=>{g(!0)},[]),!p)return null;const b=()=>{y(),i==null||i()},I=()=>{E(),a==null||a()},P=()=>{S(o),s==null||s()},A=()=>{f.setState({nodesDraggable:!m,nodesConnectable:!m,elementsSelectable:!m}),c==null||c(!m)};return z.createElement(eo,{className:Je(["react-flow__controls",u]),position:d,style:e,"data-testid":"rf__controls"},t&&z.createElement(z.Fragment,null,z.createElement(kr,{onClick:b,className:"react-flow__controls-zoomin",title:"zoom in","aria-label":"zoom in",disabled:w},z.createElement(UD,null)),z.createElement(kr,{onClick:I,className:"react-flow__controls-zoomout",title:"zoom out","aria-label":"zoom out",disabled:x},z.createElement(qD,null))),n&&z.createElement(kr,{className:"react-flow__controls-fitview",onClick:P,title:"fit view","aria-label":"fit view"},z.createElement(GD,null)),r&&z.createElement(kr,{className:"react-flow__controls-interactive",onClick:A,title:"toggle interactivity","aria-label":"toggle interactivity"},m?z.createElement(YD,null):z.createElement(XD,null)),l)};_h.displayName="Controls";var ZD=v.memo(_h),Ut;(function(e){e.Lines="lines",e.Dots="dots",e.Cross="cross"})(Ut||(Ut={}));function JD({color:e,dimensions:t,lineWidth:n}){return z.createElement("path",{stroke:e,strokeWidth:n,d:`M${t[0]/2} 0 V${t[1]} M0 ${t[1]/2} H${t[0]}`})}function QD({color:e,radius:t}){return z.createElement("circle",{cx:t,cy:t,r:t,fill:e})}const eP={[Ut.Dots]:"#91919a",[Ut.Lines]:"#eee",[Ut.Cross]:"#e2e2e2"},tP={[Ut.Dots]:1,[Ut.Lines]:1,[Ut.Cross]:6},nP=e=>({transform:e.transform,patternId:`pattern-${e.rfId}`});function Bh({id:e,variant:t=Ut.Dots,gap:n=20,size:r,lineWidth:o=1,offset:i=2,color:a,style:s,className:c}){const u=v.useRef(null),{transform:l,patternId:d}=Oe(nP,Ge),f=a||eP[t],p=r||tP[t],g=t===Ut.Dots,m=t===Ut.Cross,x=Array.isArray(n)?n:[n,n],w=[x[0]*l[2]||1,x[1]*l[2]||1],y=p*l[2],E=m?[y,y]:w,S=g?[y/i,y/i]:[E[0]/i,E[1]/i];return z.createElement("svg",{className:Je(["react-flow__background",c]),style:{...s,position:"absolute",width:"100%",height:"100%",top:0,left:0},ref:u,"data-testid":"rf__background"},z.createElement("pattern",{id:d+e,x:l[0]%w[0],y:l[1]%w[1],width:w[0],height:w[1],patternUnits:"userSpaceOnUse",patternTransform:`translate(-${S[0]},-${S[1]})`},g?z.createElement(QD,{color:f,radius:y/i}):z.createElement(JD,{dimensions:E,color:f,lineWidth:o})),z.createElement("rect",{x:"0",y:"0",width:"100%",height:"100%",fill:`url(#${d+e})`}))}Bh.displayName="Background";var rP=v.memo(Bh);const oP=150,iP=(e,t)=>[{id:"start",type:"input",data:{label:"Start Sequence",nodeId:"start",onDelete:e,onEdit:t},position:{x:100,y:100},style:{border:"2px solid #1A84DE",borderRadius:8,padding:8,background:"#fff"}}],aP=(e,t)=>{const n=t==null?void 0:t.toLowerCase();if(e==="failed"){if(n.includes("email"))return h.jsxs("div",{children:[h.jsx("div",{children:"Backend Issue: 2"}),h.jsx("div",{children:"Email Invalid: 1"}),h.jsx("div",{children:"Email Bounced: 1"}),h.jsx("div",{children:"Left Company: 1"})]});if(n.includes("sms")||n.includes("whatsapp"))return h.jsxs("div",{children:[h.jsx("div",{children:"Backend Issue: 1"}),h.jsx("div",{children:"Number Invalid: 2"})]});if(n.includes("li"))return h.jsxs("div",{children:[h.jsx("div",{children:"Scrapper Failure: 1"}),h.jsx("div",{children:"Backend Failure: 1"}),h.jsx("div",{children:"Profile Restricted: 2"})]})}else{if(e==="replies"&&n.includes("email"))return h.jsxs("div",{children:[h.jsxs("div",{style:{marginTop:"5px"},children:[h.jsx("div",{children:"Auto Replies: 3"}),h.jsxs("div",{style:{marginLeft:"10px",marginTop:"2px"},children:[h.jsx("div",{children:"Left company 4"}),h.jsx("div",{children:"At leave: 3"})]})]}),h.jsxs("div",{style:{marginTop:"5px"},children:[h.jsx("strong",{children:"Potential Replies: 7"}),h.jsxs("div",{style:{marginLeft:"10px",marginTop:"2px"},children:[h.jsx("div",{children:"Interested: 4"}),h.jsx("div",{children:"Not Interested: 3"})]})]})]});if(e==="replies")return h.jsxs("div",{children:[h.jsx("div",{children:"Auto Replies: 3"}),h.jsxs("div",{style:{marginTop:"5px"},children:[h.jsx("strong",{children:"Potential Replies: 7"}),h.jsxs("div",{style:{marginLeft:"10px",marginTop:"2px"},children:[h.jsx("div",{children:"Interested: 4"}),h.jsx("div",{children:"Not Interested: 3"})]})]})]});if(e==="accepted"&&n.includes("li"))return h.jsxs("div",{children:[h.jsxs("div",{children:[h.jsx("strong",{children:"Replied: 5"}),h.jsxs("div",{style:{marginLeft:"10px",marginTop:"2px"},children:[h.jsx("div",{children:"Interested: 3"}),h.jsx("div",{children:"Not Interested: 2"})]})]}),h.jsx("div",{style:{marginTop:"5px"},children:"Not Replied: 8"})]})}return null},sP=e=>{const t=e==null?void 0:e.toLowerCase();let n=[];return t.includes("li")?n=[{icon:h.jsx(Se,{icon:"mdi:success",width:"20",height:"20"}),label:"Sent",value:25,color:"#52c41a"},{icon:h.jsx(Se,{icon:"oui:cross",width:"20",height:"20"}),label:"Failed",value:4,color:"#ff4d4f",hasTooltip:!0},{icon:h.jsx(Se,{icon:"healthicons:i-documents-accepted-outline",width:"20",height:"20"}),label:"Accepted",value:13,color:"#1890ff",hasTooltip:!0},{icon:h.jsx(Se,{icon:"marketeq:rejected-file-2",width:"22",height:"22"}),label:"Not Accepted",value:8,color:"#faad14"}]:n=[{icon:h.jsx(Se,{icon:"mdi:success",width:"20",height:"20"}),label:"Success",value:45,color:"#52c41a"},{icon:h.jsx(Se,{icon:"oui:cross",width:"20",height:"20"}),label:"Failed",value:4,color:"#ff4d4f",hasTooltip:!0},{icon:h.jsx(Se,{icon:"bx:message-x",width:"22",height:"22"}),label:"No Response",value:12,color:"#faad14"},{icon:h.jsx(Se,{icon:"teenyicons:message-tick-outline",width:"20",height:"20"}),label:"Replies",value:10,color:"#1890ff",hasTooltip:!0}],h.jsx("div",{style:{padding:"10px 16px",display:"flex",justifyContent:"space-between",fontSize:12,pointerEvents:"auto",zIndex:10,position:"relative",backgroundColor:"#f8f9fa",borderTop:"1px solid #e9ecef"},children:n.map((r,o)=>h.jsx(Sn,{title:r.hasTooltip?aP(r.label.toLowerCase(),e):r.label,getPopupContainer:()=>document.body,placement:"top",overlayStyle:{maxWidth:"200px"},children:h.jsxs("div",{style:{color:r.color,cursor:"default",textAlign:"center",fontSize:"11px"},children:[h.jsx("div",{children:r.icon}),h.jsx("div",{style:{fontWeight:"bold",marginTop:"2px"},children:r.value}),h.jsx("div",{style:{fontSize:"9px",opacity:.8},children:r.label})]})},o))})},lP=e=>{switch(e.toLowerCase()){case"email":return h.jsx($r,{style:{fontSize:18}});case"sms":return h.jsx(La,{style:{fontSize:18}});case"call":return h.jsx(za,{style:{fontSize:18}});case"whatsapp":return h.jsx(Fa,{style:{fontSize:18}});case"li connection":case"li inmail":return h.jsx(Ba,{style:{fontSize:18}});case"email_response":return h.jsx($r,{style:{fontSize:18}});case"sms_response":return h.jsx(La,{style:{fontSize:18}});case"call_response":return h.jsx(za,{style:{fontSize:18}});case"whatsapp_response":return h.jsx(Fa,{style:{fontSize:18}});case"li connection_response":case"li inmail_response":return h.jsx(Ba,{style:{fontSize:18}});default:return h.jsx($r,{style:{fontSize:18}})}},cP=z.memo(({data:e})=>{var a;const t=v.useMemo(()=>lP(e.channel),[e.channel]),n=v.useMemo(()=>{var s;return(s=e.label)!=null&&s.toLowerCase().includes("response")?sP(e.channel):null},[e.label,e.channel]),r=(a=e.label)==null?void 0:a.toLowerCase().includes("response"),o=r?"#daf2d9":"#cfe8fc",i=r?"#3aa537":"#2196F3";return h.jsxs("div",{style:{border:"1px solid #ddd",borderRadius:6,background:"#fff",width:400,fontFamily:"sans-serif",boxShadow:"0 2px 8px rgba(0,0,0,0.08)"},children:[h.jsxs("div",{style:{padding:"6px 12px",fontSize:12,color:"#555"},children:["Queue : ",e.queue||100,e.delay>0&&h.jsxs("span",{style:{marginLeft:"10px",color:"#666"},children:["| Delay: ",e.delay," min",e.delay!==1?"s":""]}),h.jsxs("span",{style:{float:"right",cursor:"pointer",color:"#999"},onClick:s=>{s.stopPropagation(),e.onDelete&&e.onDelete(e.nodeId)},children:[h.jsx(Rg,{})," Delete"]})]}),h.jsxs("div",{style:{backgroundColor:o,height:"45px",color:r?"#3aa537":"#0d8bf2",padding:"12px",display:"flex",alignItems:"center",justifyContent:"space-between"},children:[h.jsxs("div",{style:{display:"flex",alignItems:"center",gap:8},children:[h.jsx("div",{style:{background:"#fff",height:"25px",width:"32px",color:i,padding:6,borderRadius:6,display:"flex",alignItems:"center",justifyContent:"center"},children:t}),h.jsx("strong",{children:e.label})]}),!r&&h.jsxs("div",{style:{cursor:"pointer"},onClick:s=>{s.stopPropagation(),e.onEdit&&e.onEdit(e)},children:[h.jsx(Tg,{})," Edit"]})]}),n,h.jsx(cr,{type:"target",position:ce.Top}),h.jsx(cr,{type:"source",position:ce.Bottom})]})}),uP=()=>{const{roleId:e}=ao(),t=id(R=>{var M;return((M=R.auth)==null?void 0:M.user)||R.user}),n=v.useCallback(R=>{R!=="start"&&console.log("Delete node:",R)},[]),r=v.useCallback(R=>{l(R),c(!0)},[]),[o,i,a]=jD(iP(n,r)),[s,c]=v.useState(!1),[u,l]=v.useState(null),[d,f]=v.useState(""),p=v.useCallback(R=>{R&&u&&i(M=>M.map(U=>U.id===u.nodeId?{...U,data:{...U.data,delay:R.delay||0,subject:R.subject||"",templateContent:R.templateContent||""}}:U)),c(!1),l(null)},[u,i]),g=v.useMemo(()=>({custom:cP}),[]),[m,x,w]=MD([]),[y,E]=v.useState([]),[S,b]=v.useState(-1),I=50,P=v.useCallback(R=>{E(M=>{const U=M.slice(0,S+1);return U.push([...R]),U.length>I?(U.shift(),b(G=>G)):b(G=>G+1),U})},[S,I]),A=v.useCallback(()=>{if(S>0){const R=y[S-1];x(R),b(M=>M-1)}},[S,y,x]),D=v.useCallback(()=>{if(S<y.length-1){const R=y[S+1];x(R),b(M=>M+1)}},[S,y,x]),k=S>0,L=S<y.length-1;v.useEffect(()=>{y.length===0&&(E([[]]),b(0))},[y.length]),v.useEffect(()=>{const R=M=>{M.ctrlKey&&M.key==="z"&&!M.shiftKey?(M.preventDefault(),k&&A()):M.ctrlKey&&(M.key==="y"||M.key==="z"&&M.shiftKey)&&(M.preventDefault(),L&&D())};return document.addEventListener("keydown",R),()=>{document.removeEventListener("keydown",R)}},[k,L,A,D]);const B=v.useCallback(R=>{w(R),R.some(U=>U.type==="remove")&&x(U=>{let G=[...U];return R.forEach(q=>{q.type==="remove"&&(G=G.filter(Z=>Z.id!==q.id))}),P(G),G})},[w,x,P]),j=v.useCallback(R=>R.includes("-")&&!R.includes("response")?R.replace("-","-response-"):R.includes("response")?R.replace("-response-","-"):null,[]),C=v.useCallback(R=>{if(R==="start")return;const M=j(R),U=M?[R,M]:[R];i(G=>G.filter(q=>!U.includes(q.id))),x(G=>{const q=G.filter(Z=>!U.includes(Z.source)&&!U.includes(Z.target));return P(q),q})},[i,x,P,j]),O=v.useCallback(R=>{const M=R.filter(G=>G.type==="position");if(M.length>0){const G=[...R];M.forEach(q=>{const Z=j(q.id);if(Z&&q.position&&!G.find($=>$.id===Z&&$.type==="position")){let $;q.id.includes("response")?$={x:q.position.x,y:q.position.y-100}:$={x:q.position.x,y:q.position.y+100},G.push({id:Z,type:"position",position:$,dragging:q.dragging})}}),a(G)}else a(R);const U=R.filter(G=>G.type==="position"&&G.dragging===!1);U.length!==0&&(i(G=>{const q=[...G].sort((V,X)=>V.position.y-X.position.y),Z=[],Q=50;return q.forEach(V=>{const X=Z.find(ne=>Math.abs(ne.y-V.position.y)<=Q);X?X.nodes.push(V):Z.push({y:V.position.y,nodes:[V]})}),q.map(V=>{if(V.id==="start")return{...V,data:{...V.data,order:0}};const X=Z.findIndex(ne=>ne.nodes.find(F=>F.id===V.id));return{...V,data:{...V.data,order:X+1}}})}),U.forEach(G=>{j(G.id)||i(q=>(x(Z=>{const Q=q.find(V=>V.id===G.id);if(!Q)return Z;const $=q.find(V=>{if(V.id===Q.id||j(V.id))return!1;const X=V.position.x-Q.position.x,ne=V.position.y-Q.position.y;return Math.sqrt(X*X+ne*ne)<=oP});if($&&!Z.some(X=>X.source===$.id&&X.target===Q.id)){const X=j($.id)===Q.id||j(Q.id)===$.id;return ni({id:`e-${$.id}-${Q.id}`,source:$.id,target:Q.id,markerEnd:{type:kn.ArrowClosed},animated:!X,style:X?{stroke:"#000000",strokeWidth:2}:void 0,type:X?"step-response":"default"},Z)}return Z}),q))}))},[a,i,x,j]),N=v.useCallback(R=>{x(M=>{const U=R.source&&R.target&&j(R.source)===R.target||R.source&&R.target&&j(R.target)===R.source,G=ni({...R,markerEnd:{type:kn.ArrowClosed},animated:!U,style:U?{stroke:"#000000",strokeWidth:2}:void 0,type:U?"step-response":"default"},M);return P(G),G})},[x,P,j]),_=v.useCallback(R=>{const M=`${R}-${Date.now()}`,U=`${R}-response-${Date.now()}`,G=o.find(ae=>ae.id==="start"),q=G?G.position.x:100,Z=G?G.position.y:100,Q=m.filter(ae=>ae.source==="start"),$=o.filter(ae=>{var re;return Q.some(K=>K.target===ae.id)&&!((re=ae.data.label)!=null&&re.toLowerCase().includes("response"))});let V,X;$.length===0?(V=q,X=Z+150):(X=$[0].position.y,V=q+$.length*450);const ne=X+100,F=$.length>0?$[0].data.order:1,Y=F+.1,ee={id:M,position:{x:V,y:X},data:{label:`${R} Step`,channel:R,delay:0,condition:"",queue:100,order:F,nodeId:M,onDelete:C,onEdit:r},type:"custom"},oe={id:U,position:{x:V,y:ne},data:{label:`${R} Response`,channel:`${R}_response`,delay:0,condition:"",queue:100,order:Y,nodeId:U,onDelete:C,onEdit:r},type:"custom"};i(ae=>[...ae,ee,oe]),x(ae=>{const re=ni({id:`e-${M}-${U}`,source:M,target:U,markerEnd:{type:kn.ArrowClosed},animated:!1,style:{stroke:"#000000",strokeWidth:2},type:"step-response"},ae);return P(re),re})},[o,m,i,x,P,C,r]),W=v.useCallback((R,M)=>{console.log("Node clicked:",M)},[]),T=v.useCallback(async()=>{try{if(o.length===0){Ee.warning("Please add some steps to the sequence before saving.");return}const R=["EMAIL","EMAIL_STEP","SMS","SMS_STEP","WHATSAPP","WHATSAPP_STEP","CALL","CALL_STEP","LI_CONNECTION","LI_INMAIL","LINKEDIN","LINKEDIN_STEP"],M=o.filter(K=>{const se=K.data.label.toUpperCase().replace(/\s+/g,"_");return R.includes(se)});if(M.length===0){Ee.warning("Please add at least one communication step (Email, SMS, WhatsApp, Call, or LinkedIn) to the sequence.");return}console.log("All nodes:",o.map(K=>({id:K.id,label:K.data.label}))),console.log("Filtered communication nodes:",M.map(K=>({id:K.id,label:K.data.label})));const U=M.map(K=>K.data.label),G=U.filter((K,se)=>U.indexOf(K)!==se);G.length>0&&console.warn("Duplicate node labels found:",G);const q={};M.forEach(K=>{const se=Math.floor(K.position.y/150);q[se]||(q[se]=[]),q[se].push(K)});const Z=Object.keys(q).sort((K,se)=>parseInt(K)-parseInt(se)),Q=[],$=new Set;Z.forEach((K,se)=>{q[K].forEach(ve=>{const xe=ve.data.label.toUpperCase().replace(/\s+/g,"_"),Ce=`${xe}_${se}`;if($.has(Ce)){console.warn(`Duplicate step detected: ${Ce}, skipping`);return}const Qe={EMAIL:"EMAIL",EMAIL_STEP:"EMAIL",SMS:"SMS",SMS_STEP:"SMS",WHATSAPP:"WHATSAPP",WHATSAPP_STEP:"WHATSAPP",CALL:"CALL",CALL_STEP:"CALL",LI_CONNECTION:"LINKEDIN",LI_INMAIL:"LINKEDIN",LINKEDIN:"LINKEDIN",LINKEDIN_STEP:"LINKEDIN"}[xe];if(!Qe){console.warn(`Unknown medium type: ${xe}, skipping step`);return}console.log(`Mapping: ${ve.id} - ${xe} → ${Qe} (order: ${se})`),$.add(Ce),Q.push({name:ve.data.label,order:se,type:"OUTREACH",medium:Qe,templateId:1})})}),console.log("Final sequence steps to create:",Q);const V=Q.map(K=>K.name),X=V.filter((K,se)=>V.indexOf(K)!==se);if(X.length>0){console.error("Duplicate steps detected:",X),Ee.error("Duplicate steps detected. Please remove duplicate steps and try again.");return}const{createSequence:ne,createSequenceStep:F}=await Cg(()=>Promise.resolve().then(()=>z2),void 0),Y=e;if(console.log("Current URL:",window.location.pathname),console.log("useParams roleId:",e),console.log("Using roleId:",Y),!Y)throw new Error("Role ID not found. Please ensure you are on a valid role page.");console.log("Using role ID from useParams:",Y);const ee=(t==null?void 0:t.id)||(t==null?void 0:t.userId)||(t==null?void 0:t.user_id);if(!ee)throw new Error("User not found. Please ensure you are logged in.");console.log("Using user ID from Redux:",ee);const oe={name:d||`Sequence for Role ${Y}`,description:"Created from Advanced Sequence Builder",status:"ACTIVE",userId:ee,roleId:parseInt(Y)};console.log("=== SEQUENCE CREATION DEBUG ==="),console.log("Component: AdvanceSequence.jsx"),console.log("Role ID from useParams:",e),console.log("User from Redux:",t),console.log("Final userId being used:",ee),console.log("Sequence data to be sent:",oe),console.log("Steps to be created:",Q);const ae=await ne(oe);console.log("Created sequence response:",ae);const re=[];for(const K of Q){const se={...K,roleSequenceId:ae.id},me=await F(se);re.push(me)}return console.log("=== SEQUENCE WITH STEPS SAVED TO DATABASE ==="),console.log("Sequence ID:",ae.id),console.log("Total Steps:",Q.length),console.log("Steps created:",re.length),Ee.success(`Sequence "${oe.name}" with ${Q.length} steps saved successfully! You can now execute it from the Sequence Execution Dashboard.`),{sequence:ae,steps:re}}catch(R){console.error("Error saving sequence:",R),console.error("Error details:",{message:R.message,stack:R.stack,response:R.response});let M="Failed to save sequence. ";throw R.response?M+=`Server responded with: ${R.response.status} ${R.response.statusText}`:R.message?M+=R.message:M+="Please ensure the backend is running and try again.",Ee.error(M),R}},[o,d]),H=v.useCallback(()=>{const R={};o.forEach(q=>{const Z=q.data.order??0;R[Z]||(R[Z]=[]),R[Z].push(q)});const M=Object.keys(R).sort((q,Z)=>parseFloat(q)-parseFloat(Z)),U=M.map(q=>{const Z=R[q];return Z.length===1?{order:parseFloat(q),type:"sequential",step:Z[0]}:{order:parseFloat(q),type:"parallel",steps:Z}}),G={sequenceSteps:U,edges:m,totalSteps:o.length,parallelGroups:M.filter(q=>R[q].length>1).length};console.log("=== SEQUENCE EXPORT ==="),console.log("Sequence Steps Order:"),U.forEach((q,Z)=>{q.type==="sequential"?console.log(`${Z+1}. [Sequential] Order ${q.order}: ${q.step.data.label}`):(console.log(`${Z+1}. [Parallel] Order ${q.order}:`),q.steps.forEach((Q,$)=>{console.log(`   ${$+1}. ${Q.data.label}`)}))}),console.log("Full Sequence JSON:",JSON.stringify(G,null,2))},[o,m]);return h.jsxs("div",{style:{height:"100vh",width:"100%"},children:[h.jsxs(jh,{nodeTypes:g,nodes:o,edges:m,onNodesChange:O,onEdgesChange:B,onConnect:N,onNodeClick:W,fitView:!0,children:[h.jsx(rP,{variant:"none"}),h.jsx(VD,{}),h.jsx(ZD,{}),h.jsx(eo,{position:"top-left",children:h.jsxs("div",{style:{background:"#fff",padding:10,borderRadius:8,boxShadow:"0 2px 6px rgba(0,0,0,0.1)"},children:[h.jsxs(nt,{style:{marginBottom:8},children:[h.jsx(Sn,{title:"Undo connection (Ctrl+Z)",placement:"bottom",children:h.jsx(he,{icon:h.jsx(Bv,{}),onClick:A,disabled:!k,size:"small",children:"Undo"})}),h.jsx(Sn,{title:"Redo connection (Ctrl+Y)",placement:"bottom",children:h.jsx(he,{icon:h.jsx(Tv,{}),onClick:D,disabled:!L,size:"small",children:"Redo"})})]}),h.jsx(On,{style:{margin:"8px 0"}}),h.jsxs("div",{children:[h.jsx(he,{onClick:()=>_("Email"),style:{marginRight:4,marginBottom:4},children:"Add Email"}),h.jsx(he,{onClick:()=>_("WhatsApp"),style:{marginRight:4,marginBottom:4},children:"Add WhatsApp"}),h.jsx(he,{onClick:()=>_("SMS"),style:{marginRight:4,marginBottom:4},children:"Add SMS"}),h.jsx(he,{onClick:()=>_("Call"),style:{marginRight:4,marginBottom:4},children:"Add Call"}),h.jsx(he,{onClick:()=>_("LI Connection"),style:{marginRight:4,marginBottom:4},children:"LI Connection"}),h.jsx(he,{onClick:()=>_("LI InMail"),style:{marginRight:4,marginBottom:4},children:"LI InMail"}),h.jsx(he,{onClick:H,type:"dashed",style:{marginTop:4},children:"Export"})]})]})}),h.jsx(eo,{position:"top-right",style:{backgroundColor:"#f2f5fa",width:"500px"},children:h.jsxs("div",{style:{background:"#fff",padding:10,borderRadius:8,boxShadow:"0 2px 6px rgba(0,0,0,0.1)",display:"flex",alignItems:"center"},children:[h.jsxs("div",{style:{display:"flex",flexDirection:"column",gap:"8px",width:"100%"},children:[h.jsx(_n,{placeholder:"Enter sequence name",onChange:R=>f(R.target.value),value:d,style:{padding:"5px",border:"1px solid #ced0da"},required:!0}),h.jsxs("div",{style:{fontSize:"12px",color:"#666",padding:"2px 5px"},children:["Role ID: ",e||"Loading..."]})]}),h.jsx(he,{onClick:T,style:{marginTop:4,backgroundColor:"#1A84DE",color:"white",width:"160px",height:"35px",marginLeft:"10px"},children:"Save to Database"}),h.jsx(he,{onClick:H,style:{marginTop:4,backgroundColor:"#1A84DE",color:"white",width:"160px",height:"35px",marginLeft:"10px"},children:"Save Sequence"})]})})]}),h.jsx(Xf,{open:s,onClose:p,step:u})]})};var dP={exports:{}};(function(e,t){(function(r,o){e.exports=o(v,Eg,Ig)})(zr,function(n,r,o){return function(){var i={"./src/Xarrow/utils/buzzier.js":function(u,l,d){d.r(l),d.d(l,{bzFunction:function(){return f},buzzierMinSols:function(){return p}});var f=function(m,x,w,y){return function(E){return Math.pow(1-E,3)*m+3*Math.pow(1-E,2)*E*x+3*(1-E)*Math.pow(E,2)*w+Math.pow(E,3)*y}},p=function(m,x,w,y){var E=f(m,x,w,y),S=-6*m+12*x-6*w,b=Math.pow(-6*m+12*x-6*w,2)-4*(3*x-3*m)*(-3*m+9*x-9*w+3*y),I=2*(-3*m+9*x-9*w+3*y),P=E((S+Math.sqrt(b))/I),A=E((S-Math.sqrt(b))/I);return[P,A]}},"./src/Xarrow/Xarrow.tsx":function(u,l,d){var f=this&&this.__assign||function(){return f=Object.assign||function(A){for(var D,k=1,L=arguments.length;k<L;k++){D=arguments[k];for(var B in D)Object.prototype.hasOwnProperty.call(D,B)&&(A[B]=D[B])}return A},f.apply(this,arguments)},p=this&&this.__createBinding||(Object.create?function(A,D,k,L){L===void 0&&(L=k),Object.defineProperty(A,L,{enumerable:!0,get:function(){return D[k]}})}:function(A,D,k,L){L===void 0&&(L=k),A[L]=D[k]}),g=this&&this.__setModuleDefault||(Object.create?function(A,D){Object.defineProperty(A,"default",{enumerable:!0,value:D})}:function(A,D){A.default=D}),m=this&&this.__importStar||function(A){if(A&&A.__esModule)return A;var D={};if(A!=null)for(var k in A)k!=="default"&&Object.prototype.hasOwnProperty.call(A,k)&&p(D,A,k);return g(D,A),D},x=this&&this.__spreadArray||function(A,D){for(var k=0,L=D.length,B=A.length;k<L;k++,B++)A[B]=D[k];return A},w=this&&this.__importDefault||function(A){return A&&A.__esModule?A:{default:A}};Object.defineProperty(l,"__esModule",{value:!0});var y=m(d("react")),E=w(d("./src/Xarrow/useXarrowProps.ts")),S=d("./src/Xwrapper.tsx"),b=w(d("./src/Xarrow/propTypes.ts")),I=d("./src/Xarrow/utils/GetPosition.tsx"),P=function(A){var D,k=y.useRef({svgRef:y.useRef(null),lineRef:y.useRef(null),headRef:y.useRef(null),tailRef:y.useRef(null),lineDrawAnimRef:y.useRef(null),lineDashAnimRef:y.useRef(null),headOpacityAnimRef:y.useRef(null)}),L=k.current,B=L.svgRef,j=L.lineRef,C=L.headRef;L.tailRef;var O=L.lineDrawAnimRef,N=L.lineDashAnimRef,_=L.headOpacityAnimRef;y.useContext(S.XarrowContext);var W=E.default(A,k.current),T=W[0],H=T.labels,R=T.lineColor,M=T.headColor,U=T.tailColor,G=T.strokeWidth,q=T.showHead,Z=T.showTail,Q=T.dashness,$=T.headShape,V=T.tailShape,X=T.showXarrow,ne=T.animateDrawing,F=T.zIndex,Y=T.passProps,ee=T.arrowBodyProps,oe=T.arrowHeadProps,ae=T.arrowTailProps,re=T.SVGcanvasProps,K=T.divContainerProps,se=T.divContainerStyle,me=T.SVGcanvasStyle,ve=T._debug,xe=T.shouldUpdatePosition;ne=A.animateDrawing;var Ce=y.useState(!ne),je=Ce[0],Qe=Ce[1],Nt=y.useState({}),gt=Nt[1],mt=function(){return gt({})},Re=y.useState({cx0:0,cy0:0,cw:0,ch:0,x1:0,y1:0,x2:0,y2:0,dx:0,dy:0,absDx:0,absDy:0,cpx1:0,cpy1:0,cpx2:0,cpy2:0,headOrient:0,tailOrient:0,arrowHeadOffset:{x:0,y:0},arrowTailOffset:{x:0,y:0},headOffset:0,excRight:0,excLeft:0,excUp:0,excDown:0,startPoints:[],endPoints:[],mainDivPos:{x:0,y:0},xSign:1,ySign:1,lineLength:0,fHeadSize:1,fTailSize:1,arrowPath:"",labelStartPos:{x:0,y:0},labelMiddlePos:{x:0,y:0},labelEndPos:{x:0,y:0}}),ie=Re[0],ue=Re[1];y.useLayoutEffect(function(){if(xe.current){var Be=I.getPosition(W,k);ue(Be),xe.current=!1}});var pe=ie.x2-ie.arrowHeadOffset.x,Ot=ie.y2-ie.arrowHeadOffset.y,Ve=ie.x1-ie.arrowTailOffset.x,Xe=ie.y1-ie.arrowTailOffset.y,De=Q.strokeLen+Q.nonStrokeLen,we=1;Q.animation<0&&(Q.animation*=-1,we=-1);var st,lt,rt,ot,vt=0;return ne&&je==!1?(typeof ne=="boolean"&&(ne=1),lt=ne+"s",st=ie.lineLength,ot=ie.lineLength,rt=1,ne<0&&(D=[vt,ot],ot=D[0],vt=D[1],lt=ne*-1+"s")):(st=Q.strokeLen+" "+Q.nonStrokeLen,lt=1/Q.animation+"s",ot=De*we,rt="indefinite",vt=0),y.useLayoutEffect(function(){j.current&&ue(function(Be){var Le,Yt;return f(f({},Be),{lineLength:(Yt=(Le=j.current)===null||Le===void 0?void 0:Le.getTotalLength())!==null&&Yt!==void 0?Yt:0})})},[j.current]),y.useEffect(function(){var Be=function(){window.addEventListener("resize",mt);var Yt=function(){var Lt,et;Qe(!0),(Lt=_.current)===null||Lt===void 0||Lt.beginElement(),(et=N.current)===null||et===void 0||et.beginElement()},yt=function(){return C.current.style.opacity="0"};return O.current&&C.current&&(O.current.addEventListener("endEvent",Yt),O.current.addEventListener("beginEvent",yt)),function(){window.removeEventListener("resize",mt),O.current&&(O.current.removeEventListener("endEvent",Yt),C.current&&O.current.removeEventListener("beginEvent",yt))}},Le=Be();return function(){Qe(!1),Le()}},[X]),y.default.createElement("div",f({},K,{style:f({position:"absolute",zIndex:F},se)}),X?y.default.createElement(y.default.Fragment,null,y.default.createElement("svg",f({ref:B,width:ie.cw,height:ie.ch,style:f({position:"absolute",left:ie.cx0,top:ie.cy0,pointerEvents:"none",border:ve?"1px dashed yellow":null},me),overflow:"auto"},re),y.default.createElement("path",f({ref:j,d:ie.arrowPath,stroke:R,strokeDasharray:st,strokeWidth:G,fill:"transparent",pointerEvents:"visibleStroke"},Y,ee),y.default.createElement(y.default.Fragment,null,je?y.default.createElement(y.default.Fragment,null,Q.animation?y.default.createElement("animate",{ref:N,attributeName:"stroke-dashoffset",values:De*we+";0",dur:1/Q.animation+"s",repeatCount:"indefinite"}):null):y.default.createElement(y.default.Fragment,null,ne?y.default.createElement("animate",{ref:O,id:"svgEndAnimate",attributeName:"stroke-dashoffset",values:ot+";"+vt,dur:lt,repeatCount:rt}):null))),Z?y.default.createElement("g",f({fill:U,pointerEvents:"auto",transform:"translate("+Ve+","+Xe+") rotate("+ie.tailOrient+") scale("+ie.fTailSize+")"},Y,ae),V.svgElem):null,q?y.default.createElement("g",f({ref:C,fill:M,pointerEvents:"auto",transform:"translate("+pe+","+Ot+") rotate("+ie.headOrient+") scale("+ie.fHeadSize+")",opacity:ne&&!je?0:1},Y,oe),y.default.createElement("animate",{ref:_,dur:"0.4",attributeName:"opacity",from:"0",to:"1",begin:"indefinite",repeatCount:"0",fill:"freeze"}),$.svgElem):null,ve?y.default.createElement(y.default.Fragment,null,y.default.createElement("circle",{r:"5",cx:ie.cpx1,cy:ie.cpy1,fill:"green"}),y.default.createElement("circle",{r:"5",cx:ie.cpx2,cy:ie.cpy2,fill:"blue"}),y.default.createElement("rect",{x:ie.excLeft,y:ie.excUp,width:ie.absDx,height:ie.absDy,fill:"none",stroke:"pink",strokeWidth:"2px"})):null),H.start?y.default.createElement("div",{style:{transform:ie.dx<0?"translate(-100% , -50%)":"translate(-0% , -50%)",width:"max-content",position:"absolute",left:ie.cx0+ie.labelStartPos.x,top:ie.cy0+ie.labelStartPos.y-G-5}},H.start):null,H.middle?y.default.createElement("div",{style:{display:"table",width:"max-content",transform:"translate(-50% , -50%)",position:"absolute",left:ie.cx0+ie.labelMiddlePos.x,top:ie.cy0+ie.labelMiddlePos.y}},H.middle):null,H.end?y.default.createElement("div",{style:{transform:ie.dx>0?"translate(-100% , -50%)":"translate(-0% , -50%)",width:"max-content",position:"absolute",left:ie.cx0+ie.labelEndPos.x,top:ie.cy0+ie.labelEndPos.y+G+5}},H.end):null,ve?y.default.createElement(y.default.Fragment,null,x(x([],ie.startPoints),ie.endPoints).map(function(Be,Le){return y.default.createElement("div",{key:Le,style:{background:"gray",opacity:.5,borderRadius:"50%",transform:"translate(-50%, -50%)",height:5,width:5,position:"absolute",left:Be.x-ie.mainDivPos.x,top:Be.y-ie.mainDivPos.y}})})):null):null)};P.propTypes=b.default,l.default=P},"./src/Xarrow/anchors.ts":function(u,l,d){u=d.nmd(u),Object.defineProperty(l,"__esModule",{value:!0}),l.calcAnchors=void 0;var f=function(g,m){return{middle:{x:g*.5,y:m*.5},left:{x:0,y:m*.5},right:{x:g,y:m*.5},top:{x:g*.5,y:0},bottom:{x:g*.5,y:m}}},p=function(g,m){return g.map(function(x){var w=f(m.right-m.x,m.bottom-m.y),y=w[x.position],E=y.x,S=y.y;return{x:m.x+E+x.offset.x,y:m.y+S+x.offset.y,anchor:x}})};l.calcAnchors=p,d.c[d.s]},"./src/Xarrow/propTypes.ts":function(u,l,d){var f=this&&this.__importDefault||function(k){return k&&k.__esModule?k:{default:k}};Object.defineProperty(l,"__esModule",{value:!0});var p=f(d("prop-types")),g=d("./src/constants.tsx"),m=p.default.oneOf(g.cAnchorEdge),x=p.default.exact({position:m.isRequired,offset:p.default.exact({x:p.default.number,y:p.default.number}).isRequired}),w=p.default.oneOfType([m,x]),y=p.default.oneOfType([w,p.default.arrayOf(w)]),E=p.default.oneOfType([p.default.string,p.default.exact({current:p.default.any})]),S=p.default.oneOfType([p.default.element,p.default.string]),b=p.default.exact({start:S,middle:S,end:S}),I=p.default.oneOf(Object.keys(g.arrowShapes)),P=p.default.any,A=p.default.oneOfType([I,p.default.exact({svgElem:P,offsetForward:p.default.number}).isRequired]),D={start:E.isRequired,end:E.isRequired,startAnchor:y,endAnchor:y,labels:p.default.oneOfType([S,b]),color:p.default.string,lineColor:p.default.string,showHead:p.default.bool,headColor:p.default.string,headSize:p.default.number,tailSize:p.default.number,tailColor:p.default.string,strokeWidth:p.default.number,showTail:p.default.bool,path:p.default.oneOf(g.cPaths),showXarrow:p.default.bool,curveness:p.default.number,gridBreak:p.default.string,dashness:p.default.oneOfType([p.default.bool,p.default.object]),headShape:A,tailShape:A,animateDrawing:p.default.oneOfType([p.default.bool,p.default.number]),zIndex:p.default.number,passProps:p.default.object,arrowBodyProps:p.default.object,arrowHeadProps:p.default.object,arrowTailProps:p.default.object,SVGcanvasProps:p.default.object,divContainerProps:p.default.object,_extendSVGcanvas:p.default.number,_debug:p.default.bool,_cpx1Offset:p.default.number,_cpy1Offset:p.default.number,_cpx2Offset:p.default.number,_cpy2Offset:p.default.number};l.default=D},"./src/Xarrow/useXarrowProps.ts":function(u,l,d){var f=this&&this.__assign||function(){return f=Object.assign||function($){for(var V,X=1,ne=arguments.length;X<ne;X++){V=arguments[X];for(var F in V)Object.prototype.hasOwnProperty.call(V,F)&&($[F]=V[F])}return $},f.apply(this,arguments)},p=this&&this.__createBinding||(Object.create?function($,V,X,ne){ne===void 0&&(ne=X),Object.defineProperty($,ne,{enumerable:!0,get:function(){return V[X]}})}:function($,V,X,ne){ne===void 0&&(ne=X),$[ne]=V[X]}),g=this&&this.__setModuleDefault||(Object.create?function($,V){Object.defineProperty($,"default",{enumerable:!0,value:V})}:function($,V){$.default=V}),m=this&&this.__importStar||function($){if($&&$.__esModule)return $;var V={};if($!=null)for(var X in $)X!=="default"&&Object.prototype.hasOwnProperty.call($,X)&&p(V,$,X);return g(V,$),V},x=this&&this.__importDefault||function($){return $&&$.__esModule?$:{default:$}};Object.defineProperty(l,"__esModule",{value:!0});var w=m(d("react")),y=d("./src/Xarrow/utils/index.ts"),E=x(d("lodash")),S=d("./src/constants.tsx"),b=function($){var V={start:null,middle:null,end:null};if($)if(typeof $=="string"||w.default.isValidElement($))V.middle=$;else for(var X in $)V[X]=$[X];return V},I=function($){var V=Array.isArray($)?$:[$],X=V.map(function(Y){return typeof Y=="string"?{position:Y}:Y});X=X.filter(function(Y){return S.cAnchorEdge.includes(Y.position)}),X.length==0&&(X=[{position:"auto"}]);var ne=X.filter(function(Y){return Y.position==="auto"});ne.length>0&&(X=X.filter(function(Y){return Y.position!=="auto"}),X.push.apply(X,ne.flatMap(function(Y){return["left","right","top","bottom"].map(function(ee){return f(f({},Y),{position:ee})})})));var F=X.map(function(Y){if(typeof Y=="object"){var ee=Y;return ee.position||(ee.position="auto"),ee.offset||(ee.offset={x:0,y:0}),ee.offset.y||(ee.offset.y=0),ee.offset.x||(ee.offset.x=0),ee=ee,ee}else return Y});return F},P=function($,V){var X=0,ne=0,F,Y=1;return typeof $=="object"?(X=$.strokeLen||V.strokeWidth*2,ne=$.strokeLen?$.nonStrokeLen:V.strokeWidth,F=$.animation?$.animation:null):typeof $=="boolean"&&$&&(X=V.strokeWidth*2,ne=V.strokeWidth,F=null),{strokeLen:X,nonStrokeLen:ne,animation:F,animDirection:Y}},A=function($){return typeof $=="string"&&($ in S.arrowShapes?$=S.arrowShapes[$]:(console.warn("'"+$+"' is not supported arrow shape. the supported arrow shapes is one of "+S.cArrowShapes+`.
           reverting to default shape.`),$=S.arrowShapes.arrow1)),$=$,($==null?void 0:$.offsetForward)===void 0&&($.offsetForward=.25),($==null?void 0:$.svgElem)===void 0&&($.svgElem="path"),$},D=function($){var V=y.xStr2absRelative($);return V||(V={relative:.5,abs:0}),V},k=function($,V){return V&&(V.current=!0),$},L=function($){return $},B=function($,V,X){return k($,X)},j=function($,V,X){return k(Number($),X)},C=function($){return Number($)},O={start:function($){return y.getElementByPropGiven($)},end:function($){return y.getElementByPropGiven($)},startAnchor:function($,V,X){return k(I($),X)},endAnchor:function($,V,X){return k(I($),X)},labels:function($){return b($)},color:L,lineColor:function($,V){return $||V.color},headColor:function($,V){return $||V.color},tailColor:function($,V){return $||V.color},strokeWidth:j,showHead:B,headSize:j,showTail:B,tailSize:j,path:B,curveness:j,gridBreak:function($,V,X){return k(D($),X)},dashness:function($,V){return P($,V)},headShape:function($){return A($)},tailShape:function($){return A($)},showXarrow:L,animateDrawing:L,zIndex:C,passProps:L,arrowBodyProps:B,arrowHeadProps:B,arrowTailProps:B,SVGcanvasProps:B,divContainerProps:B,divContainerStyle:B,SVGcanvasStyle:B,_extendSVGcanvas:B,_debug:B,_cpx1Offset:B,_cpy1Offset:B,_cpx2Offset:B,_cpy2Offset:B},N={};for(var _ in O)N[_]=[_];for(var W=0,T=["lineColor","headColor","tailColor"];W<T.length;W++){var _=T[W];N[_].push("color")}var H=function($,V){for(var X,ne=0,F=Object.entries($);ne<F.length;ne++){var Y=F[ne],ee=Y[0],oe=Y[1];V[ee]=(X=O==null?void 0:O[ee])===null||X===void 0?void 0:X.call(O,oe,V)}return V},R={start:null,end:null,startAnchor:"auto",endAnchor:"auto",labels:null,color:"CornflowerBlue",lineColor:null,headColor:null,tailColor:null,strokeWidth:4,showHead:!0,headSize:6,showTail:!1,tailSize:6,path:"smooth",curveness:.8,gridBreak:"50%",dashness:!1,headShape:"arrow1",tailShape:"arrow1",showXarrow:!0,animateDrawing:!1,zIndex:0,passProps:{},arrowBodyProps:{},arrowHeadProps:{},arrowTailProps:{},SVGcanvasProps:{},divContainerProps:{},divContainerStyle:{},SVGcanvasStyle:{},_extendSVGcanvas:0,_debug:!1,_cpx1Offset:0,_cpy1Offset:0,_cpx2Offset:0,_cpy2Offset:0},M={};M=H(R,M);var U={startPos:{x:0,y:0,right:0,bottom:0},endPos:{x:0,y:0,right:0,bottom:0}};function G($,V){return E.default.isEqual($,V)}function q($){var V=w.useRef();return G($,V.current)||(V.current=$),V.current}function Z($,V){w.useLayoutEffect($,V.map(q))}var Q=function($,V){var X=w.useState(M),ne=X[0],F=X[1],Y=w.useRef(!1);ne.shouldUpdatePosition=Y;var ee=f(f({},R),$),oe=function(xe){w.useLayoutEffect(function(){var Ce;ne[xe]=(Ce=O==null?void 0:O[xe])===null||Ce===void 0?void 0:Ce.call(O,ee[xe],ne,Y),F(f({},ne))},N[xe].map(function(Ce){return $[Ce]}))};for(var ae in R)oe(ae);var re=w.useState(U),K=re[0],se=re[1],me=y.getElemPos(ne.start);Z(function(){K.startPos=me,Y.current=!0,se(f({},K))},[me]);var ve=y.getElemPos(ne.end);return Z(function(){K.endPos=ve,Y.current=!0,se(f({},K))},[ve]),w.useLayoutEffect(function(){Y.current=!0,se(f({},K))},[ne.headShape.svgElem,ne.tailShape.svgElem]),[ne,K]};l.default=Q},"./src/Xarrow/utils/GetPosition.tsx":function(u,l,d){var f=this&&this.__importDefault||function(E){return E&&E.__esModule?E:{default:E}};Object.defineProperty(l,"__esModule",{value:!0}),l.getPosition=void 0;var p=d("./src/Xarrow/anchors.ts"),g=d("./src/Xarrow/utils/index.ts"),m=f(d("lodash")),x=d("./src/constants.tsx"),w=d("./src/Xarrow/utils/buzzier.js"),y=function(E,S){var b,I,P,A,D=E[0],k=E[1],L=D.startAnchor,B=D.endAnchor,j=D.strokeWidth,C=D.showHead,O=D.headSize,N=D.showTail,_=D.tailSize,W=D.path,T=D.curveness,H=D.gridBreak,R=D.headShape,M=D.tailShape,U=D._extendSVGcanvas,G=D._cpx1Offset,q=D._cpy1Offset,Z=D._cpx2Offset,Q=D._cpy2Offset,$=k.startPos,V=k.endPos,X=S.current,ne=X.svgRef,F=X.lineRef,Y=0,ee=0,oe=p.calcAnchors(L,$),ae=p.calcAnchors(B,V),re=g.getShortestLine(oe,ae),K=re.chosenStart,se=re.chosenEnd,me=K.anchor.position,ve=se.anchor.position,xe=m.default.pick(K,["x","y"]),Ce=m.default.pick(se,["x","y"]),je=g.getSvgPos(ne),Qe=Math.min(xe.x,Ce.x)-je.x,Nt=Math.min(xe.y,Ce.y)-je.y,gt=Ce.x-xe.x,mt=Ce.y-xe.y,Re=Math.abs(Ce.x-xe.x),ie=Math.abs(Ce.y-xe.y),ue=gt>0?1:-1,pe=mt>0?1:-1,Ot=[R.offsetForward,M.offsetForward],Ve=Ot[0],Xe=Ot[1],De=O*j,we=_*j,st=0,lt=0,rt=0,ot=0,vt=De*Ve,Be=we*Xe,Le=Number(T);x.cPaths.includes(W)||(W="smooth"),W==="straight"&&(Le=0,W="smooth");var Yt=O>_?O:_,yt=j+j*Yt/2,Lt=yt,et=yt,Rt=yt,In=yt;et+=Number(U),Lt+=Number(U),Rt+=Number(U),In+=Number(U);var bt=0,ct=Re,xt=0,ut=ie;if(gt<0&&(b=[ct,bt],bt=b[0],ct=b[1]),mt<0&&(I=[ut,xt],xt=I[0],ut=I[1]),Le===0){var Tt=Math.atan(ie/Re);C&&(ct-=De*(1-Ve)*ue*Math.cos(Tt),ut-=De*(1-Ve)*pe*Math.sin(Tt),Tt*=pe,ue<0&&(Tt=(Math.PI-Tt*ue)*ue),st=Math.cos(Tt)*vt-Math.sin(Tt)*De/2,lt=Math.cos(Tt)*De/2+Math.sin(Tt)*vt,Y=Tt*180/Math.PI);var jt=Math.atan(ie/Re);N&&(bt+=we*(1-Xe)*ue*Math.cos(jt),xt+=we*(1-Xe)*pe*Math.sin(jt),jt*=-pe,ue>0&&(jt=(Math.PI-jt*ue)*ue),rt=Math.cos(jt)*Be-Math.sin(jt)*we/2,ot=Math.cos(jt)*we/2+Math.sin(jt)*Be,ee=jt*180/Math.PI)}else ve==="middle"&&(Re>ie?ve=ue?"left":"right":ve=pe?"top":"bottom"),C&&(["left","right"].includes(ve)?(st+=vt*ue,ct-=De*(1-Ve)*ue,lt+=De*ue/2,ve==="left"?(Y=0,ue<0&&(Y+=180)):(Y=180,ue>0&&(Y+=180))):["top","bottom"].includes(ve)&&(st+=De*-pe/2,lt+=vt*pe,ut-=De*pe-lt,ve==="top"?(Y=270,pe>0&&(Y+=180)):(Y=90,pe<0&&(Y+=180))));N&&Le!==0&&(["left","right"].includes(me)?(rt+=Be*-ue,bt+=we*ue+rt,ot+=-(we*ue)/2,me==="left"?(ee=180,ue<0&&(ee+=180)):(ee=0,ue>0&&(ee+=180))):["top","bottom"].includes(me)&&(ot+=Be*-pe,xt+=we*pe+ot,rt+=we*pe/2,me==="top"?(ee=90,pe>0&&(ee+=180)):(ee=270,pe<0&&(ee+=180))));var Ui={x:st,y:lt},qi={x:rt,y:ot},it=bt,tt=xt,dt=ct,ft=ut,vr={};W==="smooth"?vr={hh:function(){it+=Re*Le*ue,dt-=Re*Le*ue},vv:function(){tt+=ie*Le*pe,ft-=ie*Le*pe},hv:function(){it+=Re*Le*ue,ft-=ie*Le*pe},vh:function(){tt+=ie*Le*pe,dt-=Re*Le*ue}}:W==="grid"&&(vr={hh:function(){it+=(Re*H.relative+H.abs)*ue,dt-=(Re*(1-H.relative)-H.abs)*ue,C&&(it-=De*(1-Ve)/2*ue,dt+=De*(1-Ve)/2*ue),N&&(it-=we*(1-Xe)/2*ue,dt+=we*(1-Xe)/2*ue)},vv:function(){tt+=(ie*H.relative+H.abs)*pe,ft-=(ie*(1-H.relative)-H.abs)*pe,C&&(tt-=De*(1-Ve)/2*pe,ft+=De*(1-Ve)/2*pe),N&&(tt-=we*(1-Xe)/2*pe,ft+=we*(1-Xe)/2*pe)},hv:function(){it=ct},vh:function(){tt=ut}});var wt="";["left","right"].includes(me)?wt+="h":["bottom","top"].includes(me)?wt+="v":me==="middle"&&(wt+="m"),["left","right"].includes(ve)?wt+="h":["bottom","top"].includes(ve)?wt+="v":ve==="middle"&&(wt+="m"),Re>ie?wt=wt.replace(/m/g,"h"):wt=wt.replace(/m/g,"v"),vr[wt](),it+=G,tt+=q,dt+=Z,ft+=Q;var Eo=w.buzzierMinSols(bt,it,dt,ct),Io=Eo[0],Do=Eo[1],Po=w.buzzierMinSols(xt,tt,ft,ut),yr=Po[0],Ao=Po[1];Io<0&&(et+=-Io),Do>Re&&(Lt+=Do-Re),yr<0&&(Rt+=-yr),Ao>ie&&(In+=Ao-ie),W==="grid"&&(et+=yt,Lt+=yt,Rt+=yt,In+=yt),bt+=et,ct+=et,xt+=Rt,ut+=Rt,it+=et,dt+=et,tt+=Rt,ft+=Rt;var Gi=Re+et+Lt,Xi=ie+Rt+In;Qe-=et,Nt-=Rt;var br=w.bzFunction(bt,it,dt,ct),xr=w.bzFunction(xt,tt,ft,ut),Yi={x:br(.01),y:xr(.01)},No={x:br(.5),y:xr(.5)},Ki={x:br(.99),y:xr(.99)},wr;return W==="grid"?wr="M "+bt+" "+xt+" L  "+it+" "+tt+" L "+dt+" "+ft+" "+ct+" "+ut:W==="smooth"&&(wr="M "+bt+" "+xt+" C "+it+" "+tt+", "+dt+" "+ft+", "+ct+" "+ut),{cx0:Qe,cy0:Nt,x1:bt,x2:ct,y1:xt,y2:ut,cw:Gi,ch:Xi,cpx1:it,cpy1:tt,cpx2:dt,cpy2:ft,dx:gt,dy:mt,absDx:Re,absDy:ie,headOrient:Y,tailOrient:ee,labelStartPos:Yi,labelMiddlePos:No,labelEndPos:Ki,excLeft:et,excRight:Lt,excUp:Rt,excDown:In,headOffset:vt,arrowHeadOffset:Ui,arrowTailOffset:qi,startPoints:oe,endPoints:ae,mainDivPos:je,xSign:ue,ySign:pe,lineLength:(A=(P=F.current)===null||P===void 0?void 0:P.getTotalLength())!==null&&A!==void 0?A:0,fHeadSize:De,fTailSize:we,arrowPath:wr}};l.getPosition=y},"./src/Xarrow/utils/index.ts":function(u,l){Object.defineProperty(l,"__esModule",{value:!0}),l.getSvgPos=l.getElemPos=l.getShortestLine=l.xStr2absRelative=l.factorDpathStr=l.getElementByPropGiven=void 0;var d=function(y){var E;return typeof y=="string"?E=document.getElementById(y):E=y==null?void 0:y.current,E};l.getElementByPropGiven=d;var f=function(y,E){var S=y.split(/(\d+(?:\.\d+)?)/);return S=S.map(function(b){return Number(b)?(Number(b)*E).toString():b}),S.join("")};l.factorDpathStr=f;var p=function(y){if(typeof y!="string")return{abs:0,relative:.5};var E=y.split("%"),S=0,b=0;if(E.length==1){var I=parseFloat(E[0]);if(!isNaN(I))return S=I,{abs:S,relative:0}}else if(E.length==2){var P=[parseFloat(E[0]),parseFloat(E[1])],A=P[0],D=P[1];if(isNaN(A)||(b=A/100),isNaN(D)||(S=D),!isNaN(A)||!isNaN(D))return{abs:S,relative:b}}};l.xStr2absRelative=p;var g=function(y,E){return Math.sqrt(Math.pow(y.x-E.x,2)+Math.pow(y.y-E.y,2))},m=function(y,E){var S=1/0,b=1/0,I;return y.forEach(function(P){E.forEach(function(A){b=g(P,A),b<S&&(S=b,I={chosenStart:P,chosenEnd:A})})}),I};l.getShortestLine=m;var x=function(y){if(!y)return{x:0,y:0,right:0,bottom:0};var E=y.getBoundingClientRect();return{x:E.left,y:E.top,right:E.right,bottom:E.bottom}};l.getElemPos=x;var w=function(y){if(!y.current)return{x:0,y:0};var E=y.current.getBoundingClientRect(),S=E.left,b=E.top,I=getComputedStyle(y.current),P=Number(I.left.slice(0,-2)),A=Number(I.top.slice(0,-2));return{x:S-P,y:b-A}};l.getSvgPos=w},"./src/Xwrapper.tsx":function(u,l,d){var f=this&&this.__createBinding||(Object.create?function(b,I,P,A){A===void 0&&(A=P),Object.defineProperty(b,A,{enumerable:!0,get:function(){return I[P]}})}:function(b,I,P,A){A===void 0&&(A=P),b[A]=I[P]}),p=this&&this.__setModuleDefault||(Object.create?function(b,I){Object.defineProperty(b,"default",{enumerable:!0,value:I})}:function(b,I){b.default=I}),g=this&&this.__importStar||function(b){if(b&&b.__esModule)return b;var I={};if(b!=null)for(var P in b)P!=="default"&&Object.prototype.hasOwnProperty.call(b,P)&&f(I,b,P);return p(I,b),I};Object.defineProperty(l,"__esModule",{value:!0}),l.XarrowContext=l.XelemContext=void 0;var m=g(d("react"));l.XelemContext=m.default.createContext(null),l.XarrowContext=m.default.createContext(null);var x={},w=0,y=function(b){var I=b.children,P=b.instanceCount,A=m.useState({}),D=A[1],k=function(){return D({})};return m.useEffect(function(){P.current=w,x[P.current]=k},[]),m.default.createElement(l.XarrowContext.Provider,{value:k},I)},E=function(b){var I=b.children,P=b.instanceCount;return m.default.createElement(l.XelemContext.Provider,{value:x[P.current]},I)},S=function(b){var I=b.children,P=m.useRef(w),A=m.useState({}),D=A[1];return m.useEffect(function(){return w++,D({}),function(){delete x[P.current]}},[]),m.default.createElement(E,{instanceCount:P},m.default.createElement(y,{instanceCount:P},I))};l.default=S},"./src/constants.tsx":function(u,l,d){var f=this&&this.__importDefault||function(g){return g&&g.__esModule?g:{default:g}};Object.defineProperty(l,"__esModule",{value:!0}),l.cArrowShapes=l.arrowShapes=l.cSvgElems=l.cPaths=l.cAnchorEdge=void 0;var p=f(d("react"));l.cAnchorEdge=["middle","left","right","top","bottom","auto"],l.cPaths=["smooth","grid","straight"],l.cSvgElems=["circle","ellipse","line","path","polygon","polyline","rect"],l.arrowShapes={arrow1:{svgElem:p.default.createElement("path",{d:"M 0 0 L 1 0.5 L 0 1 L 0.25 0.5 z"}),offsetForward:.25},heart:{svgElem:p.default.createElement("path",{d:"M 0,0.25 A 0.125,0.125 0,0,1 0.5,0.25 A 0.125,0.125 0,0,1 1,0.25 Q 1,0.625 0.5,1 Q 0,0.625 0,0.25 z"}),offsetForward:.1},circle:{svgElem:p.default.createElement("circle",{r:.5,cx:.5,cy:.5}),offsetForward:0}},l.cArrowShapes=Object.keys(l.arrowShapes)},"./src/index.tsx":function(u,l,d){var f=this&&this.__createBinding||(Object.create?function(y,E,S,b){b===void 0&&(b=S),Object.defineProperty(y,b,{enumerable:!0,get:function(){return E[S]}})}:function(y,E,S,b){b===void 0&&(b=S),y[b]=E[S]}),p=this&&this.__exportStar||function(y,E){for(var S in y)S!=="default"&&!Object.prototype.hasOwnProperty.call(E,S)&&f(E,y,S)},g=this&&this.__importDefault||function(y){return y&&y.__esModule?y:{default:y}};Object.defineProperty(l,"__esModule",{value:!0}),l.useXarrow=l.Xwrapper=l.default=void 0;var m=d("./src/Xarrow/Xarrow.tsx");Object.defineProperty(l,"default",{enumerable:!0,get:function(){return g(m).default}}),p(d("./src/types.ts"),l),p(d("./src/constants.tsx"),l);var x=d("./src/Xwrapper.tsx");Object.defineProperty(l,"Xwrapper",{enumerable:!0,get:function(){return g(x).default}});var w=d("./src/useXarrow.tsx");Object.defineProperty(l,"useXarrow",{enumerable:!0,get:function(){return g(w).default}})},"./src/types.ts":function(u,l){Object.defineProperty(l,"__esModule",{value:!0})},"./src/useXarrow.tsx":function(u,l,d){Object.defineProperty(l,"__esModule",{value:!0});var f=d("react"),p=d("./src/Xwrapper.tsx"),g=function(){var m=f.useState({}),x=m[1],w=function(){return x({})},y=f.useContext(p.XelemContext);return y||(y=function(){}),f.useLayoutEffect(function(){y()}),w};l.default=g},lodash:function(u){u.exports=r},"prop-types":function(u){u.exports=o},react:function(u){u.exports=n}},a={};function s(u){var l=a[u];if(l!==void 0)return l.exports;var d=a[u]={id:u,loaded:!1,exports:{}};return i[u].call(d.exports,d,d.exports,s),d.loaded=!0,d.exports}s.c=a,function(){s.d=function(u,l){for(var d in l)s.o(l,d)&&!s.o(u,d)&&Object.defineProperty(u,d,{enumerable:!0,get:l[d]})}}(),function(){s.o=function(u,l){return Object.prototype.hasOwnProperty.call(u,l)}}(),function(){s.r=function(u){typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(u,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(u,"__esModule",{value:!0})}}(),function(){s.nmd=function(u){return u.paths=[],u.children||(u.children=[]),u}}();var c=s(s.s="./src/index.tsx");return c}()})})(dP);const{TabPane:$a}=xs,{Option:Vu}=wn,{Panel:fP}=ld,pP=()=>{var X,ne;const{roleId:e}=ao(),t=id(F=>F.user.userId),[n,r]=v.useState("active-sequences"),[o,i]=v.useState([]),[a,s]=v.useState(null),[c,u]=v.useState({}),[l,d]=v.useState(!1),[f,p]=v.useState(!1),[g,m]=v.useState([]),[x,w]=v.useState([]),[y,E]=v.useState("all"),[S,b]=v.useState([]),[I,P]=v.useState({}),[A,D]=v.useState([]),[k,L]=v.useState(!1),[B,j]=v.useState([]),[C,O]=v.useState(!1);if(!e)return h.jsx(Ie,{style:{margin:24},children:h.jsx(zt,{message:"Missing Role ID",description:"Unable to load sequence execution dashboard. Role ID is required.",type:"warning",showIcon:!0})});v.useEffect(()=>{if(e){N(),W();const F=setInterval(()=>{N()},3e4);return()=>clearInterval(F)}},[e]),v.useEffect(()=>{o&&o.length>0&&_()},[o]);const N=async()=>{try{d(!0);const F=await ll(),Y=F||F||[],ee=Array.isArray(Y)?Y:[];i(ee);const oe=ee.map(K=>({...K,sequenceSteps:[],stats:{totalCandidates:0,statusBreakdown:{pending:0,completed:0,failed:0}}}));b(oe),ee&&ee.length>0&&!a&&s(ee[0]);const ae=await $i();let re={};Array.isArray(ae)?re=ae.reduce((K,se)=>(se&&se.name&&(K[se.name]=se),K),{}):ae&&typeof ae=="object"&&(re=ae),u(re);try{Fr({},Pe.get360AndPreQualificationCandidates.replace(":roleId",e),K=>{const me=(K||[]).map(ve=>({id:ve.id,name:ve.candidate.first_name+" "+ve.candidate.last_name||"N/A",phones:ve.candidate.phones[0]||[],candidate:ve.candidate||{},emails:ve.candidate.emails[0]||[]}));console.log("hjsdgfgsdgfsdgfsd",me),w(me)},K=>{var se;Ee.error(((se=K==null?void 0:K.response)==null?void 0:se.message)||"Failed to get role applications. Try refreshing the page!")})}catch{Ee.error("Failed to get role applications. Try refreshing the page!")}}catch(F){console.error("Error loading dashboard data:",F),Ee.error("Failed to load dashboard data"),u({}),i([])}finally{d(!1)}},_=async()=>{try{L(!0);const F=[],Y={};if(!Array.isArray(o)||o.length===0){console.warn("availableSequences is not an array or empty:",o);return}for(const ee of o)try{let oe=ee,ae={};try{oe=await ul(ee.id)}catch(re){console.warn(`Could not load steps for sequence ${ee.id}:`,re.message),oe={...ee,sequenceSteps:[]}}try{ae=await ki(ee.id)}catch(re){console.warn(`Could not load stats for sequence ${ee.id}:`,re.message),ae={totalCandidates:0,statusBreakdown:{pending:0,completed:0,failed:0}}}F.push({...oe,stats:ae||{}}),Y[ee.id]=ae||{}}catch(oe){console.error(`Error loading details for sequence ${ee.id}:`,oe),F.push({...ee,sequenceSteps:[],stats:{totalCandidates:0,statusBreakdown:{pending:0,completed:0,failed:0}}})}b(F),P(Y)}catch(F){console.error("Error loading detailed sequences:",F),Ee.error("Failed to load sequence details")}finally{L(!1)}},W=async()=>{try{Fr({roleId:e,userId:t},Pe.getRoleCandidates,F=>{console.log("Role candidates response:",F),j(F||[])},F=>{var Y;Ee.error(((Y=F==null?void 0:F.response)==null?void 0:Y.message)||"Failed to get role candidates. Try refreshing the page!")})}catch(F){console.error("Error loading role candidates:",F),Ee.error("Failed to load role candidates"),j([])}finally{O(!1)}},T=()=>Object.values(c||{}).reduce((F,Y)=>F+(Y&&Y.active||0),0),H=()=>Object.values(c||{}).reduce((F,Y)=>F+(Y&&Y.waiting||0),0),R=()=>Object.values(c||{}).reduce((F,Y)=>F+(Y&&Y.completed||0),0),M=()=>Object.values(c||{}).reduce((F,Y)=>F+(Y&&Y.failed||0),0),U=async()=>{if(!a||g.length===0){Ee.warning("Please select a sequence and candidates before starting execution");return}try{d(!0),await cl(a.id,g),Ee.success(`Sequence "${a.name}" started for ${g.length} candidates`),p(!1),m([]),setTimeout(N,1e3)}catch(F){Ee.error("Failed to start sequence execution"),console.error("Error starting sequence:",F)}finally{d(!1)}},G=()=>{const F=T();return M()>0?"error":F>0?"processing":"idle"},q=F=>{switch(F){case"processing":return"#1890ff";case"error":return"#ff4d4f";case"idle":return"#52c41a";default:return"#d9d9d9"}},Z=F=>{const Y=F.status?String(F.status).toUpperCase():"UNKNOWN",ee={ACTIVE:"green",INACTIVE:"red",DRAFT:"orange",PAUSED:"yellow",COMPLETED:"blue"};return h.jsx(Jt,{color:ee[Y]||"default",children:Y})},Q=F=>{switch(F==null?void 0:F.toLowerCase()){case"completed":return h.jsx(tr,{style:{color:"#52c41a"}});case"pending":return h.jsx(Kn,{style:{color:"#faad14"}});case"failed":return h.jsx(ri,{style:{color:"#ff4d4f"}});default:return h.jsx(Kn,{style:{color:"#d9d9d9"}})}},$=F=>{if(!F.stats||!F.stats.statusBreakdown)return 0;const{statusBreakdown:Y}=F.stats,ee=Object.values(Y).reduce((ae,re)=>ae+re,0),oe=Y.completed||0;return ee>0?Math.round(oe/ee*100):0},V=G();return h.jsxs("div",{style:{padding:24},children:[h.jsx(Ie,{style:{marginBottom:24},children:h.jsxs(Et,{gutter:16,align:"middle",children:[h.jsxs(be,{children:[h.jsx("h2",{style:{margin:0},children:"Sequence Execution Dashboard"}),h.jsxs("p",{style:{margin:0,color:"#666"},children:["Viewing Role: ",e," | Total Sequences: ",(o||[]).length," | Role Candidates: ",B.length," | System Status:",h.jsx(nn,{color:q(V),text:V.toUpperCase(),style:{marginLeft:8}})]})]}),h.jsx(be,{flex:"auto"}),h.jsx(be,{children:h.jsxs(nt,{children:[h.jsx(wn,{style:{width:300},placeholder:"Select a sequence to execute",value:a==null?void 0:a.id,onChange:F=>{try{const Y=(o||[]).find(ee=>ee.id===F);s(Y||null)}catch(Y){console.error("Error selecting sequence:",Y),s(null)}},children:(o||[]).map(F=>{var Y;return h.jsxs(Vu,{value:F==null?void 0:F.id,children:[(F==null?void 0:F.name)||"Unknown Sequence"," (",((Y=F==null?void 0:F.sequenceSteps)==null?void 0:Y.length)||0," steps)"]},(F==null?void 0:F.id)||Math.random())})}),h.jsx(he,{type:"primary",icon:h.jsx(Yo,{}),onClick:()=>p(!0),disabled:!a,children:"Execute Sequence"}),h.jsx(he,{icon:h.jsx(jn,{}),onClick:N,loading:l,children:"Refresh"})]})})]})}),h.jsxs(Et,{gutter:16,style:{marginBottom:24},children:[h.jsx(be,{span:6,children:h.jsx(Ie,{children:h.jsx(Fe,{title:"Active Jobs",value:T(),valueStyle:{color:"#1890ff"},prefix:h.jsx(Yo,{})})})}),h.jsx(be,{span:6,children:h.jsx(Ie,{children:h.jsx(Fe,{title:"Waiting Jobs",value:H(),valueStyle:{color:"#faad14"},prefix:h.jsx(Ul,{})})})}),h.jsx(be,{span:6,children:h.jsx(Ie,{children:h.jsx(Fe,{title:"Completed Jobs",value:R(),valueStyle:{color:"#52c41a"},prefix:h.jsx(Vl,{})})})}),h.jsx(be,{span:6,children:h.jsx(Ie,{children:h.jsx(Fe,{title:"Failed Jobs",value:M(),valueStyle:{color:"#ff4d4f"},prefix:h.jsx(hd,{})})})})]}),M()>0&&h.jsx(zt,{message:"System Alert",description:`There are ${M()} failed jobs that require attention.`,type:"error",showIcon:!0,style:{marginBottom:24},action:h.jsx(he,{size:"small",onClick:()=>r("execution-monitor"),children:"View Details"})}),a&&h.jsx(zt,{message:"Sequence Ready",description:`Sequence "${a.name}" is configured with ${((X=a.sequenceSteps)==null?void 0:X.length)||0} steps and ready for execution.`,type:"success",showIcon:!0,style:{marginBottom:24},action:h.jsx(he,{size:"small",type:"primary",onClick:()=>p(!0),children:"Execute Now"})}),(o||[]).length===0&&!l&&h.jsx(zt,{message:"No Sequences Available",description:"No sequences found in the system. Please create a sequence using the Advanced Sequences tab first.",type:"warning",showIcon:!0,style:{marginBottom:24},action:h.jsx(he,{size:"small",onClick:()=>window.location.reload(),children:"Refresh"})}),h.jsx(Ie,{children:h.jsxs(xs,{activeKey:n,onChange:r,size:"large",children:[h.jsxs($a,{tab:h.jsx(nn,{count:(o||[]).length,offset:[10,0],children:h.jsxs("span",{children:[h.jsx(zv,{}),"Active Sequences"]})}),children:[h.jsx("div",{style:{marginBottom:16},children:h.jsxs(nt,{children:[h.jsx("span",{children:"Filter by status:"}),h.jsxs(Ro.Group,{value:y,onChange:F=>E(F.target.value),buttonStyle:"solid",children:[h.jsxs(Ro.Button,{value:"all",children:[h.jsx(Sv,{})," All (",(o||[]).length,")"]}),h.jsxs(Ro.Button,{value:"active",children:[h.jsx(tr,{})," Active (",(o||[]).filter(F=>F.status&&String(F.status).toUpperCase()==="ACTIVE").length,")"]}),h.jsxs(Ro.Button,{value:"inactive",children:[h.jsx(ri,{})," Inactive (",(o||[]).filter(F=>!F.status||String(F.status).toUpperCase()!=="ACTIVE").length,")"]})]}),h.jsx(he,{icon:h.jsx(jn,{}),onClick:_,loading:k,children:"Refresh"})]})}),k?h.jsxs("div",{style:{textAlign:"center",padding:40},children:[h.jsx(jn,{spin:!0,style:{fontSize:24}}),h.jsx("p",{children:"Loading sequence details..."})]}):(o||[]).length===0?h.jsx(zt,{message:"No Sequences Found",description:"No sequences found in the system.",type:"info",showIcon:!0}):h.jsx(ld,{activeKey:A,onChange:D,ghost:!0,children:(o||[]).map(F=>{var Y,ee;return h.jsxs(fP,{header:h.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",width:"100%"},children:[h.jsx("div",{children:h.jsxs(nt,{children:[h.jsx("strong",{children:F.name||"Unnamed Sequence"}),Z(F),h.jsx(nn,{count:((Y=F.sequenceSteps)==null?void 0:Y.length)||0,showZero:!0,color:"#1890ff"})]})}),h.jsx("div",{style:{marginRight:24},children:h.jsxs(nt,{children:[h.jsx(Sn,{title:"Sequence Progress",children:h.jsx(Cs,{type:"circle",size:40,percent:$(F),format:oe=>`${oe}%`})}),h.jsx(he,{size:"small",icon:h.jsx(sd,{}),onClick:oe=>{oe.stopPropagation(),s(F),r("execution-monitor")},children:"Monitor"}),h.jsxs(he,{size:"small",type:"primary",icon:h.jsx(Yo,{}),onClick:async oe=>{var ae;oe.stopPropagation();try{d(!0),B.length===0&&await W(),B.length>0?(await tp(F.id,e,10),Ee.success(`Started sequence "${F.name}" with ${Math.min(B.length,10)} role candidates!`)):(await ep(F.id),Ee.success(`Started sequence "${F.name}" with test candidates (no role candidates found)!`)),setTimeout(()=>{N(),_()},2e3)}catch(re){Ee.error("Failed to start sequence: "+(((ae=re.response)==null?void 0:ae.message)||re.message)),console.error("Error:",re)}finally{d(!1)}},loading:l,children:["Start with Role Candidates (",B.length,")"]})]})})]}),children:[h.jsxs(Et,{gutter:16,children:[h.jsx(be,{span:12,children:h.jsx(Ie,{size:"small",title:"Sequence Details",children:h.jsxs(Ct,{size:"small",column:1,children:[h.jsx(Ct.Item,{label:"Description",children:F.description||"No description available"}),h.jsx(Ct.Item,{label:"Created",children:F.createdAt?new Date(F.createdAt).toLocaleDateString():"Unknown"}),h.jsx(Ct.Item,{label:"Total Steps",children:((ee=F.sequenceSteps)==null?void 0:ee.length)||0}),h.jsx(Ct.Item,{label:"Status",children:Z(F)})]})})}),h.jsx(be,{span:12,children:h.jsx(Ie,{size:"small",title:"Execution Statistics",children:F.stats&&F.stats.statusBreakdown?h.jsxs(Et,{gutter:8,children:[h.jsx(be,{span:8,children:h.jsx(Fe,{title:"Total Candidates",value:F.stats.totalCandidates||0,valueStyle:{fontSize:16}})}),h.jsx(be,{span:8,children:h.jsx(Fe,{title:"Completed",value:F.stats.statusBreakdown.completed||0,valueStyle:{fontSize:16,color:"#52c41a"}})}),h.jsx(be,{span:8,children:h.jsx(Fe,{title:"Failed",value:F.stats.statusBreakdown.failed||0,valueStyle:{fontSize:16,color:"#ff4d4f"}})})]}):h.jsx(zt,{message:"No execution data available",type:"info",size:"small"})})})]}),F.sequenceSteps&&F.sequenceSteps.length>0&&h.jsx(Ie,{size:"small",title:"Sequence Steps",style:{marginTop:16},children:h.jsx(Ss,{dataSource:F.sequenceSteps,rowKey:"id",size:"small",pagination:!1,columns:[{title:"Order",dataIndex:"order",key:"order",width:80,render:oe=>h.jsx(nn,{count:oe,showZero:!0,color:"#1890ff"})},{title:"Step Name",dataIndex:"name",key:"name",render:oe=>oe||"Unnamed Step"},{title:"Type",dataIndex:"type",key:"type",render:oe=>h.jsx(Jt,{color:"blue",children:oe})},{title:"Medium",dataIndex:"medium",key:"medium",render:oe=>h.jsx(Jt,{color:"green",children:oe})},{title:"Template",dataIndex:"emailTemplate",key:"template",render:oe=>(oe==null?void 0:oe.name)||"No template"},{title:"Status",key:"status",render:(oe,ae)=>h.jsxs(nt,{children:[Q("pending"),h.jsx("span",{children:"Ready"})]})}]})})]},F.id)})})]},"active-sequences"),h.jsx($a,{tab:h.jsx(nn,{count:Object.keys(c||{}).length,offset:[10,0],children:h.jsxs("span",{children:[h.jsx(Ul,{}),"Queue Monitor"]})}),children:h.jsx(H2,{})},"monitor"),h.jsx($a,{tab:h.jsxs("span",{children:[h.jsx(Vl,{}),"Execution Monitor"]}),children:a?h.jsx(W2,{sequenceId:a.id}):h.jsx(zt,{message:"No Sequence Selected",description:"Please select a sequence from the dropdown above to monitor execution.",type:"info",showIcon:!0})},"execution-monitor")]})}),h.jsx(Hr,{title:"Execute Sequence",open:f,onOk:U,onCancel:()=>p(!1),confirmLoading:l,width:600,children:h.jsxs(nt,{direction:"vertical",style:{width:"100%"},children:[a&&h.jsxs(Ie,{size:"small",children:[h.jsx("h4",{children:"Sequence Details"}),h.jsxs("p",{children:[h.jsx("strong",{children:"Name:"})," ",a.name]}),h.jsxs("p",{children:[h.jsx("strong",{children:"Description:"})," ",a.description]}),h.jsxs("p",{children:[h.jsx("strong",{children:"Total Steps:"})," ",((ne=a.sequenceSteps)==null?void 0:ne.length)||0]}),h.jsxs("p",{children:[h.jsx("strong",{children:"Status:"})," ",a.status]})]}),h.jsxs("div",{children:[h.jsx("h4",{children:"Select Candidates:"}),h.jsx(wn,{mode:"multiple",style:{width:"100%"},placeholder:"Select candidates to include in sequence execution",value:g,onChange:m,optionLabelProp:"label",children:x.map(F=>h.jsx(Vu,{value:F.id,label:`${F.name} (${F.emails.email})`,children:h.jsxs("div",{style:{display:"flex",justifyContent:"space-between"},children:[h.jsxs("span",{children:[h.jsx(jg,{})," ",F.name]}),h.jsx("span",{style:{color:"#666"},children:F.emails.email})]})},F.id))})]}),g.length>0&&h.jsx(zt,{message:`Ready to execute sequence for ${g.length} candidate(s)`,type:"info",showIcon:!0})]})})]})};class hP extends z.Component{constructor(n){super(n);jl(this,"handleRetry",()=>{this.setState({hasError:!1,error:null,errorInfo:null})});this.state={hasError:!1,error:null,errorInfo:null}}static getDerivedStateFromError(n){return{hasError:!0}}componentDidCatch(n,r){this.setState({error:n,errorInfo:r||null}),console.error("Sequence Execution Error:",n,r)}render(){if(this.state.hasError)try{return h.jsxs(Ie,{style:{margin:24},children:[h.jsx(zt,{message:"Sequence Execution Error",description:"Something went wrong with the sequence execution interface. Please try refreshing or contact support if the problem persists.",type:"error",showIcon:!0,icon:h.jsx(gv,{}),action:h.jsx(he,{size:"small",icon:h.jsx(jn,{}),onClick:this.handleRetry,children:"Retry"})}),!1]})}catch{return h.jsxs("div",{style:{padding:24,textAlign:"center"},children:[h.jsx("h3",{children:"Something went wrong"}),h.jsx("p",{children:"Please refresh the page and try again."}),h.jsx("button",{onClick:()=>window.location.reload(),children:"Refresh Page"})]})}return this.props.children}}function TP(e){ao();const[t,n]=v.useState("1"),r=[{key:"1",label:"Details",component:h.jsx($2,{})},{key:"2",label:"Sequence",component:h.jsx(k2,{})},{key:"3",label:"Advance Sequence",component:h.jsx(uP,{})},{key:"4",label:"Sequence Execution Dashboard",component:h.jsx(hP,{children:h.jsx(pP,{})})}];return h.jsxs("div",{children:[h.jsx(Dg,{children:h.jsx("title",{children:" Sequences "})}),h.jsx(Et,{style:{backgroundColor:"white",height:"60px"},children:h.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",width:"100%"},children:[h.jsx(xs,{defaultActiveKey:"1",items:r.map(o=>({...o,label:h.jsx("span",{style:{color:"#424242",fontWeight:400,border:"none",marginBottom:0,fontSize:"16px",transition:"color 0.3s ease-in-out",fontFamily:"Poppins, sans-serif"},children:o.label})})),onChange:o=>{n(o)},activeKey:t,tabBarStyle:{marginBottom:0,fontWeight:500},className:"custom-tabs"}),h.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"10px",marginRight:"15px"},children:[h.jsx("p",{style:{fontSize:"16px",color:"#424242",fontFamily:"Poppins",fontWeight:400},children:"Campaign"}),h.jsx("p",{style:{fontSize:"16px",color:"#424242",fontFamily:"Poppins",fontWeight:400},children:"|"}),h.jsxs("p",{style:{fontSize:"16px",color:"#424242",fontFamily:"Poppins",fontWeight:400,display:"flex",alignItems:"center",gap:"10px"},children:["Designer ",h.jsx(Se,{icon:"f7:person-2-fill",width:"18",height:"18"})]}),h.jsx("p",{style:{fontSize:"16px",color:"#424242",fontFamily:"Poppins",fontWeight:400},children:"|"}),h.jsx("p",{style:{fontSize:"16px",color:"#424242",fontFamily:"Poppins",fontWeight:400},children:"Status"}),h.jsx(he,{type:"primary",style:{borderRadius:"4px",height:"35px",width:"90px",fontSize:"16px",marginLeft:"10px",backgroundColor:"#1A84DE",fontWeight:500},onClick:()=>{},children:"Start"})]})]})}),h.jsx("div",{style:{marginTop:"20px"},children:r==null?void 0:r.map(o=>h.jsx("div",{style:{display:t===o.key?"block":"none"},children:o.component},o.key))})]})}export{TP as default};
