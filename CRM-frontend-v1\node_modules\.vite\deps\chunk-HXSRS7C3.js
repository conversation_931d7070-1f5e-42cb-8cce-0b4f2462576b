import {
  __esm,
  __export
} from "./chunk-WXXH56N5.js";

// node_modules/@mui/utils/clamp/clamp.js
function clamp(val, min = Number.MIN_SAFE_INTEGER, max = Number.MAX_SAFE_INTEGER) {
  return Math.max(min, Math.min(val, max));
}
var clamp_default;
var init_clamp = __esm({
  "node_modules/@mui/utils/clamp/clamp.js"() {
    clamp_default = clamp;
  }
});

// node_modules/@mui/utils/clamp/index.js
var clamp_exports = {};
__export(clamp_exports, {
  default: () => clamp_default
});
var init_clamp2 = __esm({
  "node_modules/@mui/utils/clamp/index.js"() {
    init_clamp();
  }
});

export {
  clamp_default,
  clamp_exports,
  init_clamp2 as init_clamp
};
//# sourceMappingURL=chunk-HXSRS7C3.js.map
