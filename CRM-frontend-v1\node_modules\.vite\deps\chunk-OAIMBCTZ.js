import {
  defaultTheme_default,
  identifier_default
} from "./chunk-3DRS7PW6.js";
import {
  useTheme_default
} from "./chunk-NIBAGSVZ.js";
import {
  require_react
} from "./chunk-XLKA4T3M.js";
import {
  __toESM
} from "./chunk-WXXH56N5.js";

// node_modules/@mui/material/styles/useTheme.js
var React = __toESM(require_react());
function useTheme() {
  const theme = useTheme_default(defaultTheme_default);
  if (true) {
    React.useDebugValue(theme);
  }
  return theme[identifier_default] || theme;
}

export {
  useTheme
};
//# sourceMappingURL=chunk-OAIMBCTZ.js.map
