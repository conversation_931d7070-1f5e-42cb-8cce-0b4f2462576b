<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />



    <!-- Favicon -->  
    <!-- <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png"> -->
    <!-- <link rel="manifest" href="/manifest.json"> -->
    <link rel="icon" type="image/x-icon" sizes="32x32" href="/assets/favicon-Mqiv-TXy.ico">
    <!-- <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png"> -->
    
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">

    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="<PERSON><PERSON> is react free admin template build using Vite" />
    <link rel="apple-touch-icon" href="/logo192.png" />
    <title>CRM</title>

    <!-- this is to resolve issue in old safari browser in tablet -->
    <script src="https://cdn.jsdelivr.net/npm/resize-observer-polyfill@1.5.1/dist/ResizeObserver.min.js"></script>
    <script type="module" crossorigin src="/assets/index-C8YzRPez.js"></script>
    <link rel="stylesheet" crossorigin href="/assets/index-CrrVnfYY.css">
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
 
  </body>
</html>
