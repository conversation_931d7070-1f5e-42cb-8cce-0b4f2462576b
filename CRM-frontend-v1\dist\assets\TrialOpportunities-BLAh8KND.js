import{r as o,j as s,az as c,e as u,s as l}from"./index-C8YzRPez.js";import"./Details-CjVzTy9y.js";import"./SendCandidateToClient-Ck2C0lTM.js";import"./FileSaver.min-Cr1ZWOZa.js";import{C as m}from"./index-t3u8k2cB.js";import"./styles-Bufj3DzR.js";import{V as E}from"./VerticalCarouselCards-CRUUFgfx.js";import"./PrimaryButton-BOAlYSuK.js";import"./FocusPointBox-BS774mbV.js";import"./FilePreview-Dz6aRE4w.js";import"./index-C2EZYIAX.js";import"./useClosable-D2fLMMij.js";import"./TextEditor-DytKcPMl.js";import"./index-BWafDGV1.js";import"./CustomSelectForForms-BI2fpcGx.js";import"./CloseOutlined-CK98qAnm.js";import"./FileUpload-CLRSAav9.js";import"./MenuItem--Pxpzvee.js";import"./clientStatusConstants-BE3-jCCO.js";const g={superLargeDesktop:{breakpoint:{max:4e3,min:1200},items:3},desktop:{breakpoint:{max:1200,min:1024},items:3},tablet:{breakpoint:{max:1024,min:768},items:2},mobile:{breakpoint:{max:768,min:0},items:1}};function V(h){const[e,n]=o.useState({}),[p,r]=o.useState(!1),a=[{id:1,title:"Trial Sent",color:"#FF9500",status:"SENT",leads:(e==null?void 0:e.SENT)||[]},{id:2,title:"Re-Trial",color:"#00C7BE",status:"RETRIAL",leads:(e==null?void 0:e.RETRIAL)||[]},{id:3,title:"Trial Success",color:"#5856D6",status:"RESULTS_RECIEVED",leads:(e==null?void 0:e.SUCCESS)||[]},{id:4,title:"Trial Failed",color:"#32ADE6",status:"FAILED",leads:(e==null?void 0:e.FAILED)||[]}],d=()=>{try{r(!0),c({},u.getTrialProspects,t=>{n(t),r(!1)},t=>{var i;l.error(((i=t==null?void 0:t.response)==null?void 0:i.message)||"Failed to get partially interested prospects. Try refreshing the page!"),r(!1)})}catch{l.error("Failed to get partially interested prospects. Try refreshing the page!"),r(!1)}};return o.useEffect(()=>{d()},[]),s.jsx("div",{style:{width:"100%",marginTop:"20px",height:"100vh"},children:s.jsx(m,{responsive:g,arrows:!1,autoPlay:!1,keyBoardControl:!0,containerClass:"carousel-container",removeArrowOnDeviceType:["tablet","mobile"],children:a==null?void 0:a.map(t=>s.jsx("div",{style:{height:"100%",borderRight:"2px solid #CED0DA",cursor:"pointer",userSelect:"none"},children:s.jsx(E,{title:t==null?void 0:t.title,leads:t==null?void 0:t.leads,color:t==null?void 0:t.color,status:t==null?void 0:t.status,isLaoding:p})},t.id))})})}export{V as default};
