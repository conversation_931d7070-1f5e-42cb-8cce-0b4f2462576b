import {
  require_react
} from "./chunk-XLKA4T3M.js";
import {
  __toESM
} from "./chunk-WXXH56N5.js";

// node_modules/@mui/utils/useEnhancedEffect/useEnhancedEffect.js
var React = __toESM(require_react());
var useEnhancedEffect = typeof window !== "undefined" ? React.useLayoutEffect : React.useEffect;
var useEnhancedEffect_default = useEnhancedEffect;

export {
  useEnhancedEffect_default
};
//# sourceMappingURL=chunk-SCN6NK6P.js.map
