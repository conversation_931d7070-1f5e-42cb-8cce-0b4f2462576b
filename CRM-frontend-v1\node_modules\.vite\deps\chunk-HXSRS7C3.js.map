{"version": 3, "sources": ["../../@mui/utils/clamp/clamp.js", "../../@mui/utils/clamp/index.js"], "sourcesContent": ["function clamp(val, min = Number.MIN_SAFE_INTEGER, max = Number.MAX_SAFE_INTEGER) {\n  return Math.max(min, Math.min(val, max));\n}\nexport default clamp;", "export { default } from './clamp';"], "mappings": ";;;;;;AAAA,SAAS,MAAM,KAAK,MAAM,OAAO,kBAAkB,MAAM,OAAO,kBAAkB;AAChF,SAAO,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,GAAG,CAAC;AACzC;AAFA,IAGO;AAHP;AAAA;AAGA,IAAO,gBAAQ;AAAA;AAAA;;;ACHf;AAAA;AAAA;AAAA;AAAA,IAAAA,cAAA;AAAA;AAAA;AAAA;AAAA;", "names": ["init_clamp"]}