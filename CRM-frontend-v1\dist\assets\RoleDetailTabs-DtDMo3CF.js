import{j as a,bm as e}from"./index-C8YzRPez.js";function l({items:o,activeKey:n,onChangeTab:s}){const i=t=>{console.log(t),s(t)};return a.jsxs("div",{children:[a.jsx(e,{defaultActiveKey:"1",items:o.map(t=>({...t,label:a.jsx("span",{style:{color:n===t.key?"#5DA9E9":"#344665",fontWeight:(n===t.key,500),border:"none",marginBottom:0,fontSize:"16px",transition:"color 0.3s ease-in-out",fontFamily:"Poppins"},children:t.label})})),onChange:i,activeKey:n,tabBarStyle:{marginBottom:0,fontWeight:500},className:"custom-tabs"}),a.jsx("style",{children:`
          .custom-tabs .ant-tabs-nav-wrap {
            align-items: center; /* Tabs ko center align karne ke liye */
          }
          .custom-tabs .ant-tabs-tab {
            padding: 8px 16px; /* Vertical padding adjust kiya */
          }
          .custom-tabs .ant-tabs-ink-bar {
            height: 3px !important; /* Line ki thickness */
            border-radius: 4px !important; /* Line ka shape */
            background-color: #5DA9E9 !important; /* Line ka color */
            transition: all 0.3s ease-in-out;
            bottom: 3px !important; /* Line ko upar shift kar diya */
          }
        `})]})}export{l as D};
