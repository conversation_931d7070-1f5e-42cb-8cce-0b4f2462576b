import{R as a,a1 as m,bL as C}from"./index-C8YzRPez.js";function E(s){if(s)return{closable:s.closable,closeIcon:s.closeIcon}}function g(s){const{closable:l,closeIcon:n}=s||{};return a.useMemo(()=>{if(!l&&(l===!1||n===!1||n===null))return!1;if(l===void 0&&n===void 0)return null;let e={closeIcon:typeof n!="boolean"&&n!==null?n:void 0};return l&&typeof l=="object"&&(e=Object.assign(Object.assign({},e),l)),e},[l,n])}function b(){const s={};for(var l=arguments.length,n=new Array(l),e=0;e<l;e++)n[e]=arguments[e];return n.forEach(o=>{o&&Object.keys(o).forEach(r=>{o[r]!==void 0&&(s[r]=o[r])})}),s}const p={};function h(s,l){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:p;const e=g(s),o=g(l),r=typeof e!="boolean"?!!(e!=null&&e.disabled):!1,c=a.useMemo(()=>Object.assign({closeIcon:a.createElement(m,null)},n),[n]),f=a.useMemo(()=>e===!1?!1:e?b(c,o,e):o===!1?!1:o?b(c,o):c.closable?c:!1,[e,o,c]);return a.useMemo(()=>{if(f===!1)return[!1,null,r];const{closeIconRender:u}=c,{closeIcon:d}=f;let t=d;if(t!=null){u&&(t=u(d));const i=C(f,!0);Object.keys(i).length&&(t=a.isValidElement(t)?a.cloneElement(t,i):a.createElement("span",Object.assign({},i),t))}return[!0,t,r]},[f,c])}export{E as p,h as u};
