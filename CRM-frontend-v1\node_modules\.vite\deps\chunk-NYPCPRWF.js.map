{"version": 3, "sources": ["../../@mui/material/FormControl/FormControl.js", "../../@mui/material/InputBase/utils.js", "../../@mui/material/FormControl/formControlClasses.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"children\", \"className\", \"color\", \"component\", \"disabled\", \"error\", \"focused\", \"fullWidth\", \"hiddenLabel\", \"margin\", \"required\", \"size\", \"variant\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport useThemeProps from '../styles/useThemeProps';\nimport styled from '../styles/styled';\nimport { isFilled, isAdornedStart } from '../InputBase/utils';\nimport capitalize from '../utils/capitalize';\nimport isMuiElement from '../utils/isMuiElement';\nimport FormControlContext from './FormControlContext';\nimport { getFormControlUtilityClasses } from './formControlClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    margin,\n    fullWidth\n  } = ownerState;\n  const slots = {\n    root: ['root', margin !== 'none' && `margin${capitalize(margin)}`, fullWidth && 'fullWidth']\n  };\n  return composeClasses(slots, getFormControlUtilityClasses, classes);\n};\nconst FormControlRoot = styled('div', {\n  name: 'MuiFormControl',\n  slot: 'Root',\n  overridesResolver: ({\n    ownerState\n  }, styles) => {\n    return _extends({}, styles.root, styles[`margin${capitalize(ownerState.margin)}`], ownerState.fullWidth && styles.fullWidth);\n  }\n})(({\n  ownerState\n}) => _extends({\n  display: 'inline-flex',\n  flexDirection: 'column',\n  position: 'relative',\n  // Reset fieldset default style.\n  minWidth: 0,\n  padding: 0,\n  margin: 0,\n  border: 0,\n  verticalAlign: 'top'\n}, ownerState.margin === 'normal' && {\n  marginTop: 16,\n  marginBottom: 8\n}, ownerState.margin === 'dense' && {\n  marginTop: 8,\n  marginBottom: 4\n}, ownerState.fullWidth && {\n  width: '100%'\n}));\n\n/**\n * Provides context such as filled/focused/error/required for form inputs.\n * Relying on the context provides high flexibility and ensures that the state always stays\n * consistent across the children of the `FormControl`.\n * This context is used by the following components:\n *\n *  - FormLabel\n *  - FormHelperText\n *  - Input\n *  - InputLabel\n *\n * You can find one composition example below and more going to [the demos](/material-ui/react-text-field/#components).\n *\n * ```jsx\n * <FormControl>\n *   <InputLabel htmlFor=\"my-input\">Email address</InputLabel>\n *   <Input id=\"my-input\" aria-describedby=\"my-helper-text\" />\n *   <FormHelperText id=\"my-helper-text\">We'll never share your email.</FormHelperText>\n * </FormControl>\n * ```\n *\n * ⚠️ Only one `InputBase` can be used within a FormControl because it creates visual inconsistencies.\n * For instance, only one input can be focused at the same time, the state shouldn't be shared.\n */\nconst FormControl = /*#__PURE__*/React.forwardRef(function FormControl(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiFormControl'\n  });\n  const {\n      children,\n      className,\n      color = 'primary',\n      component = 'div',\n      disabled = false,\n      error = false,\n      focused: visuallyFocused,\n      fullWidth = false,\n      hiddenLabel = false,\n      margin = 'none',\n      required = false,\n      size = 'medium',\n      variant = 'outlined'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    color,\n    component,\n    disabled,\n    error,\n    fullWidth,\n    hiddenLabel,\n    margin,\n    required,\n    size,\n    variant\n  });\n  const classes = useUtilityClasses(ownerState);\n  const [adornedStart, setAdornedStart] = React.useState(() => {\n    // We need to iterate through the children and find the Input in order\n    // to fully support server-side rendering.\n    let initialAdornedStart = false;\n    if (children) {\n      React.Children.forEach(children, child => {\n        if (!isMuiElement(child, ['Input', 'Select'])) {\n          return;\n        }\n        const input = isMuiElement(child, ['Select']) ? child.props.input : child;\n        if (input && isAdornedStart(input.props)) {\n          initialAdornedStart = true;\n        }\n      });\n    }\n    return initialAdornedStart;\n  });\n  const [filled, setFilled] = React.useState(() => {\n    // We need to iterate through the children and find the Input in order\n    // to fully support server-side rendering.\n    let initialFilled = false;\n    if (children) {\n      React.Children.forEach(children, child => {\n        if (!isMuiElement(child, ['Input', 'Select'])) {\n          return;\n        }\n        if (isFilled(child.props, true) || isFilled(child.props.inputProps, true)) {\n          initialFilled = true;\n        }\n      });\n    }\n    return initialFilled;\n  });\n  const [focusedState, setFocused] = React.useState(false);\n  if (disabled && focusedState) {\n    setFocused(false);\n  }\n  const focused = visuallyFocused !== undefined && !disabled ? visuallyFocused : focusedState;\n  let registerEffect;\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    const registeredInput = React.useRef(false);\n    registerEffect = () => {\n      if (registeredInput.current) {\n        console.error(['MUI: There are multiple `InputBase` components inside a FormControl.', 'This creates visual inconsistencies, only use one `InputBase`.'].join('\\n'));\n      }\n      registeredInput.current = true;\n      return () => {\n        registeredInput.current = false;\n      };\n    };\n  }\n  const childContext = React.useMemo(() => {\n    return {\n      adornedStart,\n      setAdornedStart,\n      color,\n      disabled,\n      error,\n      filled,\n      focused,\n      fullWidth,\n      hiddenLabel,\n      size,\n      onBlur: () => {\n        setFocused(false);\n      },\n      onEmpty: () => {\n        setFilled(false);\n      },\n      onFilled: () => {\n        setFilled(true);\n      },\n      onFocus: () => {\n        setFocused(true);\n      },\n      registerEffect,\n      required,\n      variant\n    };\n  }, [adornedStart, color, disabled, error, filled, focused, fullWidth, hiddenLabel, registerEffect, required, size, variant]);\n  return /*#__PURE__*/_jsx(FormControlContext.Provider, {\n    value: childContext,\n    children: /*#__PURE__*/_jsx(FormControlRoot, _extends({\n      as: component,\n      ownerState: ownerState,\n      className: clsx(classes.root, className),\n      ref: ref\n    }, other, {\n      children: children\n    }))\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? FormControl.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component.\n   * It supports both default and custom theme colors, which can be added as shown in the\n   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).\n   * @default 'primary'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['primary', 'secondary', 'error', 'info', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, the label, input and helper text should be displayed in a disabled state.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, the label is displayed in an error state.\n   * @default false\n   */\n  error: PropTypes.bool,\n  /**\n   * If `true`, the component is displayed in focused state.\n   */\n  focused: PropTypes.bool,\n  /**\n   * If `true`, the component will take up the full width of its container.\n   * @default false\n   */\n  fullWidth: PropTypes.bool,\n  /**\n   * If `true`, the label is hidden.\n   * This is used to increase density for a `FilledInput`.\n   * Be sure to add `aria-label` to the `input` element.\n   * @default false\n   */\n  hiddenLabel: PropTypes.bool,\n  /**\n   * If `dense` or `normal`, will adjust vertical spacing of this and contained components.\n   * @default 'none'\n   */\n  margin: PropTypes.oneOf(['dense', 'none', 'normal']),\n  /**\n   * If `true`, the label will indicate that the `input` is required.\n   * @default false\n   */\n  required: PropTypes.bool,\n  /**\n   * The size of the component.\n   * @default 'medium'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['medium', 'small']), PropTypes.string]),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The variant to use.\n   * @default 'outlined'\n   */\n  variant: PropTypes.oneOf(['filled', 'outlined', 'standard'])\n} : void 0;\nexport default FormControl;", "// Supports determination of isControlled().\n// Controlled input accepts its current value as a prop.\n//\n// @see https://facebook.github.io/react/docs/forms.html#controlled-components\n// @param value\n// @returns {boolean} true if string (including '') or number (including zero)\nexport function hasValue(value) {\n  return value != null && !(Array.isArray(value) && value.length === 0);\n}\n\n// Determine if field is empty or filled.\n// Response determines if label is presented above field or as placeholder.\n//\n// @param obj\n// @param SSR\n// @returns {boolean} False when not present or empty string.\n//                    True when any number or string with length.\nexport function isFilled(obj, SSR = false) {\n  return obj && (hasValue(obj.value) && obj.value !== '' || SSR && hasValue(obj.defaultValue) && obj.defaultValue !== '');\n}\n\n// Determine if an Input is adorned on start.\n// It's corresponding to the left with LTR.\n//\n// @param obj\n// @returns {boolean} False when no adornments.\n//                    True when adorned at the start.\nexport function isAdornedStart(obj) {\n  return obj.startAdornment;\n}", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getFormControlUtilityClasses(slot) {\n  return generateUtilityClass('MuiFormControl', slot);\n}\nconst formControlClasses = generateUtilityClasses('MuiFormControl', ['root', 'marginNone', 'marginNormal', 'marginDense', 'fullWidth', 'disabled']);\nexport default formControlClasses;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,YAAuB;AACvB,wBAAsB;;;ACAf,SAAS,SAAS,OAAO;AAC9B,SAAO,SAAS,QAAQ,EAAE,MAAM,QAAQ,KAAK,KAAK,MAAM,WAAW;AACrE;AASO,SAAS,SAAS,KAAK,MAAM,OAAO;AACzC,SAAO,QAAQ,SAAS,IAAI,KAAK,KAAK,IAAI,UAAU,MAAM,OAAO,SAAS,IAAI,YAAY,KAAK,IAAI,iBAAiB;AACtH;AAQO,SAAS,eAAe,KAAK;AAClC,SAAO,IAAI;AACb;;;AC3BO,SAAS,6BAA6B,MAAM;AACjD,SAAO,qBAAqB,kBAAkB,IAAI;AACpD;AACA,IAAM,qBAAqB,uBAAuB,kBAAkB,CAAC,QAAQ,cAAc,gBAAgB,eAAe,aAAa,UAAU,CAAC;AAClJ,IAAO,6BAAQ;;;AFUf,yBAA4B;AAZ5B,IAAM,YAAY,CAAC,YAAY,aAAa,SAAS,aAAa,YAAY,SAAS,WAAW,aAAa,eAAe,UAAU,YAAY,QAAQ,SAAS;AAarK,IAAM,oBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,QAAQ,WAAW,UAAU,SAAS,mBAAW,MAAM,CAAC,IAAI,aAAa,WAAW;AAAA,EAC7F;AACA,SAAO,eAAe,OAAO,8BAA8B,OAAO;AACpE;AACA,IAAM,kBAAkB,eAAO,OAAO;AAAA,EACpC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC;AAAA,IAClB;AAAA,EACF,GAAG,WAAW;AACZ,WAAO,SAAS,CAAC,GAAG,OAAO,MAAM,OAAO,SAAS,mBAAW,WAAW,MAAM,CAAC,EAAE,GAAG,WAAW,aAAa,OAAO,SAAS;AAAA,EAC7H;AACF,CAAC,EAAE,CAAC;AAAA,EACF;AACF,MAAM,SAAS;AAAA,EACb,SAAS;AAAA,EACT,eAAe;AAAA,EACf,UAAU;AAAA;AAAA,EAEV,UAAU;AAAA,EACV,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,eAAe;AACjB,GAAG,WAAW,WAAW,YAAY;AAAA,EACnC,WAAW;AAAA,EACX,cAAc;AAChB,GAAG,WAAW,WAAW,WAAW;AAAA,EAClC,WAAW;AAAA,EACX,cAAc;AAChB,GAAG,WAAW,aAAa;AAAA,EACzB,OAAO;AACT,CAAC,CAAC;AA0BF,IAAM,cAAiC,iBAAW,SAASA,aAAY,SAAS,KAAK;AACnF,QAAM,QAAQ,cAAc;AAAA,IAC1B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,SAAS;AAAA,IACT,WAAW;AAAA,IACX,OAAO;AAAA,IACP,UAAU;AAAA,EACZ,IAAI,OACJ,QAAQ,8BAA8B,OAAO,SAAS;AACxD,QAAM,aAAa,SAAS,CAAC,GAAG,OAAO;AAAA,IACrC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,UAAU,kBAAkB,UAAU;AAC5C,QAAM,CAAC,cAAc,eAAe,IAAU,eAAS,MAAM;AAG3D,QAAI,sBAAsB;AAC1B,QAAI,UAAU;AACZ,MAAM,eAAS,QAAQ,UAAU,WAAS;AACxC,YAAI,CAAC,qBAAa,OAAO,CAAC,SAAS,QAAQ,CAAC,GAAG;AAC7C;AAAA,QACF;AACA,cAAM,QAAQ,qBAAa,OAAO,CAAC,QAAQ,CAAC,IAAI,MAAM,MAAM,QAAQ;AACpE,YAAI,SAAS,eAAe,MAAM,KAAK,GAAG;AACxC,gCAAsB;AAAA,QACxB;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT,CAAC;AACD,QAAM,CAAC,QAAQ,SAAS,IAAU,eAAS,MAAM;AAG/C,QAAI,gBAAgB;AACpB,QAAI,UAAU;AACZ,MAAM,eAAS,QAAQ,UAAU,WAAS;AACxC,YAAI,CAAC,qBAAa,OAAO,CAAC,SAAS,QAAQ,CAAC,GAAG;AAC7C;AAAA,QACF;AACA,YAAI,SAAS,MAAM,OAAO,IAAI,KAAK,SAAS,MAAM,MAAM,YAAY,IAAI,GAAG;AACzE,0BAAgB;AAAA,QAClB;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT,CAAC;AACD,QAAM,CAAC,cAAc,UAAU,IAAU,eAAS,KAAK;AACvD,MAAI,YAAY,cAAc;AAC5B,eAAW,KAAK;AAAA,EAClB;AACA,QAAM,UAAU,oBAAoB,UAAa,CAAC,WAAW,kBAAkB;AAC/E,MAAI;AACJ,MAAI,MAAuC;AAEzC,UAAM,kBAAwB,aAAO,KAAK;AAC1C,qBAAiB,MAAM;AACrB,UAAI,gBAAgB,SAAS;AAC3B,gBAAQ,MAAM,CAAC,wEAAwE,gEAAgE,EAAE,KAAK,IAAI,CAAC;AAAA,MACrK;AACA,sBAAgB,UAAU;AAC1B,aAAO,MAAM;AACX,wBAAgB,UAAU;AAAA,MAC5B;AAAA,IACF;AAAA,EACF;AACA,QAAM,eAAqB,cAAQ,MAAM;AACvC,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,QAAQ,MAAM;AACZ,mBAAW,KAAK;AAAA,MAClB;AAAA,MACA,SAAS,MAAM;AACb,kBAAU,KAAK;AAAA,MACjB;AAAA,MACA,UAAU,MAAM;AACd,kBAAU,IAAI;AAAA,MAChB;AAAA,MACA,SAAS,MAAM;AACb,mBAAW,IAAI;AAAA,MACjB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF,GAAG,CAAC,cAAc,OAAO,UAAU,OAAO,QAAQ,SAAS,WAAW,aAAa,gBAAgB,UAAU,MAAM,OAAO,CAAC;AAC3H,aAAoB,mBAAAC,KAAK,2BAAmB,UAAU;AAAA,IACpD,OAAO;AAAA,IACP,cAAuB,mBAAAA,KAAK,iBAAiB,SAAS;AAAA,MACpD,IAAI;AAAA,MACJ;AAAA,MACA,WAAW,aAAK,QAAQ,MAAM,SAAS;AAAA,MACvC;AAAA,IACF,GAAG,OAAO;AAAA,MACR;AAAA,IACF,CAAC,CAAC;AAAA,EACJ,CAAC;AACH,CAAC;AACD,OAAwC,YAAY,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQrF,UAAU,kBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOrB,OAAO,kBAAAA,QAAgD,UAAU,CAAC,kBAAAA,QAAU,MAAM,CAAC,WAAW,aAAa,SAAS,QAAQ,WAAW,SAAS,CAAC,GAAG,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrK,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpB,OAAO,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIjB,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKnB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOrB,aAAa,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvB,QAAQ,kBAAAA,QAAU,MAAM,CAAC,SAAS,QAAQ,QAAQ,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKnD,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpB,MAAM,kBAAAA,QAAgD,UAAU,CAAC,kBAAAA,QAAU,MAAM,CAAC,UAAU,OAAO,CAAC,GAAG,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA,EAIxH,IAAI,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtJ,SAAS,kBAAAA,QAAU,MAAM,CAAC,UAAU,YAAY,UAAU,CAAC;AAC7D,IAAI;AACJ,IAAO,sBAAQ;", "names": ["FormControl", "_jsx", "PropTypes"]}