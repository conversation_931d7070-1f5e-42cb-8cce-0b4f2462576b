import { Icon } from '@iconify/react';
import { Divider } from 'antd';
import React from 'react';

function SequenceStep({ step, onDelete, onAddClick, onSetActive, onEditClick }) {
  return (
    <div
      onClick={() => {
        onSetActive(step);
      }}
    >
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', marginTop: '5px' }}>
        <p style={{ fontSize: '12px', color: '#424242', fontFamily: 'Poppins', fontWeight: 400 }}>{step?.alias} Action</p>
      </div>
      <div style={{ backgroundColor: 'white', height: 'auto', marginBottom: '20px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '0px 20px' }}>
          <p style={{ fontSize: '16px', color: '#424242', fontFamily: 'Poppins', fontWeight: 400 }}>Queue : {step?.total_candidates}</p>
          <p
            style={{
              fontSize: '16px',
              color: '#424242',
              fontFamily: 'Poppins',
              fontWeight: 400,
              display: 'flex',
              alignItems: 'center',
              gap: '10px',
              cursor: 'pointer'
            }}
            onClick={() => onDelete(step?.id)}
          >
            <Icon icon="material-symbols:delete-outline-rounded" width="18" height="18" /> Delete
          </p>
        </div>
        <Divider style={{ marginTop: 0, borderColor: '#CED0DA', borderWidth: '2px', marginBottom: '0px' }} />
        <div
          style={{
            display: 'flex',
            padding: '0px 20px',
            gap: '20px',
            alignItems: 'center',
            backgroundColor: step?.is_active === true ? '#1A84DE' : 'white',
            justifyContent: 'space-between'
          }}
        >
          <div style={{ display: 'flex', gap: '20px', alignItems: 'center' }}>
            <p
              style={{
                backgroundColor: step?.is_active === true ? 'white' : '#1A84DE',
                padding: '5px', //shorthand for 5px on all four sides.
                width: '45px',
                height: '45px',
                display: 'inline-flex', // or display: 'flex'
                justifyContent: 'center',
                alignItems: 'center',
                borderRadius: '8px',
                color: step?.is_active === true ? '#1A84DE' : 'white'
              }}
            >
              <Icon icon={step?.icon} width="24" height="24" />
            </p>
            <p style={{ fontSize: '18px', color: step?.is_active === true ? 'white' : '#1A84DE', fontFamily: 'Poppins', fontWeight: 500 }}>
              {step?.title}
            </p>
          </div>
          <p
            style={{
              fontSize: '16px',
              color: 'white',
              fontFamily: 'Poppins',
              fontWeight: 500,
              display: 'flex',
              alignItems: 'center',
              gap: '10px',
              cursor: 'pointer',
              backgroundColor: '#1a84de',
              padding: '9px 20px',
              borderRadius: '2px'
            }}
            onClick={() => onEditClick(step)}
          >
            <Icon icon="mdi:edit-outline" width="18" height="18" /> Edit
          </p>
        </div>
        <Divider style={{ marginTop: 0, borderColor: '#CED0DA', borderWidth: '2px', marginBottom: '10px' }} />
        <div style={{ display: 'flex', padding: '0px 20px', gap: '20px', alignItems: 'center', justifyContent: 'space-between' }}>
          {step?.stats.map((stat) => (
            <div key={stat.id} style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
              <p>
                <Icon icon={stat?.icon} width="24" height="24" style={{ color: stat?.color }} />
              </p>
              <p style={{ fontSize: '17px', color: '#424242', fontFamily: 'Poppins', fontWeight: 500 }}>{stat.value}</p>
            </div>
          ))}
        </div>
      </div>
      <div style={{ display: 'flex', justifyContent: 'center', flexDirection: 'column', alignItems: 'center' }}>
        <Divider
          type="vertical"
          variant="dashed"
          style={{
            borderColor: '#CED0DA',
            height: '30px'
          }}
        />
        <p
          style={{
            backgroundColor: '#dadaf9',
            padding: '5px', //shorthand for 5px on all four sides.
            width: '34px',
            height: '34px',
            display: 'inline-flex', // or display: 'flex'
            justifyContent: 'center',
            alignItems: 'center',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
          onClick={(event) => onAddClick(event)} // Pass the event here
        >
          <Icon icon="material-symbols:add-rounded" width="24" height="24" />
        </p>
        <Divider
          type="vertical"
          variant="dashed"
          style={{
            borderColor: '#CED0DA',
            height: '30px'
          }}
        />
      </div>
    </div>
  );
}

export default SequenceStep;
