import{r as l,ae as v,az as M,e as A,s as p,j as n,aH as T}from"./index-C8YzRPez.js";import{C as w}from"./CandidateCarouselCards-CDOmVhkZ.js";import{C as L}from"./index-t3u8k2cB.js";import"./styles-Bufj3DzR.js";import{W as U,S as W,T as O,U as P,a as V}from"./VoiceCallApp-BzsYDor9.js";import"./usePreviousProps-BT0RotiM.js";import"./Autocomplete-pdU48_FV.js";import"./Chip-Cl5XdTlP.js";const X={superLargeDesktop:{breakpoint:{max:4e3,min:1200},items:4},desktop:{breakpoint:{max:1200,min:1024},items:3},tablet:{breakpoint:{max:1024,min:768},items:2},mobile:{breakpoint:{max:768,min:0},items:1}};function Y(){const[y,m]=l.useState(!1),[x,c]=l.useState(!1),[R,f]=l.useState(!1),[k,u]=l.useState(!1),[I,b]=l.useState(!1),[C,h]=l.useState(null),[i,S]=l.useState(null),[j,N]=l.useState(null),{roleId:_}=v(),[g,F]=l.useState([]),D=l.useCallback(()=>{try{M({},A.get360AndPreQualificationCandidates.replace(":roleId",_),s=>{const a=(s||[]).map(t=>({role_candidate_id:t.id,id:t.candidate.id,name:t.candidate.first_name+" "+t.candidate.last_name||"N/A",replyTime:t.updated_at.split("T")[0]||"N/A",stage:t.stage||"N/A",phones:t.candidate.phones||[],candidate:t.candidate||{},emails:t.candidate.emails||[]})),o=[{id:1,title:"New Candidates",color:"#FF9500",candidates:a},{id:2,title:"Email",color:"#00C7BE",candidates:a.filter(t=>t.stage==="Email Sent")},{id:3,title:"Email Response",color:"#5856D6",candidates:a.filter(t=>t.stage==="Interested"||t.stage==="Not Interested")},{id:4,title:"No Call Replies X1",color:"#32ADE6",candidates:a.filter(t=>t.stage==="No Response")},{id:5,title:"WhatsApp",color:"#34C759",candidates:a.filter(t=>t.stage==="Msg Sent")},{id:6,title:"Mobile Msg",color:"#AF52DE",candidates:a.filter(t=>t.stage==="Msg Sent")},{id:7,title:"WhatsApp & Mobile Reply",color:"#A2845E",candidates:a.filter(t=>t.stage==="Interested"||t.stage==="Not Interested")},{id:8,title:"No Call Replies X2",color:"#E70F38",candidates:a.filter(t=>t.stage==="No Response")},{id:9,title:"LI Connection",color:"#EE8C3C",candidates:a.filter(t=>t.stage==="Conn Sent"||t.stage==="Conn not sent")},{id:10,title:"LI Connection Response",color:"#FFCC00",candidates:a.filter(t=>t.stage==="Interested"||t.stage==="Not Interested")},{id:11,title:"No Call Replies X3",color:"#EE8C3C",candidates:a.filter(t=>t.stage==="No Response")},{id:12,title:"LI InMail",color:"#34C759",candidates:a.filter(t=>t.stage==="InMail Sent")},{id:13,title:"InMail Response",color:"#F688F2",candidates:a.filter(t=>t.stage==="Interested"||t.stage==="Not Interested")},{id:14,title:"No Call Replies X4",color:"#34C759",candidates:a.filter(t=>t.stage==="No Response")}];F(o)},s=>{var e;p.error(((e=s==null?void 0:s.response)==null?void 0:e.message)||"Failed to get role applications. Try refreshing the page!")})}catch{p.error("Failed to get role applications. Try refreshing the page!")}},[_]);l.useEffect(()=>{D()},[]);const E=s=>{const e={};switch(s==null?void 0:s.group){case"screening_stage":e.screening_stage=s==null?void 0:s.response;break;case"partially_interested_stage":e.partially_interested_stage=s==null?void 0:s.response;break;case"submission_stage":e.submission_stage=s==null?void 0:s.response;break}e.chennel=j,e.role_candidate_id=i==null?void 0:i.role_candidate_id,console.log("Response:",e);try{T(e,A.updateRoleCandidateStage,a=>{p.success("Response updated successfully!"),D()},a=>{var o;p.error(((o=a==null?void 0:a.response)==null?void 0:o.message)||"Failed to update response. Try again!")})}catch{p.error("Failed to update response. Try again!")}};return n.jsxs("div",{style:{width:"100%",marginTop:"20px",height:"100vh"},children:[n.jsx(L,{responsive:X,arrows:!1,autoPlay:!1,keyBoardControl:!0,containerClass:"carousel-container",removeArrowOnDeviceType:["tablet","mobile"],children:g==null?void 0:g.map(s=>n.jsx("div",{style:{height:"100%",borderRight:"2px solid #CED0DA",cursor:"pointer",userSelect:"none"},children:n.jsx(w,{title:s.title,candidates:s.candidates,color:s.color,showActions:!0,onWhatsappClick:e=>{var a,o,t,r,d;if(S(e),console.log("Candidate:",e),((a=e.phones)==null?void 0:a.length)===0){alert("No phone number available for this candidate."),p.error("No phone number available for this candidate.");return}else((o=e==null?void 0:e.phones)==null?void 0:o.length)>1?(h((t=e==null?void 0:e.phones[0])==null?void 0:t.phone_number),m(!0)):((r=e==null?void 0:e.phones)==null?void 0:r.length)===1&&(h((d=e==null?void 0:e.phones[0])==null?void 0:d.phone_number),m(!0))},onVoiceAppClick:e=>{var a,o,t,r,d;if(S(e),console.log("Candidsdsdsdate:",e),((a=e.phones)==null?void 0:a.length)===0){alert("No phone number available for this candidate."),p.error("No phone number available for this candidate.");return}else((o=e==null?void 0:e.phones)==null?void 0:o.length)>1?(h((t=e==null?void 0:e.phones[0])==null?void 0:t.phone_number),c(!0)):((r=e==null?void 0:e.phones)==null?void 0:r.length)===1&&(h((d=e==null?void 0:e.phones[0])==null?void 0:d.phone_number),c(!0))},onSMSClick:e=>{var a,o,t,r,d;if(S(e),console.log("Candidate:",e),((a=e.phones)==null?void 0:a.length)===0){alert("No phone number available for this candidate."),p.error("No phone number available for this candidate.");return}else((o=e==null?void 0:e.phones)==null?void 0:o.length)>1?(h((t=e==null?void 0:e.phones[0])==null?void 0:t.phone_number),f(!0)):((r=e==null?void 0:e.phones)==null?void 0:r.length)===1&&(h((d=e==null?void 0:e.phones[0])==null?void 0:d.phone_number),f(!0))},onEmailClick:()=>b(!0),showSubmissionActions:!1})},s.id))}),n.jsx("br",{}),(g==null?void 0:g.length)===0&&n.jsxs("div",{style:{textAlign:"center",marginTop:"20px"},children:[n.jsx("h3",{children:"No Candidates Found"}),n.jsx("p",{children:"Please check back later or add candidates to the role."})]}),y&&n.jsx(U,{onClose:()=>{N("whatsapp"),m(!1),u(!0)},candidateNumber:C,candidate:i==null?void 0:i.candidate}),R&&n.jsx(W,{onClose:()=>{N("sms"),f(!1),u(!0)},candidateNumber:C,candidate:i==null?void 0:i.candidate,open:R}),x&&n.jsx(O,{open:x,onClose:()=>{N("Call"),c(!1),u(!0)},toNumber:C,status:"connected",candidate:i==null?void 0:i.candidate}),k&&n.jsx(P,{open:()=>u(!0),onClose:()=>u(!1),onSubmit:s=>{E(s)}}),I&&n.jsx(V,{open:()=>b(!0),onClose:()=>{b(!1)},onSubmit:s=>{E(s)}})]})}export{Y as default};
