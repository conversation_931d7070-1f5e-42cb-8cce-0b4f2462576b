import{j as e,aA as ie,av as j,aw as te,R as oe,aB as re,a3 as ne,a7 as q,r as a,aC as se,aD as ae,a6 as ce,T as de,aE as le,aF as K,aG as pe,a5 as Q,a4 as Y,a2 as _,d as X,e as z,s as y,aH as he,ae as xe,az as J,u as ye,H as ge,aI as T,aJ as me,aK as ue,aL as fe}from"./index-C8YzRPez.js";import{D as je}from"./RoleDetailTabs-DtDMo3CF.js";import{F as be,a as Ce,b as p}from"./formik.esm-BTCyQW2C.js";import{r as Se,a as we,s as ke,b as Ie,m as _e}from"./roleStatusConstants-BMtu_3ks.js";import{g as Re,a as ve}from"./ServiceHelperApis-B2MVjsG0.js";import{g as Te}from"./UserHelper-DRRImjGb.js";import{g as Ee}from"./CountryHelper-CQDYhb7W.js";import{c as Z}from"./calendar-BA2XVJI9.js";import{F as ze}from"./FileUpload-CLRSAav9.js";import{F as Me}from"./FilePreview-Dz6aRE4w.js";import{C as G}from"./Collapse-Cwu689ta.js";import{A as Be}from"./Autocomplete-pdU48_FV.js";import"./usePreviousProps-BT0RotiM.js";import"./Chip-Cl5XdTlP.js";const{Text:Pe}=te,V=[{label:e.jsx("p",{style:{fontSize:"12px",color:"#838383",cursor:"pointer",textAlign:"left",margin:0,width:"100%",display:"block"},children:"Open"}),key:"0",style:{backgroundColor:"white",padding:"5px 12px"}},{label:e.jsx("p",{style:{fontSize:"12px",color:"#838383",cursor:"pointer",textAlign:"left",margin:0,width:"100%",display:"block"},children:"Filled by us"}),key:"1",style:{backgroundColor:"white",padding:"5px 12px"}},{label:e.jsx("p",{style:{fontSize:"12px",color:"#838383",cursor:"pointer",textAlign:"left",margin:0,width:"100%",display:"block"},children:"Filled by Competitor"}),key:"2",style:{backgroundColor:"white",padding:"5px 12px"}},{label:e.jsx("p",{style:{fontSize:"12px",color:"#838383",cursor:"pointer",textAlign:"left",margin:0,width:"100%",display:"block"},children:"Filled by Client"}),key:"3",style:{backgroundColor:"white",padding:"5px 12px"}},{label:e.jsx("p",{style:{fontSize:"12px",color:"#838383",cursor:"pointer",textAlign:"left",margin:0,width:"100%",display:"block"},children:"On hold"}),key:"4",style:{backgroundColor:"white",padding:"5px 12px"}},{label:e.jsx("p",{style:{fontSize:"12px",color:"#838383",cursor:"pointer",textAlign:"left",margin:0,width:"100%",display:"block"},children:"Withdrawn by client"}),key:"5",style:{backgroundColor:"white",padding:"5px 12px"}},{label:e.jsx("p",{style:{fontSize:"12px",color:"#838383",cursor:"pointer",textAlign:"left",margin:0,width:"100%",display:"block"},children:"Withdrawn by us"}),key:"6",style:{backgroundColor:"white",padding:"5px 12px"}}];function We({onSelect:k,status:c}){return e.jsx("div",{children:e.jsx(ie,{menu:{items:V,onClick:({key:t})=>{const g=V.find(S=>S.key===t);g&&k(g.label)}},trigger:["click"],overlayStyle:{borderRadius:0},children:e.jsxs(Pe,{type:"primary",style:{display:"flex",alignItems:"center",gap:"5px",backgroundColor:"#C7FFBF",padding:"5px 10px",margin:"0px 30px",cursor:"pointer",color:"green",fontSize:"13px"},children:[c," ",e.jsx(j,{icon:"ri:arrow-down-s-line",width:"16",height:"16",style:{margin:0,cursor:"pointer"}})]})})})}const D=k=>{const c=new Date,t=new Date(k),g=Math.floor((c-t)/1e3),S=String(Math.floor(g/3600)).padStart(2,"0"),M=String(Math.floor(g%3600/60)).padStart(2,"0"),d=String(g%60).padStart(2,"0");return`${S} : ${M} : ${d}`},Ae=oe.forwardRef(function(c,t){return e.jsx(re,{direction:"left",ref:t,...c,timeout:{enter:1e3,exit:500},easing:{enter:"cubic-bezier(0.4, 0, 0.2, 1)",exit:"cubic-bezier(0.4, 0, 0.2, 1)"}})}),{Panel:ee}=G,E=ne(Y)`
  width: 79%;
  padding: 10px;
  border-radius: 0;
  border: 1px solid #ced0da;
  font-family: 'Poppins';
`,C=ne.select`
  width: 80%;
  padding: 13px;
  margin-left: 10px;
  font-weight: 500;
  border-radius: 0;
  border: 1px solid #ced0da;
  background-color: white;
  font-family: 'Poppins';
  cursor: pointer;

  &:focus {
    outline: none;
    border-color: #1a84de;
  }
`,L=[{id:"1MONTH",label:"1 Month"},{id:"3MONTHS",label:"3 Months"},{id:"6MONTHS",label:"6 Months"},{id:"9MONTHS",label:"9 Months"},{id:"12MONTHS",label:"12 Months"},{id:"15MONTHS",label:"15 Months"},{id:"18MONTHS",label:"18 Months"},{id:"24MONTHS",label:"24 Months"},{id:"36MONTHS",label:"36 Months"},{id:"48MONTHS",label:"48 Months"},{id:"60MONTHS",label:"60 Months"},{id:"MORE_THAN_60MONTHS",label:"More than 60 Months"}];function Ne({open:k,onClose:c,roleDetails:t}){console.log("roleDetails",t);const g=q(r=>r.user.userId),[S,M]=a.useState(!0),[d,R]=a.useState(!0),[I,U]=a.useState(""),[m,B]=a.useState([]),[w,u]=a.useState([]),[v,O]=a.useState([]),[P,b]=a.useState([]),[f,$]=a.useState(!1),[H,W]=a.useState(null);a.useEffect(()=>{async function r(){try{const i=await Re();O(i==null?void 0:i.data)}catch(i){console.error("Error fetching services:",i)}}r()},[]),a.useEffect(()=>{async function r(){try{const i=await ve("CLIENT");B(i==null?void 0:i.data)}catch(i){console.error("Error fetching prospects:",i)}}r()},[]),a.useEffect(()=>{async function r(){try{const i=await Te();b(i==null?void 0:i.data)}catch(i){console.error("Error fetching services:",i)}}r()},[]),a.useEffect(()=>{async function r(){try{const i=await Ee();u(i==null?void 0:i.data)}catch(i){console.error("Error fetching services:",i)}}r()},[]);const F=r=>{try{const i=new FormData;i.append("file",r),X(i,z.uploadFileToS3Bucket,h=>{console.log("response",h),U(h)},h=>{var x;y.error(((x=h==null?void 0:h.response)==null?void 0:x.message)||"Failed to upload role file. Try again!")})}catch{y.error("Failed to upload role file. Try again!")}},N=(r,i)=>{try{const h=z.editRoleById.replace(":id",t.id);he(r,h,x=>{y.success("Role updated successfully."),i.setSubmitting(!1),c()},x=>{var s;y.error(((s=x==null?void 0:x.response)==null?void 0:s.message)||"Failed to update role. Try again!."),i.setSubmitting(!1)})}catch{y.error("Failed to update role. Try again!."),i.setSubmitting(!1)}};return a.useEffect(()=>{if(t&&(m==null?void 0:m.length)>0){const r=m.find(i=>i.id===t.personId);r&&(console.log("matchedClient",r),W(r))}},[t,m]),e.jsxs(se,{open:k,onClose:c,TransitionComponent:Ae,keepMounted:!0,fullScreen:!0,PaperProps:{sx:{ml:"auto",mt:0,mr:0,width:"900px",height:"100vh",overflowY:"hidden",borderRadius:0}},BackdropProps:{sx:{backgroundColor:"rgba(0, 0, 0, 0.5)"}},children:[e.jsx(ae,{children:e.jsxs(ce,{display:"flex",justifyContent:"space-between",children:[e.jsx(de,{variant:"h4",className:"ml-8",children:"Update Role"}),e.jsx(j,{icon:"material-symbols:close-rounded",width:"24",height:"24",style:{cursor:"pointer"},onClick:c})]})}),e.jsx(le,{sx:{width:"100%",overflowY:"auto","&::-webkit-scrollbar":{width:"5px",height:"5px"},"&::-webkit-scrollbar-thumb":{backgroundColor:"#d9d9d9",borderRadius:"4px"},"&::-webkit-scrollbar-thumb:hover":{backgroundColor:"#cccccc"},"&::-webkit-scrollbar-track":{backgroundColor:"#f0f0f0"}},children:e.jsx(be,{initialValues:{id:t?t==null?void 0:t.id:null,serviceId:t?t==null?void 0:t.serviceId:null,client:null,client_number:t?t==null?void 0:t.client_number:null,category:t?t==null?void 0:t.category:null,start_date:t?K(t==null?void 0:t.start_date):null,end_date:t?K(t==null?void 0:t.end_date):null,dateRange:[null,null],candidateReq:t?t==null?void 0:t.candidates_required:null,creditsCharge:t!=null&&t.is_credit?"Yes":"No",title:t?t==null?void 0:t.title:null,postalCode:t?t==null?void 0:t.postal_code:null,country:t?t==null?void 0:t.country:null,rolePriority:t?t==null?void 0:t.priority:null,location:t?t==null?void 0:t.locations:null,salary_min:t?t==null?void 0:t.salary_min:null,salary_max:t?t==null?void 0:t.salary_max:null,salaryRange:"",salary_currency:t?t==null?void 0:t.salary_currency:null,bd:t?t==null?void 0:t.bdUserId:null,roleDescription:"",miles:t?t==null?void 0:t.radius_miles:null,salary_type:t?t==null?void 0:t.salary_type:null,industry:t?t==null?void 0:t.industry:null,months_back:t?t==null?void 0:t.months_back:null,role_number:t?t==null?void 0:t.role_number:null},onSubmit:(r,i)=>{r.salary_min=f?0:r.salary_min,r.salary_max=f?0:r.salary_max,r.salary_currency=f?null:r.salary_currency,r.salary_type=f?null:r.salary_type,r.personId=r.client,r.radius_miles=r.miles,r.acmUserId=g||localStorage.getItem("userId"),r.attachments=I,r.userId=r.bd,N(r,i)},children:({values:r,setFieldValue:i,handleSubmit:h,isSubmitting:x})=>e.jsxs("div",{children:[e.jsx(G,{activeKey:S?["1"]:[],expandIcon:()=>null,style:{border:"none",backgroundColor:"white",margin:0,padding:0},children:e.jsx(ee,{header:e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",width:"100%"},children:[e.jsxs("p",{style:{marginBottom:0,color:"#344665",fontSize:"18px",fontWeight:500},onClick:()=>M(!S),children:["Client Info"," ",e.jsx(j,{icon:S?"ri:arrow-up-s-line":"iconamoon:arrow-down-2",width:"18",height:"18",style:{cursor:"pointer",marginBottom:"-3px"}})]}),e.jsx("p",{style:{marginBottom:0,color:"#1A84DE",fontSize:"16px",cursor:"pointer"},onClick:()=>{i("service",""),i("client",""),i("category",""),i("start_date",null),i("end_date",null),i("candidateReq",""),i("creditsCharge","Yes")},children:"Clear"})]}),children:e.jsxs(Ce,{style:{padding:0},children:[e.jsxs("div",{style:{marginBottom:20,display:"flex",justifyContent:"space-between",alignItems:"center",width:"100%"},children:[e.jsx("label",{style:{fontSize:"13px",color:"#344665",fontWeight:500,width:"20%"},children:"Select Service"}),e.jsx(p,{name:"serviceId",children:({field:s})=>e.jsxs(C,{...s,onChange:n=>{const o=v.find(l=>l.id===Number(n.target.value));i("serviceId",(o==null?void 0:o.id)||"")},value:r.serviceId,children:[e.jsx("option",{value:"",children:"Select Service"}),v==null?void 0:v.map(n=>e.jsx("option",{value:n.id,children:n.name},n.id))]})})]}),e.jsxs("div",{style:{marginBottom:20,display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.jsx("label",{style:{fontSize:"13px",color:"#344665",fontWeight:500,width:"20%"},children:"Select Client"}),e.jsx(p,{name:"client",children:({field:s})=>{const n=m==null?void 0:m.filter(o=>o.serviceId===r.serviceId);return e.jsx(Be,{size:"medium",fullWidth:!0,options:n,value:H,getOptionLabel:o=>(o==null?void 0:o.client_number)+" - "+(o==null?void 0:o.first_name),onChange:(o,l)=>{i("client",l==null?void 0:l.id),i("client_number",l==null?void 0:l.client_number),W(l)},renderInput:o=>e.jsx(pe,{...o,placeholder:"Select Client",variant:"outlined",required:!0,InputLabelProps:{shrink:!0},sx:{backgroundColor:"white",width:"95%",marginLeft:"28px",borderRadius:"0.3rem",border:"1px solid #E4E4E5","& .MuiOutlinedInput-root":{"&.Mui-focused fieldset":{borderColor:"#00C1FE"}},"& .MuiInputBase-root":{"&.Mui-focused":{boxShadow:"0 0 0 1px #00C1FE"}}}})})}})]}),e.jsxs("div",{style:{marginBottom:20,display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.jsx("label",{style:{fontSize:"13px",color:"#344665",fontWeight:500,width:"20%"},children:"Category"}),e.jsx(p,{name:"category",children:({field:s})=>{var n;return e.jsxs(C,{...s,onChange:o=>{i("category",o.target.value)},value:r.category,children:[e.jsx("option",{value:"",children:"Select Category"}),(n=Se)==null?void 0:n.map(o=>e.jsx("option",{value:o.id,children:o.label},o.id))]})}})]}),e.jsxs("div",{style:{marginBottom:20,display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.jsx("label",{style:{fontSize:"13px",color:"#344665",fontWeight:500,width:"20%"},children:"Select Date Range"}),e.jsx(p,{name:"start_date",children:({field:s})=>e.jsxs(e.Fragment,{children:[e.jsx(Q,{getPopupContainer:n=>n.parentNode,suffixIcon:e.jsx("img",{src:Z}),...s,style:{padding:"10px",borderRadius:0,border:"1px solid #ced0da",width:(r==null?void 0:r.category)!=="FIX"?"79%":"38%"},onChange:n=>i("start_date",n),value:r.start_date?r.start_date:null,placeholder:"Start Date"}),(r==null?void 0:r.category)==="FIX"&&e.jsxs(e.Fragment,{children:[e.jsx("p",{style:{fontSize:"13px",color:"#344665",fontWeight:500,width:(r==null?void 0:r.category)==="FIX"?"0%":"2%"},children:" "}),e.jsx(Q,{getPopupContainer:n=>n.parentNode,suffixIcon:e.jsx("img",{src:Z}),...s,style:{padding:"10px",borderRadius:0,border:"1px solid #ced0da",width:"38%"},onChange:n=>i("end_date",n),value:r.end_date?r.end_date:null,placeholder:"End Date"})]})]})})]}),e.jsxs("div",{style:{marginBottom:20,display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.jsx("label",{style:{fontSize:"13px",color:"#344665",fontWeight:500,width:"20%"},children:"Candidates Req"}),e.jsx(p,{name:"candidateReq",children:({field:s})=>e.jsx(E,{...s,placeholder:"Enter number of candidates",onChange:n=>{i("candidateReq",n.target.value)},value:r.candidateReq})})]}),e.jsxs("div",{style:{marginBottom:20,display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.jsx("label",{style:{fontSize:"13px",color:"#344665",fontWeight:500,width:"20%"},children:"Credit Charge"}),e.jsx(p,{name:"creditsCharge",children:({field:s})=>e.jsxs(C,{...s,onChange:n=>{i("creditsCharge",n.target.value)},value:r.creditsCharge,children:[e.jsx("option",{value:"Yes",children:"Yes"}),e.jsx("option",{value:"No",children:"No"})]})})]}),e.jsxs("div",{style:{marginBottom:20,display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.jsx("label",{style:{fontSize:"13px",color:"#344665",fontWeight:500,width:"20%"},children:"Role Number"}),e.jsx(p,{name:"role_number",children:({field:s})=>e.jsx(E,{...s,placeholder:"",onChange:n=>{i("role_number",n.target.value)},value:r.role_number})})]})]})},"1")}),e.jsx(G,{activeKey:d?["1"]:[],expandIcon:()=>null,style:{border:"none",backgroundColor:"white",margin:0,padding:0},children:e.jsxs(ee,{header:e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",width:"100%"},children:[e.jsxs("p",{style:{marginBottom:0,color:"#344665",fontSize:"18px",fontWeight:500},onClick:()=>R(!d),children:["Role Info"," ",e.jsx(j,{icon:d?"ri:arrow-up-s-line":"iconamoon:arrow-down-2",width:"18",height:"18",style:{cursor:"pointer",marginBottom:"-3px"}})]}),e.jsx("p",{style:{marginBottom:0,color:"#1A84DE",fontSize:"16px",cursor:"pointer"},onClick:()=>{i("title",""),i("postalCode",""),i("country",""),i("rolePriority",""),i("location",""),i("salary_min",""),i("salary_max",""),i("bd",""),i("roleDescription",""),R(!0)},children:"Clear"})]}),children:[e.jsxs("div",{style:{marginBottom:20,display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.jsx("label",{style:{fontSize:"13px",color:"#344665",fontWeight:500,width:"20%"},children:"Role Title"}),e.jsx(p,{name:"title",children:({field:s})=>e.jsx(E,{...s,placeholder:"",onChange:n=>{i("title",n.target.value)},value:r.title})})]}),e.jsxs("div",{style:{marginBottom:20,display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.jsx("label",{style:{fontSize:"13px",color:"#344665",fontWeight:500,width:"20%"},children:"Postal Code"}),e.jsx(p,{name:"postalCode",children:({field:s})=>e.jsx(E,{...s,placeholder:"",onChange:n=>{i("postalCode",n.target.value)},value:r.postalCode})})]}),e.jsxs("div",{style:{marginBottom:20,display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.jsx("label",{style:{fontSize:"13px",color:"#344665",fontWeight:500,width:"20%"},children:"Country"}),e.jsx(p,{name:"country",children:({field:s})=>e.jsxs(C,{...s,onChange:n=>{i("country",n.target.value)},value:r.country,children:[e.jsx("option",{value:"",children:"Select Country"}),w==null?void 0:w.map(n=>e.jsx("option",{value:n.id,children:n.name},n.id)),(w==null?void 0:w.length)===0&&e.jsx("option",{value:"",children:"No countries to select"})]})})]}),e.jsxs("div",{style:{marginBottom:20,display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.jsx("label",{style:{fontSize:"13px",color:"#344665",fontWeight:500,width:"20%"},children:"Role Priority"}),e.jsx(p,{name:"priority",children:({field:s})=>{var n;return e.jsx(C,{...s,onChange:o=>{i("priority",o.target.value)},value:r.priority,children:(n=we)==null?void 0:n.map(o=>e.jsx("option",{value:o.id,children:o.label},o.id))})}})]}),!f&&e.jsx(e.Fragment,{children:e.jsxs("div",{style:{marginBottom:0,display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.jsx("label",{style:{fontSize:"13px",color:"#344665",fontWeight:500,width:"20%"},children:"Select Salary Range"}),e.jsx(p,{name:"salaryRange",children:({field:s})=>e.jsxs(e.Fragment,{children:[e.jsx(Y,{...s,placeholder:"Minimun Salary",onChange:n=>{i("salary_min",n.target.value)},value:r.salary_min,style:{padding:"10px",borderRadius:0,border:"1px solid #ced0da",width:"35%"},type:"number"}),e.jsx("p",{style:{fontSize:"13px",color:"#344665",fontWeight:500,width:"5%"},children:" To"}),e.jsx(Y,{...s,placeholder:"Maximum Salary",onChange:n=>{i("salary_max",n.target.value)},value:r.salary_max,style:{padding:"10px",borderRadius:0,border:"1px solid #ced0da",width:"35%"},type:"number"})]})})]})}),e.jsx("div",{style:{marginBottom:0,display:"flex",justifyContent:"flex-end"},children:e.jsxs("p",{style:{fontSize:"13px",color:"#1A84DE",fontWeight:400,cursor:"pointer"},onClick:()=>$(!f),children:[" ",f?"Add Salary":"Salary Not Defined ? "]})}),!f&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{style:{marginBottom:20,display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.jsx("label",{style:{fontSize:"13px",color:"#344665",fontWeight:500,width:"20%"},children:"Select Salary Currency"}),e.jsx(p,{name:"salary_currency",children:({field:s})=>{var n;return e.jsxs(C,{...s,onChange:o=>{i("salary_currency",o.target.value)},value:r.salary_currency,children:[e.jsx("option",{value:"",children:"Select Salary Currency"}),(n=ke)==null?void 0:n.map(o=>e.jsx("option",{value:o.id,children:o.label},o.id))]})}})]}),e.jsxs("div",{style:{marginBottom:20,display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.jsx("label",{style:{fontSize:"13px",color:"#344665",fontWeight:500,width:"20%"},children:"Select Salary Type"}),e.jsx(p,{name:"salary_type",children:({field:s})=>{var n;return e.jsxs(C,{...s,onChange:o=>{i("salary_type",o.target.value)},value:r.salary_type,children:[e.jsx("option",{value:"",children:"Select Salary Type"}),(n=Ie)==null?void 0:n.map(o=>e.jsx("option",{value:o.id,children:o.label},o.id))]})}})]})]}),e.jsxs("div",{style:{marginBottom:20,display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.jsx("label",{style:{fontSize:"13px",color:"#344665",fontWeight:500,width:"20%"},children:"Months Back"}),e.jsx(p,{name:"months_back",children:({field:s})=>e.jsxs(C,{...s,onChange:n=>{i("months_back",n.target.value)},value:r.months_back,children:[e.jsx("option",{value:"",children:"Select Months"}),L==null?void 0:L.map(n=>e.jsx("option",{value:n.id,children:n.label},n.id))]})})]}),e.jsxs("div",{style:{marginBottom:20,display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.jsx("label",{style:{fontSize:"13px",color:"#344665",fontWeight:500,width:"20%"},children:"Select Radius Miles"}),e.jsx(p,{name:"miles",children:({field:s})=>{var n;return e.jsxs(C,{...s,onChange:o=>{i("miles",o.target.value)},value:r.miles,children:[e.jsx("option",{value:"",children:"Select Miles"}),(n=_e)==null?void 0:n.map(o=>e.jsx("option",{value:o.id,children:o.label},o.id))]})}})]}),e.jsxs("div",{style:{marginBottom:20,display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.jsx("label",{style:{fontSize:"13px",color:"#344665",fontWeight:500,width:"20%"},children:"Industry"}),e.jsx(p,{name:"industry",children:({field:s})=>e.jsx(E,{...s,placeholder:"",onChange:n=>{i("industry",n.target.value)},value:r.industry})})]}),e.jsxs("div",{style:{marginBottom:20,display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.jsx("label",{style:{fontSize:"13px",color:"#344665",fontWeight:500,width:"20%"},children:"Location"}),e.jsx(p,{name:"location",children:({field:s})=>e.jsx(E,{...s,placeholder:"",onChange:n=>{i("location",n.target.value)},value:r.location})})]}),e.jsxs("div",{style:{marginBottom:20,display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.jsx("label",{style:{fontSize:"13px",color:"#344665",fontWeight:500,width:"20%"},children:"Select ACM"}),e.jsx(p,{name:"bd",children:({field:s})=>e.jsxs(C,{...s,onChange:n=>{i("bd",n.target.value)},value:r.bd,children:[e.jsx("option",{value:"",children:"Select ACM"}),P==null?void 0:P.map(n=>e.jsxs("option",{value:n.id,children:[n.first_name," ",n.last_name]},n.id))]})})]}),e.jsx("div",{children:e.jsx(Me,{roleDetails:t})}),e.jsx("div",{style:{marginTop:"25px"},children:e.jsx(ze,{onFileUpload:s=>F(s)})}),e.jsxs("div",{style:{marginTop:"25px",display:"flex",justifyContent:"flex-end",alignItems:"center"},children:[e.jsx(_,{type:"primary",style:{borderRadius:0,height:"50px",width:"150px",fontSize:"16px",border:"2px solid black",color:"black",backgroundColor:"white",fontWeight:500},onClick:()=>onCancel(),children:"Cancle"}),e.jsx(_,{type:"primary",icon:e.jsx(j,{icon:"tabler:cloud-upload",width:"18",height:"18"}),style:{borderRadius:0,height:"50px",width:"170px",marginLeft:"20px",fontSize:"16px",backgroundColor:"#1A84DE",fontWeight:500},loading:x,disabled:x,onClick:h,children:"Upload Role"})]}),e.jsx("br",{})]},"1")})]})})})]})}const{Title:Ue,Text:A}=te;function De(){var h,x,s;const{roleService:k,roleId:c}=xe(),[t,g]=a.useState(null),[S,M]=a.useState(),d=decodeURIComponent(k),R=q(n=>n.user.userDesignation),I=q(n=>n.user.userId),[U,m]=a.useState(null),[B,w]=a.useState(!1),[u,v]=a.useState(!1),O=()=>{try{const n=z.getRoleById.replace(":id",c);J({},n,o=>{g(o)},o=>{var l;y.error(((l=o==null?void 0:o.response)==null?void 0:l.message)||"Failed to get all services. Try refreshing the page!")})}catch{y.error("Failed to get service stats. Try refreshing the page!")}},P=a.useCallback(()=>{try{J({},z.getRoleCandidateCount.replace(":roleId",c),n=>{console.log("Candidate count response:",n),v(n)},n=>{})}catch{}},[c]);a.useEffect(()=>{P()},[]);const b=[{key:"1",label:"Details",path:`/${encodeURIComponent(d)}/${c}/role-details`},{key:"2",label:"All Candidates",path:`/${encodeURIComponent(d)}/${c}/all-candidates`}];d==="Pre Qualification"&&b.push({key:"3",label:"Applications",path:`/${encodeURIComponent(d)}/${c}/applications`},{key:"4",label:"Submissions",path:`/${encodeURIComponent(d)}/${c}/submission_stage`}),d!=null&&d.includes("360")&&b.push({key:"3",label:`Applications ( ${u==null?void 0:u.total_count} )`,path:`/${encodeURIComponent(d)}/${c}/applications`},{key:"4",label:`Submissions ( ${u==null?void 0:u.total_count} )`,path:`/${encodeURIComponent(d)}/${c}/submission_stage`},{key:"5",label:`Placements ( 0/${u==null?void 0:u.total_count} )`,path:`/${encodeURIComponent(d)}/${c}/placements`}),(d!=null&&d.includes("360")||d==="Pre Qualification")&&R==="RECRUITER_CRM"&&b.push({key:"6",label:"Sequences",path:`/${encodeURIComponent(d)}/${c}/sequences`});const f=ye(),[$,H]=a.useState("Open"),W=((h=b.find(n=>{var o;return(o=location==null?void 0:location.pathname)==null?void 0:o.includes(n==null?void 0:n.path)}))==null?void 0:h.key)||"1";a.useEffect(()=>{N(W)},[location.pathname]);const[F,N]=a.useState(W);a.useEffect(()=>{O()},[c]),a.useEffect(()=>{var n;if((n=t==null?void 0:t.roleLogs)!=null&&n.length&&I){const o=t.roleLogs.find(l=>l.action==="STARTED"&&l.status==="IN_PROGRESS"&&l.userId===I&&l.end_time===null);if(o){m(D(o==null?void 0:o.start_time));const l=setInterval(()=>{m(D(o==null?void 0:o.start_time))},1e3);return()=>clearInterval(l)}}},[t,I]);const r=()=>{try{X({roleId:c,userId:I},z.markRoleAsDone,n=>{y.success("Role marked as done successfully!")},n=>{var o;y.error(((o=n==null?void 0:n.response)==null?void 0:o.message)||"Failed to mark role done. Try again!")})}catch{y.error("Failed to mark role done. Try again!")}},i=()=>{try{X({roleId:c,userId:I},z.leaveRole,n=>{y.success("Role left successfully!")},n=>{var o;y.error(((o=n==null?void 0:n.response)==null?void 0:o.message)||"Failed to leave role. Try again!")})}catch{y.error("Failed to leave role. Try again!")}};return a.useEffect(()=>{t&&M(t)},[t,B]),e.jsxs(e.Fragment,{children:[e.jsx(ge,{children:e.jsx("title",{children:"Role Details"})}),e.jsxs("div",{style:{width:"100%",backgroundColor:"white",padding:"15px 30px",paddingBottom:"0px",position:"sticky",top:0,zIndex:1e3,boxShadow:"0px 4px 8px rgba(0, 0, 0, 0.1)"},children:[e.jsxs(T,{align:"middle",justify:"space-between",children:[e.jsxs(T,{align:"middle",children:[e.jsx(me,{size:90,style:{backgroundColor:"#AFCFFF",verticalAlign:"middle",fontSize:"30px",fontWeight:"600"},children:(x=t==null?void 0:t.title)==null?void 0:x.charAt(0).toUpperCase()}),e.jsxs(ue,{style:{marginLeft:"15px",flex:1},children:[e.jsxs(T,{align:"middle",children:[e.jsx(j,{icon:"bx:arrow-back",width:"22",height:"22",style:{cursor:"pointer"},onClick:()=>f("/trial-roles/all-roles")}),e.jsx(Ue,{level:4,style:{margin:"0 10px",fontWeight:"600",color:"#0C2340",fontFamily:"Poppins"},children:[t==null?void 0:t.title,t==null?void 0:t.locations,t==null?void 0:t.postal_code].filter(Boolean).join(" - ")})]}),e.jsx(T,{style:{marginTop:"5px"},children:e.jsx(We,{onSelect:n=>{H(n==null?void 0:n.props.children)},status:$})}),e.jsxs(T,{style:{marginTop:"5px"},children:[e.jsxs(A,{type:"primary",style:{margin:"0px 10px",fontSize:"13px"},children:[e.jsx("strong",{children:"ID :"})," ",t==null?void 0:t.id]}),e.jsx(A,{type:"primary",style:{margin:"0 10px"},children:t!=null&&t.industry?"|":""}),e.jsx(A,{type:"primary",style:{fontSize:"13px",fontFamily:"Poppins"},children:t!=null&&t.industry?t==null?void 0:t.industry:""}),e.jsx(A,{type:"primary",style:{margin:"0 10px"},children:"|"}),e.jsx(j,{icon:"proicons:location",width:"16",height:"16"}),e.jsxs(A,{type:"primary",style:{fontSize:"13px",fontFamily:"Poppins"},children:["  ",(t==null?void 0:t.locations)+", "+(t==null?void 0:t.postal_code)]})]})]})]}),((s=t==null?void 0:t.current_status)==null?void 0:s.includes("IN_PROGRESS"))&&e.jsxs("div",{style:{display:"flex",gap:"10px"},children:[e.jsx(_,{type:"primary",icon:e.jsx(j,{icon:"pepicons-pencil:leave",width:"16",height:"16"}),style:{borderRadius:"8px",height:"35px",width:"100%",fontSize:"16px",backgroundColor:"#32ade6",fontWeight:400},onClick:()=>{i()},children:"Leave Role"}),e.jsx(_,{type:"primary",icon:e.jsx(j,{icon:"hugeicons:tick-01",width:"16",height:"16"}),style:{borderRadius:"8px",height:"35px",width:"100%",fontSize:"16px",backgroundColor:"#32ade6",fontWeight:400},onClick:()=>{r()},children:"Mark Done"}),e.jsx(_,{type:"primary",icon:e.jsx(j,{icon:"mdi:timer-sand-complete",width:"16",height:"16"}),style:{borderRadius:"8px",height:"35px",width:"100%",fontSize:"16px",backgroundColor:"#32ade6",fontWeight:400},onClick:()=>{},children:"In Progress"}),e.jsx(_,{type:"primary",style:{borderRadius:"8px",height:"35px",width:"100%",fontSize:"16px",backgroundColor:"#ff9500",fontWeight:400},onClick:()=>{},children:U||"00 : 00 : 00"})]}),(R==="ACCOUNT_EXECUTIVE"||R==="BUSINESS_DEVELOPMENT_EXECUTIVE")&&e.jsx("div",{style:{display:"flex",gap:"10px"},children:e.jsx(_,{type:"primary",icon:e.jsx(j,{icon:"ic:twotone-edit",width:"16",height:"16"}),style:{borderRadius:"8px",height:"35px",width:"100%",fontSize:"16px",backgroundColor:"#32ade6",fontWeight:400},onClick:()=>{w(!0)},children:"Edit Role"})})]}),e.jsx(T,{style:{marginTop:"10px",marginBottom:0},children:e.jsx(je,{items:b==null?void 0:b.map(({key:n,label:o})=>({key:n,label:o})),onChangeTab:n=>{var o;N(n),f((o=b.find(l=>l.key===n))==null?void 0:o.path)},activeKey:F})}),B&&e.jsx(Ne,{open:B,onClose:()=>{w(!1)},roleDetails:S})]}),e.jsx("div",{style:{padding:"20px"},children:e.jsx(fe,{})})]})}export{De as default};
