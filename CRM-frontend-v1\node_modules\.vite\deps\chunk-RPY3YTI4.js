import {
  __esm,
  __export
} from "./chunk-WXXH56N5.js";

// node_modules/@mui/utils/formatMuiErrorMessage/formatMuiErrorMessage.js
function formatMuiErrorMessage(code) {
  let url = "https://mui.com/production-error/?code=" + code;
  for (let i = 1; i < arguments.length; i += 1) {
    url += "&args[]=" + encodeURIComponent(arguments[i]);
  }
  return "Minified MUI error #" + code + "; visit " + url + " for the full message.";
}
var init_formatMuiErrorMessage = __esm({
  "node_modules/@mui/utils/formatMuiErrorMessage/formatMuiErrorMessage.js"() {
  }
});

// node_modules/@mui/utils/formatMuiErrorMessage/index.js
var formatMuiErrorMessage_exports = {};
__export(formatMuiErrorMessage_exports, {
  default: () => formatMuiErrorMessage
});
var init_formatMuiErrorMessage2 = __esm({
  "node_modules/@mui/utils/formatMuiErrorMessage/index.js"() {
    init_formatMuiErrorMessage();
  }
});

// node_modules/@mui/utils/node_modules/@babel/runtime/helpers/esm/extends.js
function _extends() {
  _extends = Object.assign ? Object.assign.bind() : function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends.apply(this, arguments);
}
var init_extends = __esm({
  "node_modules/@mui/utils/node_modules/@babel/runtime/helpers/esm/extends.js"() {
  }
});

// node_modules/@mui/utils/deepmerge/deepmerge.js
function isPlainObject(item) {
  if (typeof item !== "object" || item === null) {
    return false;
  }
  const prototype = Object.getPrototypeOf(item);
  return (prototype === null || prototype === Object.prototype || Object.getPrototypeOf(prototype) === null) && !(Symbol.toStringTag in item) && !(Symbol.iterator in item);
}
function deepClone(source) {
  if (!isPlainObject(source)) {
    return source;
  }
  const output = {};
  Object.keys(source).forEach((key) => {
    output[key] = deepClone(source[key]);
  });
  return output;
}
function deepmerge(target, source, options = {
  clone: true
}) {
  const output = options.clone ? _extends({}, target) : target;
  if (isPlainObject(target) && isPlainObject(source)) {
    Object.keys(source).forEach((key) => {
      if (key === "__proto__") {
        return;
      }
      if (isPlainObject(source[key]) && key in target && isPlainObject(target[key])) {
        output[key] = deepmerge(target[key], source[key], options);
      } else if (options.clone) {
        output[key] = isPlainObject(source[key]) ? deepClone(source[key]) : source[key];
      } else {
        output[key] = source[key];
      }
    });
  }
  return output;
}
var init_deepmerge = __esm({
  "node_modules/@mui/utils/deepmerge/deepmerge.js"() {
    init_extends();
  }
});

// node_modules/@mui/utils/deepmerge/index.js
var deepmerge_exports = {};
__export(deepmerge_exports, {
  default: () => deepmerge,
  isPlainObject: () => isPlainObject
});
var init_deepmerge2 = __esm({
  "node_modules/@mui/utils/deepmerge/index.js"() {
    init_deepmerge();
    init_deepmerge();
  }
});

export {
  formatMuiErrorMessage_exports,
  init_formatMuiErrorMessage2 as init_formatMuiErrorMessage,
  _extends,
  init_extends,
  isPlainObject,
  deepmerge,
  deepmerge_exports,
  init_deepmerge2 as init_deepmerge
};
//# sourceMappingURL=chunk-RPY3YTI4.js.map
