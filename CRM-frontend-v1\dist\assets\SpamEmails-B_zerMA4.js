import{u,bH as f,r as o,j as t,aI as y,a2 as b,av as j,e3 as v,aL as E,e4 as C}from"./index-C8YzRPez.js";function T(w){var r;const i=[{key:"1",label:"All Emails",path:"/mailbox/spam/all"}],m=u(),n=f(),l=((r=i.find(e=>{var a;return(a=n.pathname)==null?void 0:a.includes(e.path)}))==null?void 0:r.key)||"1";o.useEffect(()=>{p(l)},[n.pathname]);const[x,p]=o.useState(l),[s,c]=o.useState([]),d=()=>{s.length<3?c([...s,Date.now()]):alert("You can only open 3 mails at a time")},h=e=>{c(s.filter(a=>a!==e))};return t.jsxs("div",{style:{width:"100%"},children:[t.jsx(y,{style:{backgroundColor:"white",padding:"5px",display:"flex",justifyContent:"space-between",alignItems:"center"},children:t.jsx(b,{type:"primary",icon:t.jsx(j,{icon:"mdi-light:email",width:"20",height:"20"}),style:{borderRadius:"8px",height:"35px",width:"170px",fontSize:"16px",marginLeft:"10px",backgroundColor:"#1A84DE",fontWeight:400},onClick:d,children:"Compose Email"})}),t.jsx(v,{tabs:i==null?void 0:i.map(({key:e,label:a})=>({key:e,label:a})),onChangeTab:e=>{var a;p(e),m((a=i.find(g=>g.key===e))==null?void 0:a.path)},activeKey:x}),t.jsx("div",{style:{marginTop:"15px"},children:t.jsx(E,{})}),t.jsx("div",{style:{position:"fixed",bottom:10,right:10,zIndex:1050,display:"flex",gap:"5px"},children:s==null?void 0:s.map((e,a)=>t.jsx(C,{id:a,onClose:()=>h(e)},e))})]})}export{T as default};
