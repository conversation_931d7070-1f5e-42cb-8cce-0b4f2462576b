const u=["Total Trial","CV Sourcing","Pre Qualification","360/Direct","Lead Generation","VA","CV Formatting","Development","Data Scrapping"],l=r=>{switch(console.log("service",r),r){case"CV Sourcing":return"cv-sourcing";case"Pre Qualification":return"pre-qualification";case"360/Direct":return"direct";case"Lead Generation":return"lead-generation";case"VA":return"va";case"CV Formatting":return"cv-formatting";case"Development":return"development";case"Data Scrapping":return"data-scrapping";default:return""}},c=r=>{switch(r){case"Total Trial":return"#FFC59D";case"CV Sourcing":return"#BEECFF";case"Pre Qualification":return"#9EBDF1";case"360/Direct":return"#FFB4E9";case"Lead Generation":return"#92FEAD";case"VA":return"#FCB95B";case"CV Formatting":return"#F487C5";case"Development":return"#FCC6A2";case"Data Scrapping":return"#C598FE";default:return"#FFC59D"}},i=(r,o)=>{if(!r||!Array.isArray(r))return[];let a=r.map(e=>({buttonText:e==null?void 0:e.service_name,value:Number(e==null?void 0:e.total),key:l(e==null?void 0:e.service_name),borderTop:c(e==null?void 0:e.service_name),buttonFontSize:"22px",button_id:e==null?void 0:e.service_id}));if(o==="Roles"){const e=a.reduce((n,t)=>n+(t==null?void 0:t.value),0);a.push({buttonText:"All Roles",value:e,key:"all-roles",borderTop:"#FFC59D",buttonFontSize:"22px",button_id:""})}if(o==="Prospects"){const e=a.reduce((n,t)=>n+(t==null?void 0:t.value),0);a.push({buttonText:"All Prospects",value:e,key:"all-prospects",borderTop:"#FFC59D",buttonFontSize:"22px",button_id:""})}if(o==="Clients"){const e=a.reduce((n,t)=>n+(t==null?void 0:t.value),0);a.push({buttonText:"All Clients",value:e,key:"all-clients",borderTop:"#FFC59D",buttonFontSize:"22px",button_id:""})}return a=a.sort((e,n)=>u.indexOf(e.buttonText)-u.indexOf(n.buttonText)),a};export{i as f};
