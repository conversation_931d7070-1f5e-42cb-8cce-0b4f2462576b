import{r as s,j as t,H as n,aI as d,a4 as h,av as c,az as u,e as l}from"./index-C8YzRPez.js";import m from"./SubmittedCandidates-CcmFOPr-.js";import"./CandidateCarouselCards-CDOmVhkZ.js";import"./usePreviousProps-BT0RotiM.js";import"./index-t3u8k2cB.js";import"./styles-Bufj3DzR.js";import"./Autocomplete-pdU48_FV.js";import"./Chip-Cl5XdTlP.js";function R(p){const[x,r]=s.useState([]),[i,a]=s.useState(""),o=()=>{try{u({},l.login,e=>{r(e)},e=>{})}catch{}};return s.useEffect(()=>{o()},[]),t.jsxs("div",{style:{width:"100%",height:"100vh"},children:[t.jsx(n,{children:t.jsx("title",{children:"Role Submissions"})}),t.jsx(d,{children:t.jsx(h,{variant:"borderless",placeholder:"Search name",style:{backgroundColor:"white",width:"300px",borderRadius:0,height:"40px"},suffix:t.jsx(c,{icon:"ant-design:search-outlined",width:"18",height:"18",style:{cursor:"pointer"}}),onChange:e=>a(e.target.value),value:i})}),t.jsx(m,{})]})}export{R as default};
