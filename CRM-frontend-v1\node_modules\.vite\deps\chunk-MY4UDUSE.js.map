{"version": 3, "sources": ["../../@mui/material/Paper/Paper.js", "../../@mui/material/Paper/paperClasses.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"component\", \"elevation\", \"square\", \"variant\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport integerPropType from '@mui/utils/integerPropType';\nimport chainPropTypes from '@mui/utils/chainPropTypes';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { alpha } from '@mui/system/colorManipulator';\nimport styled from '../styles/styled';\nimport getOverlayAlpha from '../styles/getOverlayAlpha';\nimport useThemeProps from '../styles/useThemeProps';\nimport useTheme from '../styles/useTheme';\nimport { getPaperUtilityClass } from './paperClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    square,\n    elevation,\n    variant,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', variant, !square && 'rounded', variant === 'elevation' && `elevation${elevation}`]\n  };\n  return composeClasses(slots, getPaperUtilityClass, classes);\n};\nconst PaperRoot = styled('div', {\n  name: 'MuiPaper',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[ownerState.variant], !ownerState.square && styles.rounded, ownerState.variant === 'elevation' && styles[`elevation${ownerState.elevation}`]];\n  }\n})(({\n  theme,\n  ownerState\n}) => {\n  var _theme$vars$overlays;\n  return _extends({\n    backgroundColor: (theme.vars || theme).palette.background.paper,\n    color: (theme.vars || theme).palette.text.primary,\n    transition: theme.transitions.create('box-shadow')\n  }, !ownerState.square && {\n    borderRadius: theme.shape.borderRadius\n  }, ownerState.variant === 'outlined' && {\n    border: `1px solid ${(theme.vars || theme).palette.divider}`\n  }, ownerState.variant === 'elevation' && _extends({\n    boxShadow: (theme.vars || theme).shadows[ownerState.elevation]\n  }, !theme.vars && theme.palette.mode === 'dark' && {\n    backgroundImage: `linear-gradient(${alpha('#fff', getOverlayAlpha(ownerState.elevation))}, ${alpha('#fff', getOverlayAlpha(ownerState.elevation))})`\n  }, theme.vars && {\n    backgroundImage: (_theme$vars$overlays = theme.vars.overlays) == null ? void 0 : _theme$vars$overlays[ownerState.elevation]\n  }));\n});\nconst Paper = /*#__PURE__*/React.forwardRef(function Paper(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiPaper'\n  });\n  const {\n      className,\n      component = 'div',\n      elevation = 1,\n      square = false,\n      variant = 'elevation'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    component,\n    elevation,\n    square,\n    variant\n  });\n  const classes = useUtilityClasses(ownerState);\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    const theme = useTheme();\n    if (theme.shadows[elevation] === undefined) {\n      console.error([`MUI: The elevation provided <Paper elevation={${elevation}}> is not available in the theme.`, `Please make sure that \\`theme.shadows[${elevation}]\\` is defined.`].join('\\n'));\n    }\n  }\n  return /*#__PURE__*/_jsx(PaperRoot, _extends({\n    as: component,\n    ownerState: ownerState,\n    className: clsx(classes.root, className),\n    ref: ref\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? Paper.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Shadow depth, corresponds to `dp` in the spec.\n   * It accepts values between 0 and 24 inclusive.\n   * @default 1\n   */\n  elevation: chainPropTypes(integerPropType, props => {\n    const {\n      elevation,\n      variant\n    } = props;\n    if (elevation > 0 && variant === 'outlined') {\n      return new Error(`MUI: Combining \\`elevation={${elevation}}\\` with \\`variant=\"${variant}\"\\` has no effect. Either use \\`elevation={0}\\` or use a different \\`variant\\`.`);\n    }\n    return null;\n  }),\n  /**\n   * If `true`, rounded corners are disabled.\n   * @default false\n   */\n  square: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The variant to use.\n   * @default 'elevation'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['elevation', 'outlined']), PropTypes.string])\n} : void 0;\nexport default Paper;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getPaperUtilityClass(slot) {\n  return generateUtilityClass('MuiPaper', slot);\n}\nconst paperClasses = generateUtilityClasses('MuiPaper', ['root', 'rounded', 'outlined', 'elevation', 'elevation0', 'elevation1', 'elevation2', 'elevation3', 'elevation4', 'elevation5', 'elevation6', 'elevation7', 'elevation8', 'elevation9', 'elevation10', 'elevation11', 'elevation12', 'elevation13', 'elevation14', 'elevation15', 'elevation16', 'elevation17', 'elevation18', 'elevation19', 'elevation20', 'elevation21', 'elevation22', 'elevation23', 'elevation24']);\nexport default paperClasses;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,YAAuB;AACvB,wBAAsB;AAKtB,8BAAsB;;;ACTf,SAAS,qBAAqB,MAAM;AACzC,SAAO,qBAAqB,YAAY,IAAI;AAC9C;AACA,IAAM,eAAe,uBAAuB,YAAY,CAAC,QAAQ,WAAW,YAAY,aAAa,cAAc,cAAc,cAAc,cAAc,cAAc,cAAc,cAAc,cAAc,cAAc,cAAc,eAAe,eAAe,eAAe,eAAe,eAAe,eAAe,eAAe,eAAe,eAAe,eAAe,eAAe,eAAe,eAAe,eAAe,aAAa,CAAC;AACjd,IAAO,uBAAQ;;;ADWf,yBAA4B;AAb5B,IAAM,YAAY,CAAC,aAAa,aAAa,aAAa,UAAU,SAAS;AAc7E,IAAM,oBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,QAAQ,SAAS,CAAC,UAAU,WAAW,YAAY,eAAe,YAAY,SAAS,EAAE;AAAA,EAClG;AACA,SAAO,eAAe,OAAO,sBAAsB,OAAO;AAC5D;AACA,IAAM,YAAY,eAAO,OAAO;AAAA,EAC9B,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW;AACpC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,WAAO,CAAC,OAAO,MAAM,OAAO,WAAW,OAAO,GAAG,CAAC,WAAW,UAAU,OAAO,SAAS,WAAW,YAAY,eAAe,OAAO,YAAY,WAAW,SAAS,EAAE,CAAC;AAAA,EACzK;AACF,CAAC,EAAE,CAAC;AAAA,EACF;AAAA,EACA;AACF,MAAM;AACJ,MAAI;AACJ,SAAO,SAAS;AAAA,IACd,kBAAkB,MAAM,QAAQ,OAAO,QAAQ,WAAW;AAAA,IAC1D,QAAQ,MAAM,QAAQ,OAAO,QAAQ,KAAK;AAAA,IAC1C,YAAY,MAAM,YAAY,OAAO,YAAY;AAAA,EACnD,GAAG,CAAC,WAAW,UAAU;AAAA,IACvB,cAAc,MAAM,MAAM;AAAA,EAC5B,GAAG,WAAW,YAAY,cAAc;AAAA,IACtC,QAAQ,cAAc,MAAM,QAAQ,OAAO,QAAQ,OAAO;AAAA,EAC5D,GAAG,WAAW,YAAY,eAAe,SAAS;AAAA,IAChD,YAAY,MAAM,QAAQ,OAAO,QAAQ,WAAW,SAAS;AAAA,EAC/D,GAAG,CAAC,MAAM,QAAQ,MAAM,QAAQ,SAAS,UAAU;AAAA,IACjD,iBAAiB,uBAAmB,+BAAM,QAAQ,wBAAgB,WAAW,SAAS,CAAC,CAAC,SAAK,+BAAM,QAAQ,wBAAgB,WAAW,SAAS,CAAC,CAAC;AAAA,EACnJ,GAAG,MAAM,QAAQ;AAAA,IACf,kBAAkB,uBAAuB,MAAM,KAAK,aAAa,OAAO,SAAS,qBAAqB,WAAW,SAAS;AAAA,EAC5H,CAAC,CAAC;AACJ,CAAC;AACD,IAAM,QAA2B,iBAAW,SAASA,OAAM,SAAS,KAAK;AACvE,QAAM,QAAQ,cAAc;AAAA,IAC1B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM;AAAA,IACF;AAAA,IACA,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,IAAI,OACJ,QAAQ,8BAA8B,OAAO,SAAS;AACxD,QAAM,aAAa,SAAS,CAAC,GAAG,OAAO;AAAA,IACrC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,UAAU,kBAAkB,UAAU;AAC5C,MAAI,MAAuC;AAEzC,UAAM,QAAQ,SAAS;AACvB,QAAI,MAAM,QAAQ,SAAS,MAAM,QAAW;AAC1C,cAAQ,MAAM,CAAC,iDAAiD,SAAS,qCAAqC,yCAAyC,SAAS,iBAAiB,EAAE,KAAK,IAAI,CAAC;AAAA,IAC/L;AAAA,EACF;AACA,aAAoB,mBAAAC,KAAK,WAAW,SAAS;AAAA,IAC3C,IAAI;AAAA,IACJ;AAAA,IACA,WAAW,aAAK,QAAQ,MAAM,SAAS;AAAA,IACvC;AAAA,EACF,GAAG,KAAK,CAAC;AACX,CAAC;AACD,OAAwC,MAAM,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ/E,UAAU,kBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMrB,WAAW,eAAe,yBAAiB,WAAS;AAClD,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,YAAY,KAAK,YAAY,YAAY;AAC3C,aAAO,IAAI,MAAM,+BAA+B,SAAS,uBAAuB,OAAO,iFAAiF;AAAA,IAC1K;AACA,WAAO;AAAA,EACT,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKD,QAAQ,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIlB,IAAI,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtJ,SAAS,kBAAAA,QAAgD,UAAU,CAAC,kBAAAA,QAAU,MAAM,CAAC,aAAa,UAAU,CAAC,GAAG,kBAAAA,QAAU,MAAM,CAAC;AACnI,IAAI;AACJ,IAAO,gBAAQ;", "names": ["Paper", "_jsx", "PropTypes"]}