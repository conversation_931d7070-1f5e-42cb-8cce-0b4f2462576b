import{ai as Ft,aj as Bt,a_ as Ce,j as O,ak as Ee,aN as ir,ar as Pe,a$ as yn,b0 as bn,al as re,r as z,an as st,b1 as Sn,I as De,aq as or,b2 as li,as as Wt,b3 as di,b4 as wn,b5 as Cn,b6 as En,b7 as fi,am as sr,aM as pi,b8 as hi,b9 as gi,ba as mi,bb as Vt,bc as ar,bd as vi,a3 as cr,e as Be,a6 as Z,be as Ht,av as te,T as le,aG as ut,B as Te,bf as _i,bg as yi,C as Tn,aC as qt,aD as ur,aE as lr,bh as B,R as bi,aB as Si}from"./index-C8YzRPez.js";import{C as wi,A as dr}from"./Autocomplete-pdU48_FV.js";import{s as Ci,p as Ei,a as Ti,e as xi,b as <PERSON>}from"./CandidateCarouselCards-CDOmVhkZ.js";function ki(n){return Bt("MuiAlert",n)}const xn=Ft("MuiAlert",["root","action","icon","message","filled","colorSuccess","colorInfo","colorWarning","colorError","filledSuccess","filledInfo","filledWarning","filledError","outlined","outlinedSuccess","outlinedInfo","outlinedWarning","outlinedError","standard","standardSuccess","standardInfo","standardWarning","standardError"]),Ii=Ce(O.jsx("path",{d:"M20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4C12.76,4 13.5,4.11 14.2, 4.31L15.77,2.74C14.61,2.26 13.34,2 12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0, 0 22,12M7.91,10.08L6.5,11.5L11,16L21,6L19.59,4.58L11,13.17L7.91,10.08Z"}),"SuccessOutlined"),Ri=Ce(O.jsx("path",{d:"M12 5.99L19.53 19H4.47L12 5.99M12 2L1 21h22L12 2zm1 14h-2v2h2v-2zm0-6h-2v4h2v-4z"}),"ReportProblemOutlined"),Ai=Ce(O.jsx("path",{d:"M11 15h2v2h-2zm0-8h2v6h-2zm.99-5C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"}),"ErrorOutline"),Di=Ce(O.jsx("path",{d:"M11,9H13V7H11M12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20, 12C20,16.41 16.41,20 12,20M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10, 10 0 0,0 12,2M11,17H13V11H11V17Z"}),"InfoOutlined"),Oi=["action","children","className","closeText","color","components","componentsProps","icon","iconMapping","onClose","role","severity","slotProps","slots","variant"],Mi=li(),ji=n=>{const{variant:t,color:e,severity:r,classes:i}=n,o={root:["root",`color${Pe(e||r)}`,`${t}${Pe(e||r)}`,`${t}`],icon:["icon"],message:["message"],action:["action"]};return Wt(o,ki,i)},Li=Ee(ir,{name:"MuiAlert",slot:"Root",overridesResolver:(n,t)=>{const{ownerState:e}=n;return[t.root,t[e.variant],t[`${e.variant}${Pe(e.color||e.severity)}`]]}})(({theme:n})=>{const t=n.palette.mode==="light"?yn:bn,e=n.palette.mode==="light"?bn:yn;return re({},n.typography.body2,{backgroundColor:"transparent",display:"flex",padding:"6px 16px",variants:[...Object.entries(n.palette).filter(([,r])=>r.main&&r.light).map(([r])=>({props:{colorSeverity:r,variant:"standard"},style:{color:n.vars?n.vars.palette.Alert[`${r}Color`]:t(n.palette[r].light,.6),backgroundColor:n.vars?n.vars.palette.Alert[`${r}StandardBg`]:e(n.palette[r].light,.9),[`& .${xn.icon}`]:n.vars?{color:n.vars.palette.Alert[`${r}IconColor`]}:{color:n.palette[r].main}}})),...Object.entries(n.palette).filter(([,r])=>r.main&&r.light).map(([r])=>({props:{colorSeverity:r,variant:"outlined"},style:{color:n.vars?n.vars.palette.Alert[`${r}Color`]:t(n.palette[r].light,.6),border:`1px solid ${(n.vars||n).palette[r].light}`,[`& .${xn.icon}`]:n.vars?{color:n.vars.palette.Alert[`${r}IconColor`]}:{color:n.palette[r].main}}})),...Object.entries(n.palette).filter(([,r])=>r.main&&r.dark).map(([r])=>({props:{colorSeverity:r,variant:"filled"},style:re({fontWeight:n.typography.fontWeightMedium},n.vars?{color:n.vars.palette.Alert[`${r}FilledColor`],backgroundColor:n.vars.palette.Alert[`${r}FilledBg`]}:{backgroundColor:n.palette.mode==="dark"?n.palette[r].dark:n.palette[r].main,color:n.palette.getContrastText(n.palette[r].main)})}))]})}),Ni=Ee("div",{name:"MuiAlert",slot:"Icon",overridesResolver:(n,t)=>t.icon})({marginRight:12,padding:"7px 0",display:"flex",fontSize:22,opacity:.9}),Ui=Ee("div",{name:"MuiAlert",slot:"Message",overridesResolver:(n,t)=>t.message})({padding:"8px 0",minWidth:0,overflow:"auto"}),Pn=Ee("div",{name:"MuiAlert",slot:"Action",overridesResolver:(n,t)=>t.action})({display:"flex",alignItems:"flex-start",padding:"4px 0 0 16px",marginLeft:"auto",marginRight:-8}),kn={success:O.jsx(Ii,{fontSize:"inherit"}),warning:O.jsx(Ri,{fontSize:"inherit"}),error:O.jsx(Ai,{fontSize:"inherit"}),info:O.jsx(Di,{fontSize:"inherit"})},fr=z.forwardRef(function(t,e){const r=Mi({props:t,name:"MuiAlert"}),{action:i,children:o,className:a,closeText:s="Close",color:d,components:u={},componentsProps:p={},icon:f,iconMapping:v=kn,onClose:y,role:_="alert",severity:b="success",slotProps:S={},slots:g={},variant:l="standard"}=r,C=st(r,Oi),E=re({},r,{color:d,severity:b,variant:l,colorSeverity:d||b}),m=ji(E),w={slots:re({closeButton:u.CloseButton,closeIcon:u.CloseIcon},g),slotProps:re({},p,S)},[R,h]=Sn("closeButton",{elementType:De,externalForwardedProps:w,ownerState:E}),[j,x]=Sn("closeIcon",{elementType:wi,externalForwardedProps:w,ownerState:E});return O.jsxs(Li,re({role:_,elevation:0,ownerState:E,className:or(m.root,a),ref:e},C,{children:[f!==!1?O.jsx(Ni,{ownerState:E,className:m.icon,children:f||v[b]||kn[b]}):null,O.jsx(Ui,{ownerState:E,className:m.message,children:o}),i!=null?O.jsx(Pn,{ownerState:E,className:m.action,children:i}):null,i==null&&y?O.jsx(Pn,{ownerState:E,className:m.action,children:O.jsx(R,re({size:"small","aria-label":s,title:s,color:"inherit",onClick:y},h,{children:O.jsx(j,re({fontSize:"small"},x))}))}):null]}))});function Fi(n={}){const{autoHideDuration:t=null,disableWindowBlurListener:e=!1,onClose:r,open:i,resumeHideDuration:o}=n,a=di();z.useEffect(()=>{if(!i)return;function g(l){l.defaultPrevented||(l.key==="Escape"||l.key==="Esc")&&(r==null||r(l,"escapeKeyDown"))}return document.addEventListener("keydown",g),()=>{document.removeEventListener("keydown",g)}},[i,r]);const s=wn((g,l)=>{r==null||r(g,l)}),d=wn(g=>{!r||g==null||a.start(g,()=>{s(null,"timeout")})});z.useEffect(()=>(i&&d(t),a.clear),[i,t,d,a]);const u=g=>{r==null||r(g,"clickaway")},p=a.clear,f=z.useCallback(()=>{t!=null&&d(o??t*.5)},[t,o,d]),v=g=>l=>{const C=g.onBlur;C==null||C(l),f()},y=g=>l=>{const C=g.onFocus;C==null||C(l),p()},_=g=>l=>{const C=g.onMouseEnter;C==null||C(l),p()},b=g=>l=>{const C=g.onMouseLeave;C==null||C(l),f()};return z.useEffect(()=>{if(!e&&i)return window.addEventListener("focus",f),window.addEventListener("blur",p),()=>{window.removeEventListener("focus",f),window.removeEventListener("blur",p)}},[e,i,f,p]),{getRootProps:(g={})=>{const l=Cn({},En(n),En(g));return Cn({role:"presentation"},g,l,{onBlur:v(l),onFocus:y(l),onMouseEnter:_(l),onMouseLeave:b(l)})},onClickAway:u}}function Bi(n){return Bt("MuiSnackbarContent",n)}Ft("MuiSnackbarContent",["root","message","action"]);const Wi=["action","className","message","role"],Vi=n=>{const{classes:t}=n;return Wt({root:["root"],action:["action"],message:["message"]},Bi,t)},Hi=Ee(ir,{name:"MuiSnackbarContent",slot:"Root",overridesResolver:(n,t)=>t.root})(({theme:n})=>{const t=n.palette.mode==="light"?.8:.98,e=fi(n.palette.background.default,t);return re({},n.typography.body2,{color:n.vars?n.vars.palette.SnackbarContent.color:n.palette.getContrastText(e),backgroundColor:n.vars?n.vars.palette.SnackbarContent.bg:e,display:"flex",alignItems:"center",flexWrap:"wrap",padding:"6px 16px",borderRadius:(n.vars||n).shape.borderRadius,flexGrow:1,[n.breakpoints.up("sm")]:{flexGrow:"initial",minWidth:288}})}),qi=Ee("div",{name:"MuiSnackbarContent",slot:"Message",overridesResolver:(n,t)=>t.message})({padding:"8px 0"}),zi=Ee("div",{name:"MuiSnackbarContent",slot:"Action",overridesResolver:(n,t)=>t.action})({display:"flex",alignItems:"center",marginLeft:"auto",paddingLeft:16,marginRight:-8}),Gi=z.forwardRef(function(t,e){const r=sr({props:t,name:"MuiSnackbarContent"}),{action:i,className:o,message:a,role:s="alert"}=r,d=st(r,Wi),u=r,p=Vi(u);return O.jsxs(Hi,re({role:s,square:!0,elevation:6,className:or(p.root,o),ownerState:u,ref:e},d,{children:[O.jsx(qi,{className:p.message,ownerState:u,children:a}),i?O.jsx(zi,{className:p.action,ownerState:u,children:i}):null]}))});function $i(n){return Bt("MuiSnackbar",n)}Ft("MuiSnackbar",["root","anchorOriginTopCenter","anchorOriginBottomCenter","anchorOriginTopRight","anchorOriginBottomRight","anchorOriginTopLeft","anchorOriginBottomLeft"]);const Ji=["onEnter","onExited"],Ki=["action","anchorOrigin","autoHideDuration","children","className","ClickAwayListenerProps","ContentProps","disableWindowBlurListener","message","onBlur","onClose","onFocus","onMouseEnter","onMouseLeave","open","resumeHideDuration","TransitionComponent","transitionDuration","TransitionProps"],Yi=n=>{const{classes:t,anchorOrigin:e}=n,r={root:["root",`anchorOrigin${Pe(e.vertical)}${Pe(e.horizontal)}`]};return Wt(r,$i,t)},In=Ee("div",{name:"MuiSnackbar",slot:"Root",overridesResolver:(n,t)=>{const{ownerState:e}=n;return[t.root,t[`anchorOrigin${Pe(e.anchorOrigin.vertical)}${Pe(e.anchorOrigin.horizontal)}`]]}})(({theme:n,ownerState:t})=>{const e={left:"50%",right:"auto",transform:"translateX(-50%)"};return re({zIndex:(n.vars||n).zIndex.snackbar,position:"fixed",display:"flex",left:8,right:8,justifyContent:"center",alignItems:"center"},t.anchorOrigin.vertical==="top"?{top:8}:{bottom:8},t.anchorOrigin.horizontal==="left"&&{justifyContent:"flex-start"},t.anchorOrigin.horizontal==="right"&&{justifyContent:"flex-end"},{[n.breakpoints.up("sm")]:re({},t.anchorOrigin.vertical==="top"?{top:24}:{bottom:24},t.anchorOrigin.horizontal==="center"&&e,t.anchorOrigin.horizontal==="left"&&{left:24,right:"auto"},t.anchorOrigin.horizontal==="right"&&{right:24,left:"auto"})})}),pr=z.forwardRef(function(t,e){const r=sr({props:t,name:"MuiSnackbar"}),i=pi(),o={enter:i.transitions.duration.enteringScreen,exit:i.transitions.duration.leavingScreen},{action:a,anchorOrigin:{vertical:s,horizontal:d}={vertical:"bottom",horizontal:"left"},autoHideDuration:u=null,children:p,className:f,ClickAwayListenerProps:v,ContentProps:y,disableWindowBlurListener:_=!1,message:b,open:S,TransitionComponent:g=mi,transitionDuration:l=o,TransitionProps:{onEnter:C,onExited:E}={}}=r,m=st(r.TransitionProps,Ji),w=st(r,Ki),R=re({},r,{anchorOrigin:{vertical:s,horizontal:d},autoHideDuration:u,disableWindowBlurListener:_,TransitionComponent:g,transitionDuration:l}),h=Yi(R),{getRootProps:j,onClickAway:x}=Fi(re({},R)),[I,T]=z.useState(!0),P=hi({elementType:In,getSlotProps:j,externalForwardedProps:w,ownerState:R,additionalProps:{ref:e},className:[h.root,f]}),c=k=>{T(!0),E&&E(k)},F=(k,M)=>{T(!1),C&&C(k,M)};return!S&&I?null:O.jsx(gi,re({onClickAway:x},v,{children:O.jsx(In,re({},P,{children:O.jsx(g,re({appear:!0,in:S,timeout:l,direction:s==="top"?"down":"up",onEnter:F,onExited:c},m,{children:p||O.jsx(Gi,re({message:b,action:a},y))}))}))}))});function hr(n){return Vt(n,Date.now())}function Xi(n,t){return ar(Vt(n,n),hr(n))}function Qi(n,t){return ar(Vt(n,n),vi(hr(n),1))}const Zi=Ce(O.jsx("path",{d:"M20.01 15.38c-1.23 0-2.42-.2-3.53-.56-.35-.12-.74-.03-1.01.24l-1.57 1.97c-2.83-1.35-5.48-3.9-6.89-6.83l1.95-1.66c.27-.28.35-.67.24-1.02-.37-1.11-.56-2.3-.56-3.53 0-.54-.45-.99-.99-.99H4.19C3.65 3 3 3.24 3 3.99 3 13.28 10.73 21 20.01 21c.71 0 .99-.63.99-1.18v-3.45c0-.54-.45-.99-.99-.99"}),"Call"),eo=Ce(O.jsx("path",{d:"M12 9c-1.6 0-3.15.25-4.6.72v3.1c0 .39-.23.74-.56.9-.98.49-1.87 1.12-2.66 1.85-.18.18-.43.28-.7.28-.28 0-.53-.11-.71-.29L.29 13.08c-.18-.17-.29-.42-.29-.7s.11-.53.29-.71C3.34 8.78 7.46 7 12 7s8.66 1.78 11.71 4.67c.18.18.29.43.29.71s-.11.53-.29.71l-2.48 2.48c-.18.18-.43.29-.71.29-.27 0-.52-.11-.7-.28-.79-.74-1.69-1.36-2.67-1.85-.33-.16-.56-.5-.56-.9v-3.1C15.15 9.25 13.6 9 12 9"}),"CallEnd"),to=Ce(O.jsx("path",{d:"M19 11h-1.7c0 .74-.16 1.43-.43 2.05l1.23 1.23c.56-.98.9-2.09.9-3.28m-4.02.17c0-.06.02-.11.02-.17V5c0-1.66-1.34-3-3-3S9 3.34 9 5v.18zM4.27 3 3 4.27l6.01 6.01V11c0 1.66 1.33 3 2.99 3 .22 0 .44-.03.65-.08l1.66 1.66c-.71.33-1.5.52-2.31.52-2.76 0-5.3-2.1-5.3-5.1H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c.91-.13 1.77-.45 2.54-.9L19.73 21 21 19.73z"}),"MicOff"),Rn=Ce(O.jsx("path",{d:"M3 9v6h4l5 5V4L7 9zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02M14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77"}),"VolumeUp"),me=Object.create(null);me.open="0";me.close="1";me.ping="2";me.pong="3";me.message="4";me.upgrade="5";me.noop="6";const Qe=Object.create(null);Object.keys(me).forEach(n=>{Qe[me[n]]=n});const It={type:"error",data:"parser error"},gr=typeof Blob=="function"||typeof Blob<"u"&&Object.prototype.toString.call(Blob)==="[object BlobConstructor]",mr=typeof ArrayBuffer=="function",vr=n=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(n):n&&n.buffer instanceof ArrayBuffer,zt=({type:n,data:t},e,r)=>gr&&t instanceof Blob?e?r(t):An(t,r):mr&&(t instanceof ArrayBuffer||vr(t))?e?r(t):An(new Blob([t]),r):r(me[n]+(t||"")),An=(n,t)=>{const e=new FileReader;return e.onload=function(){const r=e.result.split(",")[1];t("b"+(r||""))},e.readAsDataURL(n)};function Dn(n){return n instanceof Uint8Array?n:n instanceof ArrayBuffer?new Uint8Array(n):new Uint8Array(n.buffer,n.byteOffset,n.byteLength)}let bt;function no(n,t){if(gr&&n.data instanceof Blob)return n.data.arrayBuffer().then(Dn).then(t);if(mr&&(n.data instanceof ArrayBuffer||vr(n.data)))return t(Dn(n.data));zt(n,!1,e=>{bt||(bt=new TextEncoder),t(bt.encode(e))})}const On="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Fe=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(let n=0;n<On.length;n++)Fe[On.charCodeAt(n)]=n;const ro=n=>{let t=n.length*.75,e=n.length,r,i=0,o,a,s,d;n[n.length-1]==="="&&(t--,n[n.length-2]==="="&&t--);const u=new ArrayBuffer(t),p=new Uint8Array(u);for(r=0;r<e;r+=4)o=Fe[n.charCodeAt(r)],a=Fe[n.charCodeAt(r+1)],s=Fe[n.charCodeAt(r+2)],d=Fe[n.charCodeAt(r+3)],p[i++]=o<<2|a>>4,p[i++]=(a&15)<<4|s>>2,p[i++]=(s&3)<<6|d&63;return u},io=typeof ArrayBuffer=="function",Gt=(n,t)=>{if(typeof n!="string")return{type:"message",data:_r(n,t)};const e=n.charAt(0);return e==="b"?{type:"message",data:oo(n.substring(1),t)}:Qe[e]?n.length>1?{type:Qe[e],data:n.substring(1)}:{type:Qe[e]}:It},oo=(n,t)=>{if(io){const e=ro(n);return _r(e,t)}else return{base64:!0,data:n}},_r=(n,t)=>{switch(t){case"blob":return n instanceof Blob?n:new Blob([n]);case"arraybuffer":default:return n instanceof ArrayBuffer?n:n.buffer}},yr="",so=(n,t)=>{const e=n.length,r=new Array(e);let i=0;n.forEach((o,a)=>{zt(o,!1,s=>{r[a]=s,++i===e&&t(r.join(yr))})})},ao=(n,t)=>{const e=n.split(yr),r=[];for(let i=0;i<e.length;i++){const o=Gt(e[i],t);if(r.push(o),o.type==="error")break}return r};function co(){return new TransformStream({transform(n,t){no(n,e=>{const r=e.length;let i;if(r<126)i=new Uint8Array(1),new DataView(i.buffer).setUint8(0,r);else if(r<65536){i=new Uint8Array(3);const o=new DataView(i.buffer);o.setUint8(0,126),o.setUint16(1,r)}else{i=new Uint8Array(9);const o=new DataView(i.buffer);o.setUint8(0,127),o.setBigUint64(1,BigInt(r))}n.data&&typeof n.data!="string"&&(i[0]|=128),t.enqueue(i),t.enqueue(e)})}})}let St;function qe(n){return n.reduce((t,e)=>t+e.length,0)}function ze(n,t){if(n[0].length===t)return n.shift();const e=new Uint8Array(t);let r=0;for(let i=0;i<t;i++)e[i]=n[0][r++],r===n[0].length&&(n.shift(),r=0);return n.length&&r<n[0].length&&(n[0]=n[0].slice(r)),e}function uo(n,t){St||(St=new TextDecoder);const e=[];let r=0,i=-1,o=!1;return new TransformStream({transform(a,s){for(e.push(a);;){if(r===0){if(qe(e)<1)break;const d=ze(e,1);o=(d[0]&128)===128,i=d[0]&127,i<126?r=3:i===126?r=1:r=2}else if(r===1){if(qe(e)<2)break;const d=ze(e,2);i=new DataView(d.buffer,d.byteOffset,d.length).getUint16(0),r=3}else if(r===2){if(qe(e)<8)break;const d=ze(e,8),u=new DataView(d.buffer,d.byteOffset,d.length),p=u.getUint32(0);if(p>Math.pow(2,21)-1){s.enqueue(It);break}i=p*Math.pow(2,32)+u.getUint32(4),r=3}else{if(qe(e)<i)break;const d=ze(e,i);s.enqueue(Gt(o?d:St.decode(d),t)),r=0}if(i===0||i>n){s.enqueue(It);break}}}})}const br=4;function ne(n){if(n)return lo(n)}function lo(n){for(var t in ne.prototype)n[t]=ne.prototype[t];return n}ne.prototype.on=ne.prototype.addEventListener=function(n,t){return this._callbacks=this._callbacks||{},(this._callbacks["$"+n]=this._callbacks["$"+n]||[]).push(t),this};ne.prototype.once=function(n,t){function e(){this.off(n,e),t.apply(this,arguments)}return e.fn=t,this.on(n,e),this};ne.prototype.off=ne.prototype.removeListener=ne.prototype.removeAllListeners=ne.prototype.removeEventListener=function(n,t){if(this._callbacks=this._callbacks||{},arguments.length==0)return this._callbacks={},this;var e=this._callbacks["$"+n];if(!e)return this;if(arguments.length==1)return delete this._callbacks["$"+n],this;for(var r,i=0;i<e.length;i++)if(r=e[i],r===t||r.fn===t){e.splice(i,1);break}return e.length===0&&delete this._callbacks["$"+n],this};ne.prototype.emit=function(n){this._callbacks=this._callbacks||{};for(var t=new Array(arguments.length-1),e=this._callbacks["$"+n],r=1;r<arguments.length;r++)t[r-1]=arguments[r];if(e){e=e.slice(0);for(var r=0,i=e.length;r<i;++r)e[r].apply(this,t)}return this};ne.prototype.emitReserved=ne.prototype.emit;ne.prototype.listeners=function(n){return this._callbacks=this._callbacks||{},this._callbacks["$"+n]||[]};ne.prototype.hasListeners=function(n){return!!this.listeners(n).length};const lt=typeof Promise=="function"&&typeof Promise.resolve=="function"?t=>Promise.resolve().then(t):(t,e)=>e(t,0),ue=typeof self<"u"?self:typeof window<"u"?window:Function("return this")(),fo="arraybuffer";function Sr(n,...t){return t.reduce((e,r)=>(n.hasOwnProperty(r)&&(e[r]=n[r]),e),{})}const po=ue.setTimeout,ho=ue.clearTimeout;function dt(n,t){t.useNativeTimers?(n.setTimeoutFn=po.bind(ue),n.clearTimeoutFn=ho.bind(ue)):(n.setTimeoutFn=ue.setTimeout.bind(ue),n.clearTimeoutFn=ue.clearTimeout.bind(ue))}const go=1.33;function mo(n){return typeof n=="string"?vo(n):Math.ceil((n.byteLength||n.size)*go)}function vo(n){let t=0,e=0;for(let r=0,i=n.length;r<i;r++)t=n.charCodeAt(r),t<128?e+=1:t<2048?e+=2:t<55296||t>=57344?e+=3:(r++,e+=4);return e}function wr(){return Date.now().toString(36).substring(3)+Math.random().toString(36).substring(2,5)}function _o(n){let t="";for(let e in n)n.hasOwnProperty(e)&&(t.length&&(t+="&"),t+=encodeURIComponent(e)+"="+encodeURIComponent(n[e]));return t}function yo(n){let t={},e=n.split("&");for(let r=0,i=e.length;r<i;r++){let o=e[r].split("=");t[decodeURIComponent(o[0])]=decodeURIComponent(o[1])}return t}class bo extends Error{constructor(t,e,r){super(t),this.description=e,this.context=r,this.type="TransportError"}}class $t extends ne{constructor(t){super(),this.writable=!1,dt(this,t),this.opts=t,this.query=t.query,this.socket=t.socket,this.supportsBinary=!t.forceBase64}onError(t,e,r){return super.emitReserved("error",new bo(t,e,r)),this}open(){return this.readyState="opening",this.doOpen(),this}close(){return(this.readyState==="opening"||this.readyState==="open")&&(this.doClose(),this.onClose()),this}send(t){this.readyState==="open"&&this.write(t)}onOpen(){this.readyState="open",this.writable=!0,super.emitReserved("open")}onData(t){const e=Gt(t,this.socket.binaryType);this.onPacket(e)}onPacket(t){super.emitReserved("packet",t)}onClose(t){this.readyState="closed",super.emitReserved("close",t)}pause(t){}createUri(t,e={}){return t+"://"+this._hostname()+this._port()+this.opts.path+this._query(e)}_hostname(){const t=this.opts.hostname;return t.indexOf(":")===-1?t:"["+t+"]"}_port(){return this.opts.port&&(this.opts.secure&&+(this.opts.port!==443)||!this.opts.secure&&Number(this.opts.port)!==80)?":"+this.opts.port:""}_query(t){const e=_o(t);return e.length?"?"+e:""}}class So extends $t{constructor(){super(...arguments),this._polling=!1}get name(){return"polling"}doOpen(){this._poll()}pause(t){this.readyState="pausing";const e=()=>{this.readyState="paused",t()};if(this._polling||!this.writable){let r=0;this._polling&&(r++,this.once("pollComplete",function(){--r||e()})),this.writable||(r++,this.once("drain",function(){--r||e()}))}else e()}_poll(){this._polling=!0,this.doPoll(),this.emitReserved("poll")}onData(t){const e=r=>{if(this.readyState==="opening"&&r.type==="open"&&this.onOpen(),r.type==="close")return this.onClose({description:"transport closed by the server"}),!1;this.onPacket(r)};ao(t,this.socket.binaryType).forEach(e),this.readyState!=="closed"&&(this._polling=!1,this.emitReserved("pollComplete"),this.readyState==="open"&&this._poll())}doClose(){const t=()=>{this.write([{type:"close"}])};this.readyState==="open"?t():this.once("open",t)}write(t){this.writable=!1,so(t,e=>{this.doWrite(e,()=>{this.writable=!0,this.emitReserved("drain")})})}uri(){const t=this.opts.secure?"https":"http",e=this.query||{};return this.opts.timestampRequests!==!1&&(e[this.opts.timestampParam]=wr()),!this.supportsBinary&&!e.sid&&(e.b64=1),this.createUri(t,e)}}let Cr=!1;try{Cr=typeof XMLHttpRequest<"u"&&"withCredentials"in new XMLHttpRequest}catch{}const wo=Cr;function Co(){}class Eo extends So{constructor(t){if(super(t),typeof location<"u"){const e=location.protocol==="https:";let r=location.port;r||(r=e?"443":"80"),this.xd=typeof location<"u"&&t.hostname!==location.hostname||r!==t.port}}doWrite(t,e){const r=this.request({method:"POST",data:t});r.on("success",e),r.on("error",(i,o)=>{this.onError("xhr post error",i,o)})}doPoll(){const t=this.request();t.on("data",this.onData.bind(this)),t.on("error",(e,r)=>{this.onError("xhr poll error",e,r)}),this.pollXhr=t}}let Oe=class Ze extends ne{constructor(t,e,r){super(),this.createRequest=t,dt(this,r),this._opts=r,this._method=r.method||"GET",this._uri=e,this._data=r.data!==void 0?r.data:null,this._create()}_create(){var t;const e=Sr(this._opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");e.xdomain=!!this._opts.xd;const r=this._xhr=this.createRequest(e);try{r.open(this._method,this._uri,!0);try{if(this._opts.extraHeaders){r.setDisableHeaderCheck&&r.setDisableHeaderCheck(!0);for(let i in this._opts.extraHeaders)this._opts.extraHeaders.hasOwnProperty(i)&&r.setRequestHeader(i,this._opts.extraHeaders[i])}}catch{}if(this._method==="POST")try{r.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch{}try{r.setRequestHeader("Accept","*/*")}catch{}(t=this._opts.cookieJar)===null||t===void 0||t.addCookies(r),"withCredentials"in r&&(r.withCredentials=this._opts.withCredentials),this._opts.requestTimeout&&(r.timeout=this._opts.requestTimeout),r.onreadystatechange=()=>{var i;r.readyState===3&&((i=this._opts.cookieJar)===null||i===void 0||i.parseCookies(r.getResponseHeader("set-cookie"))),r.readyState===4&&(r.status===200||r.status===1223?this._onLoad():this.setTimeoutFn(()=>{this._onError(typeof r.status=="number"?r.status:0)},0))},r.send(this._data)}catch(i){this.setTimeoutFn(()=>{this._onError(i)},0);return}typeof document<"u"&&(this._index=Ze.requestsCount++,Ze.requests[this._index]=this)}_onError(t){this.emitReserved("error",t,this._xhr),this._cleanup(!0)}_cleanup(t){if(!(typeof this._xhr>"u"||this._xhr===null)){if(this._xhr.onreadystatechange=Co,t)try{this._xhr.abort()}catch{}typeof document<"u"&&delete Ze.requests[this._index],this._xhr=null}}_onLoad(){const t=this._xhr.responseText;t!==null&&(this.emitReserved("data",t),this.emitReserved("success"),this._cleanup())}abort(){this._cleanup()}};Oe.requestsCount=0;Oe.requests={};if(typeof document<"u"){if(typeof attachEvent=="function")attachEvent("onunload",Mn);else if(typeof addEventListener=="function"){const n="onpagehide"in ue?"pagehide":"unload";addEventListener(n,Mn,!1)}}function Mn(){for(let n in Oe.requests)Oe.requests.hasOwnProperty(n)&&Oe.requests[n].abort()}const To=function(){const n=Er({xdomain:!1});return n&&n.responseType!==null}();class xo extends Eo{constructor(t){super(t);const e=t&&t.forceBase64;this.supportsBinary=To&&!e}request(t={}){return Object.assign(t,{xd:this.xd},this.opts),new Oe(Er,this.uri(),t)}}function Er(n){const t=n.xdomain;try{if(typeof XMLHttpRequest<"u"&&(!t||wo))return new XMLHttpRequest}catch{}if(!t)try{return new ue[["Active"].concat("Object").join("X")]("Microsoft.XMLHTTP")}catch{}}const Tr=typeof navigator<"u"&&typeof navigator.product=="string"&&navigator.product.toLowerCase()==="reactnative";class Po extends $t{get name(){return"websocket"}doOpen(){const t=this.uri(),e=this.opts.protocols,r=Tr?{}:Sr(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(r.headers=this.opts.extraHeaders);try{this.ws=this.createSocket(t,e,r)}catch(i){return this.emitReserved("error",i)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()}addEventListeners(){this.ws.onopen=()=>{this.opts.autoUnref&&this.ws._socket.unref(),this.onOpen()},this.ws.onclose=t=>this.onClose({description:"websocket connection closed",context:t}),this.ws.onmessage=t=>this.onData(t.data),this.ws.onerror=t=>this.onError("websocket error",t)}write(t){this.writable=!1;for(let e=0;e<t.length;e++){const r=t[e],i=e===t.length-1;zt(r,this.supportsBinary,o=>{try{this.doWrite(r,o)}catch{}i&&lt(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){typeof this.ws<"u"&&(this.ws.onerror=()=>{},this.ws.close(),this.ws=null)}uri(){const t=this.opts.secure?"wss":"ws",e=this.query||{};return this.opts.timestampRequests&&(e[this.opts.timestampParam]=wr()),this.supportsBinary||(e.b64=1),this.createUri(t,e)}}const wt=ue.WebSocket||ue.MozWebSocket;class ko extends Po{createSocket(t,e,r){return Tr?new wt(t,e,r):e?new wt(t,e):new wt(t)}doWrite(t,e){this.ws.send(e)}}class Io extends $t{get name(){return"webtransport"}doOpen(){try{this._transport=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name])}catch(t){return this.emitReserved("error",t)}this._transport.closed.then(()=>{this.onClose()}).catch(t=>{this.onError("webtransport error",t)}),this._transport.ready.then(()=>{this._transport.createBidirectionalStream().then(t=>{const e=uo(Number.MAX_SAFE_INTEGER,this.socket.binaryType),r=t.readable.pipeThrough(e).getReader(),i=co();i.readable.pipeTo(t.writable),this._writer=i.writable.getWriter();const o=()=>{r.read().then(({done:s,value:d})=>{s||(this.onPacket(d),o())}).catch(s=>{})};o();const a={type:"open"};this.query.sid&&(a.data=`{"sid":"${this.query.sid}"}`),this._writer.write(a).then(()=>this.onOpen())})})}write(t){this.writable=!1;for(let e=0;e<t.length;e++){const r=t[e],i=e===t.length-1;this._writer.write(r).then(()=>{i&&lt(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){var t;(t=this._transport)===null||t===void 0||t.close()}}const Ro={websocket:ko,webtransport:Io,polling:xo},Ao=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,Do=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function Rt(n){if(n.length>8e3)throw"URI too long";const t=n,e=n.indexOf("["),r=n.indexOf("]");e!=-1&&r!=-1&&(n=n.substring(0,e)+n.substring(e,r).replace(/:/g,";")+n.substring(r,n.length));let i=Ao.exec(n||""),o={},a=14;for(;a--;)o[Do[a]]=i[a]||"";return e!=-1&&r!=-1&&(o.source=t,o.host=o.host.substring(1,o.host.length-1).replace(/;/g,":"),o.authority=o.authority.replace("[","").replace("]","").replace(/;/g,":"),o.ipv6uri=!0),o.pathNames=Oo(o,o.path),o.queryKey=Mo(o,o.query),o}function Oo(n,t){const e=/\/{2,9}/g,r=t.replace(e,"/").split("/");return(t.slice(0,1)=="/"||t.length===0)&&r.splice(0,1),t.slice(-1)=="/"&&r.splice(r.length-1,1),r}function Mo(n,t){const e={};return t.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,function(r,i,o){i&&(e[i]=o)}),e}const At=typeof addEventListener=="function"&&typeof removeEventListener=="function",et=[];At&&addEventListener("offline",()=>{et.forEach(n=>n())},!1);class we extends ne{constructor(t,e){if(super(),this.binaryType=fo,this.writeBuffer=[],this._prevBufferLen=0,this._pingInterval=-1,this._pingTimeout=-1,this._maxPayload=-1,this._pingTimeoutTime=1/0,t&&typeof t=="object"&&(e=t,t=null),t){const r=Rt(t);e.hostname=r.host,e.secure=r.protocol==="https"||r.protocol==="wss",e.port=r.port,r.query&&(e.query=r.query)}else e.host&&(e.hostname=Rt(e.host).host);dt(this,e),this.secure=e.secure!=null?e.secure:typeof location<"u"&&location.protocol==="https:",e.hostname&&!e.port&&(e.port=this.secure?"443":"80"),this.hostname=e.hostname||(typeof location<"u"?location.hostname:"localhost"),this.port=e.port||(typeof location<"u"&&location.port?location.port:this.secure?"443":"80"),this.transports=[],this._transportsByName={},e.transports.forEach(r=>{const i=r.prototype.name;this.transports.push(i),this._transportsByName[i]=r}),this.opts=Object.assign({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},e),this.opts.path=this.opts.path.replace(/\/$/,"")+(this.opts.addTrailingSlash?"/":""),typeof this.opts.query=="string"&&(this.opts.query=yo(this.opts.query)),At&&(this.opts.closeOnBeforeunload&&(this._beforeunloadEventListener=()=>{this.transport&&(this.transport.removeAllListeners(),this.transport.close())},addEventListener("beforeunload",this._beforeunloadEventListener,!1)),this.hostname!=="localhost"&&(this._offlineEventListener=()=>{this._onClose("transport close",{description:"network connection lost"})},et.push(this._offlineEventListener))),this.opts.withCredentials&&(this._cookieJar=void 0),this._open()}createTransport(t){const e=Object.assign({},this.opts.query);e.EIO=br,e.transport=t,this.id&&(e.sid=this.id);const r=Object.assign({},this.opts,{query:e,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[t]);return new this._transportsByName[t](r)}_open(){if(this.transports.length===0){this.setTimeoutFn(()=>{this.emitReserved("error","No transports available")},0);return}const t=this.opts.rememberUpgrade&&we.priorWebsocketSuccess&&this.transports.indexOf("websocket")!==-1?"websocket":this.transports[0];this.readyState="opening";const e=this.createTransport(t);e.open(),this.setTransport(e)}setTransport(t){this.transport&&this.transport.removeAllListeners(),this.transport=t,t.on("drain",this._onDrain.bind(this)).on("packet",this._onPacket.bind(this)).on("error",this._onError.bind(this)).on("close",e=>this._onClose("transport close",e))}onOpen(){this.readyState="open",we.priorWebsocketSuccess=this.transport.name==="websocket",this.emitReserved("open"),this.flush()}_onPacket(t){if(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing")switch(this.emitReserved("packet",t),this.emitReserved("heartbeat"),t.type){case"open":this.onHandshake(JSON.parse(t.data));break;case"ping":this._sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong"),this._resetPingTimeout();break;case"error":const e=new Error("server error");e.code=t.data,this._onError(e);break;case"message":this.emitReserved("data",t.data),this.emitReserved("message",t.data);break}}onHandshake(t){this.emitReserved("handshake",t),this.id=t.sid,this.transport.query.sid=t.sid,this._pingInterval=t.pingInterval,this._pingTimeout=t.pingTimeout,this._maxPayload=t.maxPayload,this.onOpen(),this.readyState!=="closed"&&this._resetPingTimeout()}_resetPingTimeout(){this.clearTimeoutFn(this._pingTimeoutTimer);const t=this._pingInterval+this._pingTimeout;this._pingTimeoutTime=Date.now()+t,this._pingTimeoutTimer=this.setTimeoutFn(()=>{this._onClose("ping timeout")},t),this.opts.autoUnref&&this._pingTimeoutTimer.unref()}_onDrain(){this.writeBuffer.splice(0,this._prevBufferLen),this._prevBufferLen=0,this.writeBuffer.length===0?this.emitReserved("drain"):this.flush()}flush(){if(this.readyState!=="closed"&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){const t=this._getWritablePackets();this.transport.send(t),this._prevBufferLen=t.length,this.emitReserved("flush")}}_getWritablePackets(){if(!(this._maxPayload&&this.transport.name==="polling"&&this.writeBuffer.length>1))return this.writeBuffer;let e=1;for(let r=0;r<this.writeBuffer.length;r++){const i=this.writeBuffer[r].data;if(i&&(e+=mo(i)),r>0&&e>this._maxPayload)return this.writeBuffer.slice(0,r);e+=2}return this.writeBuffer}_hasPingExpired(){if(!this._pingTimeoutTime)return!0;const t=Date.now()>this._pingTimeoutTime;return t&&(this._pingTimeoutTime=0,lt(()=>{this._onClose("ping timeout")},this.setTimeoutFn)),t}write(t,e,r){return this._sendPacket("message",t,e,r),this}send(t,e,r){return this._sendPacket("message",t,e,r),this}_sendPacket(t,e,r,i){if(typeof e=="function"&&(i=e,e=void 0),typeof r=="function"&&(i=r,r=null),this.readyState==="closing"||this.readyState==="closed")return;r=r||{},r.compress=r.compress!==!1;const o={type:t,data:e,options:r};this.emitReserved("packetCreate",o),this.writeBuffer.push(o),i&&this.once("flush",i),this.flush()}close(){const t=()=>{this._onClose("forced close"),this.transport.close()},e=()=>{this.off("upgrade",e),this.off("upgradeError",e),t()},r=()=>{this.once("upgrade",e),this.once("upgradeError",e)};return(this.readyState==="opening"||this.readyState==="open")&&(this.readyState="closing",this.writeBuffer.length?this.once("drain",()=>{this.upgrading?r():t()}):this.upgrading?r():t()),this}_onError(t){if(we.priorWebsocketSuccess=!1,this.opts.tryAllTransports&&this.transports.length>1&&this.readyState==="opening")return this.transports.shift(),this._open();this.emitReserved("error",t),this._onClose("transport error",t)}_onClose(t,e){if(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing"){if(this.clearTimeoutFn(this._pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),At&&(this._beforeunloadEventListener&&removeEventListener("beforeunload",this._beforeunloadEventListener,!1),this._offlineEventListener)){const r=et.indexOf(this._offlineEventListener);r!==-1&&et.splice(r,1)}this.readyState="closed",this.id=null,this.emitReserved("close",t,e),this.writeBuffer=[],this._prevBufferLen=0}}}we.protocol=br;class jo extends we{constructor(){super(...arguments),this._upgrades=[]}onOpen(){if(super.onOpen(),this.readyState==="open"&&this.opts.upgrade)for(let t=0;t<this._upgrades.length;t++)this._probe(this._upgrades[t])}_probe(t){let e=this.createTransport(t),r=!1;we.priorWebsocketSuccess=!1;const i=()=>{r||(e.send([{type:"ping",data:"probe"}]),e.once("packet",f=>{if(!r)if(f.type==="pong"&&f.data==="probe"){if(this.upgrading=!0,this.emitReserved("upgrading",e),!e)return;we.priorWebsocketSuccess=e.name==="websocket",this.transport.pause(()=>{r||this.readyState!=="closed"&&(p(),this.setTransport(e),e.send([{type:"upgrade"}]),this.emitReserved("upgrade",e),e=null,this.upgrading=!1,this.flush())})}else{const v=new Error("probe error");v.transport=e.name,this.emitReserved("upgradeError",v)}}))};function o(){r||(r=!0,p(),e.close(),e=null)}const a=f=>{const v=new Error("probe error: "+f);v.transport=e.name,o(),this.emitReserved("upgradeError",v)};function s(){a("transport closed")}function d(){a("socket closed")}function u(f){e&&f.name!==e.name&&o()}const p=()=>{e.removeListener("open",i),e.removeListener("error",a),e.removeListener("close",s),this.off("close",d),this.off("upgrading",u)};e.once("open",i),e.once("error",a),e.once("close",s),this.once("close",d),this.once("upgrading",u),this._upgrades.indexOf("webtransport")!==-1&&t!=="webtransport"?this.setTimeoutFn(()=>{r||e.open()},200):e.open()}onHandshake(t){this._upgrades=this._filterUpgrades(t.upgrades),super.onHandshake(t)}_filterUpgrades(t){const e=[];for(let r=0;r<t.length;r++)~this.transports.indexOf(t[r])&&e.push(t[r]);return e}}let Lo=class extends jo{constructor(t,e={}){const r=typeof t=="object"?t:e;(!r.transports||r.transports&&typeof r.transports[0]=="string")&&(r.transports=(r.transports||["polling","websocket","webtransport"]).map(i=>Ro[i]).filter(i=>!!i)),super(t,r)}};function No(n,t="",e){let r=n;e=e||typeof location<"u"&&location,n==null&&(n=e.protocol+"//"+e.host),typeof n=="string"&&(n.charAt(0)==="/"&&(n.charAt(1)==="/"?n=e.protocol+n:n=e.host+n),/^(https?|wss?):\/\//.test(n)||(typeof e<"u"?n=e.protocol+"//"+n:n="https://"+n),r=Rt(n)),r.port||(/^(http|ws)$/.test(r.protocol)?r.port="80":/^(http|ws)s$/.test(r.protocol)&&(r.port="443")),r.path=r.path||"/";const o=r.host.indexOf(":")!==-1?"["+r.host+"]":r.host;return r.id=r.protocol+"://"+o+":"+r.port+t,r.href=r.protocol+"://"+o+(e&&e.port===r.port?"":":"+r.port),r}const Uo=typeof ArrayBuffer=="function",Fo=n=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(n):n.buffer instanceof ArrayBuffer,xr=Object.prototype.toString,Bo=typeof Blob=="function"||typeof Blob<"u"&&xr.call(Blob)==="[object BlobConstructor]",Wo=typeof File=="function"||typeof File<"u"&&xr.call(File)==="[object FileConstructor]";function Jt(n){return Uo&&(n instanceof ArrayBuffer||Fo(n))||Bo&&n instanceof Blob||Wo&&n instanceof File}function tt(n,t){if(!n||typeof n!="object")return!1;if(Array.isArray(n)){for(let e=0,r=n.length;e<r;e++)if(tt(n[e]))return!0;return!1}if(Jt(n))return!0;if(n.toJSON&&typeof n.toJSON=="function"&&arguments.length===1)return tt(n.toJSON(),!0);for(const e in n)if(Object.prototype.hasOwnProperty.call(n,e)&&tt(n[e]))return!0;return!1}function Vo(n){const t=[],e=n.data,r=n;return r.data=Dt(e,t),r.attachments=t.length,{packet:r,buffers:t}}function Dt(n,t){if(!n)return n;if(Jt(n)){const e={_placeholder:!0,num:t.length};return t.push(n),e}else if(Array.isArray(n)){const e=new Array(n.length);for(let r=0;r<n.length;r++)e[r]=Dt(n[r],t);return e}else if(typeof n=="object"&&!(n instanceof Date)){const e={};for(const r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=Dt(n[r],t));return e}return n}function Ho(n,t){return n.data=Ot(n.data,t),delete n.attachments,n}function Ot(n,t){if(!n)return n;if(n&&n._placeholder===!0){if(typeof n.num=="number"&&n.num>=0&&n.num<t.length)return t[n.num];throw new Error("illegal attachments")}else if(Array.isArray(n))for(let e=0;e<n.length;e++)n[e]=Ot(n[e],t);else if(typeof n=="object")for(const e in n)Object.prototype.hasOwnProperty.call(n,e)&&(n[e]=Ot(n[e],t));return n}const qo=["connect","connect_error","disconnect","disconnecting","newListener","removeListener"],zo=5;var $;(function(n){n[n.CONNECT=0]="CONNECT",n[n.DISCONNECT=1]="DISCONNECT",n[n.EVENT=2]="EVENT",n[n.ACK=3]="ACK",n[n.CONNECT_ERROR=4]="CONNECT_ERROR",n[n.BINARY_EVENT=5]="BINARY_EVENT",n[n.BINARY_ACK=6]="BINARY_ACK"})($||($={}));class Go{constructor(t){this.replacer=t}encode(t){return(t.type===$.EVENT||t.type===$.ACK)&&tt(t)?this.encodeAsBinary({type:t.type===$.EVENT?$.BINARY_EVENT:$.BINARY_ACK,nsp:t.nsp,data:t.data,id:t.id}):[this.encodeAsString(t)]}encodeAsString(t){let e=""+t.type;return(t.type===$.BINARY_EVENT||t.type===$.BINARY_ACK)&&(e+=t.attachments+"-"),t.nsp&&t.nsp!=="/"&&(e+=t.nsp+","),t.id!=null&&(e+=t.id),t.data!=null&&(e+=JSON.stringify(t.data,this.replacer)),e}encodeAsBinary(t){const e=Vo(t),r=this.encodeAsString(e.packet),i=e.buffers;return i.unshift(r),i}}function jn(n){return Object.prototype.toString.call(n)==="[object Object]"}class Kt extends ne{constructor(t){super(),this.reviver=t}add(t){let e;if(typeof t=="string"){if(this.reconstructor)throw new Error("got plaintext data when reconstructing a packet");e=this.decodeString(t);const r=e.type===$.BINARY_EVENT;r||e.type===$.BINARY_ACK?(e.type=r?$.EVENT:$.ACK,this.reconstructor=new $o(e),e.attachments===0&&super.emitReserved("decoded",e)):super.emitReserved("decoded",e)}else if(Jt(t)||t.base64)if(this.reconstructor)e=this.reconstructor.takeBinaryData(t),e&&(this.reconstructor=null,super.emitReserved("decoded",e));else throw new Error("got binary data when not reconstructing a packet");else throw new Error("Unknown type: "+t)}decodeString(t){let e=0;const r={type:Number(t.charAt(0))};if($[r.type]===void 0)throw new Error("unknown packet type "+r.type);if(r.type===$.BINARY_EVENT||r.type===$.BINARY_ACK){const o=e+1;for(;t.charAt(++e)!=="-"&&e!=t.length;);const a=t.substring(o,e);if(a!=Number(a)||t.charAt(e)!=="-")throw new Error("Illegal attachments");r.attachments=Number(a)}if(t.charAt(e+1)==="/"){const o=e+1;for(;++e&&!(t.charAt(e)===","||e===t.length););r.nsp=t.substring(o,e)}else r.nsp="/";const i=t.charAt(e+1);if(i!==""&&Number(i)==i){const o=e+1;for(;++e;){const a=t.charAt(e);if(a==null||Number(a)!=a){--e;break}if(e===t.length)break}r.id=Number(t.substring(o,e+1))}if(t.charAt(++e)){const o=this.tryParse(t.substr(e));if(Kt.isPayloadValid(r.type,o))r.data=o;else throw new Error("invalid payload")}return r}tryParse(t){try{return JSON.parse(t,this.reviver)}catch{return!1}}static isPayloadValid(t,e){switch(t){case $.CONNECT:return jn(e);case $.DISCONNECT:return e===void 0;case $.CONNECT_ERROR:return typeof e=="string"||jn(e);case $.EVENT:case $.BINARY_EVENT:return Array.isArray(e)&&(typeof e[0]=="number"||typeof e[0]=="string"&&qo.indexOf(e[0])===-1);case $.ACK:case $.BINARY_ACK:return Array.isArray(e)}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null)}}class $o{constructor(t){this.packet=t,this.buffers=[],this.reconPack=t}takeBinaryData(t){if(this.buffers.push(t),this.buffers.length===this.reconPack.attachments){const e=Ho(this.reconPack,this.buffers);return this.finishedReconstruction(),e}return null}finishedReconstruction(){this.reconPack=null,this.buffers=[]}}const Jo=Object.freeze(Object.defineProperty({__proto__:null,Decoder:Kt,Encoder:Go,get PacketType(){return $},protocol:zo},Symbol.toStringTag,{value:"Module"}));function pe(n,t,e){return n.on(t,e),function(){n.off(t,e)}}const Ko=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1});class Pr extends ne{constructor(t,e,r){super(),this.connected=!1,this.recovered=!1,this.receiveBuffer=[],this.sendBuffer=[],this._queue=[],this._queueSeq=0,this.ids=0,this.acks={},this.flags={},this.io=t,this.nsp=e,r&&r.auth&&(this.auth=r.auth),this._opts=Object.assign({},r),this.io._autoConnect&&this.open()}get disconnected(){return!this.connected}subEvents(){if(this.subs)return;const t=this.io;this.subs=[pe(t,"open",this.onopen.bind(this)),pe(t,"packet",this.onpacket.bind(this)),pe(t,"error",this.onerror.bind(this)),pe(t,"close",this.onclose.bind(this))]}get active(){return!!this.subs}connect(){return this.connected?this:(this.subEvents(),this.io._reconnecting||this.io.open(),this.io._readyState==="open"&&this.onopen(),this)}open(){return this.connect()}send(...t){return t.unshift("message"),this.emit.apply(this,t),this}emit(t,...e){var r,i,o;if(Ko.hasOwnProperty(t))throw new Error('"'+t.toString()+'" is a reserved event name');if(e.unshift(t),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(e),this;const a={type:$.EVENT,data:e};if(a.options={},a.options.compress=this.flags.compress!==!1,typeof e[e.length-1]=="function"){const p=this.ids++,f=e.pop();this._registerAckCallback(p,f),a.id=p}const s=(i=(r=this.io.engine)===null||r===void 0?void 0:r.transport)===null||i===void 0?void 0:i.writable,d=this.connected&&!(!((o=this.io.engine)===null||o===void 0)&&o._hasPingExpired());return this.flags.volatile&&!s||(d?(this.notifyOutgoingListeners(a),this.packet(a)):this.sendBuffer.push(a)),this.flags={},this}_registerAckCallback(t,e){var r;const i=(r=this.flags.timeout)!==null&&r!==void 0?r:this._opts.ackTimeout;if(i===void 0){this.acks[t]=e;return}const o=this.io.setTimeoutFn(()=>{delete this.acks[t];for(let s=0;s<this.sendBuffer.length;s++)this.sendBuffer[s].id===t&&this.sendBuffer.splice(s,1);e.call(this,new Error("operation has timed out"))},i),a=(...s)=>{this.io.clearTimeoutFn(o),e.apply(this,s)};a.withError=!0,this.acks[t]=a}emitWithAck(t,...e){return new Promise((r,i)=>{const o=(a,s)=>a?i(a):r(s);o.withError=!0,e.push(o),this.emit(t,...e)})}_addToQueue(t){let e;typeof t[t.length-1]=="function"&&(e=t.pop());const r={id:this._queueSeq++,tryCount:0,pending:!1,args:t,flags:Object.assign({fromQueue:!0},this.flags)};t.push((i,...o)=>r!==this._queue[0]?void 0:(i!==null?r.tryCount>this._opts.retries&&(this._queue.shift(),e&&e(i)):(this._queue.shift(),e&&e(null,...o)),r.pending=!1,this._drainQueue())),this._queue.push(r),this._drainQueue()}_drainQueue(t=!1){if(!this.connected||this._queue.length===0)return;const e=this._queue[0];e.pending&&!t||(e.pending=!0,e.tryCount++,this.flags=e.flags,this.emit.apply(this,e.args))}packet(t){t.nsp=this.nsp,this.io._packet(t)}onopen(){typeof this.auth=="function"?this.auth(t=>{this._sendConnectPacket(t)}):this._sendConnectPacket(this.auth)}_sendConnectPacket(t){this.packet({type:$.CONNECT,data:this._pid?Object.assign({pid:this._pid,offset:this._lastOffset},t):t})}onerror(t){this.connected||this.emitReserved("connect_error",t)}onclose(t,e){this.connected=!1,delete this.id,this.emitReserved("disconnect",t,e),this._clearAcks()}_clearAcks(){Object.keys(this.acks).forEach(t=>{if(!this.sendBuffer.some(r=>String(r.id)===t)){const r=this.acks[t];delete this.acks[t],r.withError&&r.call(this,new Error("socket has been disconnected"))}})}onpacket(t){if(t.nsp===this.nsp)switch(t.type){case $.CONNECT:t.data&&t.data.sid?this.onconnect(t.data.sid,t.data.pid):this.emitReserved("connect_error",new Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case $.EVENT:case $.BINARY_EVENT:this.onevent(t);break;case $.ACK:case $.BINARY_ACK:this.onack(t);break;case $.DISCONNECT:this.ondisconnect();break;case $.CONNECT_ERROR:this.destroy();const r=new Error(t.data.message);r.data=t.data.data,this.emitReserved("connect_error",r);break}}onevent(t){const e=t.data||[];t.id!=null&&e.push(this.ack(t.id)),this.connected?this.emitEvent(e):this.receiveBuffer.push(Object.freeze(e))}emitEvent(t){if(this._anyListeners&&this._anyListeners.length){const e=this._anyListeners.slice();for(const r of e)r.apply(this,t)}super.emit.apply(this,t),this._pid&&t.length&&typeof t[t.length-1]=="string"&&(this._lastOffset=t[t.length-1])}ack(t){const e=this;let r=!1;return function(...i){r||(r=!0,e.packet({type:$.ACK,id:t,data:i}))}}onack(t){const e=this.acks[t.id];typeof e=="function"&&(delete this.acks[t.id],e.withError&&t.data.unshift(null),e.apply(this,t.data))}onconnect(t,e){this.id=t,this.recovered=e&&this._pid===e,this._pid=e,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this._drainQueue(!0)}emitBuffered(){this.receiveBuffer.forEach(t=>this.emitEvent(t)),this.receiveBuffer=[],this.sendBuffer.forEach(t=>{this.notifyOutgoingListeners(t),this.packet(t)}),this.sendBuffer=[]}ondisconnect(){this.destroy(),this.onclose("io server disconnect")}destroy(){this.subs&&(this.subs.forEach(t=>t()),this.subs=void 0),this.io._destroy(this)}disconnect(){return this.connected&&this.packet({type:$.DISCONNECT}),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}close(){return this.disconnect()}compress(t){return this.flags.compress=t,this}get volatile(){return this.flags.volatile=!0,this}timeout(t){return this.flags.timeout=t,this}onAny(t){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(t),this}prependAny(t){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(t),this}offAny(t){if(!this._anyListeners)return this;if(t){const e=this._anyListeners;for(let r=0;r<e.length;r++)if(t===e[r])return e.splice(r,1),this}else this._anyListeners=[];return this}listenersAny(){return this._anyListeners||[]}onAnyOutgoing(t){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(t),this}prependAnyOutgoing(t){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(t),this}offAnyOutgoing(t){if(!this._anyOutgoingListeners)return this;if(t){const e=this._anyOutgoingListeners;for(let r=0;r<e.length;r++)if(t===e[r])return e.splice(r,1),this}else this._anyOutgoingListeners=[];return this}listenersAnyOutgoing(){return this._anyOutgoingListeners||[]}notifyOutgoingListeners(t){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length){const e=this._anyOutgoingListeners.slice();for(const r of e)r.apply(this,t.data)}}}function Le(n){n=n||{},this.ms=n.min||100,this.max=n.max||1e4,this.factor=n.factor||2,this.jitter=n.jitter>0&&n.jitter<=1?n.jitter:0,this.attempts=0}Le.prototype.duration=function(){var n=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var t=Math.random(),e=Math.floor(t*this.jitter*n);n=Math.floor(t*10)&1?n+e:n-e}return Math.min(n,this.max)|0};Le.prototype.reset=function(){this.attempts=0};Le.prototype.setMin=function(n){this.ms=n};Le.prototype.setMax=function(n){this.max=n};Le.prototype.setJitter=function(n){this.jitter=n};class Mt extends ne{constructor(t,e){var r;super(),this.nsps={},this.subs=[],t&&typeof t=="object"&&(e=t,t=void 0),e=e||{},e.path=e.path||"/socket.io",this.opts=e,dt(this,e),this.reconnection(e.reconnection!==!1),this.reconnectionAttempts(e.reconnectionAttempts||1/0),this.reconnectionDelay(e.reconnectionDelay||1e3),this.reconnectionDelayMax(e.reconnectionDelayMax||5e3),this.randomizationFactor((r=e.randomizationFactor)!==null&&r!==void 0?r:.5),this.backoff=new Le({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(e.timeout==null?2e4:e.timeout),this._readyState="closed",this.uri=t;const i=e.parser||Jo;this.encoder=new i.Encoder,this.decoder=new i.Decoder,this._autoConnect=e.autoConnect!==!1,this._autoConnect&&this.open()}reconnection(t){return arguments.length?(this._reconnection=!!t,t||(this.skipReconnect=!0),this):this._reconnection}reconnectionAttempts(t){return t===void 0?this._reconnectionAttempts:(this._reconnectionAttempts=t,this)}reconnectionDelay(t){var e;return t===void 0?this._reconnectionDelay:(this._reconnectionDelay=t,(e=this.backoff)===null||e===void 0||e.setMin(t),this)}randomizationFactor(t){var e;return t===void 0?this._randomizationFactor:(this._randomizationFactor=t,(e=this.backoff)===null||e===void 0||e.setJitter(t),this)}reconnectionDelayMax(t){var e;return t===void 0?this._reconnectionDelayMax:(this._reconnectionDelayMax=t,(e=this.backoff)===null||e===void 0||e.setMax(t),this)}timeout(t){return arguments.length?(this._timeout=t,this):this._timeout}maybeReconnectOnOpen(){!this._reconnecting&&this._reconnection&&this.backoff.attempts===0&&this.reconnect()}open(t){if(~this._readyState.indexOf("open"))return this;this.engine=new Lo(this.uri,this.opts);const e=this.engine,r=this;this._readyState="opening",this.skipReconnect=!1;const i=pe(e,"open",function(){r.onopen(),t&&t()}),o=s=>{this.cleanup(),this._readyState="closed",this.emitReserved("error",s),t?t(s):this.maybeReconnectOnOpen()},a=pe(e,"error",o);if(this._timeout!==!1){const s=this._timeout,d=this.setTimeoutFn(()=>{i(),o(new Error("timeout")),e.close()},s);this.opts.autoUnref&&d.unref(),this.subs.push(()=>{this.clearTimeoutFn(d)})}return this.subs.push(i),this.subs.push(a),this}connect(t){return this.open(t)}onopen(){this.cleanup(),this._readyState="open",this.emitReserved("open");const t=this.engine;this.subs.push(pe(t,"ping",this.onping.bind(this)),pe(t,"data",this.ondata.bind(this)),pe(t,"error",this.onerror.bind(this)),pe(t,"close",this.onclose.bind(this)),pe(this.decoder,"decoded",this.ondecoded.bind(this)))}onping(){this.emitReserved("ping")}ondata(t){try{this.decoder.add(t)}catch(e){this.onclose("parse error",e)}}ondecoded(t){lt(()=>{this.emitReserved("packet",t)},this.setTimeoutFn)}onerror(t){this.emitReserved("error",t)}socket(t,e){let r=this.nsps[t];return r?this._autoConnect&&!r.active&&r.connect():(r=new Pr(this,t,e),this.nsps[t]=r),r}_destroy(t){const e=Object.keys(this.nsps);for(const r of e)if(this.nsps[r].active)return;this._close()}_packet(t){const e=this.encoder.encode(t);for(let r=0;r<e.length;r++)this.engine.write(e[r],t.options)}cleanup(){this.subs.forEach(t=>t()),this.subs.length=0,this.decoder.destroy()}_close(){this.skipReconnect=!0,this._reconnecting=!1,this.onclose("forced close")}disconnect(){return this._close()}onclose(t,e){var r;this.cleanup(),(r=this.engine)===null||r===void 0||r.close(),this.backoff.reset(),this._readyState="closed",this.emitReserved("close",t,e),this._reconnection&&!this.skipReconnect&&this.reconnect()}reconnect(){if(this._reconnecting||this.skipReconnect)return this;const t=this;if(this.backoff.attempts>=this._reconnectionAttempts)this.backoff.reset(),this.emitReserved("reconnect_failed"),this._reconnecting=!1;else{const e=this.backoff.duration();this._reconnecting=!0;const r=this.setTimeoutFn(()=>{t.skipReconnect||(this.emitReserved("reconnect_attempt",t.backoff.attempts),!t.skipReconnect&&t.open(i=>{i?(t._reconnecting=!1,t.reconnect(),this.emitReserved("reconnect_error",i)):t.onreconnect()}))},e);this.opts.autoUnref&&r.unref(),this.subs.push(()=>{this.clearTimeoutFn(r)})}}onreconnect(){const t=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved("reconnect",t)}}const Ne={};function We(n,t){typeof n=="object"&&(t=n,n=void 0),t=t||{};const e=No(n,t.path||"/socket.io"),r=e.source,i=e.id,o=e.path,a=Ne[i]&&o in Ne[i].nsps,s=t.forceNew||t["force new connection"]||t.multiplex===!1||a;let d;return s?d=new Mt(r,t):(Ne[i]||(Ne[i]=new Mt(r,t)),d=Ne[i]),e.query&&!t.query&&(t.query=e.queryKey),d.socket(e.path,t)}Object.assign(We,{Manager:Mt,Socket:Pr,io:We,connect:We});const Ln=["+447400270472","+15078586181"],Yo=cr("input")({clip:"rect(0 0 0 0)",clipPath:"inset(50%)",height:1,overflow:"hidden",position:"absolute",bottom:0,left:0,whiteSpace:"nowrap",width:1}),Ge=n=>n==null?void 0:n.replace("whatsapp:","").trim(),Xo=n=>{const t=new Date(n);return Xi(t)?"Today":Qi(t)?"Yesterday":_i(t,"MMMM d, yyyy")},Qo=n=>{var t;return n.mediaUrl?O.jsxs("a",{href:n.mediaUrl,target:"_blank",rel:"noopener noreferrer",style:{color:"#0089d6",textDecoration:"underline"},children:["📎 ",(t=n.mediaType)!=null&&t.includes("image")?"Image":"File"]}):(n==null?void 0:n.text)||(n==null?void 0:n.body)||(n==null?void 0:n.message)||(n==null?void 0:n.content)||"Load message again"},Dc=({onClose:n,candidateNumber:t,candidate:e})=>{const[r,i]=z.useState([]),[o,a]=z.useState(""),[s,d]=z.useState(null),[u,p]=z.useState(!1),[f,v]=z.useState(!1),y=z.useRef(null),_=z.useRef(null),b=z.useRef(null),S=()=>{var m;return(m=_.current)==null?void 0:m.scrollIntoView({behavior:"smooth"})},g=z.useCallback(async()=>{try{const w=await(await fetch(Be.getWhatsappMessagesByPhoneNumber.replace(":phoneNumber",t))).json();i(w.map(R=>({...R,sender:Ge(R.from)}))),S()}catch{p(!0)}},[t]);z.useEffect(()=>{g()},[g]),console.log("kjsdgfsdfsdfsdf",r),z.useEffect(()=>(b.current=We("https://x74v19wh-5001.inc1.devtunnels.ms",{transports:["websocket"]}),b.current.on("connect",()=>{console.log("✅ Connected to WebSocket")}),b.current.on("connect_error",m=>{console.error("❌ Connection error:",m.message)}),b.current.on("disconnect",m=>{console.warn("⚠️ Socket disconnected:",m)}),b.current.on("whatsapp-message",m=>{const w={...m,sender:Ge(m.sender),to:Ge(m.to),body:m.body||m.text,timestamp:m.timestamp||new Date().toISOString()};i(R=>[...R,w]),S()}),b.current.on("message-status-updated",m=>{i(w=>w.map(R=>R.messageSid===m.messageSid?{...R,status:m.status}:R)),S()}),()=>{b.current.disconnect()}),[]);const l=m=>Ln.includes(Ge(m)),C=async()=>{if(!(!o.trim()&&!s||f))try{v(!0);const m=r.length===0;let w=o.trim()||"Template: Welcome!",R={to:t,message:w,region:"UK",isTemplate:m};if(m&&(R.templateData={name:(e==null?void 0:e.first_name)||"Candidate",title:(e==null?void 0:e.current_title)||"You are currently in the process of applying",location:"location"}),s){const x=new FormData;x.append("file",s);const T=await(await fetch(Be.uploadFileToS3Bucket,{method:"POST",body:x})).json();R={...R,mediaUrl:T,mediaType:s.type},o.trim()||(R.message=`File: ${s.name}`)}const j=await(await fetch(Be.sendWhatsappMessage,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(R)})).json();i(x=>[...x,{...R,message:R.message,messageSid:j.messageSid,sender:Ln[0],status:"sent",timestamp:new Date().toISOString()}]),a(""),d(null),v(!1)}catch(m){console.error("Error sending message:",m),p(!0),v(!1)}},E=r.reduce((m,w)=>{const R=Xo(w.timestamp||w.createdAt);return m[R]=m[R]||[],m[R].push(w),m},{});return O.jsxs(Z,{sx:{position:"fixed",top:150,right:20,width:430,height:600,display:"flex",flexDirection:"column",bgcolor:"#E8E0D5",borderRadius:2,boxShadow:3},children:[O.jsxs(Z,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between",bgcolor:"#085B53",p:1.5},children:[O.jsxs(Z,{display:"flex",alignItems:"center",children:[O.jsx(Ht,{sx:{bgcolor:"#4CAF50"},children:O.jsx(te,{icon:"heroicons:user-16-solid"})}),O.jsx(le,{ml:1,color:"white",fontWeight:"bold",children:e==null?void 0:e.first_name}),O.jsx(le,{ml:1,color:"white",fontSize:"small",children:t})]}),O.jsx(te,{icon:"bitcoin-icons:cross-filled",onClick:n,style:{color:"white",cursor:"pointer"}})]}),O.jsxs(Z,{flex:1,p:1,overflow:"auto",sx:{display:"flex",flexDirection:"column"},children:[Object.entries(E).map(([m,w])=>O.jsxs(Z,{children:[O.jsx(le,{align:"center",variant:"caption",color:"gray",children:m}),w.map(R=>O.jsx(Z,{sx:{bgcolor:l(R.sender)?"#DCF7C5":"#FFFFFF",alignSelf:l(R.sender)?"flex-end":"flex-start",maxWidth:"75%",p:1.2,borderRadius:2,my:.5,wordWrap:"break-word"},children:Qo(R)},R.messageSid))]},m)),O.jsx("div",{ref:_})]}),O.jsxs(Z,{display:"flex",alignItems:"center",p:1,bgcolor:"#F0F0F0",children:[O.jsx(ut,{value:o,onChange:m=>a(m.target.value),fullWidth:!0,size:"small",placeholder:"Type a message...",variant:"outlined"}),O.jsx(Te,{onClick:()=>y.current.click(),children:O.jsx(te,{icon:"mdi:attachment-vertical"})}),O.jsx(Yo,{type:"file",ref:y,onChange:m=>d(m.target.files[0])}),O.jsx(Te,{onClick:C,children:O.jsx(te,{icon:"tabler:send"})})]}),O.jsx(pr,{open:u,autoHideDuration:6e3,children:O.jsx(fr,{severity:"error",onClose:()=>p(!1),children:"Error sending message"})})]})},Zo=cr("input")({clip:"rect(0 0 0 0)",clipPath:"inset(50%)",height:1,overflow:"hidden",position:"absolute",bottom:0,left:0,whiteSpace:"nowrap",width:1}),Nn=["+447400270472","+15078586181"];function Oc({onClose:n,isLoading:t,candidateNumber:e,candidate:r}){var R,h,j,x,I,T,P,c,F,k;const[i,o]=z.useState([]),[a,s]=z.useState(null),[d,u]=z.useState(!1),[p,f]=z.useState(""),[v,y]=z.useState(!1),[_,b]=z.useState(""),S=z.useRef(null),g=z.useRef(null),l=z.useRef(null),C=()=>{var M;return(M=g.current)==null?void 0:M.scrollIntoView({behavior:"smooth"})},E=async()=>{try{const A=await(await fetch(`${Be.getSmsMessagesByPhoneNumber.replace(":phoneNumber",e)}`)).json();o(A.map(L=>{var D;return{...L,sender:L.from,text:L.body,msgType:L.mediaUrl?L.mediaContentType&&L.mediaContentType.startsWith("image")?"image":"document":"text",mediaUrl:L.mediaUrl,mediaType:L.mediaContentType,filename:L.mediaUrl?((D=L.body)==null?void 0:D.replace("File: ",""))||"Open file":void 0,timestamp:L.createdAt}})),C()}catch{u(!0)}};z.useEffect(()=>{E()},[e]),z.useEffect(()=>{const M=We("https://x74v19wh-5001.inc1.devtunnels.ms",{transports:["websocket"]});return l.current=M,M.on("sms-message",A=>{o(L=>[...L,{...A,sender:A.from,timestamp:A.timestamp||new Date().toISOString()}]),C()}),M.on("message-status-updated",A=>{o(L=>L.map(D=>D.messageSid===A.messageSid?{...D,status:A.status}:D)),C()}),()=>M.disconnect()},[]);const m=async()=>{if(!(!p.trim()||v))try{const M={to:e,message:p.trim()},D={messageSid:(await(await fetch(Be.sendSmsMessage,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(M)})).json()).messageSid,from:Nn[0],to:e,text:p.trim(),status:"sent",msgType:"text",timestamp:new Date().toISOString(),sender:Nn[0]};o(N=>[...N,D]),f(""),C()}catch{u(!0)}},w=()=>{m()};return O.jsxs("div",{style:{position:"fixed",top:150,right:"20px",display:"flex",flexDirection:"column",width:"430px",height:"600px",margin:"0",borderRadius:"5px",left:"auto",overflow:"hidden",boxShadow:"0px 4px 12px rgba(0, 0, 0, 0.30)",zIndex:1300},children:[O.jsxs("div",{style:{display:"flex",backgroundColor:"#1a84de",justifyContent:"space-between",alignItems:"center",padding:"5px 15px",zIndex:1},children:[O.jsx(Z,{display:"flex",alignItems:"center",children:O.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[O.jsx(Ht,{style:{marginRight:"8px",backgroundColor:"",color:"#fff",width:"35px",height:"35px",fontSize:"16px",textAlign:"center",marginLeft:"7px"},src:yi}),O.jsx("p",{style:{fontSize:"16px",marginLeft:"7px",color:"white"},children:(r==null?void 0:r.first_name)+" "+e})]})}),O.jsx(Z,{display:"flex",justifyContent:"flex-end",children:O.jsx(te,{icon:"bitcoin-icons:cross-filled",style:{fontSize:"22px",cursor:"pointer",color:"white"},onClick:()=>n()})})]}),O.jsx("div",{style:{flex:1,padding:"10px",overflowY:"auto",display:"flex",flexDirection:"column",gap:"10px",backgroundColor:"white"},children:t?O.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:O.jsx(Tn,{thickness:2})}):O.jsxs(O.Fragment,{children:[i==null?void 0:i.map(M=>{var A,L,D,N,U,W,q,G;return O.jsxs("div",{style:{position:"relative",maxWidth:"90%",padding:"10px 15px 20px 15px",borderRadius:"8px",wordWrap:"break-word",backgroundColor:(M==null?void 0:M.sender)==="+447400270472"||(M==null?void 0:M.sender)==="+15078586181"?"#1a84de":"#FAFAFA",alignSelf:(M==null?void 0:M.sender)==="+447400270472"||(M==null?void 0:M.sender)==="+15078586181"?"flex-end":"flex-start",color:(M==null?void 0:M.sender)==="+447400270472"||(M==null?void 0:M.sender)==="+15078586181"?"white":"black",display:"flex",flexDirection:"column"},children:[M.msgType==="text"||M.msgType==="button"?O.jsx(O.Fragment,{children:O.jsx("p",{className:"chat-text",style:{margin:0},children:M==null?void 0:M.text})}):M.msgType==="document"||M.msgType==="image"?O.jsxs("a",{href:M==null?void 0:M.mediaUrl,target:"_blank",rel:"noopener noreferrer",style:{display:"flex",alignItems:"center",marginTop:"5px"},children:[(A=M==null?void 0:M.mediaType)!=null&&A.endsWith("/pdf")?O.jsx(te,{icon:"vscode-icons:file-type-pdf2",style:{fontSize:"22px",color:"#f82028",marginRight:"10px"}}):(L=M==null?void 0:M.mediaType)!=null&&L.endsWith("/msword")||(D=M==null?void 0:M.mediaType)!=null&&D.endsWith("/vnd.openxmlformats-officedocument.wordprocessingml.document")?O.jsx(te,{icon:"vscode-icons:file-type-word",style:{fontSize:"22px",marginRight:"7px"}}):(N=M==null?void 0:M.mediaType)!=null&&N.endsWith("/csv")||(U=M==null?void 0:M.mediaType)!=null&&U.endsWith("/xlsx")?O.jsx(te,{icon:"vscode-icons:file-type-excel",style:{fontSize:"22px",marginRight:"10px"}}):(W=M==null?void 0:M.mediaType)!=null&&W.endsWith("/jpg")||(q=M==null?void 0:M.mediaType)!=null&&q.endsWith("/jpeg")||(G=M==null?void 0:M.mediaType)!=null&&G.endsWith("/png")?O.jsx(te,{icon:"ic:outline-image",style:{fontSize:"24px",color:"#FFA500",marginRight:"10px"}}):O.jsx(te,{icon:"line-md:file",style:{fontSize:"22px",color:"#000",marginRight:"10px"}}),O.jsx(Z,{sx:{flexGrow:1},children:O.jsx("p",{style:{fontSize:"14px",fontWeight:"bold",whiteSpace:"normal",overflow:"visible",textOverflow:"unset",wordWrap:"break-word",margin:0},children:(M==null?void 0:M.filename)||"Open file"})})]}):null,O.jsx("div",{style:{display:"flex",justifyContent:"flex-end",alignItems:"center",fontSize:"12px",color:(M==null?void 0:M.sender)==="+447400270472"||(M==null?void 0:M.sender)==="+15078586181"?"white":"gray",marginTop:"5px",width:"100%"},children:O.jsx("span",{style:{marginLeft:"auto"},children:new Date(M.timestamp).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit",hour12:!0})})})]},M.messageSid)}),O.jsx("div",{ref:g})," "]})}),a&&O.jsx("div",{className:"file-preview",children:O.jsxs(Z,{sx:{display:"flex",alignItems:"center",backgroundColor:"#F5F5F5",padding:"15px",maxWidth:"450px",borderRadius:"8px",boxShadow:"0px 2px 5px rgba(0,0,0,0.1)"},children:[O.jsx(Z,{sx:{display:"flex",alignItems:"center",justifyContent:"center",width:"40px",height:"40px",backgroundColor:"#E0E0E0",borderRadius:"50%",marginRight:"10px"},children:(R=a==null?void 0:a.name)!=null&&R.endsWith(".pdf")?O.jsx(te,{icon:"vscode-icons:file-type-pdf2",style:{fontSize:"24px",color:"#f82028"}}):(h=a==null?void 0:a.name)!=null&&h.endsWith(".doc")||(j=a==null?void 0:a.name)!=null&&j.endsWith(".docx")?O.jsx(te,{icon:"vscode-icons:file-type-word",style:{fontSize:"24px"}}):(x=a==null?void 0:a.name)!=null&&x.endsWith(".xls")||(I=a==null?void 0:a.name)!=null&&I.endsWith(".xlsx")?O.jsx(te,{icon:"vscode-icons:file-type-excel",style:{fontSize:"24px"}}):(T=a==null?void 0:a.name)!=null&&T.endsWith(".jpg")||(P=a==null?void 0:a.name)!=null&&P.endsWith(".jpeg")||(c=a==null?void 0:a.name)!=null&&c.endsWith(".png")?O.jsx(te,{icon:"ic:outline-image",style:{fontSize:"24px",color:"#FFA500"}}):O.jsx(te,{icon:"line-md:file",style:{fontSize:"24px",color:"#000"}})}),O.jsxs(Z,{sx:{flexGrow:1},children:[O.jsx(le,{variant:"body1",sx:{fontWeight:"bold",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},children:O.jsxs(Z,{component:"span",display:"flex",alignItems:"center",children:[((F=a==null?void 0:a.name)==null?void 0:F.length)>50?`${(k=a==null?void 0:a.name)==null?void 0:k.slice(0,50)}...`:a==null?void 0:a.name," "]})}),O.jsxs(Z,{display:"flex",alignItems:"center",children:[O.jsx(le,{variant:"body2",color:"textSecondary",children:a!=null&&a.size?`${(a.size/1024).toFixed(2)} KB`:"Unknown size"}),v&&O.jsx(O.Fragment,{children:O.jsx("p",{style:{marginLeft:"20px",display:"flex",alignItems:"center"},children:O.jsx(Tn,{size:20,color:"primary"})})}),_=="Uploading file..."&&O.jsx(O.Fragment,{children:O.jsxs("p",{style:{marginLeft:"20px",display:"flex",alignItems:"center"},children:[O.jsx(te,{icon:"line-md:uploading-loop",style:{color:"green",fontSize:"20px"}}),O.jsx("span",{style:{fontSize:"12px",color:"green",fontWeight:"normal",marginLeft:"5px"},children:_})]})}),_=="Preparing to send..."&&O.jsx(O.Fragment,{children:O.jsxs("p",{style:{marginLeft:"20px",display:"flex",alignItems:"center"},children:[O.jsx(te,{icon:"lets-icons:done-ring-round-duotone-line",style:{color:"green",fontSize:"20px"}}),O.jsx("span",{style:{fontSize:"12px",color:"green",fontWeight:"normal",marginLeft:"5px"},children:_})]})})]})]})]})}),d&&O.jsx(pr,{open:d,autoHideDuration:6e3,anchorOrigin:{vertical:"bottom",horizontal:"right"},children:O.jsxs(fr,{onClose:()=>u(!1),severity:"error",children:[" ","Message not sent"," "]})}),O.jsx("div",{style:{display:"flex",backgroundColor:"#f2f5fa"},children:O.jsxs("div",{style:{display:"flex",marginBottom:"10px",backgroundColor:"#f2f5fa",flexGrow:1,alignItems:"center",padding:"5px 10px",borderTop:"1px solid #E0E0E0",gap:"5px"},children:[O.jsx(ut,{placeholder:"Write a message...",fullWidth:!0,disabled:a,value:p,onKeyPress:M=>{M.key==="Enter"&&w()},onChange:M=>f(M.target.value),sx:{borderRadius:"25px",textAlign:"center","& .MuiOutlinedInput-root":{"& fieldset":{border:"none"},"&:hover fieldset":{border:"none"},"&.Mui-focused fieldset":{border:"none"}},"& .MuiOutlinedInput-input":{padding:"12px 14px"}},size:"small"}),O.jsx(Te,{sx:{minWidth:"unset",padding:0,marginLeft:"2px",borderRadius:"50%",backgroundColor:"transparent","&:hover":{backgroundColor:"transparent !important"},"&:focus":{backgroundColor:"transparent",boxShadow:"none"}},onClick:()=>S.current.click(),children:O.jsx(te,{icon:"fa-regular:smile-beam",style:{fontSize:"22px",color:"grey"}})}),O.jsxs(Te,{sx:{minWidth:"unset",padding:0,marginLeft:"2px",borderRadius:"50%",backgroundColor:"transparent","&:hover":{backgroundColor:"transparent !important"},"&:focus":{backgroundColor:"transparent",boxShadow:"none"}},onClick:()=>S.current.click(),children:[O.jsx(te,{icon:"mdi:attachment-vertical",style:{fontSize:"24px",color:"grey"}}),O.jsx(Zo,{type:"file",ref:S,onChange:M=>{s(M.target.files[0])}})]}),O.jsx(Te,{sx:{minWidth:"unset",padding:0,marginLeft:"2px",borderRadius:"50%",backgroundColor:"transparent","&:hover":{backgroundColor:"transparent !important"},"&:focus":{backgroundColor:"transparent",boxShadow:"none"}},onClick:m,children:O.jsx(te,{icon:"ic:outline-send",style:{fontSize:"24px",color:"grey"}})})]})})]})}function Mc({open:n,onClose:t,onSubmit:e}){const r=[...Ci,...Ei,...Ti],[i,o]=z.useState(null);return O.jsx("div",{children:O.jsxs(qt,{open:n,onClose:t,PaperProps:{sx:{ml:"auto",mt:"auto",mb:"auto",mr:"auto",width:"100%",height:"380px",maxWidth:"700px",maxHeight:"500px",borderRadius:0}},BackdropProps:{sx:{backgroundColor:"rgba(0, 0, 0, 0.5)"}},children:[O.jsx(ur,{sx:{backgroundColor:"#1a84de",padding:"25px 25px"},children:O.jsx(Z,{display:"flex",justifyContent:"space-between",children:O.jsx(le,{variant:"h5",sx:{color:"#fff",fontWeight:"bold"},children:"Update Response"})})}),O.jsxs(lr,{children:[O.jsx("br",{}),O.jsx(dr,{id:"status",fullWidth:!0,value:i,options:r,groupBy:a=>a.group,getOptionLabel:a=>a.label,onChange:(a,s)=>{o(s)},renderInput:a=>O.jsx(ut,{size:"large",...a,placeholder:"Select candidate status",variant:"outlined",required:!0,InputLabelProps:{shrink:!0},sx:{padding:"10px 0px",marginTop:"40px","& .MuiOutlinedInput-root":{borderRadius:"5px","& fieldset":{borderColor:"#dfe1e5"},"&:hover fieldset":{borderColor:"#dfe1e5"},"&.Mui-focused fieldset":{borderColor:"#dfe1e5"}}}})}),O.jsx(Z,{display:"flex",justifyContent:"center",marginTop:"2rem",children:O.jsx(Te,{sx:{background:"#1a84de",color:"#fff",height:"45px",fontWeight:"bold",border:"1px solid #1a84de","&:hover":{background:"#1a84de",color:"#fff"},borderRadius:"5px",padding:"25px",marginLeft:"20px"},onClick:()=>{e({response:i==null?void 0:i.id,group:i?i.group:null}),t()},children:"Submit Response"})}),O.jsx(Z,{display:"flex",alignItems:"center",justifyContent:"center",marginTop:"1rem",children:O.jsxs("p",{style:{color:"red",fontSize:"14px",display:"flex",alignItems:"center",gap:"10px"},children:[O.jsx(te,{icon:"ri:alert-line",width:"18",height:"18"})," Dialog will be auto-closed when you submit response."]})})]})]})})}function jc({open:n,onClose:t,onSubmit:e}){const r=[...xi,...Pi],[i,o]=z.useState();return O.jsx("div",{children:O.jsxs(qt,{open:n,onClose:t,PaperProps:{sx:{ml:"auto",mt:"auto",mb:"auto",mr:"auto",width:"100%",height:"380px",maxWidth:"700px",maxHeight:"500px",borderRadius:0}},BackdropProps:{sx:{backgroundColor:"rgba(0, 0, 0, 0.5)"}},children:[O.jsx(ur,{sx:{backgroundColor:"#1a84de",padding:"25px 25px"},children:O.jsx(Z,{display:"flex",justifyContent:"space-between",children:O.jsx(le,{variant:"h5",sx:{color:"#fff",fontWeight:"bold"},children:"Update Response"})})}),O.jsxs(lr,{children:[O.jsx("br",{}),O.jsx(dr,{id:"status",fullWidth:!0,value:i,options:r,groupBy:a=>a.group,getOptionLabel:a=>a.label,onChange:(a,s)=>{o(s)},renderInput:a=>O.jsx(ut,{size:"large",...a,placeholder:"Select candidate status",variant:"outlined",required:!0,InputLabelProps:{shrink:!0},sx:{padding:"10px 0px",marginTop:"40px","& .MuiOutlinedInput-root":{borderRadius:"5px","& fieldset":{borderColor:"#dfe1e5"},"&:hover fieldset":{borderColor:"#dfe1e5"},"&.Mui-focused fieldset":{borderColor:"#dfe1e5"}}}})}),O.jsx(Z,{display:"flex",justifyContent:"center",marginTop:"2rem",children:O.jsx(Te,{sx:{background:"#1a84de",color:"#fff",height:"45px",fontWeight:"bold",border:"1px solid #1a84de","&:hover":{background:"#1a84de",color:"#fff"},borderRadius:"5px",padding:"25px",marginLeft:"20px"},onClick:()=>{e({response:i==null?void 0:i.id,group:"screening_stage"}),t()},children:"Submit Response"})}),O.jsx(Z,{display:"flex",alignItems:"center",justifyContent:"center",marginTop:"1rem",children:O.jsxs("p",{style:{color:"red",fontSize:"14px",display:"flex",alignItems:"center",gap:"10px"},children:[O.jsx(te,{icon:"ri:alert-line",width:"18",height:"18"})," Dialog will be auto-closed when you submit response."]})})]})]})})}var kr={},$e={},Yt={exports:{}},Me=typeof Reflect=="object"?Reflect:null,Un=Me&&typeof Me.apply=="function"?Me.apply:function(t,e,r){return Function.prototype.apply.call(t,e,r)},nt;Me&&typeof Me.ownKeys=="function"?nt=Me.ownKeys:Object.getOwnPropertySymbols?nt=function(t){return Object.getOwnPropertyNames(t).concat(Object.getOwnPropertySymbols(t))}:nt=function(t){return Object.getOwnPropertyNames(t)};function es(n){console&&console.warn&&console.warn(n)}var Ir=Number.isNaN||function(t){return t!==t};function X(){X.init.call(this)}Yt.exports=X;Yt.exports.once=is;X.EventEmitter=X;X.prototype._events=void 0;X.prototype._eventsCount=0;X.prototype._maxListeners=void 0;var Fn=10;function ft(n){if(typeof n!="function")throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof n)}Object.defineProperty(X,"defaultMaxListeners",{enumerable:!0,get:function(){return Fn},set:function(n){if(typeof n!="number"||n<0||Ir(n))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+n+".");Fn=n}});X.init=function(){(this._events===void 0||this._events===Object.getPrototypeOf(this)._events)&&(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0};X.prototype.setMaxListeners=function(t){if(typeof t!="number"||t<0||Ir(t))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+t+".");return this._maxListeners=t,this};function Rr(n){return n._maxListeners===void 0?X.defaultMaxListeners:n._maxListeners}X.prototype.getMaxListeners=function(){return Rr(this)};X.prototype.emit=function(t){for(var e=[],r=1;r<arguments.length;r++)e.push(arguments[r]);var i=t==="error",o=this._events;if(o!==void 0)i=i&&o.error===void 0;else if(!i)return!1;if(i){var a;if(e.length>0&&(a=e[0]),a instanceof Error)throw a;var s=new Error("Unhandled error."+(a?" ("+a.message+")":""));throw s.context=a,s}var d=o[t];if(d===void 0)return!1;if(typeof d=="function")Un(d,this,e);else for(var u=d.length,p=jr(d,u),r=0;r<u;++r)Un(p[r],this,e);return!0};function Ar(n,t,e,r){var i,o,a;if(ft(e),o=n._events,o===void 0?(o=n._events=Object.create(null),n._eventsCount=0):(o.newListener!==void 0&&(n.emit("newListener",t,e.listener?e.listener:e),o=n._events),a=o[t]),a===void 0)a=o[t]=e,++n._eventsCount;else if(typeof a=="function"?a=o[t]=r?[e,a]:[a,e]:r?a.unshift(e):a.push(e),i=Rr(n),i>0&&a.length>i&&!a.warned){a.warned=!0;var s=new Error("Possible EventEmitter memory leak detected. "+a.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");s.name="MaxListenersExceededWarning",s.emitter=n,s.type=t,s.count=a.length,es(s)}return n}X.prototype.addListener=function(t,e){return Ar(this,t,e,!1)};X.prototype.on=X.prototype.addListener;X.prototype.prependListener=function(t,e){return Ar(this,t,e,!0)};function ts(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,arguments.length===0?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function Dr(n,t,e){var r={fired:!1,wrapFn:void 0,target:n,type:t,listener:e},i=ts.bind(r);return i.listener=e,r.wrapFn=i,i}X.prototype.once=function(t,e){return ft(e),this.on(t,Dr(this,t,e)),this};X.prototype.prependOnceListener=function(t,e){return ft(e),this.prependListener(t,Dr(this,t,e)),this};X.prototype.removeListener=function(t,e){var r,i,o,a,s;if(ft(e),i=this._events,i===void 0)return this;if(r=i[t],r===void 0)return this;if(r===e||r.listener===e)--this._eventsCount===0?this._events=Object.create(null):(delete i[t],i.removeListener&&this.emit("removeListener",t,r.listener||e));else if(typeof r!="function"){for(o=-1,a=r.length-1;a>=0;a--)if(r[a]===e||r[a].listener===e){s=r[a].listener,o=a;break}if(o<0)return this;o===0?r.shift():ns(r,o),r.length===1&&(i[t]=r[0]),i.removeListener!==void 0&&this.emit("removeListener",t,s||e)}return this};X.prototype.off=X.prototype.removeListener;X.prototype.removeAllListeners=function(t){var e,r,i;if(r=this._events,r===void 0)return this;if(r.removeListener===void 0)return arguments.length===0?(this._events=Object.create(null),this._eventsCount=0):r[t]!==void 0&&(--this._eventsCount===0?this._events=Object.create(null):delete r[t]),this;if(arguments.length===0){var o=Object.keys(r),a;for(i=0;i<o.length;++i)a=o[i],a!=="removeListener"&&this.removeAllListeners(a);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if(e=r[t],typeof e=="function")this.removeListener(t,e);else if(e!==void 0)for(i=e.length-1;i>=0;i--)this.removeListener(t,e[i]);return this};function Or(n,t,e){var r=n._events;if(r===void 0)return[];var i=r[t];return i===void 0?[]:typeof i=="function"?e?[i.listener||i]:[i]:e?rs(i):jr(i,i.length)}X.prototype.listeners=function(t){return Or(this,t,!0)};X.prototype.rawListeners=function(t){return Or(this,t,!1)};X.listenerCount=function(n,t){return typeof n.listenerCount=="function"?n.listenerCount(t):Mr.call(n,t)};X.prototype.listenerCount=Mr;function Mr(n){var t=this._events;if(t!==void 0){var e=t[n];if(typeof e=="function")return 1;if(e!==void 0)return e.length}return 0}X.prototype.eventNames=function(){return this._eventsCount>0?nt(this._events):[]};function jr(n,t){for(var e=new Array(t),r=0;r<t;++r)e[r]=n[r];return e}function ns(n,t){for(;t+1<n.length;t++)n[t]=n[t+1];n.pop()}function rs(n){for(var t=new Array(n.length),e=0;e<t.length;++e)t[e]=n[e].listener||n[e];return t}function is(n,t){return new Promise(function(e,r){function i(a){n.removeListener(t,o),r(a)}function o(){typeof n.removeListener=="function"&&n.removeListener("error",i),e([].slice.call(arguments))}Lr(n,t,o,{once:!0}),t!=="error"&&os(n,i,{once:!0})})}function os(n,t,e){typeof n.on=="function"&&Lr(n,"error",t,e)}function Lr(n,t,e,r){if(typeof n.on=="function")r.once?n.once(t,e):n.on(t,e);else if(typeof n.addEventListener=="function")n.addEventListener(t,function i(o){r.once&&n.removeEventListener(t,i),e(o)});else throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof n)}var ge=Yt.exports,pt={},ss=B&&B.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var o in i)Object.prototype.hasOwnProperty.call(i,o)&&(r[o]=i[o])},n(t,e)};return function(t,e){n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}();Object.defineProperty(pt,"__esModule",{value:!0});var as=ge,cs=function(n){ss(t,n);function t(e){var r=n.call(this)||this;return Object.defineProperties(r,{_attempts:{value:0,writable:!0},_duration:{enumerable:!1,get:function(){var i=this._min*Math.pow(this._factor,this._attempts);if(this._jitter){var o=Math.random(),a=Math.floor(o*this._jitter*i);i=Math.floor(o*10)&1?i+a:i-a}return Math.min(i,this._max)|0}},_factor:{value:e.factor||2},_jitter:{value:e.jitter>0&&e.jitter<=1?e.jitter:0},_max:{value:e.max||1e4},_min:{value:e.min||100},_timeoutID:{value:null,writable:!0}}),r}return t.prototype.backoff=function(){var e=this,r=this._duration;this._timeoutID&&(clearTimeout(this._timeoutID),this._timeoutID=null),this.emit("backoff",this._attempts,r),this._timeoutID=setTimeout(function(){e.emit("ready",e._attempts,r),e._attempts++},r)},t.prototype.reset=function(){this._attempts=0,this._timeoutID&&(clearTimeout(this._timeoutID),this._timeoutID=null)},t}(as.EventEmitter);pt.default=cs;var Je={},Nr={exports:{}};(function(n){(function(t,e){n.exports?n.exports=e():t.log=e()})(B,function(){var t=function(){},e="undefined",r=typeof window!==e&&typeof window.navigator!==e&&/Trident\/|MSIE /.test(window.navigator.userAgent),i=["trace","debug","info","warn","error"];function o(b,S){var g=b[S];if(typeof g.bind=="function")return g.bind(b);try{return Function.prototype.bind.call(g,b)}catch{return function(){return Function.prototype.apply.apply(g,[b,arguments])}}}function a(){console.log&&(console.log.apply?console.log.apply(console,arguments):Function.prototype.apply.apply(console.log,[console,arguments])),console.trace&&console.trace()}function s(b){return b==="debug"&&(b="log"),typeof console===e?!1:b==="trace"&&r?a:console[b]!==void 0?o(console,b):console.log!==void 0?o(console,"log"):t}function d(b,S){for(var g=0;g<i.length;g++){var l=i[g];this[l]=g<b?t:this.methodFactory(l,b,S)}this.log=this.debug}function u(b,S,g){return function(){typeof console!==e&&(d.call(this,S,g),this[b].apply(this,arguments))}}function p(b,S,g){return s(b)||u.apply(this,arguments)}function f(b,S,g){var l=this,C,E="loglevel";b&&(E+=":"+b);function m(h){var j=(i[h]||"silent").toUpperCase();if(typeof window!==e){try{window.localStorage[E]=j;return}catch{}try{window.document.cookie=encodeURIComponent(E)+"="+j+";"}catch{}}}function w(){var h;if(typeof window!==e){try{h=window.localStorage[E]}catch{}if(typeof h===e)try{var j=window.document.cookie,x=j.indexOf(encodeURIComponent(E)+"=");x!==-1&&(h=/^([^;]+)/.exec(j.slice(x))[1])}catch{}return l.levels[h]===void 0&&(h=void 0),h}}l.name=b,l.levels={TRACE:0,DEBUG:1,INFO:2,WARN:3,ERROR:4,SILENT:5},l.methodFactory=g||p,l.getLevel=function(){return C},l.setLevel=function(h,j){if(typeof h=="string"&&l.levels[h.toUpperCase()]!==void 0&&(h=l.levels[h.toUpperCase()]),typeof h=="number"&&h>=0&&h<=l.levels.SILENT){if(C=h,j!==!1&&m(h),d.call(l,h,b),typeof console===e&&h<l.levels.SILENT)return"No console available for logging"}else throw"log.setLevel() called with invalid level: "+h},l.setDefaultLevel=function(h){w()||l.setLevel(h,!1)},l.enableAll=function(h){l.setLevel(l.levels.TRACE,h)},l.disableAll=function(h){l.setLevel(l.levels.SILENT,h)};var R=w();R==null&&(R=S??"WARN"),l.setLevel(R,!1)}var v=new f,y={};v.getLogger=function(S){if(typeof S!="string"||S==="")throw new TypeError("You must supply a name when creating a logger.");var g=y[S];return g||(g=y[S]=new f(S,v.getLevel(),v.methodFactory)),g};var _=typeof window!==e?window.log:void 0;return v.noConflict=function(){return typeof window!==e&&window.log===v&&(window.log=_),v},v.getLoggers=function(){return y},v})})(Nr);var Ur=Nr.exports,Ke={},oe={},Fr={},Xt={},us=B&&B.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var o in i)Object.prototype.hasOwnProperty.call(i,o)&&(r[o]=i[o])},n(t,e)};return function(t,e){n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}();Object.defineProperty(Xt,"__esModule",{value:!0});var ls=function(n){us(t,n);function t(e,r){var i=n.call(this)||this;Object.setPrototypeOf(i,t.prototype);var o=typeof e=="string"?e:i.explanation,a=typeof e=="object"?e:r;return i.message=i.name+" ("+i.code+"): "+o,i.originalError=a,i}return t}(Error);Xt.default=ls;(function(n){var t=B&&B.__extends||function(){var v=function(y,_){return v=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(b,S){b.__proto__=S}||function(b,S){for(var g in S)Object.prototype.hasOwnProperty.call(S,g)&&(b[g]=S[g])},v(y,_)};return function(y,_){v(y,_);function b(){this.constructor=y}y.prototype=_===null?Object.create(_):(b.prototype=_.prototype,new b)}}();Object.defineProperty(n,"__esModule",{value:!0}),n.errorsByCode=n.MediaErrors=n.SignalingErrors=n.UserMediaErrors=n.MalformedRequestErrors=n.GeneralErrors=n.SIPServerErrors=n.ClientErrors=n.SignatureValidationErrors=n.AuthorizationErrors=n.TwilioError=void 0;var e=Xt;n.TwilioError=e.default;var r;(function(v){var y=function(S){t(g,S);function g(l,C){var E=S.call(this,l,C)||this;E.causes=[],E.code=20101,E.description="Invalid access token",E.explanation="Twilio was unable to validate your Access Token",E.name="AccessTokenInvalid",E.solutions=[],Object.setPrototypeOf(E,v.AccessTokenInvalid.prototype);var m=typeof l=="string"?l:E.explanation,w=typeof l=="object"?l:C;return E.message=E.name+" ("+E.code+"): "+m,E.originalError=w,E}return g}(e.default);v.AccessTokenInvalid=y;var _=function(S){t(g,S);function g(l,C){var E=S.call(this,l,C)||this;E.causes=[],E.code=20104,E.description="Access token expired or expiration date invalid",E.explanation="The Access Token provided to the Twilio API has expired, the expiration time specified in the token was invalid, or the expiration time specified was too far in the future",E.name="AccessTokenExpired",E.solutions=[],Object.setPrototypeOf(E,v.AccessTokenExpired.prototype);var m=typeof l=="string"?l:E.explanation,w=typeof l=="object"?l:C;return E.message=E.name+" ("+E.code+"): "+m,E.originalError=w,E}return g}(e.default);v.AccessTokenExpired=_;var b=function(S){t(g,S);function g(l,C){var E=S.call(this,l,C)||this;E.causes=[],E.code=20151,E.description="Authentication Failed",E.explanation="The Authentication with the provided JWT failed",E.name="AuthenticationFailed",E.solutions=[],Object.setPrototypeOf(E,v.AuthenticationFailed.prototype);var m=typeof l=="string"?l:E.explanation,w=typeof l=="object"?l:C;return E.message=E.name+" ("+E.code+"): "+m,E.originalError=w,E}return g}(e.default);v.AuthenticationFailed=b})(r=n.AuthorizationErrors||(n.AuthorizationErrors={}));var i;(function(v){var y=function(_){t(b,_);function b(S,g){var l=_.call(this,S,g)||this;l.causes=["The access token has an invalid Account SID, API Key, or API Key Secret."],l.code=31202,l.description="Signature validation failed.",l.explanation="The provided access token failed signature validation.",l.name="AccessTokenSignatureValidationFailed",l.solutions=["Ensure the Account SID, API Key, and API Key Secret are valid when generating your access token."],Object.setPrototypeOf(l,v.AccessTokenSignatureValidationFailed.prototype);var C=typeof S=="string"?S:l.explanation,E=typeof S=="object"?S:g;return l.message=l.name+" ("+l.code+"): "+C,l.originalError=E,l}return b}(e.default);v.AccessTokenSignatureValidationFailed=y})(i=n.SignatureValidationErrors||(n.SignatureValidationErrors={}));var o;(function(v){var y=function(g){t(l,g);function l(C,E){var m=g.call(this,C,E)||this;m.causes=[],m.code=31400,m.description="Bad Request (HTTP/SIP)",m.explanation="The request could not be understood due to malformed syntax.",m.name="BadRequest",m.solutions=[],Object.setPrototypeOf(m,v.BadRequest.prototype);var w=typeof C=="string"?C:m.explanation,R=typeof C=="object"?C:E;return m.message=m.name+" ("+m.code+"): "+w,m.originalError=R,m}return l}(e.default);v.BadRequest=y;var _=function(g){t(l,g);function l(C,E){var m=g.call(this,C,E)||this;m.causes=["The outbound call was made to an invalid phone number.","The TwiML application sid is missing a Voice URL."],m.code=31404,m.description="Not Found (HTTP/SIP)",m.explanation="The server has not found anything matching the request.",m.name="NotFound",m.solutions=["Ensure the phone number dialed is valid.","Ensure the TwiML application is configured correctly with a Voice URL link."],Object.setPrototypeOf(m,v.NotFound.prototype);var w=typeof C=="string"?C:m.explanation,R=typeof C=="object"?C:E;return m.message=m.name+" ("+m.code+"): "+w,m.originalError=R,m}return l}(e.default);v.NotFound=_;var b=function(g){t(l,g);function l(C,E){var m=g.call(this,C,E)||this;m.causes=[],m.code=31480,m.description="Temporarily Unavailable (SIP)",m.explanation="The callee is currently unavailable.",m.name="TemporarilyUnavailable",m.solutions=[],Object.setPrototypeOf(m,v.TemporarilyUnavailable.prototype);var w=typeof C=="string"?C:m.explanation,R=typeof C=="object"?C:E;return m.message=m.name+" ("+m.code+"): "+w,m.originalError=R,m}return l}(e.default);v.TemporarilyUnavailable=b;var S=function(g){t(l,g);function l(C,E){var m=g.call(this,C,E)||this;m.causes=[],m.code=31486,m.description="Busy Here (SIP)",m.explanation="The callee is busy.",m.name="BusyHere",m.solutions=[],Object.setPrototypeOf(m,v.BusyHere.prototype);var w=typeof C=="string"?C:m.explanation,R=typeof C=="object"?C:E;return m.message=m.name+" ("+m.code+"): "+w,m.originalError=R,m}return l}(e.default);v.BusyHere=S})(o=n.ClientErrors||(n.ClientErrors={}));var a;(function(v){var y=function(_){t(b,_);function b(S,g){var l=_.call(this,S,g)||this;l.causes=[],l.code=31603,l.description="Decline (SIP)",l.explanation="The callee does not wish to participate in the call.",l.name="Decline",l.solutions=[],Object.setPrototypeOf(l,v.Decline.prototype);var C=typeof S=="string"?S:l.explanation,E=typeof S=="object"?S:g;return l.message=l.name+" ("+l.code+"): "+C,l.originalError=E,l}return b}(e.default);v.Decline=y})(a=n.SIPServerErrors||(n.SIPServerErrors={}));var s;(function(v){var y=function(E){t(m,E);function m(w,R){var h=E.call(this,w,R)||this;h.causes=[],h.code=31e3,h.description="Unknown Error",h.explanation="An unknown error has occurred. See error details for more information.",h.name="UnknownError",h.solutions=[],Object.setPrototypeOf(h,v.UnknownError.prototype);var j=typeof w=="string"?w:h.explanation,x=typeof w=="object"?w:R;return h.message=h.name+" ("+h.code+"): "+j,h.originalError=x,h}return m}(e.default);v.UnknownError=y;var _=function(E){t(m,E);function m(w,R){var h=E.call(this,w,R)||this;h.causes=[],h.code=31001,h.description="Application Not Found",h.explanation="",h.name="ApplicationNotFoundError",h.solutions=[],Object.setPrototypeOf(h,v.ApplicationNotFoundError.prototype);var j=typeof w=="string"?w:h.explanation,x=typeof w=="object"?w:R;return h.message=h.name+" ("+h.code+"): "+j,h.originalError=x,h}return m}(e.default);v.ApplicationNotFoundError=_;var b=function(E){t(m,E);function m(w,R){var h=E.call(this,w,R)||this;h.causes=[],h.code=31002,h.description="Connection Declined",h.explanation="",h.name="ConnectionDeclinedError",h.solutions=[],Object.setPrototypeOf(h,v.ConnectionDeclinedError.prototype);var j=typeof w=="string"?w:h.explanation,x=typeof w=="object"?w:R;return h.message=h.name+" ("+h.code+"): "+j,h.originalError=x,h}return m}(e.default);v.ConnectionDeclinedError=b;var S=function(E){t(m,E);function m(w,R){var h=E.call(this,w,R)||this;h.causes=[],h.code=31003,h.description="Connection Timeout",h.explanation="The server could not produce a response within a suitable amount of time.",h.name="ConnectionTimeoutError",h.solutions=[],Object.setPrototypeOf(h,v.ConnectionTimeoutError.prototype);var j=typeof w=="string"?w:h.explanation,x=typeof w=="object"?w:R;return h.message=h.name+" ("+h.code+"): "+j,h.originalError=x,h}return m}(e.default);v.ConnectionTimeoutError=S;var g=function(E){t(m,E);function m(w,R){var h=E.call(this,w,R)||this;h.causes=[],h.code=31005,h.description="Connection error",h.explanation="A connection error occurred during the call",h.name="ConnectionError",h.solutions=[],Object.setPrototypeOf(h,v.ConnectionError.prototype);var j=typeof w=="string"?w:h.explanation,x=typeof w=="object"?w:R;return h.message=h.name+" ("+h.code+"): "+j,h.originalError=x,h}return m}(e.default);v.ConnectionError=g;var l=function(E){t(m,E);function m(w,R){var h=E.call(this,w,R)||this;h.causes=["The incoming call was cancelled because it was not answered in time or it was accepted/rejected by another application instance registered with the same identity."],h.code=31008,h.description="Call cancelled",h.explanation="Unable to answer because the call has ended",h.name="CallCancelledError",h.solutions=[],Object.setPrototypeOf(h,v.CallCancelledError.prototype);var j=typeof w=="string"?w:h.explanation,x=typeof w=="object"?w:R;return h.message=h.name+" ("+h.code+"): "+j,h.originalError=x,h}return m}(e.default);v.CallCancelledError=l;var C=function(E){t(m,E);function m(w,R){var h=E.call(this,w,R)||this;h.causes=[],h.code=31009,h.description="Transport error",h.explanation="No transport available to send or receive messages",h.name="TransportError",h.solutions=[],Object.setPrototypeOf(h,v.TransportError.prototype);var j=typeof w=="string"?w:h.explanation,x=typeof w=="object"?w:R;return h.message=h.name+" ("+h.code+"): "+j,h.originalError=x,h}return m}(e.default);v.TransportError=C})(s=n.GeneralErrors||(n.GeneralErrors={}));var d;(function(v){var y=function(E){t(m,E);function m(w,R){var h=E.call(this,w,R)||this;h.causes=["Invalid content or MessageType passed to sendMessage method."],h.code=31100,h.description="The request had malformed syntax.",h.explanation="The request could not be understood due to malformed syntax.",h.name="MalformedRequestError",h.solutions=["Ensure content and MessageType passed to sendMessage method are valid."],Object.setPrototypeOf(h,v.MalformedRequestError.prototype);var j=typeof w=="string"?w:h.explanation,x=typeof w=="object"?w:R;return h.message=h.name+" ("+h.code+"): "+j,h.originalError=x,h}return m}(e.default);v.MalformedRequestError=y;var _=function(E){t(m,E);function m(w,R){var h=E.call(this,w,R)||this;h.causes=[],h.code=31101,h.description="Missing parameter array in request",h.explanation="",h.name="MissingParameterArrayError",h.solutions=[],Object.setPrototypeOf(h,v.MissingParameterArrayError.prototype);var j=typeof w=="string"?w:h.explanation,x=typeof w=="object"?w:R;return h.message=h.name+" ("+h.code+"): "+j,h.originalError=x,h}return m}(e.default);v.MissingParameterArrayError=_;var b=function(E){t(m,E);function m(w,R){var h=E.call(this,w,R)||this;h.causes=[],h.code=31102,h.description="Authorization token missing in request.",h.explanation="",h.name="AuthorizationTokenMissingError",h.solutions=[],Object.setPrototypeOf(h,v.AuthorizationTokenMissingError.prototype);var j=typeof w=="string"?w:h.explanation,x=typeof w=="object"?w:R;return h.message=h.name+" ("+h.code+"): "+j,h.originalError=x,h}return m}(e.default);v.AuthorizationTokenMissingError=b;var S=function(E){t(m,E);function m(w,R){var h=E.call(this,w,R)||this;h.causes=[],h.code=31103,h.description="Maximum parameter length has been exceeded.",h.explanation="Length of parameters cannot exceed MAX_PARAM_LENGTH.",h.name="MaxParameterLengthExceededError",h.solutions=[],Object.setPrototypeOf(h,v.MaxParameterLengthExceededError.prototype);var j=typeof w=="string"?w:h.explanation,x=typeof w=="object"?w:R;return h.message=h.name+" ("+h.code+"): "+j,h.originalError=x,h}return m}(e.default);v.MaxParameterLengthExceededError=S;var g=function(E){t(m,E);function m(w,R){var h=E.call(this,w,R)||this;h.causes=[],h.code=31104,h.description="Invalid bridge token",h.explanation="",h.name="InvalidBridgeTokenError",h.solutions=[],Object.setPrototypeOf(h,v.InvalidBridgeTokenError.prototype);var j=typeof w=="string"?w:h.explanation,x=typeof w=="object"?w:R;return h.message=h.name+" ("+h.code+"): "+j,h.originalError=x,h}return m}(e.default);v.InvalidBridgeTokenError=g;var l=function(E){t(m,E);function m(w,R){var h=E.call(this,w,R)||this;h.causes=["Client name contains invalid characters."],h.code=31105,h.description="Invalid client name",h.explanation="Client name should not contain control, space, delims, or unwise characters.",h.name="InvalidClientNameError",h.solutions=["Make sure that client name does not contain any of the invalid characters."],Object.setPrototypeOf(h,v.InvalidClientNameError.prototype);var j=typeof w=="string"?w:h.explanation,x=typeof w=="object"?w:R;return h.message=h.name+" ("+h.code+"): "+j,h.originalError=x,h}return m}(e.default);v.InvalidClientNameError=l;var C=function(E){t(m,E);function m(w,R){var h=E.call(this,w,R)||this;h.causes=[],h.code=31107,h.description="The reconnect parameter is invalid",h.explanation="",h.name="ReconnectParameterInvalidError",h.solutions=[],Object.setPrototypeOf(h,v.ReconnectParameterInvalidError.prototype);var j=typeof w=="string"?w:h.explanation,x=typeof w=="object"?w:R;return h.message=h.name+" ("+h.code+"): "+j,h.originalError=x,h}return m}(e.default);v.ReconnectParameterInvalidError=C})(d=n.MalformedRequestErrors||(n.MalformedRequestErrors={})),function(v){var y=function(w){t(R,w);function R(h,j){var x=w.call(this,h,j)||this;x.causes=[],x.code=31201,x.description="Authorization error",x.explanation="The request requires user authentication. The server understood the request, but is refusing to fulfill it.",x.name="AuthorizationError",x.solutions=[],Object.setPrototypeOf(x,v.AuthorizationError.prototype);var I=typeof h=="string"?h:x.explanation,T=typeof h=="object"?h:j;return x.message=x.name+" ("+x.code+"): "+I,x.originalError=T,x}return R}(e.default);v.AuthorizationError=y;var _=function(w){t(R,w);function R(h,j){var x=w.call(this,h,j)||this;x.causes=[],x.code=31203,x.description="No valid account",x.explanation="",x.name="NoValidAccountError",x.solutions=[],Object.setPrototypeOf(x,v.NoValidAccountError.prototype);var I=typeof h=="string"?h:x.explanation,T=typeof h=="object"?h:j;return x.message=x.name+" ("+x.code+"): "+I,x.originalError=T,x}return R}(e.default);v.NoValidAccountError=_;var b=function(w){t(R,w);function R(h,j){var x=w.call(this,h,j)||this;x.causes=[],x.code=31204,x.description="Invalid JWT token",x.explanation="",x.name="InvalidJWTTokenError",x.solutions=[],Object.setPrototypeOf(x,v.InvalidJWTTokenError.prototype);var I=typeof h=="string"?h:x.explanation,T=typeof h=="object"?h:j;return x.message=x.name+" ("+x.code+"): "+I,x.originalError=T,x}return R}(e.default);v.InvalidJWTTokenError=b;var S=function(w){t(R,w);function R(h,j){var x=w.call(this,h,j)||this;x.causes=[],x.code=31205,x.description="JWT token expired",x.explanation="",x.name="JWTTokenExpiredError",x.solutions=[],Object.setPrototypeOf(x,v.JWTTokenExpiredError.prototype);var I=typeof h=="string"?h:x.explanation,T=typeof h=="object"?h:j;return x.message=x.name+" ("+x.code+"): "+I,x.originalError=T,x}return R}(e.default);v.JWTTokenExpiredError=S;var g=function(w){t(R,w);function R(h,j){var x=w.call(this,h,j)||this;x.causes=["Rate limit exceeded."],x.code=31206,x.description="Rate exceeded authorized limit.",x.explanation="The request performed exceeds the authorized limit.",x.name="RateExceededError",x.solutions=["Ensure message send rate does not exceed authorized limits."],Object.setPrototypeOf(x,v.RateExceededError.prototype);var I=typeof h=="string"?h:x.explanation,T=typeof h=="object"?h:j;return x.message=x.name+" ("+x.code+"): "+I,x.originalError=T,x}return R}(e.default);v.RateExceededError=g;var l=function(w){t(R,w);function R(h,j){var x=w.call(this,h,j)||this;x.causes=[],x.code=31207,x.description="JWT token expiration too long",x.explanation="",x.name="JWTTokenExpirationTooLongError",x.solutions=[],Object.setPrototypeOf(x,v.JWTTokenExpirationTooLongError.prototype);var I=typeof h=="string"?h:x.explanation,T=typeof h=="object"?h:j;return x.message=x.name+" ("+x.code+"): "+I,x.originalError=T,x}return R}(e.default);v.JWTTokenExpirationTooLongError=l;var C=function(w){t(R,w);function R(h,j){var x=w.call(this,h,j)||this;x.causes=[],x.code=31209,x.description="Reconnect attempt is not authorized.",x.explanation="",x.name="ReconnectAttemptError",x.solutions=[],Object.setPrototypeOf(x,v.ReconnectAttemptError.prototype);var I=typeof h=="string"?h:x.explanation,T=typeof h=="object"?h:j;return x.message=x.name+" ("+x.code+"): "+I,x.originalError=T,x}return R}(e.default);v.ReconnectAttemptError=C;var E=function(w){t(R,w);function R(h,j){var x=w.call(this,h,j)||this;x.causes=["The Call Message Event Type is invalid and is not understood by Twilio Voice."],x.code=31210,x.description="Call Message Event Type is invalid.",x.explanation="The Call Message Event Type is invalid and is not understood by Twilio Voice.",x.name="CallMessageEventTypeInvalidError",x.solutions=["Ensure the Call Message Event Type is Valid and understood by Twilio Voice and try again."],Object.setPrototypeOf(x,v.CallMessageEventTypeInvalidError.prototype);var I=typeof h=="string"?h:x.explanation,T=typeof h=="object"?h:j;return x.message=x.name+" ("+x.code+"): "+I,x.originalError=T,x}return R}(e.default);v.CallMessageEventTypeInvalidError=E;var m=function(w){t(R,w);function R(h,j){var x=w.call(this,h,j)||this;x.causes=["The payload size of Call Message Event exceeds the authorized limit."],x.code=31212,x.description="Call Message Event Payload size exceeded authorized limit.",x.explanation="The request performed to send a Call Message Event exceeds the payload size authorized limit",x.name="PayloadSizeExceededError",x.solutions=["Reduce payload size of Call Message Event to be within the authorized limit and try again."],Object.setPrototypeOf(x,v.PayloadSizeExceededError.prototype);var I=typeof h=="string"?h:x.explanation,T=typeof h=="object"?h:j;return x.message=x.name+" ("+x.code+"): "+I,x.originalError=T,x}return R}(e.default);v.PayloadSizeExceededError=m}(r=n.AuthorizationErrors||(n.AuthorizationErrors={}));var u;(function(v){var y=function(b){t(S,b);function S(g,l){var C=b.call(this,g,l)||this;C.causes=["The user denied the getUserMedia request.","The browser denied the getUserMedia request."],C.code=31401,C.description="UserMedia Permission Denied Error",C.explanation="The browser or end-user denied permissions to user media. Therefore we were unable to acquire input audio.",C.name="PermissionDeniedError",C.solutions=["The user should accept the request next time prompted. If the browser saved the deny, the user should change that permission in their browser.","The user should to verify that the browser has permission to access the microphone at this address."],Object.setPrototypeOf(C,v.PermissionDeniedError.prototype);var E=typeof g=="string"?g:C.explanation,m=typeof g=="object"?g:l;return C.message=C.name+" ("+C.code+"): "+E,C.originalError=m,C}return S}(e.default);v.PermissionDeniedError=y;var _=function(b){t(S,b);function S(g,l){var C=b.call(this,g,l)||this;C.causes=["NotFoundError - The deviceID specified was not found.","The getUserMedia constraints were overconstrained and no devices matched."],C.code=31402,C.description="UserMedia Acquisition Failed Error",C.explanation="The browser and end-user allowed permissions, however getting the media failed. Usually this is due to bad constraints, but can sometimes fail due to browser, OS or hardware issues.",C.name="AcquisitionFailedError",C.solutions=["Ensure the deviceID being specified exists.","Try acquiring media with fewer constraints."],Object.setPrototypeOf(C,v.AcquisitionFailedError.prototype);var E=typeof g=="string"?g:C.explanation,m=typeof g=="object"?g:l;return C.message=C.name+" ("+C.code+"): "+E,C.originalError=m,C}return S}(e.default);v.AcquisitionFailedError=_})(u=n.UserMediaErrors||(n.UserMediaErrors={}));var p;(function(v){var y=function(b){t(S,b);function S(g,l){var C=b.call(this,g,l)||this;C.causes=[],C.code=53e3,C.description="Signaling connection error",C.explanation="Raised whenever a signaling connection error occurs that is not covered by a more specific error code.",C.name="ConnectionError",C.solutions=[],Object.setPrototypeOf(C,v.ConnectionError.prototype);var E=typeof g=="string"?g:C.explanation,m=typeof g=="object"?g:l;return C.message=C.name+" ("+C.code+"): "+E,C.originalError=m,C}return S}(e.default);v.ConnectionError=y;var _=function(b){t(S,b);function S(g,l){var C=b.call(this,g,l)||this;C.causes=["The device running your application lost its Internet connection."],C.code=53001,C.description="Signaling connection disconnected",C.explanation="Raised whenever the signaling connection is unexpectedly disconnected.",C.name="ConnectionDisconnected",C.solutions=["Ensure the device running your application has access to a stable Internet connection."],Object.setPrototypeOf(C,v.ConnectionDisconnected.prototype);var E=typeof g=="string"?g:C.explanation,m=typeof g=="object"?g:l;return C.message=C.name+" ("+C.code+"): "+E,C.originalError=m,C}return S}(e.default);v.ConnectionDisconnected=_})(p=n.SignalingErrors||(n.SignalingErrors={}));var f;(function(v){var y=function(S){t(g,S);function g(l,C){var E=S.call(this,l,C)||this;E.causes=["The Client may not be using a supported WebRTC implementation.","The Client may not have the necessary resources to create or apply a new media description."],E.code=53400,E.description="Client is unable to create or apply a local media description",E.explanation="Raised whenever a Client is unable to create or apply a local media description.",E.name="ClientLocalDescFailed",E.solutions=["If you are experiencing this error using the JavaScript SDK, ensure you are running it with a supported WebRTC implementation."],Object.setPrototypeOf(E,v.ClientLocalDescFailed.prototype);var m=typeof l=="string"?l:E.explanation,w=typeof l=="object"?l:C;return E.message=E.name+" ("+E.code+"): "+m,E.originalError=w,E}return g}(e.default);v.ClientLocalDescFailed=y;var _=function(S){t(g,S);function g(l,C){var E=S.call(this,l,C)||this;E.causes=["The Client may not be using a supported WebRTC implementation.","The Client may be connecting peer-to-peer with another Participant that is not using a supported WebRTC implementation.","The Client may not have the necessary resources to apply a new media description."],E.code=53402,E.description="Client is unable to apply a remote media description",E.explanation="Raised whenever the Client receives a remote media description but is unable to apply it.",E.name="ClientRemoteDescFailed",E.solutions=["If you are experiencing this error using the JavaScript SDK, ensure you are running it with a supported WebRTC implementation."],Object.setPrototypeOf(E,v.ClientRemoteDescFailed.prototype);var m=typeof l=="string"?l:E.explanation,w=typeof l=="object"?l:C;return E.message=E.name+" ("+E.code+"): "+m,E.originalError=w,E}return g}(e.default);v.ClientRemoteDescFailed=_;var b=function(S){t(g,S);function g(l,C){var E=S.call(this,l,C)||this;E.causes=["The Client was unable to establish a media connection.","A media connection which was active failed liveliness checks."],E.code=53405,E.description="Media connection failed",E.explanation="Raised by the Client or Server whenever a media connection fails.",E.name="ConnectionError",E.solutions=["If the problem persists, try connecting to another region.","Check your Client's network connectivity.","If you've provided custom ICE Servers then ensure that the URLs and credentials are valid."],Object.setPrototypeOf(E,v.ConnectionError.prototype);var m=typeof l=="string"?l:E.explanation,w=typeof l=="object"?l:C;return E.message=E.name+" ("+E.code+"): "+m,E.originalError=w,E}return g}(e.default);v.ConnectionError=b})(f=n.MediaErrors||(n.MediaErrors={})),n.errorsByCode=new Map([[20101,r.AccessTokenInvalid],[20104,r.AccessTokenExpired],[20151,r.AuthenticationFailed],[31202,i.AccessTokenSignatureValidationFailed],[31400,o.BadRequest],[31404,o.NotFound],[31480,o.TemporarilyUnavailable],[31486,o.BusyHere],[31603,a.Decline],[31e3,s.UnknownError],[31001,s.ApplicationNotFoundError],[31002,s.ConnectionDeclinedError],[31003,s.ConnectionTimeoutError],[31005,s.ConnectionError],[31008,s.CallCancelledError],[31009,s.TransportError],[31100,d.MalformedRequestError],[31101,d.MissingParameterArrayError],[31102,d.AuthorizationTokenMissingError],[31103,d.MaxParameterLengthExceededError],[31104,d.InvalidBridgeTokenError],[31105,d.InvalidClientNameError],[31107,d.ReconnectParameterInvalidError],[31201,r.AuthorizationError],[31203,r.NoValidAccountError],[31204,r.InvalidJWTTokenError],[31205,r.JWTTokenExpiredError],[31206,r.RateExceededError],[31207,r.JWTTokenExpirationTooLongError],[31209,r.ReconnectAttemptError],[31210,r.CallMessageEventTypeInvalidError],[31212,r.PayloadSizeExceededError],[31401,u.PermissionDeniedError],[31402,u.AcquisitionFailedError],[53e3,p.ConnectionError],[53001,p.ConnectionDisconnected],[53400,f.ClientLocalDescFailed],[53402,f.ClientRemoteDescFailed],[53405,f.ConnectionError]]),Object.freeze(n.errorsByCode)})(Fr);(function(n){var t=B&&B.__extends||function(){var p=function(f,v){return p=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(y,_){y.__proto__=_}||function(y,_){for(var b in _)Object.prototype.hasOwnProperty.call(_,b)&&(y[b]=_[b])},p(f,v)};return function(f,v){p(f,v);function y(){this.constructor=f}f.prototype=v===null?Object.create(v):(y.prototype=v.prototype,new y)}}();Object.defineProperty(n,"__esModule",{value:!0}),n.UserMediaErrors=n.TwilioError=n.SIPServerErrors=n.SignatureValidationErrors=n.SignalingErrors=n.MediaErrors=n.MalformedRequestErrors=n.GeneralErrors=n.ClientErrors=n.AuthorizationErrors=n.hasErrorByCode=n.getErrorByCode=n.NotSupportedError=n.InvalidStateError=n.InvalidArgumentError=n.getPreciseSignalingErrorByCode=void 0;var e=Fr;Object.defineProperty(n,"AuthorizationErrors",{enumerable:!0,get:function(){return e.AuthorizationErrors}}),Object.defineProperty(n,"ClientErrors",{enumerable:!0,get:function(){return e.ClientErrors}}),Object.defineProperty(n,"GeneralErrors",{enumerable:!0,get:function(){return e.GeneralErrors}}),Object.defineProperty(n,"MalformedRequestErrors",{enumerable:!0,get:function(){return e.MalformedRequestErrors}}),Object.defineProperty(n,"MediaErrors",{enumerable:!0,get:function(){return e.MediaErrors}}),Object.defineProperty(n,"SignalingErrors",{enumerable:!0,get:function(){return e.SignalingErrors}}),Object.defineProperty(n,"SignatureValidationErrors",{enumerable:!0,get:function(){return e.SignatureValidationErrors}}),Object.defineProperty(n,"SIPServerErrors",{enumerable:!0,get:function(){return e.SIPServerErrors}}),Object.defineProperty(n,"TwilioError",{enumerable:!0,get:function(){return e.TwilioError}}),Object.defineProperty(n,"UserMediaErrors",{enumerable:!0,get:function(){return e.UserMediaErrors}});var r=new Set([31001,31002,31003,31101,31102,31103,31104,31105,31107,31201,31202,31203,31204,31205,31207,31404,31480,31486,31603]);function i(p,f){if(typeof f=="number"&&u(f)){var v=p?!0:!r.has(f);if(v)return d(f)}}n.getPreciseSignalingErrorByCode=i;var o=function(p){t(f,p);function f(v){var y=p.call(this,v)||this;return y.name="InvalidArgumentError",y}return f}(Error);n.InvalidArgumentError=o;var a=function(p){t(f,p);function f(v){var y=p.call(this,v)||this;return y.name="InvalidStateError",y}return f}(Error);n.InvalidStateError=a;var s=function(p){t(f,p);function f(v){var y=p.call(this,v)||this;return y.name="NotSupportedError",y}return f}(Error);n.NotSupportedError=s;function d(p){var f=e.errorsByCode.get(p);if(!f)throw new o("Error code "+p+" not found");return f}n.getErrorByCode=d;function u(p){return e.errorsByCode.has(p)}n.hasErrorByCode=u})(oe);var se={},ie={};Object.defineProperty(ie,"__esModule",{value:!0});ie.SOUNDS_BASE_URL=ie.RELEASE_VERSION=ie.PACKAGE_NAME=ie.ECHO_TEST_DURATION=ie.COWBELL_AUDIO_URL=void 0;var ds="@twilio/voice-sdk";ie.PACKAGE_NAME=ds;var Br="2.12.4";ie.RELEASE_VERSION=Br;var Wr="https://sdk.twilio.com/js/client/sounds/releases/1.0.0";ie.SOUNDS_BASE_URL=Wr;var fs=Wr+"/cowbell.mp3?cache="+Br;ie.COWBELL_AUDIO_URL=fs;var ps=2e4;ie.ECHO_TEST_DURATION=ps;var Ye=B&&B.__spreadArrays||function(){for(var n=0,t=0,e=arguments.length;t<e;t++)n+=arguments[t].length;for(var r=Array(n),i=0,t=0;t<e;t++)for(var o=arguments[t],a=0,s=o.length;a<s;a++,i++)r[i]=o[a];return r};Object.defineProperty(se,"__esModule",{value:!0});se.Logger=void 0;var Bn=Ur,hs=ie,Vr=function(){function n(t,e){this._log=n.getLogLevelInstance(e),this._prefix="[TwilioVoice]["+t+"]"}return n.getLogLevelInstance=function(t){if(!n.loglevelInstance)try{n.loglevelInstance=(t&&t.LogLevelModule?t.LogLevelModule:Bn).getLogger(hs.PACKAGE_NAME)}catch{console.warn("Cannot create custom logger"),n.loglevelInstance=console}return n.loglevelInstance},n.prototype.debug=function(){for(var t,e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];(t=this._log).debug.apply(t,Ye([this._prefix],e))},n.prototype.error=function(){for(var t,e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];(t=this._log).error.apply(t,Ye([this._prefix],e))},n.prototype.info=function(){for(var t,e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];(t=this._log).info.apply(t,Ye([this._prefix],e))},n.prototype.setDefaultLevel=function(t){this._log.setDefaultLevel?this._log.setDefaultLevel(t):console.warn("Logger cannot setDefaultLevel")},n.prototype.warn=function(){for(var t,e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];(t=this._log).warn.apply(t,Ye([this._prefix],e))},n.levels=Bn.levels,n}();se.Logger=Vr.getLogLevelInstance();se.default=Vr;var Qt={};Object.defineProperty(Qt,"__esModule",{value:!0});var gs=ie,Ue=oe,ms=se,vs=gs.SOUNDS_BASE_URL+"/outgoing.mp3",_s=function(){function n(t,e,r,i){this._name=t,this._availableDevices=e,this._beforeChange=r,this._isSupported=i,this._activeDevices=new Set,this._log=new ms.default("OutputDeviceCollection")}return n.prototype.delete=function(t){this._log.debug(".delete",t);var e=!!this._activeDevices.delete(t),r=this._availableDevices.get("default")||Array.from(this._availableDevices.values())[0];!this._activeDevices.size&&r&&this._activeDevices.add(r);var i=Array.from(this._activeDevices.values()).map(function(o){return o.deviceId});return this._beforeChange(this._name,i),!!e},n.prototype.get=function(){return this._activeDevices},n.prototype.set=function(t){var e=this;if(this._log.debug(".set",t),!this._isSupported)return Promise.reject(new Ue.NotSupportedError("This browser does not support audio output selection"));var r=Array.isArray(t)?t:[t];if(!r.length)return Promise.reject(new Ue.InvalidArgumentError("Must specify at least one device to set"));var i=[],o=r.map(function(a){var s=e._availableDevices.get(a);return s||i.push(a),s});return i.length?Promise.reject(new Ue.InvalidArgumentError("Devices not found: "+i.join(", "))):new Promise(function(a){a(e._beforeChange(e._name,r))}).then(function(){e._activeDevices.clear(),o.forEach(e._activeDevices.add,e._activeDevices)})},n.prototype.test=function(t){return t===void 0&&(t=vs),this._isSupported?this._activeDevices.size?Promise.all(Array.from(this._activeDevices).map(function(e){var r;return new Promise(function(i){r=new Audio(t),r.oncanplay=i}).then(function(){return r.setSinkId(e.deviceId).then(function(){return r.play()})})})):Promise.reject(new Ue.InvalidStateError("No active output devices to test")):Promise.reject(new Ue.NotSupportedError("This browser does not support audio output selection"))},n}();Qt.default=_s;var Zt={};Object.defineProperty(Zt,"__esModule",{value:!0});var ys=function(){function n(t){Object.defineProperties(this,{deviceId:{get:function(){return t.deviceId}},groupId:{get:function(){return t.groupId}},kind:{get:function(){return t.kind}},label:{get:function(){return t.label}}})}return n}();Zt.default=ys;var K={};Object.defineProperty(K,"__esModule",{value:!0});K.promisifyEvents=K.flatMap=K.queryToJson=K.isUnifiedPlanDefault=K.isSafari=K.isLegacyEdge=K.isFirefox=K.isChrome=K.isElectron=K.difference=K.average=K.Exception=void 0;function at(n){if(!(this instanceof at))return new at(n);this.message=n}at.prototype.toString=function(){return"Twilio.Exception: "+this.message};function bs(n){return n&&n.length?n.reduce(function(t,e){return t+e})/n.length:0}K.average=bs;function Ss(n,t,e){e=e||function(i){return i};var r=new Set(t.map(e));return n.filter(function(i){return!r.has(e(i))})}K.difference=Ss;function Hr(n){return!!n.userAgent.match("Electron")}K.isElectron=Hr;function qr(n,t){var e=!!t.userAgent.match("CriOS"),r=!!t.userAgent.match("HeadlessChrome"),i=typeof n.chrome<"u"&&t.vendor==="Google Inc."&&t.userAgent.indexOf("OPR")===-1&&t.userAgent.indexOf("Edge")===-1;return e||Hr(t)||i||r}K.isChrome=qr;function zr(n){return n=n||window.navigator,!!n&&typeof n.userAgent=="string"&&/firefox|fxios/i.test(n.userAgent)}K.isFirefox=zr;function ws(n){return n=n||window.navigator,!!n&&typeof n.userAgent=="string"&&/edge\/\d+/i.test(n.userAgent)}K.isLegacyEdge=ws;function Gr(n){return!!n.vendor&&n.vendor.indexOf("Apple")!==-1&&n.userAgent&&n.userAgent.indexOf("CriOS")===-1&&n.userAgent.indexOf("FxiOS")===-1}K.isSafari=Gr;function Cs(n,t,e,r){if(typeof n>"u"||typeof t>"u"||typeof e>"u"||typeof r>"u"||typeof e.prototype>"u"||typeof r.prototype>"u")return!1;if(qr(n,t)&&e.prototype.addTransceiver){var i=new e,o=!0;try{i.addTransceiver("audio")}catch{o=!1}return i.close(),o}else{if(zr(t))return!0;if(Gr(t))return"currentDirection"in r.prototype}return!1}K.isUnifiedPlanDefault=Cs;function Es(n){return n?n.split("&").reduce(function(t,e){var r=e.split("="),i=r[0],o=decodeURIComponent((r[1]||"").replace(/\+/g,"%20"));return i&&(t[i]=o),t},{}):""}K.queryToJson=Es;function Ts(n,t){var e=n instanceof Map||n instanceof Set?Array.from(n.values()):n;return t=t||function(r){return r},e.reduce(function(r,i){var o=t(i);return r.concat(o)},[])}K.flatMap=Ts;function xs(n,t,e){return new Promise(function(r,i){function o(){n.removeListener(e,a),r()}function a(){n.removeListener(t,o),i()}n.once(t,o),n.once(e,a)})}K.promisifyEvents=xs;var Ps=at;K.Exception=Ps;var Wn;function ks(){if(Wn)return Ke;Wn=1;var n=B&&B.__extends||function(){var v=function(y,_){return v=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(b,S){b.__proto__=S}||function(b,S){for(var g in S)Object.prototype.hasOwnProperty.call(S,g)&&(b[g]=S[g])},v(y,_)};return function(y,_){v(y,_);function b(){this.constructor=y}y.prototype=_===null?Object.create(_):(b.prototype=_.prototype,new b)}}(),t=B&&B.__awaiter||function(v,y,_,b){function S(g){return g instanceof _?g:new _(function(l){l(g)})}return new(_||(_=Promise))(function(g,l){function C(w){try{m(b.next(w))}catch(R){l(R)}}function E(w){try{m(b.throw(w))}catch(R){l(R)}}function m(w){w.done?g(w.value):S(w.value).then(C,E)}m((b=b.apply(v,y||[])).next())})},e=B&&B.__generator||function(v,y){var _={label:0,sent:function(){if(g[0]&1)throw g[1];return g[1]},trys:[],ops:[]},b,S,g,l;return l={next:C(0),throw:C(1),return:C(2)},typeof Symbol=="function"&&(l[Symbol.iterator]=function(){return this}),l;function C(m){return function(w){return E([m,w])}}function E(m){if(b)throw new TypeError("Generator is already executing.");for(;_;)try{if(b=1,S&&(g=m[0]&2?S.return:m[0]?S.throw||((g=S.return)&&g.call(S),0):S.next)&&!(g=g.call(S,m[1])).done)return g;switch(S=0,g&&(m=[m[0]&2,g.value]),m[0]){case 0:case 1:g=m;break;case 4:return _.label++,{value:m[1],done:!1};case 5:_.label++,S=m[1],m=[0];continue;case 7:m=_.ops.pop(),_.trys.pop();continue;default:if(g=_.trys,!(g=g.length>0&&g[g.length-1])&&(m[0]===6||m[0]===2)){_=0;continue}if(m[0]===3&&(!g||m[1]>g[0]&&m[1]<g[3])){_.label=m[1];break}if(m[0]===6&&_.label<g[1]){_.label=g[1],g=m;break}if(g&&_.label<g[2]){_.label=g[2],_.ops.push(m);break}g[2]&&_.ops.pop(),_.trys.pop();continue}m=y.call(v,_)}catch(w){m=[6,w],S=0}finally{b=g=0}if(m[0]&5)throw m[1];return{value:m[0]?m[1]:void 0,done:!0}}};Object.defineProperty(Ke,"__esModule",{value:!0});var r=ge,i=_t(),o=oe,a=se,s=Qt,d=Zt,u=K,p={audioinput:"Audio Input",audiooutput:"Audio Output"},f=function(v){n(y,v);function y(_,b,S){var g,l=v.call(this)||this;l.availableInputDevices=new Map,l.availableOutputDevices=new Map,l._audioConstraints=null,l._defaultInputDeviceStream=null,l._enabledSounds=(g={},g[i.default.SoundName.Disconnect]=!0,g[i.default.SoundName.Incoming]=!0,g[i.default.SoundName.Outgoing]=!0,g),l._inputDevice=null,l._inputDevicePromise=null,l._isPollingInputVolume=!1,l._log=new a.default("AudioHelper"),l._processedStream=null,l._selectedInputDeviceStream=null,l._unknownDeviceIndexes={audioinput:{},audiooutput:{}},l._updateAvailableDevices=function(){return!l._mediaDevices||!l._enumerateDevices?Promise.reject("Enumeration not supported"):l._enumerateDevices().then(function(w){l._updateDevices(w.filter(function(h){return h.kind==="audiooutput"}),l.availableOutputDevices,l._removeLostOutput),l._updateDevices(w.filter(function(h){return h.kind==="audioinput"}),l.availableInputDevices,l._removeLostInput);var R=l.availableOutputDevices.get("default")||Array.from(l.availableOutputDevices.values())[0];[l.speakerDevices,l.ringtoneDevices].forEach(function(h){!h.get().size&&l.availableOutputDevices.size&&l.isOutputSelectionSupported&&h.set(R.deviceId).catch(function(j){l._log.warn("Unable to set audio output devices. "+j)})})})},l._removeLostInput=function(w){if(!l.inputDevice||l.inputDevice.deviceId!==w.deviceId)return!1;l._destroyProcessedStream(),l._replaceStream(null),l._inputDevice=null,l._maybeStopPollingVolume();var R=l.availableInputDevices.get("default")||Array.from(l.availableInputDevices.values())[0];return R&&l.setInputDevice(R.deviceId),!0},l._removeLostOutput=function(w){var R=l.speakerDevices.delete(w),h=l.ringtoneDevices.delete(w);return R||h},S=Object.assign({AudioContext:typeof AudioContext<"u"&&AudioContext,setSinkId:typeof HTMLAudioElement<"u"&&HTMLAudioElement.prototype.setSinkId},S),l._beforeSetInputDevice=S.beforeSetInputDevice||function(){return Promise.resolve()},l._updateUserOptions(S),l._audioProcessorEventObserver=S.audioProcessorEventObserver,l._mediaDevices=S.mediaDevices||navigator.mediaDevices,l._onActiveInputChanged=b,l._enumerateDevices=typeof S.enumerateDevices=="function"?S.enumerateDevices:l._mediaDevices&&l._mediaDevices.enumerateDevices.bind(l._mediaDevices);var C=!!(S.AudioContext||S.audioContext),E=!!l._enumerateDevices;S.enabledSounds&&(l._enabledSounds=S.enabledSounds);var m=typeof S.setSinkId=="function";return l.isOutputSelectionSupported=E&&m,l.isVolumeSupported=C,l.isVolumeSupported&&(l._audioContext=S.audioContext||S.AudioContext&&new S.AudioContext,l._audioContext&&(l._inputVolumeAnalyser=l._audioContext.createAnalyser(),l._inputVolumeAnalyser.fftSize=32,l._inputVolumeAnalyser.smoothingTimeConstant=.3)),l.ringtoneDevices=new s.default("ringtone",l.availableOutputDevices,_,l.isOutputSelectionSupported),l.speakerDevices=new s.default("speaker",l.availableOutputDevices,_,l.isOutputSelectionSupported),l.addListener("newListener",function(w){w==="inputVolume"&&l._maybeStartPollingVolume()}),l.addListener("removeListener",function(w){w==="inputVolume"&&l._maybeStopPollingVolume()}),l.once("newListener",function(){l.isOutputSelectionSupported||l._log.warn("Warning: This browser does not support audio output selection."),l.isVolumeSupported||l._log.warn("Warning: This browser does not support Twilio's volume indicator feature.")}),E&&l._initializeEnumeration(),navigator&&navigator.permissions&&typeof navigator.permissions.query=="function"?navigator.permissions.query({name:"microphone"}).then(function(w){if(w.state!=="granted"){var R=function(){l._updateAvailableDevices(),l._stopMicrophonePermissionListener()};w.addEventListener("change",R),l._microphonePermissionStatus=w,l._onMicrophonePermissionStatusChanged=R}}).catch(function(w){return l._log.warn("Warning: unable to listen for microphone permission changes. "+w)}):l._log.warn("Warning: current browser does not support permissions API."),l}return Object.defineProperty(y.prototype,"audioConstraints",{get:function(){return this._audioConstraints},enumerable:!1,configurable:!0}),Object.defineProperty(y.prototype,"inputDevice",{get:function(){return this._inputDevice},enumerable:!1,configurable:!0}),Object.defineProperty(y.prototype,"inputStream",{get:function(){return this._processedStream||this._selectedInputDeviceStream},enumerable:!1,configurable:!0}),Object.defineProperty(y.prototype,"processedStream",{get:function(){return this._processedStream},enumerable:!1,configurable:!0}),y.prototype._destroy=function(){this._stopDefaultInputDeviceStream(),this._stopSelectedInputDeviceStream(),this._destroyProcessedStream(),this._maybeStopPollingVolume(),this.removeAllListeners(),this._stopMicrophonePermissionListener(),this._unbind()},y.prototype._getInputDevicePromise=function(){return this._inputDevicePromise},y.prototype._maybeStartPollingVolume=function(){var _=this;if(!(!this.isVolumeSupported||!this.inputStream)&&(this._updateVolumeSource(),!(this._isPollingInputVolume||!this._inputVolumeAnalyser))){var b=this._inputVolumeAnalyser.frequencyBinCount,S=new Uint8Array(b);this._isPollingInputVolume=!0;var g=function(){if(_._isPollingInputVolume){if(_._inputVolumeAnalyser){_._inputVolumeAnalyser.getByteFrequencyData(S);var l=u.average(S);_.emit("inputVolume",l/255)}requestAnimationFrame(g)}};requestAnimationFrame(g)}},y.prototype._maybeStopPollingVolume=function(){this.isVolumeSupported&&(!this._isPollingInputVolume||this.inputStream&&this.listenerCount("inputVolume")||(this._inputVolumeSource&&(this._inputVolumeSource.disconnect(),delete this._inputVolumeSource),this._isPollingInputVolume=!1))},y.prototype._openDefaultDeviceWithConstraints=function(_){var b=this;return this._log.info("Opening default device with constraints",_),this._getUserMedia(_).then(function(S){return b._log.info("Opened default device. Updating available devices."),b._updateAvailableDevices().catch(function(g){b._log.warn("Unable to updateAvailableDevices after gUM call",g)}),b._defaultInputDeviceStream=S,b._maybeCreateProcessedStream(S)})},y.prototype._stopDefaultInputDeviceStream=function(){this._defaultInputDeviceStream&&(this._log.info("stopping default device stream"),this._defaultInputDeviceStream.getTracks().forEach(function(_){return _.stop()}),this._defaultInputDeviceStream=null,this._destroyProcessedStream())},y.prototype._unbind=function(){var _;!((_=this._mediaDevices)===null||_===void 0)&&_.removeEventListener&&this._mediaDevices.removeEventListener("devicechange",this._updateAvailableDevices)},y.prototype._updateUserOptions=function(_){typeof _.enumerateDevices=="function"&&(this._enumerateDevices=_.enumerateDevices),typeof _.getUserMedia=="function"&&(this._getUserMedia=_.getUserMedia)},y.prototype.addProcessor=function(_){if(this._log.debug(".addProcessor"),this._processor)throw new o.NotSupportedError("Adding multiple AudioProcessors is not supported at this time.");if(typeof _!="object"||_===null)throw new o.InvalidArgumentError("Missing AudioProcessor argument.");if(typeof _.createProcessedStream!="function")throw new o.InvalidArgumentError("Missing createProcessedStream() method.");if(typeof _.destroyProcessedStream!="function")throw new o.InvalidArgumentError("Missing destroyProcessedStream() method.");return this._processor=_,this._audioProcessorEventObserver.emit("add"),this._restartStreams()},y.prototype.disconnect=function(_){return this._log.debug(".disconnect",_),this._maybeEnableSound(i.default.SoundName.Disconnect,_)},y.prototype.incoming=function(_){return this._log.debug(".incoming",_),this._maybeEnableSound(i.default.SoundName.Incoming,_)},y.prototype.outgoing=function(_){return this._log.debug(".outgoing",_),this._maybeEnableSound(i.default.SoundName.Outgoing,_)},y.prototype.removeProcessor=function(_){if(this._log.debug(".removeProcessor"),typeof _!="object"||_===null)throw new o.InvalidArgumentError("Missing AudioProcessor argument.");if(this._processor!==_)throw new o.InvalidArgumentError("Cannot remove an AudioProcessor that has not been previously added.");return this._destroyProcessedStream(),this._processor=null,this._audioProcessorEventObserver.emit("remove"),this._restartStreams()},y.prototype.setAudioConstraints=function(_){return this._log.debug(".setAudioConstraints",_),this._audioConstraints=Object.assign({},_),delete this._audioConstraints.deviceId,this.inputDevice?this._setInputDevice(this.inputDevice.deviceId,!0):Promise.resolve()},y.prototype.setInputDevice=function(_){return this._log.debug(".setInputDevice",_),this._setInputDevice(_,!1)},y.prototype.unsetAudioConstraints=function(){return this._log.debug(".unsetAudioConstraints"),this._audioConstraints=null,this.inputDevice?this._setInputDevice(this.inputDevice.deviceId,!0):Promise.resolve()},y.prototype.unsetInputDevice=function(){var _=this;return this._log.debug(".unsetInputDevice",this.inputDevice),this.inputDevice?(this._destroyProcessedStream(),this._onActiveInputChanged(null).then(function(){_._replaceStream(null),_._inputDevice=null,_._maybeStopPollingVolume()})):Promise.resolve()},y.prototype._destroyProcessedStream=function(){if(this._processor&&this._processedStream){this._log.info("destroying processed stream");var _=this._processedStream;this._processedStream.getTracks().forEach(function(b){return b.stop()}),this._processedStream=null,this._processor.destroyProcessedStream(_),this._audioProcessorEventObserver.emit("destroy")}},y.prototype._getUnknownDeviceIndex=function(_){var b=_.deviceId,S=_.kind,g=this._unknownDeviceIndexes[S][b];return g||(g=Object.keys(this._unknownDeviceIndexes[S]).length+1,this._unknownDeviceIndexes[S][b]=g),g},y.prototype._initializeEnumeration=function(){var _=this;if(!this._mediaDevices||!this._enumerateDevices)throw new o.NotSupportedError("Enumeration is not supported");this._mediaDevices.addEventListener&&this._mediaDevices.addEventListener("devicechange",this._updateAvailableDevices),this._updateAvailableDevices().then(function(){_.isOutputSelectionSupported&&Promise.all([_.speakerDevices.set("default"),_.ringtoneDevices.set("default")]).catch(function(b){_._log.warn("Warning: Unable to set audio output devices. "+b)})})},y.prototype._maybeCreateProcessedStream=function(_){var b=this;return this._processor?(this._log.info("Creating processed stream"),this._processor.createProcessedStream(_).then(function(S){return b._processedStream=S,b._audioProcessorEventObserver.emit("create"),b._processedStream})):Promise.resolve(_)},y.prototype._maybeEnableSound=function(_,b){return typeof b<"u"&&(this._enabledSounds[_]=b),this._enabledSounds[_]},y.prototype._replaceStream=function(_){this._log.info("Replacing with new stream."),this._selectedInputDeviceStream&&(this._log.info("Old stream detected. Stopping tracks."),this._stopSelectedInputDeviceStream()),this._selectedInputDeviceStream=_},y.prototype._restartStreams=function(){if(this.inputDevice&&this._selectedInputDeviceStream)return this._log.info("Restarting selected input device"),this._setInputDevice(this.inputDevice.deviceId,!0);if(this._defaultInputDeviceStream){var _=this.availableInputDevices.get("default")||Array.from(this.availableInputDevices.values())[0];return this._log.info("Restarting default input device, now becoming selected."),this._setInputDevice(_.deviceId,!0)}return Promise.resolve()},y.prototype._setInputDevice=function(_,b){return t(this,void 0,void 0,function(){var S,g=this;return e(this,function(l){return S=function(){return t(g,void 0,void 0,function(){var C,E,m=this;return e(this,function(w){switch(w.label){case 0:return[4,this._beforeSetInputDevice()];case 1:if(w.sent(),typeof _!="string")return[2,Promise.reject(new o.InvalidArgumentError("Must specify the device to set"))];if(C=this.availableInputDevices.get(_),!C)return[2,Promise.reject(new o.InvalidArgumentError("Device not found: "+_))];if(this._log.info("Setting input device. ID: "+_),this._inputDevice&&this._inputDevice.deviceId===_&&this._selectedInputDeviceStream){if(!b)return[2,Promise.resolve()];this._log.info("Same track detected on setInputDevice, stopping old tracks."),this._stopSelectedInputDeviceStream()}return this._stopDefaultInputDeviceStream(),E={audio:Object.assign({deviceId:{exact:_}},this.audioConstraints)},this._log.info("setInputDevice: getting new tracks."),[2,this._getUserMedia(E).then(function(R){return m._destroyProcessedStream(),m._maybeCreateProcessedStream(R).then(function(h){return m._log.info("setInputDevice: invoking _onActiveInputChanged."),m._onActiveInputChanged(h).then(function(){m._replaceStream(R),m._inputDevice=C,m._maybeStartPollingVolume()})})})]}})})},[2,this._inputDevicePromise=S().finally(function(){g._inputDevicePromise=null})]})})},y.prototype._stopMicrophonePermissionListener=function(){var _;!((_=this._microphonePermissionStatus)===null||_===void 0)&&_.removeEventListener&&this._microphonePermissionStatus.removeEventListener("change",this._onMicrophonePermissionStatusChanged)},y.prototype._stopSelectedInputDeviceStream=function(){this._selectedInputDeviceStream&&(this._log.info("Stopping selected device stream"),this._selectedInputDeviceStream.getTracks().forEach(function(_){return _.stop()}))},y.prototype._updateDevices=function(_,b,S){var g=this,l=_.map(function(x){return x.deviceId}),C=Array.from(b.values()).map(function(x){return x.deviceId}),E=[],m=u.difference(C,l);m.forEach(function(x){var I=b.get(x);I&&(b.delete(x),S(I)&&E.push(I))});var w=!1;if(_.forEach(function(x){var I=b.get(x.deviceId),T=g._wrapMediaDeviceInfo(x);(!I||I.label!==T.label)&&(b.set(x.deviceId,T),w=!0)}),w||m.length){var R="default",h=this.inputDevice&&this.inputDevice.deviceId===R,j=this._defaultInputDeviceStream&&this.availableInputDevices.get(R);(h||j)&&(this._log.warn("Calling getUserMedia after device change to ensure that the           tracks of the active device (default) have not gone stale."),setTimeout(function(){g._setInputDevice(R,!0)},0)),this._log.debug("#deviceChange",E),this.emit("deviceChange",E)}},y.prototype._updateVolumeSource=function(){if(!(!this.inputStream||!this._audioContext||!this._inputVolumeAnalyser)){this._inputVolumeSource&&this._inputVolumeSource.disconnect();try{this._inputVolumeSource=this._audioContext.createMediaStreamSource(this.inputStream),this._inputVolumeSource.connect(this._inputVolumeAnalyser)}catch(_){this._log.warn("Unable to update volume source",_),delete this._inputVolumeSource}}},y.prototype._wrapMediaDeviceInfo=function(_){var b={deviceId:_.deviceId,groupId:_.groupId,kind:_.kind,label:_.label};if(!b.label)if(b.deviceId==="default")b.label="Default";else{var S=this._getUnknownDeviceIndex(_);b.label="Unknown "+p[b.kind]+" Device "+S}return new d.default(b)},y}(r.EventEmitter);return f||(f={}),Ke.default=f,Ke}var ht={},Is=B&&B.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var o in i)Object.prototype.hasOwnProperty.call(i,o)&&(r[o]=i[o])},n(t,e)};return function(t,e){n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}();Object.defineProperty(ht,"__esModule",{value:!0});ht.AudioProcessorEventObserver=void 0;var Rs=ge,As=se,Ds=function(n){Is(t,n);function t(){var e=n.call(this)||this;return e._log=new As.default("AudioProcessorEventObserver"),e._log.info("Creating AudioProcessorEventObserver instance"),e.on("enabled",function(){return e._reEmitEvent("enabled")}),e.on("add",function(){return e._reEmitEvent("add")}),e.on("remove",function(){return e._reEmitEvent("remove")}),e.on("create",function(){return e._reEmitEvent("create-processed-stream")}),e.on("destroy",function(){return e._reEmitEvent("destroy-processed-stream")}),e}return t.prototype.destroy=function(){this.removeAllListeners()},t.prototype._reEmitEvent=function(e){this._log.info("AudioProcessor:"+e),this.emit("event",{name:e,group:"audio-processor"})},t}(Rs.EventEmitter);ht.AudioProcessorEventObserver=Ds;var en={};Object.defineProperty(en,"__esModule",{value:!0});var Os=oe,Ms={dtmf0:[1360,960],dtmf1:[1230,720],dtmf2:[1360,720],dtmf3:[1480,720],dtmf4:[1230,790],dtmf5:[1360,790],dtmf6:[1480,790],dtmf7:[1230,870],dtmf8:[1360,870],dtmf9:[1480,870],dtmfh:[1480,960],dtmfs:[1230,960]},js=function(){function n(t){var e=this;this._context=t,this._gainNodes=[],this._gainNodes=[this._context.createGain(),this._context.createGain()],this._gainNodes.forEach(function(r){r.connect(e._context.destination),r.gain.value=.1,e._gainNodes.push(r)})}return n.prototype.cleanup=function(){this._gainNodes.forEach(function(t){t.disconnect()})},n.prototype.play=function(t){var e=this,r=Ms[t];if(!r)throw new Os.InvalidArgumentError("Invalid DTMF sound name");var i=[this._context.createOscillator(),this._context.createOscillator()];i.forEach(function(o,a){o.type="sine",o.frequency.value=r[a],o.connect(e._gainNodes[a]),o.start(),o.stop(e._context.currentTime+.1),o.addEventListener("ended",function(){return o.disconnect()})})},n}();en.default=js;var tn={},nn={};Object.defineProperty(nn,"__esModule",{value:!0});function Ls(n,t,e){var r=JSON.stringify(t.body||{}),i=new Headers;t.headers=t.headers||[],Object.entries(t.headers).forEach(function(o){var a=o[0],s=o[1];return i.append(a,s)}),fetch(t.url,{body:r,headers:i,method:n}).then(function(o){return o.text()},e).then(function(o){return e(null,o)},e)}var rn=Ls;rn.get=function(t,e){return new this("GET",t,e)};rn.post=function(t,e){return new this("POST",t,e)};nn.default=rn;var Ns=B&&B.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var o in i)Object.prototype.hasOwnProperty.call(i,o)&&(r[o]=i[o])},n(t,e)};return function(t,e){n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}();Object.defineProperty(tn,"__esModule",{value:!0});var Us=ge,Fs=se,Bs=nn,fe=function(n){Ns(t,n);function t(e,r,i){var o=n.call(this)||this;if(!(o instanceof t))return new t(e,r,i);i=Object.assign({defaultPayload:function(){return{}}},i);var a=i.defaultPayload;typeof a!="function"&&(a=function(){return Object.assign({},i.defaultPayload)});var s=!0,d=Object.assign({app_name:void 0,app_version:void 0},i.metadata);return Object.defineProperties(o,{_defaultPayload:{value:a},_host:{value:i.host,writable:!0},_isEnabled:{get:function(){return s},set:function(u){s=u}},_log:{value:new Fs.default("EventPublisher")},_request:{value:i.request||Bs.default,writable:!0},_token:{value:r,writable:!0},isEnabled:{enumerable:!0,get:function(){return s}},metadata:{enumerable:!0,get:function(){return d}},productName:{enumerable:!0,value:e},token:{enumerable:!0,get:function(){return this._token}}}),o}return t}(Us.EventEmitter);fe.prototype._post=function(t,e,r,i,o,a,s){var d=this;if(!this.isEnabled&&!s||!this._host)return this._log.debug("Publishing cancelled",JSON.stringify({isEnabled:this.isEnabled,force:s,host:this._host})),Promise.resolve();if(!a||(!a.parameters||!a.parameters.CallSid)&&!a.outboundConnectionId)return a?this._log.debug("Publishing cancelled. Missing connection info",JSON.stringify({outboundConnectionId:a.outboundConnectionId,parameters:a.parameters})):this._log.debug("Publishing cancelled. Missing connection object"),Promise.resolve();var u={group:r,level:e.toUpperCase(),name:i,payload:o&&o.forEach?o.slice(0):Object.assign(this._defaultPayload(a),o),payload_type:"application/json",private:!1,publisher:this.productName,timestamp:new Date().toISOString()};this.metadata&&(u.publisher_metadata=this.metadata),t==="EndpointEvents"&&this._log.debug("Publishing insights",JSON.stringify({endpointName:t,event:u,force:s,host:this._host}));var p={body:u,headers:{"Content-Type":"application/json","X-Twilio-Token":this.token},url:"https://"+this._host+"/v4/"+t};return new Promise(function(f,v){d._request.post(p,function(y){y?(d.emit("error",y),v(y)):f()})}).catch(function(f){d._log.error("Unable to post "+r+" "+i+" event to Insights. Received error: "+f)})};fe.prototype.post=function(t,e,r,i,o,a){return this._post("EndpointEvents",t,e,r,i,o,a)};fe.prototype.debug=function(t,e,r,i){return this.post("debug",t,e,r,i)};fe.prototype.info=function(t,e,r,i){return this.post("info",t,e,r,i)};fe.prototype.warn=function(t,e,r,i){return this.post("warning",t,e,r,i)};fe.prototype.error=function(t,e,r,i){return this.post("error",t,e,r,i)};fe.prototype.postMetrics=function(t,e,r,i,o){var a=this;return new Promise(function(s){var d=r.map(Ws).map(function(u){return Object.assign(u,i)});s(a._post("EndpointMetrics","info",t,e,d,o))})};fe.prototype.setHost=function(t){this._host=t};fe.prototype.setToken=function(t){this._token=t};fe.prototype.enable=function(){this._isEnabled=!0};fe.prototype.disable=function(){this._isEnabled=!1};function Ws(n){return{audio_codec:n.codecName,audio_level_in:n.audioInputLevel,audio_level_out:n.audioOutputLevel,bytes_received:n.bytesReceived,bytes_sent:n.bytesSent,call_volume_input:n.inputVolume,call_volume_output:n.outputVolume,jitter:n.jitter,mos:n.mos&&Math.round(n.mos*100)/100,packets_lost:n.packetsLost,packets_lost_fraction:n.packetsLostFraction&&Math.round(n.packetsLostFraction*100)/100,packets_received:n.packetsReceived,rtt:n.rtt,timestamp:new Date(n.timestamp).toISOString(),total_bytes_received:n.totals.bytesReceived,total_bytes_sent:n.totals.bytesSent,total_packets_lost:n.totals.packetsLost,total_packets_received:n.totals.packetsReceived,total_packets_sent:n.totals.packetsSent}}tn.default=fe;var Ct={},ke={},on={};Object.defineProperty(on,"__esModule",{value:!0});var Vn=32767,Hn=typeof window<"u"?window.RTCStatsReport:void 0;function he(n){if(!(this instanceof he))return new he(n);var t=this;Object.defineProperties(this,{_map:{value:n},size:{enumerable:!0,get:function(){return t._map.size}}}),this[Symbol.iterator]=n[Symbol.iterator]}Hn&&(he.prototype=Object.create(Hn.prototype),he.prototype.constructor=he);["entries","forEach","get","has","keys","values"].forEach(function(n){he.prototype[n]=function(){for(var t,e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];return(t=this._map)[n].apply(t,e)}});he.fromArray=function(t){return new he(t.reduce(function(e,r){return e.set(r.id,r),e},new Map))};he.fromRTCStatsResponse=function(t){var e,r=new Map,i=t.result().reduce(function(a,s){var d=s.id;switch(s.type){case"googCertificate":a.set(d,$s(s));break;case"datachannel":a.set(d,Js(s));break;case"googCandidatePair":Jr(s,"googActiveConnection")&&(e=d),a.set(d,Gs(s));break;case"localcandidate":a.set(d,zn(s,!1));break;case"remotecandidate":a.set(d,zn(s,!0));break;case"ssrc":xe(s,"packetsReceived")?a.set("rtp-"+d,qs(s)):a.set("rtp-"+d,zs(s)),a.set("track-"+d,Hs(s)),a.set("codec-"+d,Vs(s));break;case"googComponent":var u=qn(s);r.set(u.selectedCandidatePairId,d),a.set(d,qn(s));break}return a},new Map);if(e){var o=r.get(e);o&&(i.get(o).dtlsState="connected")}return new he(i)};function qn(n){return{bytesReceived:void 0,bytesSent:void 0,dtlsState:void 0,id:n.id,localCertificateId:n.stat("localCertificateId"),remoteCertificateId:n.stat("remoteCertificateId"),rtcpTransportStatsId:void 0,selectedCandidatePairId:n.stat("selectedCandidatePairId"),timestamp:Date.parse(n.timestamp),type:"transport"}}function Vs(n){return{channels:void 0,clockRate:void 0,id:n.id,implementation:void 0,mimeType:n.stat("mediaType")+"/"+n.stat("googCodecName"),payloadType:void 0,sdpFmtpLine:void 0,timestamp:Date.parse(n.timestamp),type:"codec"}}function Hs(n){return{audioLevel:xe(n,"audioOutputLevel")?Y(n,"audioOutputLevel")/Vn:(Y(n,"audioInputLevel")||0)/Vn,detached:void 0,echoReturnLoss:Lt(n,"googEchoCancellationReturnLoss"),echoReturnLossEnhancement:Lt(n,"googEchoCancellationReturnLossEnhancement"),ended:void 0,frameHeight:xe(n,"googFrameHeightReceived")?Y(n,"googFrameHeightReceived"):Y(n,"googFrameHeightSent"),frameWidth:xe(n,"googFrameWidthReceived")?Y(n,"googFrameWidthReceived"):Y(n,"googFrameWidthSent"),framesCorrupted:void 0,framesDecoded:Y(n,"framesDecoded"),framesDropped:void 0,framesPerSecond:void 0,framesReceived:void 0,framesSent:Y(n,"framesEncoded"),fullFramesLost:void 0,id:n.id,kind:n.stat("mediaType"),partialFramesLost:void 0,remoteSource:void 0,ssrcIds:void 0,timestamp:Date.parse(n.timestamp),trackIdentifier:n.stat("googTrackId"),type:"track"}}function $r(n,t){return{associateStatsId:void 0,codecId:"codec-"+n.id,firCount:t?Y(n,"googFirsSent"):void 0,id:n.id,isRemote:void 0,mediaType:n.stat("mediaType"),nackCount:t?Y(n,"googNacksSent"):Y(n,"googNacksReceived"),pliCount:t?Y(n,"googPlisSent"):Y(n,"googPlisReceived"),qpSum:Y(n,"qpSum"),sliCount:void 0,ssrc:n.stat("ssrc"),timestamp:Date.parse(n.timestamp),trackId:"track-"+n.id,transportId:n.stat("transportId")}}function qs(n){var t=$r(n,!0);return Object.assign(t,{burstDiscardCount:void 0,burstDiscardRate:void 0,burstLossCount:void 0,burstLossRate:void 0,burstPacketsDiscarded:void 0,burstPacketsLost:void 0,bytesReceived:Y(n,"bytesReceived"),fractionLost:void 0,framesDecoded:Y(n,"framesDecoded"),gapDiscardRate:void 0,gapLossRate:void 0,jitter:jt(n.stat("googJitterReceived")),packetsDiscarded:void 0,packetsLost:Y(n,"packetsLost"),packetsReceived:Y(n,"packetsReceived"),packetsRepaired:void 0,roundTripTime:jt(n.stat("googRtt")),type:"inbound-rtp"}),t}function zs(n){var t=$r(n,!1);return Object.assign(t,{bytesSent:Y(n,"bytesSent"),framesEncoded:Y(n,"framesEncoded"),packetsSent:Y(n,"packetsSent"),remoteTimestamp:void 0,targetBitrate:void 0,type:"outbound-rtp"}),t}function zn(n,t){return{candidateType:Ks(n.stat("candidateType")),deleted:void 0,id:n.id,ip:n.stat("ipAddress"),isRemote:t,port:Y(n,"portNumber"),priority:Lt(n,"priority"),protocol:n.stat("transport"),relayProtocol:void 0,timestamp:Date.parse(n.timestamp),transportId:void 0,type:t?"remote-candidate":"local-candidate",url:void 0}}function Gs(n){return{availableIncomingBitrate:void 0,availableOutgoingBitrate:void 0,bytesReceived:Y(n,"bytesReceived"),bytesSent:Y(n,"bytesSent"),consentRequestsSent:Y(n,"consentRequestsSent"),currentRoundTripTime:jt(n.stat("googRtt")),id:n.id,lastPacketReceivedTimestamp:void 0,lastPacketSentTimestamp:void 0,localCandidateId:n.stat("localCandidateId"),nominated:void 0,priority:void 0,readable:void 0,remoteCandidateId:n.stat("remoteCandidateId"),requestsReceived:Y(n,"requestsReceived"),requestsSent:Y(n,"requestsSent"),responsesReceived:Y(n,"responsesReceived"),responsesSent:Y(n,"responsesSent"),retransmissionsReceived:void 0,retransmissionsSent:void 0,state:void 0,timestamp:Date.parse(n.timestamp),totalRoundTripTime:void 0,transportId:n.stat("googChannelId"),type:"candidate-pair",writable:Jr(n,"googWritable")}}function $s(n){return{base64Certificate:n.stat("googDerBase64"),fingerprint:n.stat("googFingerprint"),fingerprintAlgorithm:n.stat("googFingerprintAlgorithm"),id:n.id,issuerCertificateId:n.stat("googIssuerId"),timestamp:Date.parse(n.timestamp),type:"certificate"}}function Js(n){return{bytesReceived:void 0,bytesSent:void 0,datachannelid:n.stat("datachannelid"),id:n.id,label:n.stat("label"),messagesReceived:void 0,messagesSent:void 0,protocol:n.stat("protocol"),state:n.stat("state"),timestamp:Date.parse(n.timestamp),transportId:n.stat("transportId"),type:"data-channel"}}function jt(n){return isNaN(n)||n===""?void 0:parseInt(n,10)/1e3}function Ks(n){switch(n){case"peerreflexive":return"prflx";case"serverreflexive":return"srflx";case"host":case"relay":default:return n}}function Y(n,t){var e=n.stat(t);return xe(n,t)?parseInt(e,10):void 0}function Lt(n,t){var e=n.stat(t);return xe(n,t)?parseFloat(e):void 0}function Jr(n,t){var e=n.stat(t);return xe(n,t)?e==="true"||e===!0:void 0}function xe(n,t){var e=n.stat(t);return typeof e<"u"&&e!==""}on.default=he;var Ys=B&&B.__spreadArrays||function(){for(var n=0,t=0,e=arguments.length;t<e;t++)n+=arguments[t].length;for(var r=Array(n),i=0,t=0;t<e;t++)for(var o=arguments[t],a=0,s=o.length;a<s;a++,i++)r[i]=o[a];return r};Object.defineProperty(ke,"__esModule",{value:!0});ke.getRTCIceCandidateStatsReport=ke.getRTCStats=void 0;var Gn=oe,Xs=on,Qs="PeerConnection is null",Zs="WebRTC statistics are unsupported";function Ae(n,t){return typeof n.get=="function"?n.get(t):n.find(function(e){return e.id===t})}function Kr(n){if(!n)return Promise.reject(new Gn.InvalidArgumentError(Qs));if(typeof n.getStats!="function")return Promise.reject(new Gn.NotSupportedError(Zs));var t;try{t=n.getStats()}catch{t=new Promise(function(r){return n.getStats(r)}).then(Xs.default.fromRTCStatsResponse)}return t}function ea(n,t){return t=Object.assign({createRTCSample:ra},t),Kr(n).then(t.createRTCSample)}ke.getRTCStats=ea;function ta(n){return Kr(n).then(function(t){var e=Array.from(t.values()).reduce(function(u,p){switch(["candidatePairs","localCandidates","remoteCandidates"].forEach(function(f){u[f]||(u[f]=[])}),p.type){case"candidate-pair":u.candidatePairs.push(p);break;case"local-candidate":u.localCandidates.push(p);break;case"remote-candidate":u.remoteCandidates.push(p);break;case"transport":p.selectedCandidatePairId&&(u.transport=p);break}return u},{}),r=e.candidatePairs,i=e.localCandidates,o=e.remoteCandidates,a=e.transport,s=r.find(function(u){return u.selected||a&&u.id===a.selectedCandidatePairId}),d;return s&&(d={localCandidate:i.find(function(u){return u.id===s.localCandidateId}),remoteCandidate:o.find(function(u){return u.id===s.remoteCandidateId})}),{iceCandidateStats:Ys(i,o),selectedIceCandidatePairStats:d}})}ke.getRTCIceCandidateStatsReport=ta;function na(){}function ra(n){var t=null,e=new na,r;Array.from(n.values()).forEach(function(d){if(!d.isRemote){var u=d.type.replace("-","");if(r=r||d.timestamp,d.remoteId){var p=Ae(n,d.remoteId);p&&p.roundTripTime&&(e.rtt=p.roundTripTime*1e3)}switch(u){case"inboundrtp":e.timestamp=e.timestamp||d.timestamp,e.jitter=d.jitter*1e3,e.packetsLost=d.packetsLost,e.packetsReceived=d.packetsReceived,e.bytesReceived=d.bytesReceived;break;case"outboundrtp":if(e.timestamp=d.timestamp,e.packetsSent=d.packetsSent,e.bytesSent=d.bytesSent,d.codecId){var f=Ae(n,d.codecId);e.codecName=f?f.mimeType&&f.mimeType.match(/(.*\/)?(.*)/)[2]:d.codecId}break;case"transport":t=d.id;break}}}),e.timestamp||(e.timestamp=r);var i=Ae(n,t);if(!i)return e;var o=Ae(n,i.selectedCandidatePairId);if(!o)return e;var a=Ae(n,o.localCandidateId),s=Ae(n,o.remoteCandidateId);return e.rtt||(e.rtt=o&&o.currentRoundTripTime*1e3),Object.assign(e,{localAddress:a&&(a.address||a.ip),remoteAddress:s&&(s.address||s.ip)}),e}var $n;function Yr(){return $n||($n=1,function(n){var t=B&&B.__extends||function(){var y=function(_,b){return y=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(S,g){S.__proto__=g}||function(S,g){for(var l in g)Object.prototype.hasOwnProperty.call(g,l)&&(S[l]=g[l])},y(_,b)};return function(_,b){y(_,b);function S(){this.constructor=_}_.prototype=b===null?Object.create(b):(S.prototype=b.prototype,new S)}}(),e=B&&B.__assign||function(){return e=Object.assign||function(y){for(var _,b=1,S=arguments.length;b<S;b++){_=arguments[b];for(var g in _)Object.prototype.hasOwnProperty.call(_,g)&&(y[g]=_[g])}return y},e.apply(this,arguments)},r=B&&B.__awaiter||function(y,_,b,S){function g(l){return l instanceof b?l:new b(function(C){C(l)})}return new(b||(b=Promise))(function(l,C){function E(R){try{w(S.next(R))}catch(h){C(h)}}function m(R){try{w(S.throw(R))}catch(h){C(h)}}function w(R){R.done?l(R.value):g(R.value).then(E,m)}w((S=S.apply(y,_||[])).next())})},i=B&&B.__generator||function(y,_){var b={label:0,sent:function(){if(l[0]&1)throw l[1];return l[1]},trys:[],ops:[]},S,g,l,C;return C={next:E(0),throw:E(1),return:E(2)},typeof Symbol=="function"&&(C[Symbol.iterator]=function(){return this}),C;function E(w){return function(R){return m([w,R])}}function m(w){if(S)throw new TypeError("Generator is already executing.");for(;b;)try{if(S=1,g&&(l=w[0]&2?g.return:w[0]?g.throw||((l=g.return)&&l.call(g),0):g.next)&&!(l=l.call(g,w[1])).done)return l;switch(g=0,l&&(w=[w[0]&2,l.value]),w[0]){case 0:case 1:l=w;break;case 4:return b.label++,{value:w[1],done:!1};case 5:b.label++,g=w[1],w=[0];continue;case 7:w=b.ops.pop(),b.trys.pop();continue;default:if(l=b.trys,!(l=l.length>0&&l[l.length-1])&&(w[0]===6||w[0]===2)){b=0;continue}if(w[0]===3&&(!l||w[1]>l[0]&&w[1]<l[3])){b.label=w[1];break}if(w[0]===6&&b.label<l[1]){b.label=l[1],l=w;break}if(l&&b.label<l[2]){b.label=l[2],b.ops.push(w);break}l[2]&&b.ops.pop(),b.trys.pop();continue}w=_.call(y,b)}catch(R){w=[6,R],g=0}finally{S=l=0}if(w[0]&5)throw w[1];return{value:w[0]?w[1]:void 0,done:!0}}};Object.defineProperty(n,"__esModule",{value:!0}),n.PreflightTest=void 0;var o=ge,a=_n(),s=_t(),d=oe,u=se,p=ke,f=ie,v=function(y){t(_,y);function _(b,S){var g=y.call(this)||this;g._hasInsightsErrored=!1,g._log=new u.default("PreflightTest"),g._networkTiming={},g._options={codecPreferences:[a.default.Codec.PCMU,a.default.Codec.Opus],edge:"roaming",fakeMicInput:!1,logLevel:"error",signalingTimeoutMs:1e4},g._status=_.Status.Connecting,Object.assign(g._options,S),g._samples=[],g._warnings=[],g._startTime=Date.now(),g._initDevice(b,e(e({},g._options),{fileInputStream:g._options.fakeMicInput?g._getStreamFromFile():void 0}));var l=["codecPreferences","edge","fakeMicInput","logLevel","signalingTimeoutMs"],C=["audioContext","deviceFactory","fileInputStream","getRTCIceCandidateStatsReport","iceServers","rtcConfiguration"];if(typeof S=="object"){var E=e({},S);Object.keys(E).forEach(function(m){!l.includes(m)&&!C.includes(m)&&delete E[m],C.includes(m)&&(E[m]=!0)}),g._log.debug(".constructor",JSON.stringify(E))}return g}return _.prototype.stop=function(){var b=this;this._log.debug(".stop");var S=new d.GeneralErrors.CallCancelledError;this._device?(this._device.once(s.default.EventName.Unregistered,function(){return b._onFailed(S)}),this._device.destroy()):this._onFailed(S)},_.prototype._emitWarning=function(b,S,g){var l={name:b,description:S};g&&(l.rtcWarning=g),this._warnings.push(l),this._log.debug("#"+_.Events.Warning,JSON.stringify(l)),this.emit(_.Events.Warning,l)},_.prototype._getCallQuality=function(b){return b>4.2?_.CallQuality.Excellent:b>=4.1&&b<=4.2?_.CallQuality.Great:b>=3.7&&b<=4?_.CallQuality.Good:b>=3.1&&b<=3.6?_.CallQuality.Fair:_.CallQuality.Degraded},_.prototype._getReport=function(){var b,S,g,l=this._getRTCStats(),C={start:this._startTime};this._endTime&&(C.end=this._endTime,C.duration=this._endTime-this._startTime);var E={callSid:this._callSid,edge:this._edge,iceCandidateStats:(S=(b=this._rtcIceCandidateStatsReport)===null||b===void 0?void 0:b.iceCandidateStats)!==null&&S!==void 0?S:[],networkTiming:this._networkTiming,samples:this._samples,selectedEdge:this._options.edge,stats:l,testTiming:C,totals:this._getRTCSampleTotals(),warnings:this._warnings},m=(g=this._rtcIceCandidateStatsReport)===null||g===void 0?void 0:g.selectedIceCandidatePairStats;return m&&(E.selectedIceCandidatePairStats=m,E.isTurnRequired=m.localCandidate.candidateType==="relay"||m.remoteCandidate.candidateType==="relay"),l&&(E.callQuality=this._getCallQuality(l.mos.average)),E},_.prototype._getRTCSampleTotals=function(){if(this._latestSample)return e({},this._latestSample.totals)},_.prototype._getRTCStats=function(){var b=this._samples.findIndex(function(g){return typeof g.mos=="number"&&g.mos>0}),S=b>=0?this._samples.slice(b):[];if(!(!S||!S.length))return["jitter","mos","rtt"].reduce(function(g,l){var C,E=S.map(function(m){return m[l]});return e(e({},g),(C={},C[l]={average:Number((E.reduce(function(m,w){return m+w})/E.length).toPrecision(5)),max:Math.max.apply(Math,E),min:Math.min.apply(Math,E)},C))},{})},_.prototype._getStreamFromFile=function(){var b=this._options.audioContext;if(!b)throw new d.NotSupportedError("Cannot fake input audio stream: AudioContext is not supported by this browser.");var S=new Audio(f.COWBELL_AUDIO_URL);S.addEventListener("canplaythrough",function(){return S.play()}),typeof S.setAttribute=="function"&&S.setAttribute("crossorigin","anonymous");var g=b.createMediaElementSource(S),l=b.createMediaStreamDestination();return g.connect(l),l.stream},_.prototype._initDevice=function(b,S){var g=this;try{this._device=new(S.deviceFactory||s.default)(b,{chunderw:S.chunderw,codecPreferences:S.codecPreferences,edge:S.edge,eventgw:S.eventgw,fileInputStream:S.fileInputStream,logLevel:S.logLevel,preflight:!0}),this._device.once(s.default.EventName.Registered,function(){g._onDeviceRegistered()}),this._device.once(s.default.EventName.Error,function(l){g._onDeviceError(l)}),this._device.register()}catch(l){setTimeout(function(){g._onFailed(l)});return}this._signalingTimeoutTimer=setTimeout(function(){g._onDeviceError(new d.SignalingErrors.ConnectionError("WebSocket Connection Timeout"))},S.signalingTimeoutMs)},_.prototype._onDeviceError=function(b){this._device.destroy(),this._onFailed(b)},_.prototype._onDeviceRegistered=function(){return r(this,void 0,void 0,function(){var b,S,g,l=this;return i(this,function(C){switch(C.label){case 0:return clearTimeout(this._echoTimer),clearTimeout(this._signalingTimeoutTimer),b=this,[4,this._device.connect({rtcConfiguration:this._options.rtcConfiguration})];case 1:return b._call=C.sent(),this._networkTiming.signaling={start:Date.now()},this._setupCallHandlers(this._call),this._edge=this._device.edge||void 0,this._options.fakeMicInput&&(this._echoTimer=setTimeout(function(){return l._device.disconnectAll()},f.ECHO_TEST_DURATION),S=this._device.audio,S&&(S.disconnect(!1),S.outgoing(!1))),this._call.once("disconnect",function(){l._device.once(s.default.EventName.Unregistered,function(){return l._onUnregistered()}),l._device.destroy()}),g=this._call._publisher,g.on("error",function(){l._hasInsightsErrored||l._emitWarning("insights-connection-error","Received an error when attempting to connect to Insights gateway"),l._hasInsightsErrored=!0}),[2]}})})},_.prototype._onFailed=function(b){clearTimeout(this._echoTimer),clearTimeout(this._signalingTimeoutTimer),this._releaseHandlers(),this._endTime=Date.now(),this._status=_.Status.Failed,this._log.debug("#"+_.Events.Failed,b),this.emit(_.Events.Failed,b)},_.prototype._onUnregistered=function(){var b=this;setTimeout(function(){b._status!==_.Status.Failed&&(clearTimeout(b._echoTimer),clearTimeout(b._signalingTimeoutTimer),b._releaseHandlers(),b._endTime=Date.now(),b._status=_.Status.Completed,b._report=b._getReport(),b._log.debug("#"+_.Events.Completed,JSON.stringify(b._report)),b.emit(_.Events.Completed,b._report))},10)},_.prototype._releaseHandlers=function(){[this._device,this._call].forEach(function(b){b&&b.eventNames().forEach(function(S){return b.removeAllListeners(S)})})},_.prototype._setupCallHandlers=function(b){var S=this;this._options.fakeMicInput&&b.once("volume",function(){b._mediaHandler.outputs.forEach(function(g){return g.audio.muted=!0})}),b.on("warning",function(g,l){S._emitWarning(g,"Received an RTCWarning. See .rtcWarning for the RTCWarning",l)}),b.once("accept",function(){S._callSid=b._mediaHandler.callSid,S._status=_.Status.Connected,S._log.debug("#"+_.Events.Connected),S.emit(_.Events.Connected)}),b.on("sample",function(g){return r(S,void 0,void 0,function(){var l;return i(this,function(C){switch(C.label){case 0:return this._latestSample?[3,2]:(l=this,[4,(this._options.getRTCIceCandidateStatsReport||p.getRTCIceCandidateStatsReport)(b._mediaHandler.version.pc)]);case 1:l._rtcIceCandidateStatsReport=C.sent(),C.label=2;case 2:return this._latestSample=g,this._samples.push(g),this._log.debug("#"+_.Events.Sample,JSON.stringify(g)),this.emit(_.Events.Sample,g),[2]}})})}),[{reportLabel:"peerConnection",type:"pcconnection"},{reportLabel:"ice",type:"iceconnection"},{reportLabel:"dtls",type:"dtlstransport"},{reportLabel:"signaling",type:"signaling"}].forEach(function(g){var l=g.type,C=g.reportLabel,E="on"+l+"statechange",m=b._mediaHandler[E];b._mediaHandler[E]=function(w){var R=S._networkTiming[C]=S._networkTiming[C]||{start:0};w==="connecting"||w==="checking"?R.start=Date.now():(w==="connected"||w==="stable")&&!R.duration&&(R.end=Date.now(),R.duration=R.end-R.start),m(w)}})},Object.defineProperty(_.prototype,"callSid",{get:function(){return this._callSid},enumerable:!1,configurable:!0}),Object.defineProperty(_.prototype,"endTime",{get:function(){return this._endTime},enumerable:!1,configurable:!0}),Object.defineProperty(_.prototype,"latestSample",{get:function(){return this._latestSample},enumerable:!1,configurable:!0}),Object.defineProperty(_.prototype,"report",{get:function(){return this._report},enumerable:!1,configurable:!0}),Object.defineProperty(_.prototype,"startTime",{get:function(){return this._startTime},enumerable:!1,configurable:!0}),Object.defineProperty(_.prototype,"status",{get:function(){return this._status},enumerable:!1,configurable:!0}),_}(o.EventEmitter);n.PreflightTest=v,function(y){(function(_){_.Excellent="excellent",_.Great="great",_.Good="good",_.Fair="fair",_.Degraded="degraded"})(y.CallQuality||(y.CallQuality={})),function(_){_.Completed="completed",_.Connected="connected",_.Failed="failed",_.Sample="sample",_.Warning="warning"}(y.Events||(y.Events={})),function(_){_.Connecting="connecting",_.Connected="connected",_.Completed="completed",_.Failed="failed"}(y.Status||(y.Status={}))}(v=n.PreflightTest||(n.PreflightTest={})),n.PreflightTest=v}(Ct)),Ct}var sn={},Xr={};(function(n){var t=B&&B.__extends||function(){var g=function(l,C){return g=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(E,m){E.__proto__=m}||function(E,m){for(var w in m)Object.prototype.hasOwnProperty.call(m,w)&&(E[w]=m[w])},g(l,C)};return function(l,C){g(l,C);function E(){this.constructor=l}l.prototype=C===null?Object.create(C):(E.prototype=C.prototype,new E)}}(),e=B&&B.__assign||function(){return e=Object.assign||function(g){for(var l,C=1,E=arguments.length;C<E;C++){l=arguments[C];for(var m in l)Object.prototype.hasOwnProperty.call(l,m)&&(g[m]=l[m])}return g},e.apply(this,arguments)};Object.defineProperty(n,"__esModule",{value:!0}),n.WSTransportState=void 0;var r=ge,i=pt,o=oe,a=se,s=globalThis.WebSocket,d=1e4,u=5e3,p=15e3,f=15e3,v=1/0,y=1e3,_=2e4,b;(function(g){g.Connecting="connecting",g.Closed="closed",g.Open="open"})(b=n.WSTransportState||(n.WSTransportState={}));var S=function(g){t(l,g);function l(C,E){E===void 0&&(E={});var m=g.call(this)||this;return m.state=b.Closed,m._backoffStartTime={preferred:null,primary:null},m._connectedUri=null,m._log=new a.default("WSTransport"),m._shouldFallback=!1,m._uriIndex=0,m._moveUriIndex=function(){m._uriIndex++,m._uriIndex>=m._uris.length&&(m._uriIndex=0)},m._onSocketClose=function(w){if(m._log.error("Received websocket close event code: "+w.code+". Reason: "+w.reason),w.code===1006||w.code===1015){m.emit("error",{code:31005,message:w.reason||"Websocket connection to Twilio's signaling servers were unexpectedly ended. If this is happening consistently, there may be an issue resolving the hostname provided. If a region or an edge is being specified in Device setup, ensure it is valid.",twilioError:new o.SignalingErrors.ConnectionError});var R=m.state===b.Open||m._previousState===b.Open;(m._shouldFallback||!R)&&m._moveUriIndex(),m._shouldFallback=!0}m._closeSocket()},m._onSocketError=function(w){m._log.error("WebSocket received error: "+w.message),m.emit("error",{code:31e3,message:w.message||"WSTransport socket error",twilioError:new o.SignalingErrors.ConnectionDisconnected})},m._onSocketMessage=function(w){if(m._setHeartbeatTimeout(),m._socket&&w.data===`
`){m._socket.send(`
`),m._log.debug("heartbeat");return}w&&typeof w.data=="string"&&m._log.debug("Received: "+w.data),m.emit("message",w)},m._onSocketOpen=function(){m._log.info("WebSocket opened successfully."),m._timeOpened=Date.now(),m._shouldFallback=!1,m._setState(b.Open),clearTimeout(m._connectTimeout),m._resetBackoffs(),m._setHeartbeatTimeout(),m.emit("open")},m._options=e(e({},l.defaultConstructorOptions),E),m._uris=C,m._backoff=m._setupBackoffs(),m}return l.prototype.close=function(){this._log.info("WSTransport.close() called..."),this._close()},l.prototype.open=function(){if(this._log.info("WSTransport.open() called..."),this._socket&&(this._socket.readyState===s.CONNECTING||this._socket.readyState===s.OPEN)){this._log.info("WebSocket already open.");return}this._preferredUri?this._connect(this._preferredUri):this._connect(this._uris[this._uriIndex])},l.prototype.send=function(C){if(this._log.debug("Sending: "+C),!this._socket||this._socket.readyState!==s.OPEN)return this._log.debug("Cannot send message. WebSocket is not open."),!1;try{this._socket.send(C)}catch(E){return this._log.error("Error while sending message:",E.message),this._closeSocket(),!1}return!0},l.prototype.updatePreferredURI=function(C){this._preferredUri=C},l.prototype.updateURIs=function(C){typeof C=="string"&&(C=[C]),this._uris=C,this._uriIndex=0},l.prototype._close=function(){this._setState(b.Closed),this._closeSocket()},l.prototype._closeSocket=function(){if(clearTimeout(this._connectTimeout),clearTimeout(this._heartbeatTimeout),this._log.info("Closing and cleaning up WebSocket..."),!this._socket){this._log.info("No WebSocket to clean up.");return}this._socket.removeEventListener("close",this._onSocketClose),this._socket.removeEventListener("error",this._onSocketError),this._socket.removeEventListener("message",this._onSocketMessage),this._socket.removeEventListener("open",this._onSocketOpen),(this._socket.readyState===s.CONNECTING||this._socket.readyState===s.OPEN)&&this._socket.close(),this._timeOpened&&Date.now()-this._timeOpened>d&&this._resetBackoffs(),this.state!==b.Closed&&this._performBackoff(),delete this._socket,this.emit("close")},l.prototype._connect=function(C,E){var m=this;this._log.info(typeof E=="number"?"Attempting to reconnect (retry #"+E+")...":"Attempting to connect..."),this._closeSocket(),this._setState(b.Connecting),this._connectedUri=C;try{this._socket=new this._options.WebSocket(this._connectedUri)}catch(w){this._log.error("Could not connect to endpoint:",w.message),this._close(),this.emit("error",{code:31e3,message:w.message||"Could not connect to "+this._connectedUri,twilioError:new o.SignalingErrors.ConnectionDisconnected});return}this._socket.addEventListener("close",this._onSocketClose),this._socket.addEventListener("error",this._onSocketError),this._socket.addEventListener("message",this._onSocketMessage),this._socket.addEventListener("open",this._onSocketOpen),delete this._timeOpened,this._connectTimeout=setTimeout(function(){m._log.info("WebSocket connection attempt timed out."),m._moveUriIndex(),m._closeSocket()},this._options.connectTimeoutMs)},l.prototype._performBackoff=function(){this._preferredUri?(this._log.info("Preferred URI set; backing off."),this._backoff.preferred.backoff()):(this._log.info("Preferred URI not set; backing off."),this._backoff.primary.backoff())},l.prototype._resetBackoffs=function(){this._backoff.preferred.reset(),this._backoff.primary.reset(),this._backoffStartTime.preferred=null,this._backoffStartTime.primary=null},l.prototype._setHeartbeatTimeout=function(){var C=this;clearTimeout(this._heartbeatTimeout),this._heartbeatTimeout=setTimeout(function(){C._log.info("No messages received in "+p/1e3+" seconds. Reconnecting..."),C._shouldFallback=!0,C._closeSocket()},p)},l.prototype._setState=function(C){this._previousState=this.state,this.state=C},l.prototype._setupBackoffs=function(){var C=this,E={factor:2,jitter:.4,max:this._options.maxPreferredDelayMs,min:100};this._log.info("Initializing preferred transport backoff using config: ",E);var m=new i.default(E);m.on("backoff",function(h,j){if(C.state===b.Closed){C._log.info("Preferred backoff initiated but transport state is closed; not attempting a connection.");return}C._log.info("Will attempt to reconnect Websocket to preferred URI in "+j+"ms"),h===0&&(C._backoffStartTime.preferred=Date.now(),C._log.info("Preferred backoff start; "+C._backoffStartTime.preferred))}),m.on("ready",function(h,j){if(C.state===b.Closed){C._log.info("Preferred backoff ready but transport state is closed; not attempting a connection.");return}if(C._backoffStartTime.preferred===null){C._log.info("Preferred backoff start time invalid; not attempting a connection.");return}if(Date.now()-C._backoffStartTime.preferred>C._options.maxPreferredDurationMs){C._log.info("Max preferred backoff attempt time exceeded; falling back to primary backoff."),C._preferredUri=null,C._backoff.primary.backoff();return}if(typeof C._preferredUri!="string"){C._log.info("Preferred URI cleared; falling back to primary backoff."),C._preferredUri=null,C._backoff.primary.backoff();return}C._connect(C._preferredUri,h+1)});var w={factor:2,jitter:.4,max:this._options.maxPrimaryDelayMs,min:this._uris&&this._uris.length>1?Math.floor(Math.random()*4001)+1e3:100};this._log.info("Initializing primary transport backoff using config: ",w);var R=new i.default(w);return R.on("backoff",function(h,j){if(C.state===b.Closed){C._log.info("Primary backoff initiated but transport state is closed; not attempting a connection.");return}C._log.info("Will attempt to reconnect WebSocket in "+j+"ms"),h===0&&(C._backoffStartTime.primary=Date.now(),C._log.info("Primary backoff start; "+C._backoffStartTime.primary))}),R.on("ready",function(h,j){if(C.state===b.Closed){C._log.info("Primary backoff ready but transport state is closed; not attempting a connection.");return}if(C._backoffStartTime.primary===null){C._log.info("Primary backoff start time invalid; not attempting a connection.");return}if(Date.now()-C._backoffStartTime.primary>C._options.maxPrimaryDurationMs){C._log.info("Max primary backoff attempt time exceeded; not attempting a connection.");return}C._connect(C._uris[C._uriIndex],h+1)}),{preferred:m,primary:R}},Object.defineProperty(l.prototype,"uri",{get:function(){return this._connectedUri},enumerable:!1,configurable:!0}),l.defaultConstructorOptions={WebSocket:s,connectTimeoutMs:u,maxPreferredDelayMs:y,maxPreferredDurationMs:f,maxPrimaryDelayMs:_,maxPrimaryDurationMs:v},l}(r.EventEmitter);n.default=S})(Xr);var ia=B&&B.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var o in i)Object.prototype.hasOwnProperty.call(i,o)&&(r[o]=i[o])},n(t,e)};return function(t,e){n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}();Object.defineProperty(sn,"__esModule",{value:!0});var oa=ge,sa=ie,an=oe,aa=se,ca=Xr,ua="1.6",la=30,ee=function(n){ia(t,n);function t(e,r,i){var o=n.call(this)||this;if(!(o instanceof t))return new t(e,r,i);var a={TransportFactory:ca.default};i=i||{};for(var s in a)s in i||(i[s]=a[s]);o.options=i,o.token=e||"",o.status="disconnected",o.gateway=null,o.region=null,o._messageQueue=[],o._preferredUri=null,o._uris=r,o._handleTransportClose=o._handleTransportClose.bind(o),o._handleTransportError=o._handleTransportError.bind(o),o._handleTransportMessage=o._handleTransportMessage.bind(o),o._handleTransportOpen=o._handleTransportOpen.bind(o),o._log=new aa.default("PStream"),o.on("error",function(){o._log.warn("Unexpected error handled in pstream")});var d=o;return o.addListener("ready",function(){d.status="ready"}),o.addListener("offline",function(){d.status="offline"}),o.addListener("close",function(){d._log.info('Received "close" from server. Destroying PStream...'),d._destroy()}),o.transport=new o.options.TransportFactory(o._uris,{backoffMaxMs:o.options.backoffMaxMs,maxPreferredDurationMs:o.options.maxPreferredDurationMs}),Object.defineProperties(o,{uri:{enumerable:!0,get:function(){return this.transport.uri}}}),o.transport.on("close",o._handleTransportClose),o.transport.on("error",o._handleTransportError),o.transport.on("message",o._handleTransportMessage),o.transport.on("open",o._handleTransportOpen),o.transport.open(),o}return t}(oa.EventEmitter);ee.prototype._handleTransportClose=function(){this.emit("transportClose"),this.status!=="disconnected"&&(this.status!=="offline"&&this.emit("offline",this),this.status="disconnected")};ee.prototype._handleTransportError=function(n){if(!n){this.emit("error",{error:{code:31e3,message:"Websocket closed without a provided reason",twilioError:new an.SignalingErrors.ConnectionDisconnected}});return}this.emit("error",typeof n.code<"u"?{error:n}:n)};ee.prototype._handleTransportMessage=function(n){if(!(!n||!n.data||typeof n.data!="string")){var t=JSON.parse(n.data),e=t.type,r=t.payload,i=r===void 0?{}:r;this.gateway=i.gateway||this.gateway,this.region=i.region||this.region,e==="error"&&i.error&&(i.error.twilioError=new an.SignalingErrors.ConnectionError),this.emit(e,i)}};ee.prototype._handleTransportOpen=function(){var n=this;this.status="connected",this.setToken(this.token),this.emit("transportOpen");var t=this._messageQueue.splice(0,this._messageQueue.length);t.forEach(function(e){return n._publish.apply(n,e)})};ee.toString=function(){return"[Twilio.PStream class]"};ee.prototype.toString=function(){return"[Twilio.PStream instance]"};ee.prototype.setToken=function(n){this._log.info("Setting token and publishing listen"),this.token=n;var t=0,e=this.options.maxPreferredDurationMs;this._log.info("maxPreferredDurationMs:"+e),typeof e=="number"&&e>=0&&(t=Math.min(Math.ceil(e/1e3),la)),this._log.info("reconnectTimeout:"+t);var r={browserinfo:da(),reconnectTimeout:t,token:n};this._publish("listen",r)};ee.prototype.sendMessage=function(n,t,e,r,i){e===void 0&&(e="application/json");var o={callsid:n,content:t,contenttype:e,messagetype:r,voiceeventsid:i};this._publish("message",o,!0)};ee.prototype.register=function(n){var t={media:n};this._publish("register",t,!0)};ee.prototype.invite=function(n,t,e){var r={callsid:t,sdp:n,twilio:e?{params:e}:{}};this._publish("invite",r,!0)};ee.prototype.reconnect=function(n,t,e){var r={callsid:t,reconnect:e,sdp:n,twilio:{}};this._publish("invite",r,!0)};ee.prototype.answer=function(n,t){this._publish("answer",{sdp:n,callsid:t},!0)};ee.prototype.dtmf=function(n,t){this._publish("dtmf",{callsid:n,dtmf:t},!0)};ee.prototype.hangup=function(n,t){var e=t?{callsid:n,message:t}:{callsid:n};this._publish("hangup",e,!0)};ee.prototype.reject=function(n){this._publish("reject",{callsid:n},!0)};ee.prototype.reinvite=function(n,t){this._publish("reinvite",{sdp:n,callsid:t},!1)};ee.prototype._destroy=function(){this.transport.removeListener("close",this._handleTransportClose),this.transport.removeListener("error",this._handleTransportError),this.transport.removeListener("message",this._handleTransportMessage),this.transport.removeListener("open",this._handleTransportOpen),this.transport.close(),this.emit("offline",this)};ee.prototype.destroy=function(){return this._log.info("PStream.destroy() called..."),this._destroy(),this};ee.prototype.updatePreferredURI=function(n){this._preferredUri=n,this.transport.updatePreferredURI(n)};ee.prototype.updateURIs=function(n){this._uris=n,this.transport.updateURIs(this._uris)};ee.prototype.publish=function(n,t){return this._publish(n,t,!0)};ee.prototype._publish=function(n,t,e){var r=JSON.stringify({payload:t,type:n,version:ua}),i=!!this.transport.send(r);i||(this.emit("error",{error:{code:31009,message:"No transport available to send or receive messages",twilioError:new an.GeneralErrors.TransportError}}),e&&this._messageQueue.push([n,t,!0]))};function da(){var n=typeof navigator<"u"?navigator:{},t={browser:{platform:n.platform||"unknown",userAgent:n.userAgent||"unknown"},p:"browser",plugin:"rtc",v:sa.RELEASE_VERSION};return t}sn.default=ee;var Qr={};(function(n){var t;Object.defineProperty(n,"__esModule",{value:!0}),n.getRegionShortcode=n.getChunderURIs=n.createSignalingEndpointURL=n.createEventGatewayURI=n.defaultEdge=n.regionToEdge=n.regionShortcodes=n.Region=n.Edge=void 0;var e=oe,r;(function(f){f.Sydney="sydney",f.SaoPaulo="sao-paulo",f.Dublin="dublin",f.Frankfurt="frankfurt",f.Tokyo="tokyo",f.Singapore="singapore",f.Ashburn="ashburn",f.Umatilla="umatilla",f.Roaming="roaming",f.AshburnIx="ashburn-ix",f.SanJoseIx="san-jose-ix",f.LondonIx="london-ix",f.FrankfurtIx="frankfurt-ix",f.SingaporeIx="singapore-ix",f.SydneyIx="sydney-ix",f.TokyoIx="tokyo-ix"})(r=n.Edge||(n.Edge={}));var i;(function(f){f.Au1="au1",f.Au1Ix="au1-ix",f.Br1="br1",f.De1="de1",f.De1Ix="de1-ix",f.Gll="gll",f.Ie1="ie1",f.Ie1Ix="ie1-ix",f.Ie1Tnx="ie1-tnx",f.Jp1="jp1",f.Jp1Ix="jp1-ix",f.Sg1="sg1",f.Sg1Ix="sg1-ix",f.Sg1Tnx="sg1-tnx",f.Us1="us1",f.Us1Ix="us1-ix",f.Us1Tnx="us1-tnx",f.Us2="us2",f.Us2Ix="us2-ix",f.Us2Tnx="us2-tnx"})(i=n.Region||(n.Region={})),n.regionShortcodes={ASIAPAC_SINGAPORE:i.Sg1,ASIAPAC_SYDNEY:i.Au1,ASIAPAC_TOKYO:i.Jp1,EU_FRANKFURT:i.De1,EU_IRELAND:i.Ie1,SOUTH_AMERICA_SAO_PAULO:i.Br1,US_EAST_VIRGINIA:i.Us1,US_WEST_OREGON:i.Us2},n.regionToEdge=(t={},t[i.Au1]=r.Sydney,t[i.Br1]=r.SaoPaulo,t[i.Ie1]=r.Dublin,t[i.De1]=r.Frankfurt,t[i.Jp1]=r.Tokyo,t[i.Sg1]=r.Singapore,t[i.Us1]=r.Ashburn,t[i.Us2]=r.Umatilla,t[i.Gll]=r.Roaming,t[i.Us1Ix]=r.AshburnIx,t[i.Us2Ix]=r.SanJoseIx,t[i.Ie1Ix]=r.LondonIx,t[i.De1Ix]=r.FrankfurtIx,t[i.Sg1Ix]=r.SingaporeIx,t[i.Au1Ix]=r.SydneyIx,t[i.Jp1Ix]=r.TokyoIx,t[i.Us1Tnx]=r.AshburnIx,t[i.Us2Tnx]=r.AshburnIx,t[i.Ie1Tnx]=r.LondonIx,t[i.Sg1Tnx]=r.SingaporeIx,t),n.defaultEdge=r.Roaming;var o="eventgw.twilio.com";function a(f){return"voice-js."+f+".twilio.com"}function s(f){return f?"eventgw."+f+".twilio.com":o}n.createEventGatewayURI=s;function d(f){return"wss://"+f+"/signal"}n.createSignalingEndpointURL=d;function u(f){if(f&&typeof f!="string"&&!Array.isArray(f))throw new e.InvalidArgumentError("If `edge` is provided, it must be of type `string` or an array of strings.");var v;if(f){var y=Array.isArray(f)?f:[f];v=y.map(function(_){return a(_)})}else v=[a(n.defaultEdge)];return v}n.getChunderURIs=u;function p(f){return n.regionShortcodes[f]||null}n.getRegionShortcode=p})(Qr);var _e={},cn={},gt={},de={};Object.defineProperty(de,"__esModule",{value:!0});de.setMaxAverageBitrate=de.setIceAggressiveNomination=de.setCodecPreferences=de.getPreferredCodecInfo=void 0;var rt=K,Jn={0:"PCMU",8:"PCMA"},fa=111,pa=51e4,ha=6e3;function ga(n){var t=/a=rtpmap:(\d+) (\S+)/m.exec(n)||[null,"",""],e=t[1],r=t[2],i=new RegExp("a=fmtp:"+e+" (\\S+)","m"),o=i.exec(n)||[null,""],a=o[1];return{codecName:r,codecParams:a}}de.getPreferredCodecInfo=ga;function ma(n){return rt.isChrome(window,window.navigator)?n.split(`
`).filter(function(t){return t.indexOf("a=ice-lite")===-1}).join(`
`):n}de.setIceAggressiveNomination=ma;function va(n,t){if(typeof t!="number"||t<ha||t>pa)return n;var e=/a=rtpmap:(\d+) opus/m.exec(n),r=e&&e.length?e[1]:fa,i=new RegExp("a=fmtp:"+r),o=n.split(`
`).map(function(a){return i.test(a)?a+(";maxaveragebitrate="+t):a});return o.join(`
`)}de.setMaxAverageBitrate=va;function _a(n,t){var e=ya(n),r=n.split(`\r
m=`)[0];return[r].concat(e.map(function(i){if(!/^m=(audio|video)/.test(i))return i;var o=i.match(/^m=(audio|video)/)[1],a=ba(i),s=Sa(a,t),d=wa(s,i),u=a.get("pcma")||[],p=a.get("pcmu")||[],f=o==="audio"?new Set(u.concat(p)):new Set;return f.has(s[0])?d.replace(/\r\nb=(AS|TIAS):([0-9]+)/g,""):d})).join(`\r
`)}de.setCodecPreferences=_a;function ya(n,t,e){return n.replace(/\r\n\r\n$/,`\r
`).split(`\r
m=`).slice(1).map(function(r){return"m="+r}).filter(function(r){var i=new RegExp("m=.*","gm"),o=new RegExp("a=.*","gm");return i.test(r)&&o.test(r)})}function ba(n){return Array.from(Ca(n)).reduce(function(t,e){var r=e[0],i=e[1],o=t.get(i)||[];return t.set(i,o.concat(r))},new Map)}function Sa(n,t){t=t.map(function(o){return o.toLowerCase()});var e=rt.flatMap(t,function(o){return n.get(o)||[]}),r=rt.difference(Array.from(n.keys()),t),i=rt.flatMap(r,function(o){return n.get(o)});return e.concat(i)}function wa(n,t){var e=t.split(`\r
`),r=e[0],i=e.slice(1);return r=r.replace(/([0-9]+\s?)+$/,n.join(" ")),[r].concat(i).join(`\r
`)}function Ca(n){return Ea(n).reduce(function(t,e){var r=new RegExp("a=rtpmap:"+e+" ([^/]+)"),i=n.match(r),o=i?i[1].toLowerCase():Jn[e]?Jn[e].toLowerCase():"";return t.set(e,o)},new Map)}function Ea(n){var t=n.split(`\r
`)[0],e=t.match(/([0-9]+)/g);return e?e.slice(1).map(function(r){return parseInt(r,10)}):[]}var Zr={exports:{}};(function(n){var t={};t.generateIdentifier=function(){return Math.random().toString(36).substr(2,10)},t.localCName=t.generateIdentifier(),t.splitLines=function(e){return e.trim().split(`
`).map(function(r){return r.trim()})},t.splitSections=function(e){var r=e.split(`
m=`);return r.map(function(i,o){return(o>0?"m="+i:i).trim()+`\r
`})},t.getDescription=function(e){var r=t.splitSections(e);return r&&r[0]},t.getMediaSections=function(e){var r=t.splitSections(e);return r.shift(),r},t.matchPrefix=function(e,r){return t.splitLines(e).filter(function(i){return i.indexOf(r)===0})},t.parseCandidate=function(e){var r;e.indexOf("a=candidate:")===0?r=e.substring(12).split(" "):r=e.substring(10).split(" ");for(var i={foundation:r[0],component:parseInt(r[1],10),protocol:r[2].toLowerCase(),priority:parseInt(r[3],10),ip:r[4],address:r[4],port:parseInt(r[5],10),type:r[7]},o=8;o<r.length;o+=2)switch(r[o]){case"raddr":i.relatedAddress=r[o+1];break;case"rport":i.relatedPort=parseInt(r[o+1],10);break;case"tcptype":i.tcpType=r[o+1];break;case"ufrag":i.ufrag=r[o+1],i.usernameFragment=r[o+1];break;default:i[r[o]]=r[o+1];break}return i},t.writeCandidate=function(e){var r=[];r.push(e.foundation),r.push(e.component),r.push(e.protocol.toUpperCase()),r.push(e.priority),r.push(e.address||e.ip),r.push(e.port);var i=e.type;return r.push("typ"),r.push(i),i!=="host"&&e.relatedAddress&&e.relatedPort&&(r.push("raddr"),r.push(e.relatedAddress),r.push("rport"),r.push(e.relatedPort)),e.tcpType&&e.protocol.toLowerCase()==="tcp"&&(r.push("tcptype"),r.push(e.tcpType)),(e.usernameFragment||e.ufrag)&&(r.push("ufrag"),r.push(e.usernameFragment||e.ufrag)),"candidate:"+r.join(" ")},t.parseIceOptions=function(e){return e.substr(14).split(" ")},t.parseRtpMap=function(e){var r=e.substr(9).split(" "),i={payloadType:parseInt(r.shift(),10)};return r=r[0].split("/"),i.name=r[0],i.clockRate=parseInt(r[1],10),i.channels=r.length===3?parseInt(r[2],10):1,i.numChannels=i.channels,i},t.writeRtpMap=function(e){var r=e.payloadType;e.preferredPayloadType!==void 0&&(r=e.preferredPayloadType);var i=e.channels||e.numChannels||1;return"a=rtpmap:"+r+" "+e.name+"/"+e.clockRate+(i!==1?"/"+i:"")+`\r
`},t.parseExtmap=function(e){var r=e.substr(9).split(" ");return{id:parseInt(r[0],10),direction:r[0].indexOf("/")>0?r[0].split("/")[1]:"sendrecv",uri:r[1]}},t.writeExtmap=function(e){return"a=extmap:"+(e.id||e.preferredId)+(e.direction&&e.direction!=="sendrecv"?"/"+e.direction:"")+" "+e.uri+`\r
`},t.parseFmtp=function(e){for(var r={},i,o=e.substr(e.indexOf(" ")+1).split(";"),a=0;a<o.length;a++)i=o[a].trim().split("="),r[i[0].trim()]=i[1];return r},t.writeFmtp=function(e){var r="",i=e.payloadType;if(e.preferredPayloadType!==void 0&&(i=e.preferredPayloadType),e.parameters&&Object.keys(e.parameters).length){var o=[];Object.keys(e.parameters).forEach(function(a){e.parameters[a]?o.push(a+"="+e.parameters[a]):o.push(a)}),r+="a=fmtp:"+i+" "+o.join(";")+`\r
`}return r},t.parseRtcpFb=function(e){var r=e.substr(e.indexOf(" ")+1).split(" ");return{type:r.shift(),parameter:r.join(" ")}},t.writeRtcpFb=function(e){var r="",i=e.payloadType;return e.preferredPayloadType!==void 0&&(i=e.preferredPayloadType),e.rtcpFeedback&&e.rtcpFeedback.length&&e.rtcpFeedback.forEach(function(o){r+="a=rtcp-fb:"+i+" "+o.type+(o.parameter&&o.parameter.length?" "+o.parameter:"")+`\r
`}),r},t.parseSsrcMedia=function(e){var r=e.indexOf(" "),i={ssrc:parseInt(e.substr(7,r-7),10)},o=e.indexOf(":",r);return o>-1?(i.attribute=e.substr(r+1,o-r-1),i.value=e.substr(o+1)):i.attribute=e.substr(r+1),i},t.parseSsrcGroup=function(e){var r=e.substr(13).split(" ");return{semantics:r.shift(),ssrcs:r.map(function(i){return parseInt(i,10)})}},t.getMid=function(e){var r=t.matchPrefix(e,"a=mid:")[0];if(r)return r.substr(6)},t.parseFingerprint=function(e){var r=e.substr(14).split(" ");return{algorithm:r[0].toLowerCase(),value:r[1]}},t.getDtlsParameters=function(e,r){var i=t.matchPrefix(e+r,"a=fingerprint:");return{role:"auto",fingerprints:i.map(t.parseFingerprint)}},t.writeDtlsParameters=function(e,r){var i="a=setup:"+r+`\r
`;return e.fingerprints.forEach(function(o){i+="a=fingerprint:"+o.algorithm+" "+o.value+`\r
`}),i},t.parseCryptoLine=function(e){var r=e.substr(9).split(" ");return{tag:parseInt(r[0],10),cryptoSuite:r[1],keyParams:r[2],sessionParams:r.slice(3)}},t.writeCryptoLine=function(e){return"a=crypto:"+e.tag+" "+e.cryptoSuite+" "+(typeof e.keyParams=="object"?t.writeCryptoKeyParams(e.keyParams):e.keyParams)+(e.sessionParams?" "+e.sessionParams.join(" "):"")+`\r
`},t.parseCryptoKeyParams=function(e){if(e.indexOf("inline:")!==0)return null;var r=e.substr(7).split("|");return{keyMethod:"inline",keySalt:r[0],lifeTime:r[1],mkiValue:r[2]?r[2].split(":")[0]:void 0,mkiLength:r[2]?r[2].split(":")[1]:void 0}},t.writeCryptoKeyParams=function(e){return e.keyMethod+":"+e.keySalt+(e.lifeTime?"|"+e.lifeTime:"")+(e.mkiValue&&e.mkiLength?"|"+e.mkiValue+":"+e.mkiLength:"")},t.getCryptoParameters=function(e,r){var i=t.matchPrefix(e+r,"a=crypto:");return i.map(t.parseCryptoLine)},t.getIceParameters=function(e,r){var i=t.matchPrefix(e+r,"a=ice-ufrag:")[0],o=t.matchPrefix(e+r,"a=ice-pwd:")[0];return i&&o?{usernameFragment:i.substr(12),password:o.substr(10)}:null},t.writeIceParameters=function(e){return"a=ice-ufrag:"+e.usernameFragment+`\r
a=ice-pwd:`+e.password+`\r
`},t.parseRtpParameters=function(e){for(var r={codecs:[],headerExtensions:[],fecMechanisms:[],rtcp:[]},i=t.splitLines(e),o=i[0].split(" "),a=3;a<o.length;a++){var s=o[a],d=t.matchPrefix(e,"a=rtpmap:"+s+" ")[0];if(d){var u=t.parseRtpMap(d),p=t.matchPrefix(e,"a=fmtp:"+s+" ");switch(u.parameters=p.length?t.parseFmtp(p[0]):{},u.rtcpFeedback=t.matchPrefix(e,"a=rtcp-fb:"+s+" ").map(t.parseRtcpFb),r.codecs.push(u),u.name.toUpperCase()){case"RED":case"ULPFEC":r.fecMechanisms.push(u.name.toUpperCase());break}}}return t.matchPrefix(e,"a=extmap:").forEach(function(f){r.headerExtensions.push(t.parseExtmap(f))}),r},t.writeRtpDescription=function(e,r){var i="";i+="m="+e+" ",i+=r.codecs.length>0?"9":"0",i+=" UDP/TLS/RTP/SAVPF ",i+=r.codecs.map(function(a){return a.preferredPayloadType!==void 0?a.preferredPayloadType:a.payloadType}).join(" ")+`\r
`,i+=`c=IN IP4 0.0.0.0\r
`,i+=`a=rtcp:9 IN IP4 0.0.0.0\r
`,r.codecs.forEach(function(a){i+=t.writeRtpMap(a),i+=t.writeFmtp(a),i+=t.writeRtcpFb(a)});var o=0;return r.codecs.forEach(function(a){a.maxptime>o&&(o=a.maxptime)}),o>0&&(i+="a=maxptime:"+o+`\r
`),i+=`a=rtcp-mux\r
`,r.headerExtensions&&r.headerExtensions.forEach(function(a){i+=t.writeExtmap(a)}),i},t.parseRtpEncodingParameters=function(e){var r=[],i=t.parseRtpParameters(e),o=i.fecMechanisms.indexOf("RED")!==-1,a=i.fecMechanisms.indexOf("ULPFEC")!==-1,s=t.matchPrefix(e,"a=ssrc:").map(function(v){return t.parseSsrcMedia(v)}).filter(function(v){return v.attribute==="cname"}),d=s.length>0&&s[0].ssrc,u,p=t.matchPrefix(e,"a=ssrc-group:FID").map(function(v){var y=v.substr(17).split(" ");return y.map(function(_){return parseInt(_,10)})});p.length>0&&p[0].length>1&&p[0][0]===d&&(u=p[0][1]),i.codecs.forEach(function(v){if(v.name.toUpperCase()==="RTX"&&v.parameters.apt){var y={ssrc:d,codecPayloadType:parseInt(v.parameters.apt,10)};d&&u&&(y.rtx={ssrc:u}),r.push(y),o&&(y=JSON.parse(JSON.stringify(y)),y.fec={ssrc:d,mechanism:a?"red+ulpfec":"red"},r.push(y))}}),r.length===0&&d&&r.push({ssrc:d});var f=t.matchPrefix(e,"b=");return f.length&&(f[0].indexOf("b=TIAS:")===0?f=parseInt(f[0].substr(7),10):f[0].indexOf("b=AS:")===0?f=parseInt(f[0].substr(5),10)*1e3*.95-50*40*8:f=void 0,r.forEach(function(v){v.maxBitrate=f})),r},t.parseRtcpParameters=function(e){var r={},i=t.matchPrefix(e,"a=ssrc:").map(function(s){return t.parseSsrcMedia(s)}).filter(function(s){return s.attribute==="cname"})[0];i&&(r.cname=i.value,r.ssrc=i.ssrc);var o=t.matchPrefix(e,"a=rtcp-rsize");r.reducedSize=o.length>0,r.compound=o.length===0;var a=t.matchPrefix(e,"a=rtcp-mux");return r.mux=a.length>0,r},t.parseMsid=function(e){var r,i=t.matchPrefix(e,"a=msid:");if(i.length===1)return r=i[0].substr(7).split(" "),{stream:r[0],track:r[1]};var o=t.matchPrefix(e,"a=ssrc:").map(function(a){return t.parseSsrcMedia(a)}).filter(function(a){return a.attribute==="msid"});if(o.length>0)return r=o[0].value.split(" "),{stream:r[0],track:r[1]}},t.parseSctpDescription=function(e){var r=t.parseMLine(e),i=t.matchPrefix(e,"a=max-message-size:"),o;i.length>0&&(o=parseInt(i[0].substr(19),10)),isNaN(o)&&(o=65536);var a=t.matchPrefix(e,"a=sctp-port:");if(a.length>0)return{port:parseInt(a[0].substr(12),10),protocol:r.fmt,maxMessageSize:o};var s=t.matchPrefix(e,"a=sctpmap:");if(s.length>0){var d=t.matchPrefix(e,"a=sctpmap:")[0].substr(10).split(" ");return{port:parseInt(d[0],10),protocol:d[1],maxMessageSize:o}}},t.writeSctpDescription=function(e,r){var i=[];return e.protocol!=="DTLS/SCTP"?i=["m="+e.kind+" 9 "+e.protocol+" "+r.protocol+`\r
`,`c=IN IP4 0.0.0.0\r
`,"a=sctp-port:"+r.port+`\r
`]:i=["m="+e.kind+" 9 "+e.protocol+" "+r.port+`\r
`,`c=IN IP4 0.0.0.0\r
`,"a=sctpmap:"+r.port+" "+r.protocol+` 65535\r
`],r.maxMessageSize!==void 0&&i.push("a=max-message-size:"+r.maxMessageSize+`\r
`),i.join("")},t.generateSessionId=function(){return Math.random().toString().substr(2,21)},t.writeSessionBoilerplate=function(e,r,i){var o,a=r!==void 0?r:2;e?o=e:o=t.generateSessionId();var s=i||"thisisadapterortc";return`v=0\r
o=`+s+" "+o+" "+a+` IN IP4 127.0.0.1\r
s=-\r
t=0 0\r
`},t.writeMediaSection=function(e,r,i,o){var a=t.writeRtpDescription(e.kind,r);if(a+=t.writeIceParameters(e.iceGatherer.getLocalParameters()),a+=t.writeDtlsParameters(e.dtlsTransport.getLocalParameters(),i==="offer"?"actpass":"active"),a+="a=mid:"+e.mid+`\r
`,e.direction?a+="a="+e.direction+`\r
`:e.rtpSender&&e.rtpReceiver?a+=`a=sendrecv\r
`:e.rtpSender?a+=`a=sendonly\r
`:e.rtpReceiver?a+=`a=recvonly\r
`:a+=`a=inactive\r
`,e.rtpSender){var s="msid:"+o.id+" "+e.rtpSender.track.id+`\r
`;a+="a="+s,a+="a=ssrc:"+e.sendEncodingParameters[0].ssrc+" "+s,e.sendEncodingParameters[0].rtx&&(a+="a=ssrc:"+e.sendEncodingParameters[0].rtx.ssrc+" "+s,a+="a=ssrc-group:FID "+e.sendEncodingParameters[0].ssrc+" "+e.sendEncodingParameters[0].rtx.ssrc+`\r
`)}return a+="a=ssrc:"+e.sendEncodingParameters[0].ssrc+" cname:"+t.localCName+`\r
`,e.rtpSender&&e.sendEncodingParameters[0].rtx&&(a+="a=ssrc:"+e.sendEncodingParameters[0].rtx.ssrc+" cname:"+t.localCName+`\r
`),a},t.getDirection=function(e,r){for(var i=t.splitLines(e),o=0;o<i.length;o++)switch(i[o]){case"a=sendrecv":case"a=sendonly":case"a=recvonly":case"a=inactive":return i[o].substr(2)}return r?t.getDirection(r):"sendrecv"},t.getKind=function(e){var r=t.splitLines(e),i=r[0].split(" ");return i[0].substr(2)},t.isRejected=function(e){return e.split(" ",2)[1]==="0"},t.parseMLine=function(e){var r=t.splitLines(e),i=r[0].substr(2).split(" ");return{kind:i[0],port:parseInt(i[1],10),protocol:i[2],fmt:i.slice(3).join(" ")}},t.parseOLine=function(e){var r=t.matchPrefix(e,"o=")[0],i=r.substr(2).split(" ");return{username:i[0],sessionId:i[1],sessionVersion:parseInt(i[2],10),netType:i[3],addressType:i[4],address:i[5]}},t.isValidSDP=function(e){if(typeof e!="string"||e.length===0)return!1;for(var r=t.splitLines(e),i=0;i<r.length;i++)if(r[i].length<2||r[i].charAt(1)!=="=")return!1;return!0},n.exports=t})(Zr);var Ta=Zr.exports,H=Ta;function Kn(n,t,e,r,i){var o=H.writeRtpDescription(n.kind,t);if(o+=H.writeIceParameters(n.iceGatherer.getLocalParameters()),o+=H.writeDtlsParameters(n.dtlsTransport.getLocalParameters(),e==="offer"?"actpass":i||"active"),o+="a=mid:"+n.mid+`\r
`,n.rtpSender&&n.rtpReceiver?o+=`a=sendrecv\r
`:n.rtpSender?o+=`a=sendonly\r
`:n.rtpReceiver?o+=`a=recvonly\r
`:o+=`a=inactive\r
`,n.rtpSender){var a=n.rtpSender._initialTrackId||n.rtpSender.track.id;n.rtpSender._initialTrackId=a;var s="msid:"+(r?r.id:"-")+" "+a+`\r
`;o+="a="+s,o+="a=ssrc:"+n.sendEncodingParameters[0].ssrc+" "+s,n.sendEncodingParameters[0].rtx&&(o+="a=ssrc:"+n.sendEncodingParameters[0].rtx.ssrc+" "+s,o+="a=ssrc-group:FID "+n.sendEncodingParameters[0].ssrc+" "+n.sendEncodingParameters[0].rtx.ssrc+`\r
`)}return o+="a=ssrc:"+n.sendEncodingParameters[0].ssrc+" cname:"+H.localCName+`\r
`,n.rtpSender&&n.sendEncodingParameters[0].rtx&&(o+="a=ssrc:"+n.sendEncodingParameters[0].rtx.ssrc+" cname:"+H.localCName+`\r
`),o}function xa(n,t){var e=!1;return n=JSON.parse(JSON.stringify(n)),n.filter(function(r){if(r&&(r.urls||r.url)){var i=r.urls||r.url;r.url&&!r.urls&&console.warn("RTCIceServer.url is deprecated! Use urls instead.");var o=typeof i=="string";return o&&(i=[i]),i=i.filter(function(a){var s=a.indexOf("turn:")===0&&a.indexOf("transport=udp")!==-1&&a.indexOf("turn:[")===-1&&!e;return s?(e=!0,!0):a.indexOf("stun:")===0&&t>=14393&&a.indexOf("?transport=udp")===-1}),delete r.url,r.urls=o?i[0]:i,!!i.length}})}function Et(n,t){var e={codecs:[],headerExtensions:[],fecMechanisms:[]},r=function(o,a){o=parseInt(o,10);for(var s=0;s<a.length;s++)if(a[s].payloadType===o||a[s].preferredPayloadType===o)return a[s]},i=function(o,a,s,d){var u=r(o.parameters.apt,s),p=r(a.parameters.apt,d);return u&&p&&u.name.toLowerCase()===p.name.toLowerCase()};return n.codecs.forEach(function(o){for(var a=0;a<t.codecs.length;a++){var s=t.codecs[a];if(o.name.toLowerCase()===s.name.toLowerCase()&&o.clockRate===s.clockRate){if(o.name.toLowerCase()==="rtx"&&o.parameters&&s.parameters.apt&&!i(o,s,n.codecs,t.codecs))continue;s=JSON.parse(JSON.stringify(s)),s.numChannels=Math.min(o.numChannels,s.numChannels),e.codecs.push(s),s.rtcpFeedback=s.rtcpFeedback.filter(function(d){for(var u=0;u<o.rtcpFeedback.length;u++)if(o.rtcpFeedback[u].type===d.type&&o.rtcpFeedback[u].parameter===d.parameter)return!0;return!1});break}}}),n.headerExtensions.forEach(function(o){for(var a=0;a<t.headerExtensions.length;a++){var s=t.headerExtensions[a];if(o.uri===s.uri){e.headerExtensions.push(s);break}}}),e}function Yn(n,t,e){return{offer:{setLocalDescription:["stable","have-local-offer"],setRemoteDescription:["stable","have-remote-offer"]},answer:{setLocalDescription:["have-remote-offer","have-local-pranswer"],setRemoteDescription:["have-local-offer","have-remote-pranswer"]}}[t][n].indexOf(e)!==-1}function Tt(n,t){var e=n.getRemoteCandidates().find(function(r){return t.foundation===r.foundation&&t.ip===r.ip&&t.port===r.port&&t.priority===r.priority&&t.protocol===r.protocol&&t.type===r.type});return e||n.addRemoteCandidate(t),!e}function ce(n,t){var e=new Error(t);return e.name=n,e}var Pa=function(n,t){function e(s,d){d.addTrack(s),d.dispatchEvent(new n.MediaStreamTrackEvent("addtrack",{track:s}))}function r(s,d){d.removeTrack(s),d.dispatchEvent(new n.MediaStreamTrackEvent("removetrack",{track:s}))}function i(s,d,u,p){var f=new Event("track");f.track=d,f.receiver=u,f.transceiver={receiver:u},f.streams=p,n.setTimeout(function(){s._dispatchEvent("track",f)})}var o=function(s){var d=this,u=document.createDocumentFragment();if(["addEventListener","removeEventListener","dispatchEvent"].forEach(function(f){d[f]=u[f].bind(u)}),this.canTrickleIceCandidates=null,this.needNegotiation=!1,this.localStreams=[],this.remoteStreams=[],this.localDescription=null,this.remoteDescription=null,this.signalingState="stable",this.iceConnectionState="new",this.iceGatheringState="new",s=JSON.parse(JSON.stringify(s||{})),this.usingBundle=s.bundlePolicy==="max-bundle",s.rtcpMuxPolicy==="negotiate")throw ce("NotSupportedError","rtcpMuxPolicy 'negotiate' is not supported");switch(s.rtcpMuxPolicy||(s.rtcpMuxPolicy="require"),s.iceTransportPolicy){case"all":case"relay":break;default:s.iceTransportPolicy="all";break}switch(s.bundlePolicy){case"balanced":case"max-compat":case"max-bundle":break;default:s.bundlePolicy="balanced";break}if(s.iceServers=xa(s.iceServers||[],t),this._iceGatherers=[],s.iceCandidatePoolSize)for(var p=s.iceCandidatePoolSize;p>0;p--)this._iceGatherers.push(new n.RTCIceGatherer({iceServers:s.iceServers,gatherPolicy:s.iceTransportPolicy}));else s.iceCandidatePoolSize=0;this._config=s,this.transceivers=[],this._sdpSessionId=H.generateSessionId(),this._sdpSessionVersion=0,this._dtlsRole=void 0,this._isClosed=!1};o.prototype.onicecandidate=null,o.prototype.onaddstream=null,o.prototype.ontrack=null,o.prototype.onremovestream=null,o.prototype.onsignalingstatechange=null,o.prototype.oniceconnectionstatechange=null,o.prototype.onicegatheringstatechange=null,o.prototype.onnegotiationneeded=null,o.prototype.ondatachannel=null,o.prototype._dispatchEvent=function(s,d){this._isClosed||(this.dispatchEvent(d),typeof this["on"+s]=="function"&&this["on"+s](d))},o.prototype._emitGatheringStateChange=function(){var s=new Event("icegatheringstatechange");this._dispatchEvent("icegatheringstatechange",s)},o.prototype.getConfiguration=function(){return this._config},o.prototype.getLocalStreams=function(){return this.localStreams},o.prototype.getRemoteStreams=function(){return this.remoteStreams},o.prototype._createTransceiver=function(s){var d=this.transceivers.length>0,u={track:null,iceGatherer:null,iceTransport:null,dtlsTransport:null,localCapabilities:null,remoteCapabilities:null,rtpSender:null,rtpReceiver:null,kind:s,mid:null,sendEncodingParameters:null,recvEncodingParameters:null,stream:null,associatedRemoteMediaStreams:[],wantReceive:!0};if(this.usingBundle&&d)u.iceTransport=this.transceivers[0].iceTransport,u.dtlsTransport=this.transceivers[0].dtlsTransport;else{var p=this._createIceAndDtlsTransports();u.iceTransport=p.iceTransport,u.dtlsTransport=p.dtlsTransport}return this.transceivers.push(u),u},o.prototype.addTrack=function(s,d){if(this._isClosed)throw ce("InvalidStateError","Attempted to call addTrack on a closed peerconnection.");var u=this.transceivers.find(function(v){return v.track===s});if(u)throw ce("InvalidAccessError","Track already exists.");for(var p,f=0;f<this.transceivers.length;f++)!this.transceivers[f].track&&this.transceivers[f].kind===s.kind&&(p=this.transceivers[f]);return p||(p=this._createTransceiver(s.kind)),this._maybeFireNegotiationNeeded(),this.localStreams.indexOf(d)===-1&&this.localStreams.push(d),p.track=s,p.stream=d,p.rtpSender=new n.RTCRtpSender(s,p.dtlsTransport),p.rtpSender},o.prototype.addStream=function(s){var d=this;if(t>=15025)s.getTracks().forEach(function(p){d.addTrack(p,s)});else{var u=s.clone();s.getTracks().forEach(function(p,f){var v=u.getTracks()[f];p.addEventListener("enabled",function(y){v.enabled=y.enabled})}),u.getTracks().forEach(function(p){d.addTrack(p,u)})}},o.prototype.removeTrack=function(s){if(this._isClosed)throw ce("InvalidStateError","Attempted to call removeTrack on a closed peerconnection.");if(!(s instanceof n.RTCRtpSender))throw new TypeError("Argument 1 of RTCPeerConnection.removeTrack does not implement interface RTCRtpSender.");var d=this.transceivers.find(function(f){return f.rtpSender===s});if(!d)throw ce("InvalidAccessError","Sender was not created by this connection.");var u=d.stream;d.rtpSender.stop(),d.rtpSender=null,d.track=null,d.stream=null;var p=this.transceivers.map(function(f){return f.stream});p.indexOf(u)===-1&&this.localStreams.indexOf(u)>-1&&this.localStreams.splice(this.localStreams.indexOf(u),1),this._maybeFireNegotiationNeeded()},o.prototype.removeStream=function(s){var d=this;s.getTracks().forEach(function(u){var p=d.getSenders().find(function(f){return f.track===u});p&&d.removeTrack(p)})},o.prototype.getSenders=function(){return this.transceivers.filter(function(s){return!!s.rtpSender}).map(function(s){return s.rtpSender})},o.prototype.getReceivers=function(){return this.transceivers.filter(function(s){return!!s.rtpReceiver}).map(function(s){return s.rtpReceiver})},o.prototype._createIceGatherer=function(s,d){var u=this;if(d&&s>0)return this.transceivers[0].iceGatherer;if(this._iceGatherers.length)return this._iceGatherers.shift();var p=new n.RTCIceGatherer({iceServers:this._config.iceServers,gatherPolicy:this._config.iceTransportPolicy});return Object.defineProperty(p,"state",{value:"new",writable:!0}),this.transceivers[s].bufferedCandidateEvents=[],this.transceivers[s].bufferCandidates=function(f){var v=!f.candidate||Object.keys(f.candidate).length===0;p.state=v?"completed":"gathering",u.transceivers[s].bufferedCandidateEvents!==null&&u.transceivers[s].bufferedCandidateEvents.push(f)},p.addEventListener("localcandidate",this.transceivers[s].bufferCandidates),p},o.prototype._gather=function(s,d){var u=this,p=this.transceivers[d].iceGatherer;if(!p.onlocalcandidate){var f=this.transceivers[d].bufferedCandidateEvents;this.transceivers[d].bufferedCandidateEvents=null,p.removeEventListener("localcandidate",this.transceivers[d].bufferCandidates),p.onlocalcandidate=function(v){if(!(u.usingBundle&&d>0)){var y=new Event("icecandidate");y.candidate={sdpMid:s,sdpMLineIndex:d};var _=v.candidate,b=!_||Object.keys(_).length===0;if(b)(p.state==="new"||p.state==="gathering")&&(p.state="completed");else{p.state==="new"&&(p.state="gathering"),_.component=1;var S=H.writeCandidate(_);y.candidate=Object.assign(y.candidate,H.parseCandidate(S)),y.candidate.candidate=S}var g=H.getMediaSections(u.localDescription.sdp);b?g[y.candidate.sdpMLineIndex]+=`a=end-of-candidates\r
`:g[y.candidate.sdpMLineIndex]+="a="+y.candidate.candidate+`\r
`,u.localDescription.sdp=H.getDescription(u.localDescription.sdp)+g.join("");var l=u.transceivers.every(function(C){return C.iceGatherer&&C.iceGatherer.state==="completed"});u.iceGatheringState!=="gathering"&&(u.iceGatheringState="gathering",u._emitGatheringStateChange()),b||u._dispatchEvent("icecandidate",y),l&&(u._dispatchEvent("icecandidate",new Event("icecandidate")),u.iceGatheringState="complete",u._emitGatheringStateChange())}},n.setTimeout(function(){f.forEach(function(v){p.onlocalcandidate(v)})},0)}},o.prototype._createIceAndDtlsTransports=function(){var s=this,d=new n.RTCIceTransport(null);d.onicestatechange=function(){s._updateConnectionState()};var u=new n.RTCDtlsTransport(d);return u.ondtlsstatechange=function(){s._updateConnectionState()},u.onerror=function(){Object.defineProperty(u,"state",{value:"failed",writable:!0}),s._updateConnectionState()},{iceTransport:d,dtlsTransport:u}},o.prototype._disposeIceAndDtlsTransports=function(s){var d=this.transceivers[s].iceGatherer;d&&(delete d.onlocalcandidate,delete this.transceivers[s].iceGatherer);var u=this.transceivers[s].iceTransport;u&&(delete u.onicestatechange,delete this.transceivers[s].iceTransport);var p=this.transceivers[s].dtlsTransport;p&&(delete p.ondtlsstatechange,delete p.onerror,delete this.transceivers[s].dtlsTransport)},o.prototype._transceive=function(s,d,u){var p=Et(s.localCapabilities,s.remoteCapabilities);d&&s.rtpSender&&(p.encodings=s.sendEncodingParameters,p.rtcp={cname:H.localCName,compound:s.rtcpParameters.compound},s.recvEncodingParameters.length&&(p.rtcp.ssrc=s.recvEncodingParameters[0].ssrc),s.rtpSender.send(p)),u&&s.rtpReceiver&&p.codecs.length>0&&(s.kind==="video"&&s.recvEncodingParameters&&t<15019&&s.recvEncodingParameters.forEach(function(f){delete f.rtx}),s.recvEncodingParameters.length?p.encodings=s.recvEncodingParameters:p.encodings=[{}],p.rtcp={compound:s.rtcpParameters.compound},s.rtcpParameters.cname&&(p.rtcp.cname=s.rtcpParameters.cname),s.sendEncodingParameters.length&&(p.rtcp.ssrc=s.sendEncodingParameters[0].ssrc),s.rtpReceiver.receive(p))},o.prototype.setLocalDescription=function(s){var d=this;if(["offer","answer"].indexOf(s.type)===-1)return Promise.reject(ce("TypeError",'Unsupported type "'+s.type+'"'));if(!Yn("setLocalDescription",s.type,d.signalingState)||d._isClosed)return Promise.reject(ce("InvalidStateError","Can not set local "+s.type+" in state "+d.signalingState));var u,p;if(s.type==="offer")u=H.splitSections(s.sdp),p=u.shift(),u.forEach(function(v,y){var _=H.parseRtpParameters(v);d.transceivers[y].localCapabilities=_}),d.transceivers.forEach(function(v,y){d._gather(v.mid,y)});else if(s.type==="answer"){u=H.splitSections(d.remoteDescription.sdp),p=u.shift();var f=H.matchPrefix(p,"a=ice-lite").length>0;u.forEach(function(v,y){var _=d.transceivers[y],b=_.iceGatherer,S=_.iceTransport,g=_.dtlsTransport,l=_.localCapabilities,C=_.remoteCapabilities,E=H.isRejected(v)&&H.matchPrefix(v,"a=bundle-only").length===0;if(!E&&!_.isDatachannel){var m=H.getIceParameters(v,p),w=H.getDtlsParameters(v,p);f&&(w.role="server"),(!d.usingBundle||y===0)&&(d._gather(_.mid,y),S.state==="new"&&S.start(b,m,f?"controlling":"controlled"),g.state==="new"&&g.start(w));var R=Et(l,C);d._transceive(_,R.codecs.length>0,!1)}})}return d.localDescription={type:s.type,sdp:s.sdp},s.type==="offer"?d._updateSignalingState("have-local-offer"):d._updateSignalingState("stable"),Promise.resolve()},o.prototype.setRemoteDescription=function(s){var d=this;if(["offer","answer"].indexOf(s.type)===-1)return Promise.reject(ce("TypeError",'Unsupported type "'+s.type+'"'));if(!Yn("setRemoteDescription",s.type,d.signalingState)||d._isClosed)return Promise.reject(ce("InvalidStateError","Can not set remote "+s.type+" in state "+d.signalingState));var u={};d.remoteStreams.forEach(function(S){u[S.id]=S});var p=[],f=H.splitSections(s.sdp),v=f.shift(),y=H.matchPrefix(v,"a=ice-lite").length>0,_=H.matchPrefix(v,"a=group:BUNDLE ").length>0;d.usingBundle=_;var b=H.matchPrefix(v,"a=ice-options:")[0];return b?d.canTrickleIceCandidates=b.substr(14).split(" ").indexOf("trickle")>=0:d.canTrickleIceCandidates=!1,f.forEach(function(S,g){var l=H.splitLines(S),C=H.getKind(S),E=H.isRejected(S)&&H.matchPrefix(S,"a=bundle-only").length===0,m=l[0].substr(2).split(" ")[2],w=H.getDirection(S,v),R=H.parseMsid(S),h=H.getMid(S)||H.generateIdentifier();if(C==="application"&&m==="DTLS/SCTP"){d.transceivers[g]={mid:h,isDatachannel:!0};return}var j,x,I,T,P,c,F,k,M,A=H.parseRtpParameters(S),L,D;E||(L=H.getIceParameters(S,v),D=H.getDtlsParameters(S,v),D.role="client"),F=H.parseRtpEncodingParameters(S);var N=H.parseRtcpParameters(S),U=H.matchPrefix(S,"a=end-of-candidates",v).length>0,W=H.matchPrefix(S,"a=candidate:").map(function(J){return H.parseCandidate(J)}).filter(function(J){return J.component===1});if((s.type==="offer"||s.type==="answer")&&!E&&_&&g>0&&d.transceivers[g]&&(d._disposeIceAndDtlsTransports(g),d.transceivers[g].iceGatherer=d.transceivers[0].iceGatherer,d.transceivers[g].iceTransport=d.transceivers[0].iceTransport,d.transceivers[g].dtlsTransport=d.transceivers[0].dtlsTransport,d.transceivers[g].rtpSender&&d.transceivers[g].rtpSender.setTransport(d.transceivers[0].dtlsTransport),d.transceivers[g].rtpReceiver&&d.transceivers[g].rtpReceiver.setTransport(d.transceivers[0].dtlsTransport)),s.type==="offer"&&!E){j=d.transceivers[g]||d._createTransceiver(C),j.mid=h,j.iceGatherer||(j.iceGatherer=d._createIceGatherer(g,_)),W.length&&j.iceTransport.state==="new"&&(U&&(!_||g===0)?j.iceTransport.setRemoteCandidates(W):W.forEach(function(J){Tt(j.iceTransport,J)})),k=n.RTCRtpReceiver.getCapabilities(C),t<15019&&(k.codecs=k.codecs.filter(function(J){return J.name!=="rtx"})),c=j.sendEncodingParameters||[{ssrc:(2*g+2)*1001}];var q=!1;if(w==="sendrecv"||w==="sendonly"){if(q=!j.rtpReceiver,P=j.rtpReceiver||new n.RTCRtpReceiver(j.dtlsTransport,C),q){var G;M=P.track,R&&R.stream==="-"||(R?(u[R.stream]||(u[R.stream]=new n.MediaStream,Object.defineProperty(u[R.stream],"id",{get:function(){return R.stream}})),Object.defineProperty(M,"id",{get:function(){return R.track}}),G=u[R.stream]):(u.default||(u.default=new n.MediaStream),G=u.default)),G&&(e(M,G),j.associatedRemoteMediaStreams.push(G)),p.push([M,P,G])}}else j.rtpReceiver&&j.rtpReceiver.track&&(j.associatedRemoteMediaStreams.forEach(function(J){var Q=J.getTracks().find(function(ae){return ae.id===j.rtpReceiver.track.id});Q&&r(Q,J)}),j.associatedRemoteMediaStreams=[]);j.localCapabilities=k,j.remoteCapabilities=A,j.rtpReceiver=P,j.rtcpParameters=N,j.sendEncodingParameters=c,j.recvEncodingParameters=F,d._transceive(d.transceivers[g],!1,q)}else s.type==="answer"&&!E&&(j=d.transceivers[g],x=j.iceGatherer,I=j.iceTransport,T=j.dtlsTransport,P=j.rtpReceiver,c=j.sendEncodingParameters,k=j.localCapabilities,d.transceivers[g].recvEncodingParameters=F,d.transceivers[g].remoteCapabilities=A,d.transceivers[g].rtcpParameters=N,W.length&&I.state==="new"&&((y||U)&&(!_||g===0)?I.setRemoteCandidates(W):W.forEach(function(J){Tt(j.iceTransport,J)})),(!_||g===0)&&(I.state==="new"&&I.start(x,L,"controlling"),T.state==="new"&&T.start(D)),d._transceive(j,w==="sendrecv"||w==="recvonly",w==="sendrecv"||w==="sendonly"),P&&(w==="sendrecv"||w==="sendonly")?(M=P.track,R?(u[R.stream]||(u[R.stream]=new n.MediaStream),e(M,u[R.stream]),p.push([M,P,u[R.stream]])):(u.default||(u.default=new n.MediaStream),e(M,u.default),p.push([M,P,u.default]))):delete j.rtpReceiver)}),d._dtlsRole===void 0&&(d._dtlsRole=s.type==="offer"?"active":"passive"),d.remoteDescription={type:s.type,sdp:s.sdp},s.type==="offer"?d._updateSignalingState("have-remote-offer"):d._updateSignalingState("stable"),Object.keys(u).forEach(function(S){var g=u[S];if(g.getTracks().length){if(d.remoteStreams.indexOf(g)===-1){d.remoteStreams.push(g);var l=new Event("addstream");l.stream=g,n.setTimeout(function(){d._dispatchEvent("addstream",l)})}p.forEach(function(C){var E=C[0],m=C[1];g.id===C[2].id&&i(d,E,m,[g])})}}),p.forEach(function(S){S[2]||i(d,S[0],S[1],[])}),n.setTimeout(function(){d&&d.transceivers&&d.transceivers.forEach(function(S){S.iceTransport&&S.iceTransport.state==="new"&&S.iceTransport.getRemoteCandidates().length>0&&(console.warn("Timeout for addRemoteCandidate. Consider sending an end-of-candidates notification"),S.iceTransport.addRemoteCandidate({}))})},4e3),Promise.resolve()},o.prototype.close=function(){this.transceivers.forEach(function(s){s.iceTransport&&s.iceTransport.stop(),s.dtlsTransport&&s.dtlsTransport.stop(),s.rtpSender&&s.rtpSender.stop(),s.rtpReceiver&&s.rtpReceiver.stop()}),this._isClosed=!0,this._updateSignalingState("closed")},o.prototype._updateSignalingState=function(s){this.signalingState=s;var d=new Event("signalingstatechange");this._dispatchEvent("signalingstatechange",d)},o.prototype._maybeFireNegotiationNeeded=function(){var s=this;this.signalingState!=="stable"||this.needNegotiation===!0||(this.needNegotiation=!0,n.setTimeout(function(){if(s.needNegotiation){s.needNegotiation=!1;var d=new Event("negotiationneeded");s._dispatchEvent("negotiationneeded",d)}},0))},o.prototype._updateConnectionState=function(){var s,d={new:0,closed:0,connecting:0,checking:0,connected:0,completed:0,disconnected:0,failed:0};if(this.transceivers.forEach(function(p){d[p.iceTransport.state]++,d[p.dtlsTransport.state]++}),d.connected+=d.completed,s="new",d.failed>0?s="failed":d.connecting>0||d.checking>0?s="connecting":d.disconnected>0?s="disconnected":d.new>0?s="new":(d.connected>0||d.completed>0)&&(s="connected"),s!==this.iceConnectionState){this.iceConnectionState=s;var u=new Event("iceconnectionstatechange");this._dispatchEvent("iceconnectionstatechange",u)}},o.prototype.createOffer=function(){var s=this;if(s._isClosed)return Promise.reject(ce("InvalidStateError","Can not call createOffer after close"));var d=s.transceivers.filter(function(y){return y.kind==="audio"}).length,u=s.transceivers.filter(function(y){return y.kind==="video"}).length,p=arguments[0];if(p){if(p.mandatory||p.optional)throw new TypeError("Legacy mandatory/optional constraints not supported.");p.offerToReceiveAudio!==void 0&&(p.offerToReceiveAudio===!0?d=1:p.offerToReceiveAudio===!1?d=0:d=p.offerToReceiveAudio),p.offerToReceiveVideo!==void 0&&(p.offerToReceiveVideo===!0?u=1:p.offerToReceiveVideo===!1?u=0:u=p.offerToReceiveVideo)}for(s.transceivers.forEach(function(y){y.kind==="audio"?(d--,d<0&&(y.wantReceive=!1)):y.kind==="video"&&(u--,u<0&&(y.wantReceive=!1))});d>0||u>0;)d>0&&(s._createTransceiver("audio"),d--),u>0&&(s._createTransceiver("video"),u--);var f=H.writeSessionBoilerplate(s._sdpSessionId,s._sdpSessionVersion++);s.transceivers.forEach(function(y,_){var b=y.track,S=y.kind,g=y.mid||H.generateIdentifier();y.mid=g,y.iceGatherer||(y.iceGatherer=s._createIceGatherer(_,s.usingBundle));var l=n.RTCRtpSender.getCapabilities(S);t<15019&&(l.codecs=l.codecs.filter(function(E){return E.name!=="rtx"})),l.codecs.forEach(function(E){E.name==="H264"&&E.parameters["level-asymmetry-allowed"]===void 0&&(E.parameters["level-asymmetry-allowed"]="1"),y.remoteCapabilities&&y.remoteCapabilities.codecs&&y.remoteCapabilities.codecs.forEach(function(m){E.name.toLowerCase()===m.name.toLowerCase()&&E.clockRate===m.clockRate&&(E.preferredPayloadType=m.payloadType)})}),l.headerExtensions.forEach(function(E){var m=y.remoteCapabilities&&y.remoteCapabilities.headerExtensions||[];m.forEach(function(w){E.uri===w.uri&&(E.id=w.id)})});var C=y.sendEncodingParameters||[{ssrc:(2*_+1)*1001}];b&&t>=15019&&S==="video"&&!C[0].rtx&&(C[0].rtx={ssrc:C[0].ssrc+1}),y.wantReceive&&(y.rtpReceiver=new n.RTCRtpReceiver(y.dtlsTransport,S)),y.localCapabilities=l,y.sendEncodingParameters=C}),s._config.bundlePolicy!=="max-compat"&&(f+="a=group:BUNDLE "+s.transceivers.map(function(y){return y.mid}).join(" ")+`\r
`),f+=`a=ice-options:trickle\r
`,s.transceivers.forEach(function(y,_){f+=Kn(y,y.localCapabilities,"offer",y.stream,s._dtlsRole),f+=`a=rtcp-rsize\r
`,y.iceGatherer&&s.iceGatheringState!=="new"&&(_===0||!s.usingBundle)&&(y.iceGatherer.getLocalCandidates().forEach(function(b){b.component=1,f+="a="+H.writeCandidate(b)+`\r
`}),y.iceGatherer.state==="completed"&&(f+=`a=end-of-candidates\r
`))});var v=new n.RTCSessionDescription({type:"offer",sdp:f});return Promise.resolve(v)},o.prototype.createAnswer=function(){var s=this;if(s._isClosed)return Promise.reject(ce("InvalidStateError","Can not call createAnswer after close"));var d=H.writeSessionBoilerplate(s._sdpSessionId,s._sdpSessionVersion++);s.usingBundle&&(d+="a=group:BUNDLE "+s.transceivers.map(function(f){return f.mid}).join(" ")+`\r
`);var u=H.getMediaSections(s.remoteDescription.sdp).length;s.transceivers.forEach(function(f,v){if(!(v+1>u)){if(f.isDatachannel){d+=`m=application 0 DTLS/SCTP 5000\r
c=IN IP4 0.0.0.0\r
a=mid:`+f.mid+`\r
`;return}if(f.stream){var y;f.kind==="audio"?y=f.stream.getAudioTracks()[0]:f.kind==="video"&&(y=f.stream.getVideoTracks()[0]),y&&t>=15019&&f.kind==="video"&&!f.sendEncodingParameters[0].rtx&&(f.sendEncodingParameters[0].rtx={ssrc:f.sendEncodingParameters[0].ssrc+1})}var _=Et(f.localCapabilities,f.remoteCapabilities),b=_.codecs.filter(function(S){return S.name.toLowerCase()==="rtx"}).length;!b&&f.sendEncodingParameters[0].rtx&&delete f.sendEncodingParameters[0].rtx,d+=Kn(f,_,"answer",f.stream,s._dtlsRole),f.rtcpParameters&&f.rtcpParameters.reducedSize&&(d+=`a=rtcp-rsize\r
`)}});var p=new n.RTCSessionDescription({type:"answer",sdp:d});return Promise.resolve(p)},o.prototype.addIceCandidate=function(s){var d=this,u;return s&&!(s.sdpMLineIndex!==void 0||s.sdpMid)?Promise.reject(new TypeError("sdpMLineIndex or sdpMid required")):new Promise(function(p,f){if(d.remoteDescription)if(!s||s.candidate==="")for(var v=0;v<d.transceivers.length&&!(!d.transceivers[v].isDatachannel&&(d.transceivers[v].iceTransport.addRemoteCandidate({}),u=H.getMediaSections(d.remoteDescription.sdp),u[v]+=`a=end-of-candidates\r
`,d.remoteDescription.sdp=H.getDescription(d.remoteDescription.sdp)+u.join(""),d.usingBundle));v++);else{var y=s.sdpMLineIndex;if(s.sdpMid){for(var _=0;_<d.transceivers.length;_++)if(d.transceivers[_].mid===s.sdpMid){y=_;break}}var b=d.transceivers[y];if(b){if(b.isDatachannel)return p();var S=Object.keys(s.candidate).length>0?H.parseCandidate(s.candidate):{};if(S.protocol==="tcp"&&(S.port===0||S.port===9)||S.component&&S.component!==1)return p();if((y===0||y>0&&b.iceTransport!==d.transceivers[0].iceTransport)&&!Tt(b.iceTransport,S))return f(ce("OperationError","Can not add ICE candidate"));var g=s.candidate.trim();g.indexOf("a=")===0&&(g=g.substr(2)),u=H.getMediaSections(d.remoteDescription.sdp),u[y]+="a="+(S.type?g:"end-of-candidates")+`\r
`,d.remoteDescription.sdp=u.join("")}else return f(ce("OperationError","Can not add ICE candidate"))}else return f(ce("InvalidStateError","Can not add ICE candidate without a remote description"));p()})},o.prototype.getStats=function(){var s=[];this.transceivers.forEach(function(u){["rtpSender","rtpReceiver","iceGatherer","iceTransport","dtlsTransport"].forEach(function(p){u[p]&&s.push(u[p].getStats())})});var d=function(u){return{inboundrtp:"inbound-rtp",outboundrtp:"outbound-rtp",candidatepair:"candidate-pair",localcandidate:"local-candidate",remotecandidate:"remote-candidate"}[u.type]||u.type};return new Promise(function(u){var p=new Map;Promise.all(s).then(function(f){f.forEach(function(v){Object.keys(v).forEach(function(y){v[y].type=d(v[y]),p.set(y,v[y])})}),u(p)})})};var a=["createOffer","createAnswer"];return a.forEach(function(s){var d=o.prototype[s];o.prototype[s]=function(){var u=arguments;return typeof u[0]=="function"||typeof u[1]=="function"?d.apply(this,[arguments[2]]).then(function(p){typeof u[0]=="function"&&u[0].apply(null,[p])},function(p){typeof u[1]=="function"&&u[1].apply(null,[p])}):d.apply(this,arguments)}}),a=["setLocalDescription","setRemoteDescription","addIceCandidate"],a.forEach(function(s){var d=o.prototype[s];o.prototype[s]=function(){var u=arguments;return typeof u[1]=="function"||typeof u[2]=="function"?d.apply(this,arguments).then(function(){typeof u[1]=="function"&&u[1].apply(null)},function(p){typeof u[2]=="function"&&u[2].apply(null,[p])}):d.apply(this,arguments)}}),["getStats"].forEach(function(s){var d=o.prototype[s];o.prototype[s]=function(){var u=arguments;return typeof u[1]=="function"?d.apply(this,arguments).then(function(){typeof u[1]=="function"&&u[1].apply(null)}):d.apply(this,arguments)}}),o};Object.defineProperty(gt,"__esModule",{value:!0});var ka=se,un=K,je=de,Ia=Pa;function be(n){if(typeof window>"u"){this.log.info("No RTCPeerConnection implementation available. The window object was not found.");return}n&&n.RTCPeerConnection?this.RTCPeerConnection=n.RTCPeerConnection:un.isLegacyEdge()?this.RTCPeerConnection=new Ia(window):typeof window.RTCPeerConnection=="function"?this.RTCPeerConnection=window.RTCPeerConnection:typeof window.webkitRTCPeerConnection=="function"?this.RTCPeerConnection=webkitRTCPeerConnection:typeof window.mozRTCPeerConnection=="function"?(this.RTCPeerConnection=mozRTCPeerConnection,window.RTCSessionDescription=mozRTCSessionDescription,window.RTCIceCandidate=mozRTCIceCandidate):this.log.info("No RTCPeerConnection implementation available")}be.prototype.create=function(n){this.log=new ka.default("RTCPC"),this.pc=new this.RTCPeerConnection(n)};be.prototype.createModernConstraints=function(n){if(typeof n>"u")return null;var t=Object.assign({},n);return typeof webkitRTCPeerConnection<"u"&&!un.isLegacyEdge()?(t.mandatory={},typeof n.audio<"u"&&(t.mandatory.OfferToReceiveAudio=n.audio),typeof n.video<"u"&&(t.mandatory.OfferToReceiveVideo=n.video)):(typeof n.audio<"u"&&(t.offerToReceiveAudio=n.audio),typeof n.video<"u"&&(t.offerToReceiveVideo=n.video)),delete t.audio,delete t.video,t};be.prototype.createOffer=function(n,t,e,r,i){var o=this;return e=this.createModernConstraints(e),ti(this.pc.createOffer,this.pc)(e).then(function(a){if(!o.pc)return Promise.resolve();var s=je.setMaxAverageBitrate(a.sdp,n);return mt(o.pc.setLocalDescription,o.pc)(new RTCSessionDescription({sdp:je.setCodecPreferences(s,t),type:"offer"}))}).then(r,i)};be.prototype.createAnswer=function(n,t,e,r,i){var o=this;return e=this.createModernConstraints(e),ti(this.pc.createAnswer,this.pc)(e).then(function(a){if(!o.pc)return Promise.resolve();var s=je.setMaxAverageBitrate(a.sdp,n);return mt(o.pc.setLocalDescription,o.pc)(new RTCSessionDescription({sdp:je.setCodecPreferences(s,t),type:"answer"}))}).then(r,i)};be.prototype.processSDP=function(n,t,e,r,i,o){var a=this;e=je.setCodecPreferences(e,t);var s=new RTCSessionDescription({sdp:e,type:"offer"});return mt(this.pc.setRemoteDescription,this.pc)(s).then(function(){a.createAnswer(n,t,r,i,o)})};be.prototype.getSDP=function(){return this.pc.localDescription.sdp};be.prototype.processAnswer=function(n,t,e,r){return this.pc?(t=je.setCodecPreferences(t,n),mt(this.pc.setRemoteDescription,this.pc)(new RTCSessionDescription({sdp:t,type:"answer"})).then(e,r)):Promise.resolve()};be.test=function(){if(typeof navigator=="object"){var n=navigator.mediaDevices&&navigator.mediaDevices.getUserMedia||navigator.webkitGetUserMedia||navigator.mozGetUserMedia||navigator.getUserMedia;if(un.isLegacyEdge(navigator))return!1;if(n&&typeof window.RTCPeerConnection=="function")return!0;if(n&&typeof window.webkitRTCPeerConnection=="function")return!0;if(n&&typeof window.mozRTCPeerConnection=="function"){try{var t=new window.mozRTCPeerConnection;if(typeof t.getLocalStreams!="function")return!1}catch{return!1}return!0}else if(typeof RTCIceGatherer<"u")return!0}return!1};function ei(n,t,e,r){return function(){var i=Array.prototype.slice.call(arguments);return new Promise(function(o){var a=n.apply(t,i);if(!r){o(a);return}if(typeof a=="object"&&typeof a.then=="function")o(a);else throw new Error}).catch(function(){return new Promise(function(o,a){n.apply(t,e?[o,a].concat(i):i.concat([o,a]))})})}}function ti(n,t){return ei(n,t,!0,!0)}function mt(n,t){return ei(n,t,!1,!1)}gt.default=be;Object.defineProperty(cn,"__esModule",{value:!0});var ve=oe,Ra=se,Aa=K,ct=gt,Da=de,Oa=15e3,Ma="none",ja="timeout",La="new",Xn=50;function V(n,t,e){if(!n||!t)throw new ve.InvalidArgumentError("Audiohelper, and pstream are required arguments");if(!(this instanceof V))return new V(n,t,e);this._log=new Ra.default("PeerConnection");function r(){this._log.warn("Unexpected noop call in peerconnection")}this.onaudio=r,this.onopen=r,this.onerror=r,this.onclose=r,this.ondisconnected=r,this.onfailed=r,this.onconnected=r,this.onreconnected=r,this.onsignalingstatechange=r,this.ondtlstransportstatechange=r,this.onicegatheringfailure=r,this.onicegatheringstatechange=r,this.oniceconnectionstatechange=r,this.onpcconnectionstatechange=r,this.onicecandidate=r,this.onselectedcandidatepairchange=r,this.onvolume=r,this.version=null,this.pstream=t,this.stream=null,this.sinkIds=new Set(["default"]),this.outputs=new Map,this.status="connecting",this.callSid=null,this.isMuted=!1;var i=typeof window<"u"&&(window.AudioContext||window.webkitAudioContext);return this._isSinkSupported=!!i&&typeof HTMLAudioElement<"u"&&HTMLAudioElement.prototype.setSinkId,this._audioContext=i&&n._audioContext,this._audioHelper=n,this._hasIceCandidates=!1,this._hasIceGatheringFailures=!1,this._iceGatheringTimeoutId=null,this._masterAudio=null,this._masterAudioDeviceId=null,this._mediaStreamSource=null,this._dtmfSender=null,this._dtmfSenderUnsupported=!1,this._callEvents=[],this._nextTimeToPublish=Date.now(),this._onAnswerOrRinging=r,this._onHangup=r,this._remoteStream=null,this._shouldManageStream=!0,this._iceState=La,this._isUnifiedPlan=e.isUnifiedPlan,this.options=e=e||{},this.navigator=e.navigator||(typeof navigator<"u"?navigator:null),this.util=e.util||Aa,this.codecPreferences=e.codecPreferences,this}V.prototype.uri=function(){return this._uri};V.prototype.openDefaultDeviceWithConstraints=function(n){return this._audioHelper._openDefaultDeviceWithConstraints(n).then(this._setInputTracksFromStream.bind(this,!1))};V.prototype.setInputTracksFromStream=function(n){var t=this;return this._setInputTracksFromStream(!0,n).then(function(){t._shouldManageStream=!1})};V.prototype._createAnalyser=function(n,t){t=Object.assign({fftSize:32,smoothingTimeConstant:.3},t);var e=n.createAnalyser();for(var r in t)e[r]=t[r];return e};V.prototype._setVolumeHandler=function(n){this.onvolume=n};V.prototype._startPollingVolume=function(){if(!(!this._audioContext||!this.stream||!this._remoteStream)){var n=this._audioContext,t=this._inputAnalyser=this._createAnalyser(n),e=t.frequencyBinCount,r=new Uint8Array(e);this._inputAnalyser2=this._createAnalyser(n,{maxDecibels:0,minDecibels:-127,smoothingTimeConstant:0});var i=this._outputAnalyser=this._createAnalyser(n),o=i.frequencyBinCount,a=new Uint8Array(o);this._outputAnalyser2=this._createAnalyser(n,{maxDecibels:0,minDecibels:-127,smoothingTimeConstant:0}),this._updateInputStreamSource(this.stream),this._updateOutputStreamSource(this._remoteStream);var s=this;setTimeout(function d(){if(s._audioContext){if(s.status==="closed"){s._inputAnalyser.disconnect(),s._outputAnalyser.disconnect(),s._inputAnalyser2.disconnect(),s._outputAnalyser2.disconnect();return}}else return;s._inputAnalyser.getByteFrequencyData(r);var u=s.util.average(r);s._inputAnalyser2.getByteFrequencyData(r);var p=s.util.average(r);s._outputAnalyser.getByteFrequencyData(a);var f=s.util.average(a);s._outputAnalyser2.getByteFrequencyData(a);var v=s.util.average(a);s.onvolume(u/255,f/255,p,v),setTimeout(d,Xn)},Xn)}};V.prototype._stopStream=function(){this._shouldManageStream&&this._audioHelper._stopDefaultInputDeviceStream()};V.prototype._updateInputStreamSource=function(n){this._inputStreamSource&&this._inputStreamSource.disconnect();try{this._inputStreamSource=this._audioContext.createMediaStreamSource(n),this._inputStreamSource.connect(this._inputAnalyser),this._inputStreamSource.connect(this._inputAnalyser2)}catch(t){this._log.warn("Unable to update input MediaStreamSource",t),this._inputStreamSource=null}};V.prototype._updateOutputStreamSource=function(n){this._outputStreamSource&&this._outputStreamSource.disconnect();try{this._outputStreamSource=this._audioContext.createMediaStreamSource(n),this._outputStreamSource.connect(this._outputAnalyser),this._outputStreamSource.connect(this._outputAnalyser2)}catch(t){this._log.warn("Unable to update output MediaStreamSource",t),this._outputStreamSource=null}};V.prototype._setInputTracksFromStream=function(n,t){return this._isUnifiedPlan?this._setInputTracksForUnifiedPlan(n,t):this._setInputTracksForPlanB(n,t)};V.prototype._setInputTracksForPlanB=function(n,t){var e=this;if(!t)return Promise.reject(new ve.InvalidArgumentError("Can not set input stream to null while in a call"));if(!t.getAudioTracks().length)return Promise.reject(new ve.InvalidArgumentError("Supplied input stream has no audio tracks"));var r=this.stream;return r?(this._stopStream(),Na(this.version.pc,r),r.getAudioTracks().forEach(r.removeTrack,r),t.getAudioTracks().forEach(r.addTrack,r),ni(this.version.pc,t),this._updateInputStreamSource(this.stream)):this.stream=n?Nt(t):t,this.mute(this.isMuted),this.version?new Promise(function(i,o){e.version.createOffer(e.options.maxAverageBitrate,e.codecPreferences,{audio:!0},function(){e.version.processAnswer(e.codecPreferences,e._answerSdp,function(){i(e.stream)},o)},o)}):Promise.resolve(this.stream)};V.prototype._setInputTracksForUnifiedPlan=function(n,t){var e=this;if(!t)return Promise.reject(new ve.InvalidArgumentError("Can not set input stream to null while in a call"));if(!t.getAudioTracks().length)return Promise.reject(new ve.InvalidArgumentError("Supplied input stream has no audio tracks"));var r=this.stream,i=function(){return e.mute(e.isMuted),Promise.resolve(e.stream)};if(!r)this.stream=n?Nt(t):t;else return this._shouldManageStream&&this._stopStream(),this._sender||(this._sender=this.version.pc.getSenders()[0]),this._sender.replaceTrack(t.getAudioTracks()[0]).then(function(){return e._updateInputStreamSource(t),e.stream=n?Nt(t):t,i()});return i()};V.prototype._onInputDevicesChanged=function(){if(this.stream){var n=this.stream.getAudioTracks().every(function(t){return t.readyState==="ended"});n&&this._shouldManageStream&&this.openDefaultDeviceWithConstraints({audio:!0})}};V.prototype._onIceGatheringFailure=function(n){this._hasIceGatheringFailures=!0,this.onicegatheringfailure(n)};V.prototype._onMediaConnectionStateChange=function(n){var t=this._iceState;if(!(t===n||n!=="connected"&&n!=="disconnected"&&n!=="failed")){this._iceState=n;var e;switch(n){case"connected":t==="disconnected"||t==="failed"?(e="ICE liveliness check succeeded. Connection with Twilio restored",this._log.info(e),this.onreconnected(e)):(e="Media connection established.",this._log.info(e),this.onconnected(e)),this._stopIceGatheringTimeout(),this._hasIceGatheringFailures=!1;break;case"disconnected":e="ICE liveliness check failed. May be having trouble connecting to Twilio",this._log.warn(e),this.ondisconnected(e);break;case"failed":e="Connection with Twilio was interrupted.",this._log.warn(e),this.onfailed(e);break}}};V.prototype._setSinkIds=function(n){return this._isSinkSupported?(this.sinkIds=new Set(n.forEach?n:[n]),this.version?this._updateAudioOutputs():Promise.resolve()):Promise.reject(new ve.NotSupportedError("Audio output selection is not supported by this browser"))};V.prototype._startIceGatheringTimeout=function(){var t=this;this._stopIceGatheringTimeout(),this._iceGatheringTimeoutId=setTimeout(function(){t._onIceGatheringFailure(ja)},Oa)};V.prototype._stopIceGatheringTimeout=function(){clearInterval(this._iceGatheringTimeoutId)};V.prototype._updateAudioOutputs=function(){var t=Array.from(this.sinkIds).filter(function(o){return!this.outputs.has(o)},this),e=Array.from(this.outputs.keys()).filter(function(o){return!this.sinkIds.has(o)},this),r=this,i=t.map(this._createAudioOutput,this);return Promise.all(i).then(function(){return Promise.all(e.map(r._removeAudioOutput,r))})};V.prototype._createAudio=function(t){var e=new Audio(t);return this.onaudio(e),e};V.prototype._createAudioOutput=function(t){var e=null;this._mediaStreamSource&&(e=this._audioContext.createMediaStreamDestination(),this._mediaStreamSource.connect(e));var r=this._createAudio();ln(r,e&&e.stream?e.stream:this.pcStream);var i=this;return r.setSinkId(t).then(function(){return r.play()}).then(function(){i.outputs.set(t,{audio:r,dest:e})})};V.prototype._removeAudioOutputs=function(){return this._masterAudio&&typeof this._masterAudioDeviceId<"u"&&(this._disableOutput(this,this._masterAudioDeviceId),this.outputs.delete(this._masterAudioDeviceId),this._masterAudioDeviceId=null,this._masterAudio.paused||this._masterAudio.pause(),typeof this._masterAudio.srcObject<"u"?this._masterAudio.srcObject=null:this._masterAudio.src="",this._masterAudio=null),Array.from(this.outputs.keys()).map(this._removeAudioOutput,this)};V.prototype._disableOutput=function(t,e){var r=t.outputs.get(e);r&&(r.audio&&(r.audio.pause(),r.audio.src=""),r.dest&&r.dest.disconnect())};V.prototype._reassignMasterOutput=function(t,e){var r=t.outputs.get(e);t.outputs.delete(e);var i=this,o=Array.from(t.outputs.keys())[0],a=typeof o=="string"?o:"default";return r.audio.setSinkId(a).then(function(){i._disableOutput(t,a),t.outputs.set(a,r),t._masterAudioDeviceId=a}).catch(function(){t.outputs.set(e,r),i._log.info("Could not reassign master output. Attempted to roll back.")})};V.prototype._removeAudioOutput=function(t){return this._masterAudioDeviceId===t?this._reassignMasterOutput(this,t):(this._disableOutput(this,t),this.outputs.delete(t),Promise.resolve())};V.prototype._onAddTrack=function(t,e){var r=t._masterAudio=this._createAudio();ln(r,e),r.play();var i=Array.from(t.outputs.keys())[0],o=typeof i=="string"?i:"default";t._masterAudioDeviceId=o,t.outputs.set(o,{audio:r});try{t._mediaStreamSource=t._audioContext.createMediaStreamSource(e)}catch(a){this._log.warn("Unable to create a MediaStreamSource from onAddTrack",a),this._mediaStreamSource=null}t.pcStream=e,t._updateAudioOutputs()};V.prototype._fallbackOnAddTrack=function(t,e){var r=document&&document.createElement("audio");r.autoplay=!0,ln(r,e)||t._log.info("Error attaching stream to element."),t.outputs.set("default",{audio:r})};V.prototype._setEncodingParameters=function(n){if(!(!n||!this._sender||typeof this._sender.getParameters!="function"||typeof this._sender.setParameters!="function")){var t=this._sender.getParameters();!t.priority&&!(t.encodings&&t.encodings.length)||(t.priority="high",t.encodings&&t.encodings.length&&t.encodings.forEach(function(e){e.priority="high",e.networkPriority="high"}),this._sender.setParameters(t))}};V.prototype._setupPeerConnection=function(n){var t=this,e=this,r=new(this.options.rtcpcFactory||ct.default)({RTCPeerConnection:this.options.RTCPeerConnection});r.create(n),ni(r.pc,this.stream);var i="ontrack"in r.pc?"ontrack":"onaddstream";return r.pc[i]=function(o){var a=e._remoteStream=o.stream||o.streams[0];typeof r.pc.getSenders=="function"&&(t._sender=r.pc.getSenders()[0]),e._isSinkSupported?e._onAddTrack(e,a):e._fallbackOnAddTrack(e,a),e._startPollingVolume()},r};V.prototype._maybeSetIceAggressiveNomination=function(n){return this.options.forceAggressiveIceNomination?Da.setIceAggressiveNomination(n):n};V.prototype._setupChannel=function(){var n=this,t=this.version.pc;this.version.pc.onopen=function(){n.status="open",n.onopen()},this.version.pc.onstatechange=function(){n.version.pc&&n.version.pc.readyState==="stable"&&(n.status="open",n.onopen())},this.version.pc.onsignalingstatechange=function(){var e=t.signalingState;n._log.info('signalingState is "'+e+'"'),n.version.pc&&n.version.pc.signalingState==="stable"&&(n.status="open",n.onopen()),n.onsignalingstatechange(t.signalingState)},t.onconnectionstatechange=function(e){var r=t.connectionState;if(!r&&e&&e.target){var i=e.target;r=i.connectionState||i.connectionState_,n._log.info("pc.connectionState not detected. Using target PC. State="+r)}r?n._log.info('pc.connectionState is "'+r+'"'):n._log.warn('onconnectionstatechange detected but state is "'+r+'"'),n.onpcconnectionstatechange(r),n._onMediaConnectionStateChange(r)},t.onicecandidate=function(e){var r=e.candidate;r&&(n._hasIceCandidates=!0,n.onicecandidate(r),n._setupRTCIceTransportListener()),n._log.info("ICE Candidate: "+JSON.stringify(r))},t.onicegatheringstatechange=function(){var e=t.iceGatheringState;e==="gathering"?n._startIceGatheringTimeout():e==="complete"&&(n._stopIceGatheringTimeout(),n._hasIceCandidates||n._onIceGatheringFailure(Ma),n._hasIceCandidates&&n._hasIceGatheringFailures&&n._startIceGatheringTimeout()),n._log.info('pc.iceGatheringState is "'+t.iceGatheringState+'"'),n.onicegatheringstatechange(e)},t.oniceconnectionstatechange=function(){n._log.info('pc.iceConnectionState is "'+t.iceConnectionState+'"'),n.oniceconnectionstatechange(t.iceConnectionState),n._onMediaConnectionStateChange(t.iceConnectionState)}};V.prototype._initializeMediaStream=function(n){return this.status==="open"?!1:this.pstream.status==="disconnected"?(this.onerror({info:{code:31e3,message:"Cannot establish connection. Client is disconnected",twilioError:new ve.SignalingErrors.ConnectionDisconnected}}),this.close(),!1):(this.version=this._setupPeerConnection(n),this._setupChannel(),!0)};V.prototype._removeReconnectionListeners=function(){this.pstream&&(this.pstream.removeListener("answer",this._onAnswerOrRinging),this.pstream.removeListener("hangup",this._onHangup))};V.prototype._setupRTCDtlsTransportListener=function(){var n=this,t=this.getRTCDtlsTransport();if(!(!t||t.onstatechange)){var e=function(){n._log.info('dtlsTransportState is "'+t.state+'"'),n.ondtlstransportstatechange(t.state)};e(),t.onstatechange=e}};V.prototype._setupRTCIceTransportListener=function(){var n=this,t=this._getRTCIceTransport();!t||t.onselectedcandidatepairchange||(t.onselectedcandidatepairchange=function(){return n.onselectedcandidatepairchange(t.getSelectedCandidatePair())})};V.prototype.iceRestart=function(){var n=this;this._log.info("Attempting to restart ICE..."),this._hasIceCandidates=!1,this.version.createOffer(this.options.maxAverageBitrate,this.codecPreferences,{iceRestart:!0}).then(function(){n._removeReconnectionListeners(),n._onAnswerOrRinging=function(t){if(n._removeReconnectionListeners(),!t.sdp||n.version.pc.signalingState!=="have-local-offer"){var e="Invalid state or param during ICE Restart:"+("hasSdp:"+!!t.sdp+", signalingState:"+n.version.pc.signalingState);n._log.warn(e);return}var r=n._maybeSetIceAggressiveNomination(t.sdp);n._answerSdp=r,n.status!=="closed"&&n.version.processAnswer(n.codecPreferences,r,null,function(i){var o=i&&i.message?i.message:i;n._log.error("Failed to process answer during ICE Restart. Error: "+o)})},n._onHangup=function(){n._log.info("Received hangup during ICE Restart"),n._removeReconnectionListeners()},n.pstream.on("answer",n._onAnswerOrRinging),n.pstream.on("hangup",n._onHangup),n.pstream.reinvite(n.version.getSDP(),n.callSid)}).catch(function(t){var e=t&&t.message?t.message:t;n._log.error("Failed to createOffer during ICE Restart. Error: "+e),n.onfailed(e)})};V.prototype.makeOutgoingCall=function(n,t,e,r,i){var o=this;if(!this._initializeMediaStream(r))return;var a=this;this.callSid=e;function s(){a.options&&a._setEncodingParameters(a.options.dscp),i(a.version.pc)}function d(f){var v=f.message||f;a.onerror({info:{code:31e3,message:"Error processing answer: "+v,twilioError:new ve.MediaErrors.ClientRemoteDescFailed}})}this._onAnswerOrRinging=function(f){if(f.sdp){var v=o._maybeSetIceAggressiveNomination(f.sdp);a._answerSdp=v,a.status!=="closed"&&a.version.processAnswer(o.codecPreferences,v,s,d),a.pstream.removeListener("answer",a._onAnswerOrRinging),a.pstream.removeListener("ringing",a._onAnswerOrRinging)}},this.pstream.on("answer",this._onAnswerOrRinging),this.pstream.on("ringing",this._onAnswerOrRinging);function u(){a.status!=="closed"&&(t?a.pstream.reconnect(a.version.getSDP(),a.callSid,t):a.pstream.invite(a.version.getSDP(),a.callSid,n),a._setupRTCDtlsTransportListener())}function p(f){var v=f.message||f;a.onerror({info:{code:31e3,message:"Error creating the offer: "+v,twilioError:new ve.MediaErrors.ClientLocalDescFailed}})}this.version.createOffer(this.options.maxAverageBitrate,this.codecPreferences,{audio:!0},u,p)};V.prototype.answerIncomingCall=function(n,t,e,r){if(!this._initializeMediaStream(e))return;t=this._maybeSetIceAggressiveNomination(t),this._answerSdp=t.replace(/^a=setup:actpass$/gm,"a=setup:passive"),this.callSid=n;var i=this;function o(){i.status!=="closed"&&(i.pstream.answer(i.version.getSDP(),n),i.options&&i._setEncodingParameters(i.options.dscp),r(i.version.pc),i._setupRTCDtlsTransportListener())}function a(s){var d=s.message||s;i.onerror({info:{code:31e3,message:"Error creating the answer: "+d,twilioError:new ve.MediaErrors.ClientRemoteDescFailed}})}this.version.processSDP(this.options.maxAverageBitrate,this.codecPreferences,t,{audio:!0},o,a)};V.prototype.close=function(){this.version&&this.version.pc&&(this.version.pc.signalingState!=="closed"&&this.version.pc.close(),this.version.pc=null),this.stream&&(this.mute(!1),this._stopStream()),this.stream=null,this._removeReconnectionListeners(),this._stopIceGatheringTimeout(),Promise.all(this._removeAudioOutputs()).catch(function(){}),this._mediaStreamSource&&this._mediaStreamSource.disconnect(),this._inputAnalyser&&this._inputAnalyser.disconnect(),this._outputAnalyser&&this._outputAnalyser.disconnect(),this._inputAnalyser2&&this._inputAnalyser2.disconnect(),this._outputAnalyser2&&this._outputAnalyser2.disconnect(),this.status="closed",this.onclose()};V.prototype.reject=function(n){this.callSid=n};V.prototype.ignore=function(n){this.callSid=n};V.prototype.mute=function(n){if(this.isMuted=n,!!this.stream)if(this._sender&&this._sender.track)this._sender.track.enabled=!n;else{var t=typeof this.stream.getAudioTracks=="function"?this.stream.getAudioTracks():this.stream.audioTracks;t.forEach(function(e){e.enabled=!n})}};V.prototype.getOrCreateDTMFSender=function(){if(this._dtmfSender||this._dtmfSenderUnsupported)return this._dtmfSender||null;var t=this,e=this.version.pc;if(!e)return this._log.warn("No RTCPeerConnection available to call createDTMFSender on"),null;if(typeof e.getSenders=="function"&&(typeof RTCDTMFSender=="function"||typeof RTCDtmfSender=="function")){var r=e.getSenders().find(function(o){return o.dtmf});if(r)return this._log.info("Using RTCRtpSender#dtmf"),this._dtmfSender=r.dtmf,this._dtmfSender}if(typeof e.createDTMFSender=="function"&&typeof e.getLocalStreams=="function"){var i=e.getLocalStreams().map(function(o){var a=t._getAudioTracks(o);return a&&a[0]})[0];return i?(this._log.info("Creating RTCDTMFSender"),this._dtmfSender=e.createDTMFSender(i),this._dtmfSender):(this._log.warn("No local audio MediaStreamTrack available on the RTCPeerConnection to pass to createDTMFSender"),null)}return this._log.info("RTCPeerConnection does not support RTCDTMFSender"),this._dtmfSenderUnsupported=!0,null};V.prototype.getRTCDtlsTransport=function(){var t=this.version&&this.version.pc&&typeof this.version.pc.getSenders=="function"&&this.version.pc.getSenders()[0];return t&&t.transport||null};V.prototype._canStopMediaStreamTrack=function(){return typeof MediaStreamTrack.prototype.stop=="function"};V.prototype._getAudioTracks=function(n){return typeof n.getAudioTracks=="function"?n.getAudioTracks():n.audioTracks};V.prototype._getRTCIceTransport=function(){var t=this.getRTCDtlsTransport();return t&&t.iceTransport||null};V.protocol=function(){return ct.default.test()?new ct.default:null}();function ni(n,t){typeof n.addTrack=="function"?t.getAudioTracks().forEach(function(e){n.addTrack(e,t)}):n.addStream(t)}function Nt(n){var t=typeof MediaStream<"u"?new MediaStream:new webkitMediaStream;return n.getAudioTracks().forEach(t.addTrack,t),t}function Na(n,t){typeof n.removeTrack=="function"?n.getSenders().forEach(function(e){n.removeTrack(e)}):n.removeStream(t)}function ln(n,t){if(typeof n.srcObject<"u")n.srcObject=t;else if(typeof n.mozSrcObject<"u")n.mozSrcObject=t;else if(typeof n.src<"u"){var e=n.options.window||window;n.src=(e.URL||e.webkitURL).createObjectURL(t)}else return!1;return!0}V.enabled=ct.default.test();cn.default=V;Object.defineProperty(_e,"__esModule",{value:!0});_e.PeerConnection=_e.getMediaEngine=_e.enabled=void 0;var Ua=cn;_e.PeerConnection=Ua.default;var Fa=gt;function Ba(){return Fa.default.test()}_e.enabled=Ba;function Wa(){return typeof RTCIceGatherer<"u"?"ORTC":"WebRTC"}_e.getMediaEngine=Wa;var dn={};Object.defineProperty(dn,"__esModule",{value:!0});var xt=oe,Va=K;function Ha(n,t){return t=t||{},t.util=t.util||Va,t.navigator=t.navigator||(typeof navigator<"u"?navigator:null),new Promise(function(e,r){if(!t.navigator)throw new xt.NotSupportedError("getUserMedia is not supported");switch("function"){case typeof(t.navigator.mediaDevices&&t.navigator.mediaDevices.getUserMedia):return e(t.navigator.mediaDevices.getUserMedia(n));case typeof t.navigator.webkitGetUserMedia:return t.navigator.webkitGetUserMedia(n,e,r);case typeof t.navigator.mozGetUserMedia:return t.navigator.mozGetUserMedia(n,e,r);case typeof t.navigator.getUserMedia:return t.navigator.getUserMedia(n,e,r);default:throw new xt.NotSupportedError("getUserMedia is not supported")}}).catch(function(e){throw t.util.isFirefox()&&e.name==="NotReadableError"?new xt.NotSupportedError(`Firefox does not currently support opening multiple audio input trackssimultaneously, even across different tabs.
Related Bugzilla thread: https://bugzilla.mozilla.org/show_bug.cgi?id=1299324`):e})}dn.default=Ha;var fn={},vt={},pn={};Object.defineProperty(pn,"__esModule",{value:!0});var qa=function(){function n(){var t=this;this._promise=new Promise(function(e,r){t._resolve=e,t._reject=r})}return Object.defineProperty(n.prototype,"promise",{get:function(){return this._promise},enumerable:!1,configurable:!0}),n.prototype.reject=function(t){this._reject(t)},n.prototype.resolve=function(t){this._resolve(t)},n}();pn.default=qa;var za=B&&B.__awaiter||function(n,t,e,r){function i(o){return o instanceof e?o:new e(function(a){a(o)})}return new(e||(e=Promise))(function(o,a){function s(p){try{u(r.next(p))}catch(f){a(f)}}function d(p){try{u(r.throw(p))}catch(f){a(f)}}function u(p){p.done?o(p.value):i(p.value).then(s,d)}u((r=r.apply(n,t||[])).next())})},Ga=B&&B.__generator||function(n,t){var e={label:0,sent:function(){if(o[0]&1)throw o[1];return o[1]},trys:[],ops:[]},r,i,o,a;return a={next:s(0),throw:s(1),return:s(2)},typeof Symbol=="function"&&(a[Symbol.iterator]=function(){return this}),a;function s(u){return function(p){return d([u,p])}}function d(u){if(r)throw new TypeError("Generator is already executing.");for(;e;)try{if(r=1,i&&(o=u[0]&2?i.return:u[0]?i.throw||((o=i.return)&&o.call(i),0):i.next)&&!(o=o.call(i,u[1])).done)return o;switch(i=0,o&&(u=[u[0]&2,o.value]),u[0]){case 0:case 1:o=u;break;case 4:return e.label++,{value:u[1],done:!1};case 5:e.label++,i=u[1],u=[0];continue;case 7:u=e.ops.pop(),e.trys.pop();continue;default:if(o=e.trys,!(o=o.length>0&&o[o.length-1])&&(u[0]===6||u[0]===2)){e=0;continue}if(u[0]===3&&(!o||u[1]>o[0]&&u[1]<o[3])){e.label=u[1];break}if(u[0]===6&&e.label<o[1]){e.label=o[1],o=u;break}if(o&&e.label<o[2]){e.label=o[2],e.ops.push(u);break}o[2]&&e.ops.pop(),e.trys.pop();continue}u=t.call(n,e)}catch(p){u=[6,p],i=0}finally{r=o=0}if(u[0]&5)throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}};Object.defineProperty(vt,"__esModule",{value:!0});vt.AsyncQueue=void 0;var $a=pn,Ja=function(){function n(){this._operations=[]}return n.prototype.enqueue=function(t){var e=!!this._operations.length,r=new $a.default;return this._operations.push({deferred:r,callback:t}),e||this._processQueue(),r.promise},n.prototype._processQueue=function(){return za(this,void 0,void 0,function(){var t,e,r,i,o,a,s;return Ga(this,function(d){switch(d.label){case 0:if(!this._operations.length)return[3,5];t=this._operations[0],e=t.deferred,r=t.callback,i=void 0,o=void 0,a=void 0,d.label=1;case 1:return d.trys.push([1,3,,4]),[4,r()];case 2:return i=d.sent(),a=!0,[3,4];case 3:return s=d.sent(),o=s,[3,4];case 4:return this._operations.shift(),a?e.resolve(i):e.reject(o),[3,0];case 5:return[2]}})})},n}();vt.AsyncQueue=Ja;var hn={},gn={};Object.defineProperty(gn,"__esModule",{value:!0});var Ka=function(){function n(){var t=this;this.promise=new Promise(function(e,r){t._resolve=e,t._reject=r})}return Object.defineProperty(n.prototype,"reject",{get:function(){return this._reject},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"resolve",{get:function(){return this._resolve},enumerable:!1,configurable:!0}),n}();gn.default=Ka;var mn={},Ya=B&&B.__spreadArrays||function(){for(var n=0,t=0,e=arguments.length;t<e;t++)n+=arguments[t].length;for(var r=Array(n),i=0,t=0;t<e;t++)for(var o=arguments[t],a=0,s=o.length;a<s;a++,i++)r[i]=o[a];return r};Object.defineProperty(mn,"__esModule",{value:!0});var Xa=ge,Qa=function(){function n(){this._eventEmitter=new Xa.EventEmitter}return n.prototype.addEventListener=function(t,e){return this._eventEmitter.addListener(t,e)},n.prototype.dispatchEvent=function(t){for(var e,r=[],i=1;i<arguments.length;i++)r[i-1]=arguments[i];return(e=this._eventEmitter).emit.apply(e,Ya([t],r))},n.prototype.removeEventListener=function(t,e){return this._eventEmitter.removeListener(t,e)},n}();mn.default=Qa;var Za=B&&B.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var o in i)Object.prototype.hasOwnProperty.call(i,o)&&(r[o]=i[o])},n(t,e)};return function(t,e){n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),it=B&&B.__awaiter||function(n,t,e,r){function i(o){return o instanceof e?o:new e(function(a){a(o)})}return new(e||(e=Promise))(function(o,a){function s(p){try{u(r.next(p))}catch(f){a(f)}}function d(p){try{u(r.throw(p))}catch(f){a(f)}}function u(p){p.done?o(p.value):i(p.value).then(s,d)}u((r=r.apply(n,t||[])).next())})},ot=B&&B.__generator||function(n,t){var e={label:0,sent:function(){if(o[0]&1)throw o[1];return o[1]},trys:[],ops:[]},r,i,o,a;return a={next:s(0),throw:s(1),return:s(2)},typeof Symbol=="function"&&(a[Symbol.iterator]=function(){return this}),a;function s(u){return function(p){return d([u,p])}}function d(u){if(r)throw new TypeError("Generator is already executing.");for(;e;)try{if(r=1,i&&(o=u[0]&2?i.return:u[0]?i.throw||((o=i.return)&&o.call(i),0):i.next)&&!(o=o.call(i,u[1])).done)return o;switch(i=0,o&&(u=[u[0]&2,o.value]),u[0]){case 0:case 1:o=u;break;case 4:return e.label++,{value:u[1],done:!1};case 5:e.label++,i=u[1],u=[0];continue;case 7:u=e.ops.pop(),e.trys.pop();continue;default:if(o=e.trys,!(o=o.length>0&&o[o.length-1])&&(u[0]===6||u[0]===2)){e=0;continue}if(u[0]===3&&(!o||u[1]>o[0]&&u[1]<o[3])){e.label=u[1];break}if(u[0]===6&&e.label<o[1]){e.label=o[1],o=u;break}if(o&&e.label<o[2]){e.label=o[2],e.ops.push(u);break}o[2]&&e.ops.pop(),e.trys.pop();continue}u=t.call(n,e)}catch(p){u=[6,p],i=0}finally{r=o=0}if(u[0]&5)throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}};Object.defineProperty(hn,"__esModule",{value:!0});var ec=gn,tc=mn,nc=function(n){Za(t,n);function t(e,r,i){r===void 0&&(r={}),i===void 0&&(i={});var o=n.call(this)||this;return o._audioNode=null,o._loop=!1,o._pendingPlayDeferreds=[],o._sinkId="default",o._src="",typeof r!="string"&&(i=r),o._audioContext=e,o._audioElement=new(i.AudioFactory||Audio),o._bufferPromise=o._createPlayDeferred().promise,o._destination=o._audioContext.destination,o._gainNode=o._audioContext.createGain(),o._gainNode.connect(o._destination),o._XMLHttpRequest=i.XMLHttpRequestFactory||XMLHttpRequest,o.addEventListener("canplaythrough",function(){o._resolvePlayDeferreds()}),typeof r=="string"&&(o.src=r),o}return Object.defineProperty(t.prototype,"destination",{get:function(){return this._destination},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"loop",{get:function(){return this._loop},set:function(e){var r=this;function i(){r._audioNode.removeEventListener("ended",i),r.pause()}!e&&this.loop&&!this.paused&&this._audioNode.addEventListener("ended",i),this._loop=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"muted",{get:function(){return this._gainNode.gain.value===0},set:function(e){this._gainNode.gain.value=e?0:1},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"paused",{get:function(){return this._audioNode===null},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"src",{get:function(){return this._src},set:function(e){this._load(e)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"srcObject",{get:function(){return this._audioElement.srcObject},set:function(e){this._audioElement.srcObject=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"sinkId",{get:function(){return this._sinkId},enumerable:!1,configurable:!0}),t.prototype.load=function(){this._load(this._src)},t.prototype.pause=function(){this.paused||(this._audioElement.pause(),this._audioNode.stop(),this._audioNode.disconnect(this._gainNode),this._audioNode=null,this._rejectPlayDeferreds(new Error("The play() request was interrupted by a call to pause().")))},t.prototype.play=function(){return it(this,void 0,void 0,function(){var e,r=this;return ot(this,function(i){switch(i.label){case 0:return this.paused?[3,2]:[4,this._bufferPromise];case 1:if(i.sent(),!this.paused)return[2];throw new Error("The play() request was interrupted by a call to pause().");case 2:return this._audioNode=this._audioContext.createBufferSource(),this._audioNode.loop=this.loop,this._audioNode.addEventListener("ended",function(){r._audioNode&&r._audioNode.loop||r.dispatchEvent("ended")}),[4,this._bufferPromise];case 3:if(e=i.sent(),this.paused)throw new Error("The play() request was interrupted by a call to pause().");return this._audioNode.buffer=e,this._audioNode.connect(this._gainNode),this._audioNode.start(),this._audioElement.srcObject?[2,this._audioElement.play()]:[2]}})})},t.prototype.setSinkId=function(e){return it(this,void 0,void 0,function(){return ot(this,function(r){switch(r.label){case 0:if(typeof this._audioElement.setSinkId!="function")throw new Error("This browser does not support setSinkId.");return e===this.sinkId?[2]:e==="default"?(this.paused||this._gainNode.disconnect(this._destination),this._audioElement.srcObject=null,this._destination=this._audioContext.destination,this._gainNode.connect(this._destination),this._sinkId=e,[2]):[4,this._audioElement.setSinkId(e)];case 1:return r.sent(),this._audioElement.srcObject?[2]:(this._gainNode.disconnect(this._audioContext.destination),this._destination=this._audioContext.createMediaStreamDestination(),this._audioElement.srcObject=this._destination.stream,this._sinkId=e,this._gainNode.connect(this._destination),[2])}})})},t.prototype._createPlayDeferred=function(){var e=new ec.default;return this._pendingPlayDeferreds.push(e),e},t.prototype._load=function(e){var r=this;this._src&&this._src!==e&&this.pause(),this._src=e,this._bufferPromise=new Promise(function(i,o){return it(r,void 0,void 0,function(){var a;return ot(this,function(s){switch(s.label){case 0:return e?[4,rc(this._audioContext,this._XMLHttpRequest,e)]:[2,this._createPlayDeferred().promise];case 1:return a=s.sent(),this.dispatchEvent("canplaythrough"),i(a),[2]}})})})},t.prototype._rejectPlayDeferreds=function(e){var r=this._pendingPlayDeferreds;r.splice(0,r.length).forEach(function(i){var o=i.reject;return o(e)})},t.prototype._resolvePlayDeferreds=function(e){var r=this._pendingPlayDeferreds;r.splice(0,r.length).forEach(function(i){var o=i.resolve;return o(e)})},t}(tc.default);function rc(n,t,e){return it(this,void 0,void 0,function(){var r,i;return ot(this,function(o){switch(o.label){case 0:return r=new t,r.open("GET",e,!0),r.responseType="arraybuffer",[4,new Promise(function(a){r.addEventListener("load",a),r.send()})];case 1:i=o.sent();try{return[2,n.decodeAudioData(i.target.response)]}catch{return[2,new Promise(function(s){n.decodeAudioData(i.target.response,s)})]}return[2]}})})}hn.default=nc;Object.defineProperty(fn,"__esModule",{value:!0});var ic=vt,Qn=hn,ri=oe;function ye(n,t,e){if(!(this instanceof ye))return new ye(n,t,e);if(!n||!t)throw new ri.InvalidArgumentError("name and url are required arguments");e=Object.assign({AudioFactory:typeof Audio<"u"?Audio:null,maxDuration:0,shouldLoop:!1},e),e.AudioPlayer=e.audioContext?Qn.default.bind(Qn.default,e.audioContext):e.AudioFactory,Object.defineProperties(this,{_Audio:{value:e.AudioPlayer},_activeEls:{value:new Map},_isSinkSupported:{value:e.AudioFactory!==null&&typeof e.AudioFactory.prototype.setSinkId=="function"},_maxDuration:{value:e.maxDuration},_maxDurationTimeout:{value:null,writable:!0},_operations:{value:new ic.AsyncQueue},_playPromise:{value:null,writable:!0},_shouldLoop:{value:e.shouldLoop},_sinkIds:{value:["default"]},isPlaying:{enumerable:!0,get:function(){return!!this._playPromise}},name:{enumerable:!0,value:n},url:{enumerable:!0,value:t}}),this._Audio&&this._play(!0,!1)}function ii(n){n&&(n.pause(),n.src="",n.srcObject=null,n.load())}ye.prototype._playAudioElement=function(t,e,r){var i=this,o=this._activeEls.get(t);if(!o)throw new ri.InvalidArgumentError('sinkId: "'+t+`" doesn't have an audio element`);return o.muted=!!e,o.loop=!!r,o.play().then(function(){return o}).catch(function(a){throw ii(o),i._activeEls.delete(t),a})};ye.prototype._play=function(t,e){this.isPlaying&&this._stop(),this._maxDuration>0&&(this._maxDurationTimeout=setTimeout(this._stop.bind(this),this._maxDuration)),e=typeof e=="boolean"?e:this._shouldLoop;var r=this,i=this._playPromise=Promise.all(this._sinkIds.map(function(a){if(!r._Audio)return Promise.resolve();var s=r._activeEls.get(a);return s?r._playAudioElement(a,t,e):(s=new r._Audio(r.url),typeof s.setAttribute=="function"&&s.setAttribute("crossorigin","anonymous"),new Promise(function(d){s.addEventListener("canplaythrough",d)}).then(function(){return(r._isSinkSupported?s.setSinkId(a):Promise.resolve()).then(function(){return r._activeEls.set(a,s),r._playPromise?r._playAudioElement(a,t,e):Promise.resolve()})}))}));return i};ye.prototype._stop=function(){var t=this;this._activeEls.forEach(function(e,r){t._sinkIds.includes(r)?(e.pause(),e.currentTime=0):(ii(e),t._activeEls.delete(r))}),clearTimeout(this._maxDurationTimeout),this._playPromise=null,this._maxDurationTimeout=null};ye.prototype.setSinkIds=function(t){this._isSinkSupported&&(t=t.forEach?t:[t],[].splice.apply(this._sinkIds,[0,this._sinkIds.length].concat(t)))};ye.prototype.stop=function(){var t=this;this._operations.enqueue(function(){return t._stop(),Promise.resolve()})};ye.prototype.play=function(){var t=this;return this._operations.enqueue(function(){return t._play()})};fn.default=ye;var He={},oi={exports:{}},si={exports:{}};(function(){var n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",t={rotl:function(e,r){return e<<r|e>>>32-r},rotr:function(e,r){return e<<32-r|e>>>r},endian:function(e){if(e.constructor==Number)return t.rotl(e,8)&16711935|t.rotl(e,24)&**********;for(var r=0;r<e.length;r++)e[r]=t.endian(e[r]);return e},randomBytes:function(e){for(var r=[];e>0;e--)r.push(Math.floor(Math.random()*256));return r},bytesToWords:function(e){for(var r=[],i=0,o=0;i<e.length;i++,o+=8)r[o>>>5]|=e[i]<<24-o%32;return r},wordsToBytes:function(e){for(var r=[],i=0;i<e.length*32;i+=8)r.push(e[i>>>5]>>>24-i%32&255);return r},bytesToHex:function(e){for(var r=[],i=0;i<e.length;i++)r.push((e[i]>>>4).toString(16)),r.push((e[i]&15).toString(16));return r.join("")},hexToBytes:function(e){for(var r=[],i=0;i<e.length;i+=2)r.push(parseInt(e.substr(i,2),16));return r},bytesToBase64:function(e){for(var r=[],i=0;i<e.length;i+=3)for(var o=e[i]<<16|e[i+1]<<8|e[i+2],a=0;a<4;a++)i*8+a*6<=e.length*8?r.push(n.charAt(o>>>6*(3-a)&63)):r.push("=");return r.join("")},base64ToBytes:function(e){e=e.replace(/[^A-Z0-9+\/]/ig,"");for(var r=[],i=0,o=0;i<e.length;o=++i%4)o!=0&&r.push((n.indexOf(e.charAt(i-1))&Math.pow(2,-2*o+8)-1)<<o*2|n.indexOf(e.charAt(i))>>>6-o*2);return r}};si.exports=t})();var oc=si.exports,Ut={utf8:{stringToBytes:function(n){return Ut.bin.stringToBytes(unescape(encodeURIComponent(n)))},bytesToString:function(n){return decodeURIComponent(escape(Ut.bin.bytesToString(n)))}},bin:{stringToBytes:function(n){for(var t=[],e=0;e<n.length;e++)t.push(n.charCodeAt(e)&255);return t},bytesToString:function(n){for(var t=[],e=0;e<n.length;e++)t.push(String.fromCharCode(n[e]));return t.join("")}}},Zn=Ut;/*!
 * Determine if an object is a Buffer
 *
 * <AUTHOR> Aboukhadijeh <https://feross.org>
 * @license  MIT
 */var sc=function(n){return n!=null&&(ai(n)||ac(n)||!!n._isBuffer)};function ai(n){return!!n.constructor&&typeof n.constructor.isBuffer=="function"&&n.constructor.isBuffer(n)}function ac(n){return typeof n.readFloatLE=="function"&&typeof n.slice=="function"&&ai(n.slice(0,0))}(function(){var n=oc,t=Zn.utf8,e=sc,r=Zn.bin,i=function(o,a){o.constructor==String?a&&a.encoding==="binary"?o=r.stringToBytes(o):o=t.stringToBytes(o):e(o)?o=Array.prototype.slice.call(o,0):!Array.isArray(o)&&o.constructor!==Uint8Array&&(o=o.toString());for(var s=n.bytesToWords(o),d=o.length*8,u=**********,p=-271733879,f=-**********,v=271733878,y=0;y<s.length;y++)s[y]=(s[y]<<8|s[y]>>>24)&16711935|(s[y]<<24|s[y]>>>8)&**********;s[d>>>5]|=128<<d%32,s[(d+64>>>9<<4)+14]=d;for(var _=i._ff,b=i._gg,S=i._hh,g=i._ii,y=0;y<s.length;y+=16){var l=u,C=p,E=f,m=v;u=_(u,p,f,v,s[y+0],7,-680876936),v=_(v,u,p,f,s[y+1],12,-389564586),f=_(f,v,u,p,s[y+2],17,606105819),p=_(p,f,v,u,s[y+3],22,-**********),u=_(u,p,f,v,s[y+4],7,-176418897),v=_(v,u,p,f,s[y+5],12,1200080426),f=_(f,v,u,p,s[y+6],17,-1473231341),p=_(p,f,v,u,s[y+7],22,-45705983),u=_(u,p,f,v,s[y+8],7,1770035416),v=_(v,u,p,f,s[y+9],12,-1958414417),f=_(f,v,u,p,s[y+10],17,-42063),p=_(p,f,v,u,s[y+11],22,-1990404162),u=_(u,p,f,v,s[y+12],7,1804603682),v=_(v,u,p,f,s[y+13],12,-40341101),f=_(f,v,u,p,s[y+14],17,-1502002290),p=_(p,f,v,u,s[y+15],22,1236535329),u=b(u,p,f,v,s[y+1],5,-165796510),v=b(v,u,p,f,s[y+6],9,-1069501632),f=b(f,v,u,p,s[y+11],14,643717713),p=b(p,f,v,u,s[y+0],20,-373897302),u=b(u,p,f,v,s[y+5],5,-701558691),v=b(v,u,p,f,s[y+10],9,38016083),f=b(f,v,u,p,s[y+15],14,-660478335),p=b(p,f,v,u,s[y+4],20,-405537848),u=b(u,p,f,v,s[y+9],5,568446438),v=b(v,u,p,f,s[y+14],9,-1019803690),f=b(f,v,u,p,s[y+3],14,-187363961),p=b(p,f,v,u,s[y+8],20,1163531501),u=b(u,p,f,v,s[y+13],5,-1444681467),v=b(v,u,p,f,s[y+2],9,-51403784),f=b(f,v,u,p,s[y+7],14,1735328473),p=b(p,f,v,u,s[y+12],20,-1926607734),u=S(u,p,f,v,s[y+5],4,-378558),v=S(v,u,p,f,s[y+8],11,-2022574463),f=S(f,v,u,p,s[y+11],16,1839030562),p=S(p,f,v,u,s[y+14],23,-35309556),u=S(u,p,f,v,s[y+1],4,-1530992060),v=S(v,u,p,f,s[y+4],11,1272893353),f=S(f,v,u,p,s[y+7],16,-155497632),p=S(p,f,v,u,s[y+10],23,-1094730640),u=S(u,p,f,v,s[y+13],4,681279174),v=S(v,u,p,f,s[y+0],11,-358537222),f=S(f,v,u,p,s[y+3],16,-722521979),p=S(p,f,v,u,s[y+6],23,76029189),u=S(u,p,f,v,s[y+9],4,-640364487),v=S(v,u,p,f,s[y+12],11,-421815835),f=S(f,v,u,p,s[y+15],16,530742520),p=S(p,f,v,u,s[y+2],23,-995338651),u=g(u,p,f,v,s[y+0],6,-198630844),v=g(v,u,p,f,s[y+7],10,1126891415),f=g(f,v,u,p,s[y+14],15,-1416354905),p=g(p,f,v,u,s[y+5],21,-57434055),u=g(u,p,f,v,s[y+12],6,1700485571),v=g(v,u,p,f,s[y+3],10,-1894986606),f=g(f,v,u,p,s[y+10],15,-1051523),p=g(p,f,v,u,s[y+1],21,-2054922799),u=g(u,p,f,v,s[y+8],6,1873313359),v=g(v,u,p,f,s[y+15],10,-30611744),f=g(f,v,u,p,s[y+6],15,-1560198380),p=g(p,f,v,u,s[y+13],21,1309151649),u=g(u,p,f,v,s[y+4],6,-145523070),v=g(v,u,p,f,s[y+11],10,-1120210379),f=g(f,v,u,p,s[y+2],15,718787259),p=g(p,f,v,u,s[y+9],21,-343485551),u=u+l>>>0,p=p+C>>>0,f=f+E>>>0,v=v+m>>>0}return n.endian([u,p,f,v])};i._ff=function(o,a,s,d,u,p,f){var v=o+(a&s|~a&d)+(u>>>0)+f;return(v<<p|v>>>32-p)+a},i._gg=function(o,a,s,d,u,p,f){var v=o+(a&d|s&~d)+(u>>>0)+f;return(v<<p|v>>>32-p)+a},i._hh=function(o,a,s,d,u,p,f){var v=o+(a^s^d)+(u>>>0)+f;return(v<<p|v>>>32-p)+a},i._ii=function(o,a,s,d,u,p,f){var v=o+(s^(a|~d))+(u>>>0)+f;return(v<<p|v>>>32-p)+a},i._blocksize=16,i._digestsize=16,oi.exports=function(o,a){if(o==null)throw new Error("Illegal argument "+o);var s=n.wordsToBytes(i(o,a));return a&&a.asBytes?s:a&&a.asString?r.bytesToString(s):n.bytesToHex(s)}})();var cc=oi.exports;Object.defineProperty(He,"__esModule",{value:!0});He.generateVoiceEventSid=void 0;var Pt=cc,Xe=oe,uc=typeof Pt=="function"?Pt:Pt.default;function lc(){if(typeof window!="object")throw new Xe.NotSupportedError("This platform is not supported.");var n=window.crypto;if(typeof n!="object")throw new Xe.NotSupportedError("The `crypto` module is not available on this platform.");if(typeof(n.randomUUID||n.getRandomValues)>"u")throw new Xe.NotSupportedError("Neither `crypto.randomUUID` or `crypto.getRandomValues` are available on this platform.");var t=window.Uint32Array;if(typeof t>"u")throw new Xe.NotSupportedError("The `Uint32Array` module is not available on this platform.");var e=typeof n.randomUUID=="function"?function(){return n.randomUUID()}:function(){return n.getRandomValues(new Uint32Array(32)).toString()};return uc(e())}function dc(){return"KX"+lc()}He.generateVoiceEventSid=dc;var er;function _t(){if(er)return Je;er=1;var n=B&&B.__extends||function(){var I=function(T,P){return I=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(c,F){c.__proto__=F}||function(c,F){for(var k in F)Object.prototype.hasOwnProperty.call(F,k)&&(c[k]=F[k])},I(T,P)};return function(T,P){I(T,P);function c(){this.constructor=T}T.prototype=P===null?Object.create(P):(c.prototype=P.prototype,new c)}}(),t=B&&B.__assign||function(){return t=Object.assign||function(I){for(var T,P=1,c=arguments.length;P<c;P++){T=arguments[P];for(var F in T)Object.prototype.hasOwnProperty.call(T,F)&&(I[F]=T[F])}return I},t.apply(this,arguments)},e=B&&B.__awaiter||function(I,T,P,c){function F(k){return k instanceof P?k:new P(function(M){M(k)})}return new(P||(P=Promise))(function(k,M){function A(N){try{D(c.next(N))}catch(U){M(U)}}function L(N){try{D(c.throw(N))}catch(U){M(U)}}function D(N){N.done?k(N.value):F(N.value).then(A,L)}D((c=c.apply(I,T||[])).next())})},r=B&&B.__generator||function(I,T){var P={label:0,sent:function(){if(k[0]&1)throw k[1];return k[1]},trys:[],ops:[]},c,F,k,M;return M={next:A(0),throw:A(1),return:A(2)},typeof Symbol=="function"&&(M[Symbol.iterator]=function(){return this}),M;function A(D){return function(N){return L([D,N])}}function L(D){if(c)throw new TypeError("Generator is already executing.");for(;P;)try{if(c=1,F&&(k=D[0]&2?F.return:D[0]?F.throw||((k=F.return)&&k.call(F),0):F.next)&&!(k=k.call(F,D[1])).done)return k;switch(F=0,k&&(D=[D[0]&2,k.value]),D[0]){case 0:case 1:k=D;break;case 4:return P.label++,{value:D[1],done:!1};case 5:P.label++,F=D[1],D=[0];continue;case 7:D=P.ops.pop(),P.trys.pop();continue;default:if(k=P.trys,!(k=k.length>0&&k[k.length-1])&&(D[0]===6||D[0]===2)){P=0;continue}if(D[0]===3&&(!k||D[1]>k[0]&&D[1]<k[3])){P.label=D[1];break}if(D[0]===6&&P.label<k[1]){P.label=k[1],k=D;break}if(k&&P.label<k[2]){P.label=k[2],P.ops.push(D);break}k[2]&&P.ops.pop(),P.trys.pop();continue}D=T.call(I,P)}catch(N){D=[6,N],F=0}finally{c=k=0}if(D[0]&5)throw D[1];return{value:D[0]?D[1]:void 0,done:!0}}};Object.defineProperty(Je,"__esModule",{value:!0});var i=ge,o=Ur,a=ks(),s=ht,d=_n(),u=ie,p=en,f=oe,v=tn,y=se,_=Yr(),b=sn,S=Qr,g=_e,l=dn,C=fn,E=K,m=He,w=3e4,R=2e3,h="twilio-js-sdk",j='Parameter "token" must be of type "string".',x=function(I){n(T,I);function T(P,c){var F;c===void 0&&(c={});var k=I.call(this)||this;if(k._activeCall=null,k._audio=null,k._audioProcessorEventObserver=null,k._callInputStream=null,k._calls=[],k._callSinkIds=["default"],k._chunderURIs=[],k._defaultOptions={allowIncomingWhileBusy:!1,closeProtection:!1,codecPreferences:[d.default.Codec.PCMU,d.default.Codec.Opus],dscp:!0,enableImprovedSignalingErrorPrecision:!1,forceAggressiveIceNomination:!1,logLevel:o.levels.ERROR,maxCallSignalingTimeoutMs:0,preflight:!1,sounds:{},tokenRefreshMs:1e4,voiceEventSidGenerator:m.generateVoiceEventSid},k._edge=null,k._home=null,k._identity=null,k._log=new y.default("Device"),k._makeCallPromise=null,k._options={},k._preferredURI=null,k._publisher=null,k._region=null,k._regTimer=null,k._shouldReRegister=!1,k._soundcache=new Map,k._state=T.State.Unregistered,k._stateEventMapping=(F={},F[T.State.Destroyed]=T.EventName.Destroyed,F[T.State.Unregistered]=T.EventName.Unregistered,F[T.State.Registering]=T.EventName.Registering,F[T.State.Registered]=T.EventName.Registered,F),k._stream=null,k._streamConnectedPromise=null,k._tokenWillExpireTimeout=null,k._createDefaultPayload=function(D){var N={aggressive_nomination:k._options.forceAggressiveIceNomination,browser_extension:k._isBrowserExtension,dscp:!!k._options.dscp,ice_restart_enabled:!0,platform:g.getMediaEngine(),sdk_version:u.RELEASE_VERSION};function U(q,G){G&&(N[q]=G)}if(D){var W=D.parameters.CallSid;U("call_sid",/^TJ/.test(W)?void 0:W),U("temp_call_sid",D.outboundConnectionId),U("audio_codec",D.codec),N.direction=D.direction}return U("gateway",k._stream&&k._stream.gateway),U("region",k._stream&&k._stream.region),N},k._onSignalingClose=function(){k._stream=null,k._streamConnectedPromise=null},k._onSignalingConnected=function(D){var N,U=S.getRegionShortcode(D.region);if(k._edge=D.edge||S.regionToEdge[U]||D.region,k._region=U||D.region,k._home=D.home,(N=k._publisher)===null||N===void 0||N.setHost(S.createEventGatewayURI(D.home)),D.token&&(k._identity=D.token.identity,typeof D.token.ttl=="number"&&typeof k._options.tokenRefreshMs=="number")){var W=D.token.ttl*1e3,q=Math.max(0,W-k._options.tokenRefreshMs);k._tokenWillExpireTimeout=setTimeout(function(){k._log.debug("#tokenWillExpire"),k.emit("tokenWillExpire",k),k._tokenWillExpireTimeout&&(clearTimeout(k._tokenWillExpireTimeout),k._tokenWillExpireTimeout=null)},q)}var G=k._getChunderws()||S.getChunderURIs(k._edge);if(G.length>0){var J=G[0];k._preferredURI=S.createSignalingEndpointURL(J)}else k._log.warn("Could not parse a preferred URI from the stream#connected event.");k._shouldReRegister&&k.register()},k._onSignalingError=function(D){if(typeof D!="object"){k._log.warn("Invalid signaling error payload",D);return}var N=D.error,U=D.callsid,W=D.voiceeventsid;if(typeof N!="object"||W){k._log.warn("Ignoring signaling error payload",{originalError:N,voiceeventsid:W});return}var q=typeof U=="string"&&k._findCall(U)||void 0,G=N.code,J=N.message,Q=N.twilioError;if(typeof G=="number")if(G===31201)Q=new f.AuthorizationErrors.AuthenticationFailed(N);else if(G===31204)Q=new f.AuthorizationErrors.AccessTokenInvalid(N);else if(G===31205)k._stopRegistrationTimer(),Q=new f.AuthorizationErrors.AccessTokenExpired(N);else{var ae=f.getPreciseSignalingErrorByCode(!!k._options.enableImprovedSignalingErrorPrecision,G);typeof ae<"u"&&(Q=new ae(N))}Q||(k._log.error("Unknown signaling error: ",N),Q=new f.GeneralErrors.UnknownError(J,N)),k._log.error("Received error: ",Q),k._log.debug("#error",N),k.emit(T.EventName.Error,Q,q)},k._onSignalingInvite=function(D){return e(k,void 0,void 0,function(){var N,U,W,q,G,J=this,Q;return r(this,function(ae){switch(ae.label){case 0:if(N=!!this._activeCall,N&&!this._options.allowIncomingWhileBusy)return this._log.info("Device busy; ignoring incoming invite"),[2];if(!D.callsid||!D.sdp)return this._log.debug("#error",D),this.emit(T.EventName.Error,new f.ClientErrors.BadRequest("Malformed invite from gateway")),[2];U=D.parameters||{},U.CallSid=U.CallSid||D.callsid,W=Object.assign({},E.queryToJson(U.Params)),this._makeCallPromise=this._makeCall(W,{callParameters:U,enableImprovedSignalingErrorPrecision:!!this._options.enableImprovedSignalingErrorPrecision,offerSdp:D.sdp,reconnectToken:D.reconnect,voiceEventSidGenerator:this._options.voiceEventSidGenerator}),ae.label=1;case 1:return ae.trys.push([1,,3,4]),[4,this._makeCallPromise];case 2:return q=ae.sent(),[3,4];case 3:return this._makeCallPromise=null,[7];case 4:return this._calls.push(q),q.once("accept",function(){J._soundcache.get(T.SoundName.Incoming).stop(),J._publishNetworkChange()}),G=!((Q=this._audio)===null||Q===void 0)&&Q.incoming()&&!N?function(){return J._soundcache.get(T.SoundName.Incoming).play()}:function(){return Promise.resolve()},this._showIncomingCall(q,G),[2]}})})},k._onSignalingOffline=function(){k._log.info("Stream is offline"),k._edge=null,k._region=null,k._shouldReRegister=k.state!==T.State.Unregistered,k._setState(T.State.Unregistered)},k._onSignalingReady=function(){k._log.info("Stream is ready"),k._setState(T.State.Registered)},k._publishNetworkChange=function(){k._activeCall&&k._networkInformation&&k._publisher.info("network-information","network-change",{connection_type:k._networkInformation.type,downlink:k._networkInformation.downlink,downlinkMax:k._networkInformation.downlinkMax,effective_type:k._networkInformation.effectiveType,rtt:k._networkInformation.rtt},k._activeCall)},k._updateInputStream=function(D){var N=k._activeCall;return N&&!D?Promise.reject(new f.InvalidStateError("Cannot unset input device while a call is in progress.")):(k._callInputStream=D,N?N._setInputTracksFromStream(D):Promise.resolve())},k._updateSinkIds=function(D,N){var U=D==="ringtone"?k._updateRingtoneSinkIds(N):k._updateSpeakerSinkIds(N);return U.then(function(){k._publisher.info("audio",D+"-devices-set",{audio_device_ids:N},k._activeCall)},function(W){throw k._publisher.error("audio",D+"-devices-set-failed",{audio_device_ids:N,message:W.message},k._activeCall),W})},k._setupLoglevel(c.logLevel),k._logOptions("constructor",c),k.updateToken(P),E.isLegacyEdge())throw new f.NotSupportedError("Microsoft Edge Legacy (https://support.microsoft.com/en-us/help/4533505/what-is-microsoft-edge-legacy) is deprecated and will not be able to connect to Twilio to make or receive calls after September 1st, 2020. Please see this documentation for a list of supported browsers https://www.twilio.com/docs/voice/client/javascript#supported-browsers");if(!T.isSupported&&c.ignoreBrowserSupport)throw window&&window.location&&window.location.protocol==="http:"?new f.NotSupportedError("twilio.js wasn't able to find WebRTC browser support.           This is most likely because this page is served over http rather than https,           which does not support WebRTC in many browsers. Please load this page over https and           try again."):new f.NotSupportedError("twilio.js 1.3+ SDKs require WebRTC browser support.         For more information, see <https://www.twilio.com/docs/api/client/twilio-js>.         If you have any questions about this announcement, please contact         Twilio Support at <<EMAIL>>.");var M=globalThis,A=M.msBrowser||M.browser||M.chrome;if(k._isBrowserExtension=!!A&&!!A.runtime&&!!A.runtime.id||!!M.safari&&!!M.safari.extension,k._isBrowserExtension&&k._log.info("Running as browser extension."),navigator){var L=navigator;k._networkInformation=L.connection||L.mozConnection||L.webkitConnection}return k._networkInformation&&typeof k._networkInformation.addEventListener=="function"&&k._networkInformation.addEventListener("change",k._publishNetworkChange),T._getOrCreateAudioContext(),T._audioContext&&(T._dialtonePlayer||(T._dialtonePlayer=new p.default(T._audioContext))),typeof T._isUnifiedPlanDefault>"u"&&(T._isUnifiedPlanDefault=typeof window<"u"&&typeof RTCPeerConnection<"u"&&typeof RTCRtpTransceiver<"u"?E.isUnifiedPlanDefault(window,window.navigator,RTCPeerConnection,RTCRtpTransceiver):!1),k._boundDestroy=k.destroy.bind(k),k._boundConfirmClose=k._confirmClose.bind(k),typeof window<"u"&&window.addEventListener&&(window.addEventListener("unload",k._boundDestroy),window.addEventListener("pagehide",k._boundDestroy)),k.updateOptions(c),k}return Object.defineProperty(T,"audioContext",{get:function(){return T._audioContext},enumerable:!1,configurable:!0}),Object.defineProperty(T,"extension",{get:function(){var P=typeof document<"u"?document.createElement("audio"):{canPlayType:!1},c;try{c=P.canPlayType&&!!P.canPlayType("audio/mpeg").replace(/no/,"")}catch{c=!1}var F;try{F=P.canPlayType&&!!P.canPlayType("audio/ogg;codecs='vorbis'").replace(/no/,"")}catch{F=!1}return F&&!c?"ogg":"mp3"},enumerable:!1,configurable:!0}),Object.defineProperty(T,"isSupported",{get:function(){return g.enabled()},enumerable:!1,configurable:!0}),Object.defineProperty(T,"packageName",{get:function(){return u.PACKAGE_NAME},enumerable:!1,configurable:!0}),T.runPreflight=function(P,c){return new _.PreflightTest(P,t({audioContext:T._getOrCreateAudioContext()},c))},T.toString=function(){return"[Twilio.Device class]"},Object.defineProperty(T,"version",{get:function(){return u.RELEASE_VERSION},enumerable:!1,configurable:!0}),T._getOrCreateAudioContext=function(){return T._audioContext||(typeof AudioContext<"u"?T._audioContext=new AudioContext:typeof webkitAudioContext<"u"&&(T._audioContext=new webkitAudioContext)),T._audioContext},Object.defineProperty(T.prototype,"audio",{get:function(){return this._audio},enumerable:!1,configurable:!0}),T.prototype.connect=function(P){return P===void 0&&(P={}),e(this,void 0,void 0,function(){var c,F,k,M,A,L,D,N,U;return r(this,function(W){switch(W.label){case 0:if(this._log.debug(".connect",JSON.stringify(P)),this._throwIfDestroyed(),this._activeCall)throw new f.InvalidStateError("A Call is already active");if(P.connectToken){try{M=JSON.parse(decodeURIComponent(atob(P.connectToken))),c=M.customParameters,F=M.parameters,k=M.signalingReconnectToken}catch{throw new f.InvalidArgumentError("Cannot parse connectToken")}if(!F||!F.CallSid||!k)throw new f.InvalidArgumentError("Invalid connectToken")}A=!1,L={},D={enableImprovedSignalingErrorPrecision:!!this._options.enableImprovedSignalingErrorPrecision,rtcConfiguration:P.rtcConfiguration,voiceEventSidGenerator:this._options.voiceEventSidGenerator},k&&F?(A=!0,D.callParameters=F,D.reconnectCallSid=F.CallSid,D.reconnectToken=k,L=c||L):L=P.params||L,this._makeCallPromise=this._makeCall(L,D,A),W.label=1;case 1:return W.trys.push([1,,3,4]),U=this,[4,this._makeCallPromise];case 2:return N=U._activeCall=W.sent(),[3,4];case 3:return this._makeCallPromise=null,[7];case 4:return this._calls.splice(0).forEach(function(q){return q.ignore()}),this._soundcache.get(T.SoundName.Incoming).stop(),N.accept({rtcConstraints:P.rtcConstraints}),this._publishNetworkChange(),[2,N]}})})},Object.defineProperty(T.prototype,"calls",{get:function(){return this._calls},enumerable:!1,configurable:!0}),T.prototype.destroy=function(){var P;this._log.debug(".destroy"),this._log.debug("Rejecting any incoming calls");var c=this._calls.slice(0);c.forEach(function(F){return F.reject()}),this.disconnectAll(),this._stopRegistrationTimer(),this._destroyStream(),this._destroyAudioHelper(),(P=this._audioProcessorEventObserver)===null||P===void 0||P.destroy(),this._destroyPublisher(),this._networkInformation&&typeof this._networkInformation.removeEventListener=="function"&&this._networkInformation.removeEventListener("change",this._publishNetworkChange),typeof window<"u"&&window.removeEventListener&&(window.removeEventListener("beforeunload",this._boundConfirmClose),window.removeEventListener("unload",this._boundDestroy),window.removeEventListener("pagehide",this._boundDestroy)),this._setState(T.State.Destroyed),i.EventEmitter.prototype.removeAllListeners.call(this)},T.prototype.disconnectAll=function(){this._log.debug(".disconnectAll");var P=this._calls.splice(0);P.forEach(function(c){return c.disconnect()}),this._activeCall&&this._activeCall.disconnect()},Object.defineProperty(T.prototype,"edge",{get:function(){return this._edge},enumerable:!1,configurable:!0}),Object.defineProperty(T.prototype,"home",{get:function(){return this._home},enumerable:!1,configurable:!0}),Object.defineProperty(T.prototype,"identity",{get:function(){return this._identity},enumerable:!1,configurable:!0}),Object.defineProperty(T.prototype,"isBusy",{get:function(){return!!this._activeCall},enumerable:!1,configurable:!0}),T.prototype.register=function(){return e(this,void 0,void 0,function(){return r(this,function(P){switch(P.label){case 0:if(this._log.debug(".register"),this.state!==T.State.Unregistered)throw new f.InvalidStateError('Attempt to register when device is in state "'+this.state+'". '+('Must be "'+T.State.Unregistered+'".'));return this._shouldReRegister=!1,this._setState(T.State.Registering),[4,this._streamConnectedPromise||this._setupStream()];case 1:return P.sent(),[4,this._sendPresence(!0)];case 2:return P.sent(),[4,E.promisifyEvents(this,T.State.Registered,T.State.Unregistered)];case 3:return P.sent(),[2]}})})},Object.defineProperty(T.prototype,"state",{get:function(){return this._state},enumerable:!1,configurable:!0}),Object.defineProperty(T.prototype,"token",{get:function(){return this._token},enumerable:!1,configurable:!0}),T.prototype.toString=function(){return"[Twilio.Device instance]"},T.prototype.unregister=function(){return e(this,void 0,void 0,function(){var P,c;return r(this,function(F){switch(F.label){case 0:if(this._log.debug(".unregister"),this.state!==T.State.Registered)throw new f.InvalidStateError('Attempt to unregister when device is in state "'+this.state+'". '+('Must be "'+T.State.Registered+'".'));return this._shouldReRegister=!1,[4,this._streamConnectedPromise];case 1:return P=F.sent(),c=new Promise(function(k){P.on("offline",k)}),[4,this._sendPresence(!1)];case 2:return F.sent(),[4,c];case 3:return F.sent(),[2]}})})},T.prototype.updateOptions=function(P){if(P===void 0&&(P={}),this._logOptions("updateOptions",P),this.state===T.State.Destroyed)throw new f.InvalidStateError('Attempt to "updateOptions" when device is in state "'+this.state+'".');this._options=t(t(t({},this._defaultOptions),this._options),P);var c=new Set(this._chunderURIs),F=this._chunderURIs=(this._getChunderws()||S.getChunderURIs(this._options.edge)).map(S.createSignalingEndpointURL),k=c.size!==F.length;if(!k)for(var M=0,A=F;M<A.length;M++){var L=A[M];if(!c.has(L)){k=!0;break}}if(this.isBusy&&k)throw new f.InvalidStateError("Cannot change Edge while on an active Call");this._setupLoglevel(this._options.logLevel);for(var D=0,N=Object.keys(T._defaultSounds);D<N.length;D++){var U=N[D],W=T._defaultSounds[U],q=u.SOUNDS_BASE_URL+"/"+W.filename+"."+T.extension+("?cache="+u.RELEASE_VERSION),G=this._options.sounds&&this._options.sounds[U]||q,J=new(this._options.Sound||C.default)(U,G,{audioContext:this._options.disableAudioContextSounds?null:T.audioContext,maxDuration:W.maxDuration,shouldLoop:W.shouldLoop});this._soundcache.set(U,J)}this._setupAudioHelper(),this._setupPublisher(),k&&this._streamConnectedPromise&&this._setupStream(),typeof window<"u"&&typeof window.addEventListener=="function"&&this._options.closeProtection&&(window.removeEventListener("beforeunload",this._boundConfirmClose),window.addEventListener("beforeunload",this._boundConfirmClose))},T.prototype.updateToken=function(P){if(this._log.debug(".updateToken"),this.state===T.State.Destroyed)throw new f.InvalidStateError('Attempt to "updateToken" when device is in state "'+this.state+'".');if(typeof P!="string")throw new f.InvalidArgumentError(j);this._token=P,this._stream&&this._stream.setToken(this._token),this._publisher&&this._publisher.setToken(this._token)},T.prototype._confirmClose=function(P){if(!this._activeCall)return"";var c=this._options.closeProtection||!1,F=typeof c!="string"?"A call is currently in-progress. Leaving or reloading this page will end the call.":c;return(P||window.event).returnValue=F,F},T.prototype._destroyAudioHelper=function(){this._audio&&(this._audio._destroy(),this._audio=null)},T.prototype._destroyPublisher=function(){this._publisher&&(this._publisher=null)},T.prototype._destroyStream=function(){this._stream&&(this._stream.removeListener("close",this._onSignalingClose),this._stream.removeListener("connected",this._onSignalingConnected),this._stream.removeListener("error",this._onSignalingError),this._stream.removeListener("invite",this._onSignalingInvite),this._stream.removeListener("offline",this._onSignalingOffline),this._stream.removeListener("ready",this._onSignalingReady),this._stream.destroy(),this._stream=null),this._onSignalingOffline(),this._streamConnectedPromise=null},T.prototype._findCall=function(P){return this._calls.find(function(c){return c.parameters.CallSid===P||c.outboundConnectionId===P})||null},T.prototype._getChunderws=function(){return typeof this._options.chunderw=="string"?[this._options.chunderw]:Array.isArray(this._options.chunderw)?this._options.chunderw:null},T.prototype._logOptions=function(P,c){c===void 0&&(c={});var F=["allowIncomingWhileBusy","appName","appVersion","closeProtection","codecPreferences","disableAudioContextSounds","dscp","edge","enableImprovedSignalingErrorPrecision","forceAggressiveIceNomination","logLevel","maxAverageBitrate","maxCallSignalingTimeoutMs","sounds","tokenRefreshMs"],k=["RTCPeerConnection","enumerateDevices","getUserMedia"];if(typeof c=="object"){var M=t({},c);Object.keys(M).forEach(function(A){!F.includes(A)&&!k.includes(A)&&delete M[A],k.includes(A)&&(M[A]=!0)}),this._log.debug("."+P,JSON.stringify(M))}},T.prototype._makeCall=function(P,c,F){var k;return F===void 0&&(F=!1),e(this,void 0,void 0,function(){var M,A,L,D,N,U=this;return r(this,function(W){switch(W.label){case 0:if(typeof T._isUnifiedPlanDefault>"u")throw new f.InvalidStateError("Device has not been initialized.");return M=(k=this._audio)===null||k===void 0?void 0:k._getInputDevicePromise(),M?(this._log.debug("inputDevicePromise detected, waiting..."),[4,M]):[3,2];case 1:W.sent(),this._log.debug("inputDevicePromise resolved"),W.label=2;case 2:return N={audioHelper:this._audio,isUnifiedPlanDefault:T._isUnifiedPlanDefault,onIgnore:function(){U._soundcache.get(T.SoundName.Incoming).stop()}},[4,this._streamConnectedPromise||this._setupStream()];case 3:return A=(N.pstream=W.sent(),N.publisher=this._publisher,N.soundcache=this._soundcache,N),c=Object.assign({MediaStream:this._options.MediaStream||g.PeerConnection,RTCPeerConnection:this._options.RTCPeerConnection,beforeAccept:function(q){!U._activeCall||U._activeCall===q||(U._activeCall.disconnect(),U._removeCall(U._activeCall))},codecPreferences:this._options.codecPreferences,customSounds:this._options.sounds,dialtonePlayer:T._dialtonePlayer,dscp:this._options.dscp,forceAggressiveIceNomination:this._options.forceAggressiveIceNomination,getInputStream:function(){return U._options.fileInputStream||U._callInputStream},getSinkIds:function(){return U._callSinkIds},maxAverageBitrate:this._options.maxAverageBitrate,preflight:this._options.preflight,rtcConstraints:this._options.rtcConstraints,shouldPlayDisconnect:function(){var q;return(q=U._audio)===null||q===void 0?void 0:q.disconnect()},twimlParams:P,voiceEventSidGenerator:this._options.voiceEventSidGenerator},c),L=function(){if(!U._stream){U._log.warn("UnsetPreferredUri called without a stream");return}U._activeCall===null&&U._calls.length===0&&U._stream.updatePreferredURI(null)},D=new(this._options.Call||d.default)(A,c),this._publisher.info("settings","init",{RTCPeerConnection:!!this._options.RTCPeerConnection,enumerateDevices:!!this._options.enumerateDevices,getUserMedia:!!this._options.getUserMedia},D),D.once("accept",function(){var q,G,J;U._stream.updatePreferredURI(U._preferredURI),U._removeCall(D),U._activeCall=D,U._audio&&U._audio._maybeStartPollingVolume(),D.direction===d.default.CallDirection.Outgoing&&(!((q=U._audio)===null||q===void 0)&&q.outgoing())&&!F&&U._soundcache.get(T.SoundName.Outgoing).play();var Q={edge:U._edge||U._region};U._options.edge&&(Q.selected_edge=Array.isArray(U._options.edge)?U._options.edge:[U._options.edge]),U._publisher.info("settings","edge",Q,D),!((G=U._audio)===null||G===void 0)&&G.processedStream&&((J=U._audioProcessorEventObserver)===null||J===void 0||J.emit("enabled"))}),D.addListener("error",function(q){D.status()==="closed"&&(U._removeCall(D),L()),U._audio&&U._audio._maybeStopPollingVolume(),U._maybeStopIncomingSound()}),D.once("cancel",function(){U._log.info("Canceled: "+D.parameters.CallSid),U._removeCall(D),L(),U._audio&&U._audio._maybeStopPollingVolume(),U._maybeStopIncomingSound()}),D.once("disconnect",function(){U._audio&&U._audio._maybeStopPollingVolume(),U._removeCall(D),L(),U._maybeStopIncomingSound()}),D.once("reject",function(){U._log.info("Rejected: "+D.parameters.CallSid),U._audio&&U._audio._maybeStopPollingVolume(),U._removeCall(D),L(),U._maybeStopIncomingSound()}),D.on("transportClose",function(){D.status()===d.default.State.Pending&&(U._audio&&U._audio._maybeStopPollingVolume(),U._removeCall(D),U._maybeStopIncomingSound())}),[2,D]}})})},T.prototype._maybeStopIncomingSound=function(){this._calls.length||this._soundcache.get(T.SoundName.Incoming).stop()},T.prototype._removeCall=function(P){this._activeCall===P&&(this._activeCall=null,this._makeCallPromise=null);for(var c=this._calls.length-1;c>=0;c--)P===this._calls[c]&&this._calls.splice(c,1)},T.prototype._sendPresence=function(P){return e(this,void 0,void 0,function(){var c;return r(this,function(F){switch(F.label){case 0:return[4,this._streamConnectedPromise];case 1:return c=F.sent(),c?(c.register({audio:P}),P?this._startRegistrationTimer():this._stopRegistrationTimer(),[2]):[2]}})})},T.prototype._setState=function(P){if(P!==this.state){this._state=P;var c=this._stateEventMapping[P];this._log.debug("#"+c),this.emit(c)}},T.prototype._setupAudioHelper=function(){var P=this;this._audioProcessorEventObserver||(this._audioProcessorEventObserver=new s.AudioProcessorEventObserver,this._audioProcessorEventObserver.on("event",function(F){var k=F.name,M=F.group;P._publisher.info(M,k,{},P._activeCall)}));var c={audioContext:T.audioContext,audioProcessorEventObserver:this._audioProcessorEventObserver,beforeSetInputDevice:function(){return P._makeCallPromise?(P._log.debug("beforeSetInputDevice pause detected"),P._makeCallPromise):(P._log.debug("beforeSetInputDevice pause not detected, setting default"),Promise.resolve())},enumerateDevices:this._options.enumerateDevices,getUserMedia:this._options.getUserMedia||l.default};if(this._audio){this._log.info("Found existing audio helper; updating options..."),this._audio._updateUserOptions(c);return}this._audio=new(this._options.AudioHelper||a.default)(this._updateSinkIds,this._updateInputStream,c),this._audio.on("deviceChange",function(F){var k=P._activeCall,M=F.map(function(A){return A.deviceId});P._publisher.info("audio","device-change",{lost_active_device_ids:M},k),k&&k._mediaHandler._onInputDevicesChanged()})},T.prototype._setupLoglevel=function(P){var c=typeof P=="number"||typeof P=="string"?P:o.levels.ERROR;this._log.setDefaultLevel(c),this._log.info("Set logger default level to",c)},T.prototype._setupPublisher=function(){var P=this;this._publisher&&(this._log.info("Found existing publisher; destroying..."),this._destroyPublisher());var c={defaultPayload:this._createDefaultPayload,metadata:{app_name:this._options.appName,app_version:this._options.appVersion}};return this._options.eventgw&&(c.host=this._options.eventgw),this._home&&(c.host=S.createEventGatewayURI(this._home)),this._publisher=new(this._options.Publisher||v.default)(h,this.token,c),this._options.publishEvents===!1?this._publisher.disable():this._publisher.on("error",function(F){P._log.warn("Cannot connect to insights.",F)}),this._publisher},T.prototype._setupStream=function(){var P=this;return this._stream&&(this._log.info("Found existing stream; destroying..."),this._destroyStream()),this._log.info("Setting up VSP"),this._stream=new(this._options.PStream||b.default)(this.token,this._chunderURIs,{backoffMaxMs:this._options.backoffMaxMs,maxPreferredDurationMs:this._options.maxCallSignalingTimeoutMs}),this._stream.addListener("close",this._onSignalingClose),this._stream.addListener("connected",this._onSignalingConnected),this._stream.addListener("error",this._onSignalingError),this._stream.addListener("invite",this._onSignalingInvite),this._stream.addListener("offline",this._onSignalingOffline),this._stream.addListener("ready",this._onSignalingReady),this._streamConnectedPromise=E.promisifyEvents(this._stream,"connected","close").then(function(){return P._stream})},T.prototype._showIncomingCall=function(P,c){var F=this,k;return Promise.race([c(),new Promise(function(M,A){k=setTimeout(function(){var L="Playing incoming ringtone took too long; it might not play. Continuing execution...";A(new Error(L))},R)})]).catch(function(M){F._log.warn(M.message)}).then(function(){clearTimeout(k),F._log.debug("#incoming",JSON.stringify({customParameters:P.customParameters,parameters:P.parameters})),F.emit(T.EventName.Incoming,P)})},T.prototype._startRegistrationTimer=function(){var P=this;this._stopRegistrationTimer(),this._regTimer=setTimeout(function(){P._sendPresence(!0)},w)},T.prototype._stopRegistrationTimer=function(){this._regTimer&&clearTimeout(this._regTimer)},T.prototype._throwIfDestroyed=function(){if(this.state===T.State.Destroyed)throw new f.InvalidStateError("Device has been destroyed.")},T.prototype._updateRingtoneSinkIds=function(P){return Promise.resolve(this._soundcache.get(T.SoundName.Incoming).setSinkIds(P))},T.prototype._updateSpeakerSinkIds=function(P){Array.from(this._soundcache.entries()).filter(function(F){return F[0]!==T.SoundName.Incoming}).forEach(function(F){return F[1].setSinkIds(P)}),this._callSinkIds=P;var c=this._activeCall;return c?c._setSinkIds(P):Promise.resolve()},T._defaultSounds={disconnect:{filename:"disconnect",maxDuration:3e3},dtmf0:{filename:"dtmf-0",maxDuration:1e3},dtmf1:{filename:"dtmf-1",maxDuration:1e3},dtmf2:{filename:"dtmf-2",maxDuration:1e3},dtmf3:{filename:"dtmf-3",maxDuration:1e3},dtmf4:{filename:"dtmf-4",maxDuration:1e3},dtmf5:{filename:"dtmf-5",maxDuration:1e3},dtmf6:{filename:"dtmf-6",maxDuration:1e3},dtmf7:{filename:"dtmf-7",maxDuration:1e3},dtmf8:{filename:"dtmf-8",maxDuration:1e3},dtmf9:{filename:"dtmf-9",maxDuration:1e3},dtmfh:{filename:"dtmf-hash",maxDuration:1e3},dtmfs:{filename:"dtmf-star",maxDuration:1e3},incoming:{filename:"incoming",shouldLoop:!0},outgoing:{filename:"outgoing",maxDuration:3e3}},T}(i.EventEmitter);return function(I){(function(T){T.Error="error",T.Incoming="incoming",T.Destroyed="destroyed",T.Unregistered="unregistered",T.Registering="registering",T.Registered="registered",T.TokenWillExpire="tokenWillExpire"})(I.EventName||(I.EventName={})),function(T){T.Destroyed="destroyed",T.Unregistered="unregistered",T.Registering="registering",T.Registered="registered"}(I.State||(I.State={})),function(T){T.Incoming="incoming",T.Outgoing="outgoing",T.Disconnect="disconnect",T.Dtmf0="dtmf0",T.Dtmf1="dtmf1",T.Dtmf2="dtmf2",T.Dtmf3="dtmf3",T.Dtmf4="dtmf4",T.Dtmf5="dtmf5",T.Dtmf6="dtmf6",T.Dtmf7="dtmf7",T.Dtmf8="dtmf8",T.Dtmf9="dtmf9",T.DtmfS="dtmfs",T.DtmfH="dtmfh"}(I.SoundName||(I.SoundName={}))}(x||(x={})),Je.default=x,Je}var yt={};Object.defineProperty(yt,"__esModule",{value:!0});yt.IceCandidate=void 0;var fc=function(){function n(t,e){e===void 0&&(e=!1),this.deleted=!1;var r,i=t.candidate.split("network-cost ");i[1]&&(r=parseInt(i[1],10)),this.candidateType=t.type,this.ip=t.ip||t.address,this.isRemote=e,this.networkCost=r,this.port=t.port,this.priority=t.priority,this.protocol=t.protocol,this.relatedAddress=t.relatedAddress,this.relatedPort=t.relatedPort,this.tcpType=t.tcpType,this.transportId=t.sdpMid}return n.prototype.toPayload=function(){return{candidate_type:this.candidateType,deleted:this.deleted,ip:this.ip,is_remote:this.isRemote,"network-cost":this.networkCost,port:this.port,priority:this.priority,protocol:this.protocol,related_address:this.relatedAddress,related_port:this.relatedPort,tcp_type:this.tcpType,transport_id:this.transportId}},n}();yt.IceCandidate=fc;var vn={},Ie={};Object.defineProperty(Ie,"__esModule",{value:!0});Ie.isNonNegativeNumber=Ie.calculate=void 0;var tr=94.768;function ci(n,t,e){if(typeof n!="number"||typeof t!="number"||typeof e!="number"||!Ve(n)||!Ve(t)||!Ve(e))return null;var r=n+t*2+10,i=0;switch(!0){case r<160:i=tr-r/40;break;case r<1e3:i=tr-(r-120)/10;break}switch(!0){case e<=i/2.5:i=Math.max(i-e*2.5,6.52);break;default:i=0;break}var o=1+.035*i+7e-6*i*(i-60)*(100-i);return o}Ie.calculate=ci;function Ve(n){return typeof n=="number"&&!isNaN(n)&&isFinite(n)&&n>=0}Ie.isNonNegativeNumber=Ve;Ie.default={calculate:ci,isNonNegativeNumber:Ve};var pc=B&&B.__extends||function(){var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var o in i)Object.prototype.hasOwnProperty.call(i,o)&&(r[o]=i[o])},n(t,e)};return function(t,e){n(t,e);function r(){this.constructor=t}t.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}}(),Se=B&&B.__assign||function(){return Se=Object.assign||function(n){for(var t,e=1,r=arguments.length;e<r;e++){t=arguments[e];for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(n[i]=t[i])}return n},Se.apply(this,arguments)},ui=B&&B.__spreadArrays||function(){for(var n=0,t=0,e=arguments.length;t<e;t++)n+=arguments[t].length;for(var r=Array(n),i=0,t=0;t<e;t++)for(var o=arguments[t],a=0,s=o.length;a<s;a++,i++)r[i]=o[a];return r};Object.defineProperty(vn,"__esModule",{value:!0});var hc=ge,nr=oe,gc=Ie,mc=ke,kt=K,vc=5,_c=0,yc=3,bc=1e3,Sc=5*1e3,wc={audioInputLevel:{minStandardDeviation:327.67,sampleCount:10},audioOutputLevel:{minStandardDeviation:327.67,sampleCount:10},bytesReceived:{clearCount:2,min:1,raiseCount:3,sampleCount:3},bytesSent:{clearCount:2,min:1,raiseCount:3,sampleCount:3},jitter:{max:30},mos:{min:3},packetsLostFraction:[{max:1},{clearValue:1,maxAverage:3,sampleCount:7}],rtt:{max:400}};function Cc(n,t){return t.reduce(function(e,r){return e+=r>n?1:0},0)}function Ec(n,t){return t.reduce(function(e,r){return e+=r<n?1:0},0)}function Tc(n){if(n.length<=0)return null;var t=n.reduce(function(i,o){return i+o},0)/n.length,e=n.map(function(i){return Math.pow(i-t,2)}),r=Math.sqrt(e.reduce(function(i,o){return i+o},0)/e.length);return r}function xc(n){return n.reduce(function(t,e){return ui(t,e)},[])}var Pc=function(n){pc(t,n);function t(e){var r=n.call(this)||this;r._activeWarnings=new Map,r._currentStreaks=new Map,r._inputVolumes=[],r._outputVolumes=[],r._sampleBuffer=[],r._supplementalSampleBuffers={audioInputLevel:[],audioOutputLevel:[]},r._warningsEnabled=!0,e=e||{},r._getRTCStats=e.getRTCStats||mc.getRTCStats,r._mos=e.Mos||gc.default,r._peerConnection=e.peerConnection,r._thresholds=Se(Se({},wc),e.thresholds);var i=Object.values(r._thresholds).map(function(o){return o.sampleCount}).filter(function(o){return!!o});return r._maxSampleCount=Math.max.apply(Math,ui([vc],i)),r._peerConnection&&r.enable(r._peerConnection),r}return t.prototype.addVolumes=function(e,r){this._inputVolumes.push(e),this._outputVolumes.push(r)},t.prototype.disable=function(){return this._sampleInterval&&(clearInterval(this._sampleInterval),delete this._sampleInterval),this},t.prototype.disableWarnings=function(){return this._warningsEnabled&&this._activeWarnings.clear(),this._warningsEnabled=!1,this},t.prototype.enable=function(e){if(e){if(this._peerConnection&&e!==this._peerConnection)throw new nr.InvalidArgumentError("Attempted to replace an existing PeerConnection in StatsMonitor.enable");this._peerConnection=e}if(!this._peerConnection)throw new nr.InvalidArgumentError("Can not enable StatsMonitor without a PeerConnection");return this._sampleInterval=this._sampleInterval||setInterval(this._fetchSample.bind(this),bc),this},t.prototype.enableWarnings=function(){return this._warningsEnabled=!0,this},t.prototype.hasActiveWarning=function(e,r){var i=e+":"+r;return!!this._activeWarnings.get(i)},t.prototype._addSample=function(e){var r=this._sampleBuffer;r.push(e),r.length>this._maxSampleCount&&r.splice(0,r.length-this._maxSampleCount)},t.prototype._clearWarning=function(e,r,i){var o=e+":"+r,a=this._activeWarnings.get(o);!a||Date.now()-a.timeRaised<Sc||(this._activeWarnings.delete(o),this.emit("warning-cleared",Se(Se({},i),{name:e,threshold:{name:r,value:this._thresholds[e][r]}})))},t.prototype._createSample=function(e,r){var i=r&&r.totals.bytesSent||0,o=r&&r.totals.bytesReceived||0,a=r&&r.totals.packetsSent||0,s=r&&r.totals.packetsReceived||0,d=r&&r.totals.packetsLost||0,u=e.bytesSent-i,p=e.bytesReceived-o,f=e.packetsSent-a,v=e.packetsReceived-s,y=e.packetsLost-d,_=v+y,b=_>0?y/_*100:0,S=e.packetsReceived+e.packetsLost,g=S>0?e.packetsLost/S*100:100,l=typeof e.rtt=="number"||!r?e.rtt:r.rtt,C=this._inputVolumes.splice(0);this._supplementalSampleBuffers.audioInputLevel.push(C);var E=this._outputVolumes.splice(0);return this._supplementalSampleBuffers.audioOutputLevel.push(E),{audioInputLevel:Math.round(kt.average(C)),audioOutputLevel:Math.round(kt.average(E)),bytesReceived:p,bytesSent:u,codecName:e.codecName,jitter:e.jitter,mos:this._mos.calculate(l,e.jitter,r&&b),packetsLost:y,packetsLostFraction:b,packetsReceived:v,packetsSent:f,rtt:l,timestamp:e.timestamp,totals:{bytesReceived:e.bytesReceived,bytesSent:e.bytesSent,packetsLost:e.packetsLost,packetsLostFraction:g,packetsReceived:e.packetsReceived,packetsSent:e.packetsSent}}},t.prototype._fetchSample=function(){var e=this;this._getSample().then(function(r){e._addSample(r),e._raiseWarnings(),e.emit("sample",r)}).catch(function(r){e.disable(),e.emit("error",r)})},t.prototype._getSample=function(){var e=this;return this._getRTCStats(this._peerConnection).then(function(r){var i=null;return e._sampleBuffer.length&&(i=e._sampleBuffer[e._sampleBuffer.length-1]),e._createSample(r,i)})},t.prototype._raiseWarning=function(e,r,i){var o=e+":"+r;if(!this._activeWarnings.has(o)){this._activeWarnings.set(o,{timeRaised:Date.now()});var a=this._thresholds[e],s;if(Array.isArray(a)){var d=a.find(function(u){return r in u});d&&(s=d[r])}else s=this._thresholds[e][r];this.emit("warning",Se(Se({},i),{name:e,threshold:{name:r,value:s}}))}},t.prototype._raiseWarnings=function(){var e=this;this._warningsEnabled&&Object.keys(this._thresholds).forEach(function(r){return e._raiseWarningsForStat(r)})},t.prototype._raiseWarningsForStat=function(e){var r=this,i=Array.isArray(this._thresholds[e])?this._thresholds[e]:[this._thresholds[e]];i.forEach(function(o){var a=r._sampleBuffer,s=o.clearCount||_c,d=o.raiseCount||yc,u=o.sampleCount||r._maxSampleCount,p=a.slice(-u),f=p.map(function(m){return m[e]}),v=f.some(function(m){return typeof m>"u"||m===null});if(!v){var y;if(typeof o.max=="number"&&(y=Cc(o.max,f),y>=d?r._raiseWarning(e,"max",{values:f,samples:p}):y<=s&&r._clearWarning(e,"max",{values:f,samples:p})),typeof o.min=="number"&&(y=Ec(o.min,f),y>=d?r._raiseWarning(e,"min",{values:f,samples:p}):y<=s&&r._clearWarning(e,"min",{values:f,samples:p})),typeof o.maxDuration=="number"&&a.length>1){p=a.slice(-2);var _=p[0][e],b=p[1][e],S=r._currentStreaks.get(e)||0,g=_===b?S+1:0;r._currentStreaks.set(e,g),g>=o.maxDuration?r._raiseWarning(e,"maxDuration",{value:g}):g===0&&r._clearWarning(e,"maxDuration",{value:S})}if(typeof o.minStandardDeviation=="number"){var l=r._supplementalSampleBuffers[e];if(!l||l.length<o.sampleCount)return;l.length>o.sampleCount&&l.splice(0,l.length-o.sampleCount);var C=xc(l.slice(-u)),E=Tc(C);if(typeof E!="number")return;E<o.minStandardDeviation?r._raiseWarning(e,"minStandardDeviation",{value:E}):r._clearWarning(e,"minStandardDeviation",{value:E})}[["maxAverage",function(m,w){return m>w}],["minAverage",function(m,w){return m<w}]].forEach(function(m){var w=m[0],R=m[1];if(typeof o[w]=="number"&&f.length>=u){var h=kt.average(f);R(h,o[w])?r._raiseWarning(e,w,{values:f,samples:p}):R(h,o.clearValue||o[w])||r._clearWarning(e,w,{values:f,samples:p})}})}})},t}(hc.EventEmitter);vn.default=Pc;var rr;function _n(){if(rr)return $e;rr=1;var n=B&&B.__extends||function(){var x=function(I,T){return x=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(P,c){P.__proto__=c}||function(P,c){for(var F in c)Object.prototype.hasOwnProperty.call(c,F)&&(P[F]=c[F])},x(I,T)};return function(I,T){x(I,T);function P(){this.constructor=I}I.prototype=T===null?Object.create(T):(P.prototype=T.prototype,new P)}}(),t=B&&B.__assign||function(){return t=Object.assign||function(x){for(var I,T=1,P=arguments.length;T<P;T++){I=arguments[T];for(var c in I)Object.prototype.hasOwnProperty.call(I,c)&&(x[c]=I[c])}return x},t.apply(this,arguments)};Object.defineProperty($e,"__esModule",{value:!0});var e=ge,r=pt,i=_t(),o=oe,a=se,s=_e,d=yt,u=de,p=vn,f=K,v=He,y=ie,_={factor:1.1,jitter:.5,max:3e4,min:1},b=70,S=500,g=160,l=10,C=5e3,E={disconnect:!0,info:{code:31003,message:"Connection with Twilio was interrupted.",twilioError:new o.MediaErrors.ConnectionError}},m={packetsLostFraction:{max:"packet-loss",maxAverage:"packets-lost-fraction"}},w={audioInputLevel:"audio-input-level",audioOutputLevel:"audio-output-level",bytesReceived:"bytes-received",bytesSent:"bytes-sent",jitter:"jitter",mos:"mos",rtt:"rtt"},R={max:"high-",maxAverage:"high-",maxDuration:"constant-",min:"low-",minStandardDeviation:"constant-"},h=function(x){n(I,x);function I(T,P){var c=x.call(this)||this;c.parameters={},c._inputVolumeStreak=0,c._isAnswered=!1,c._isCancelled=!1,c._isRejected=!1,c._latestInputVolume=0,c._latestOutputVolume=0,c._log=new a.default("Call"),c._mediaStatus=I.State.Pending,c._messages=new Map,c._metricsSamples=[],c._options={MediaHandler:s.PeerConnection,enableImprovedSignalingErrorPrecision:!1,offerSdp:null,shouldPlayDisconnect:function(){return!0},voiceEventSidGenerator:v.generateVoiceEventSid},c._outputVolumeStreak=0,c._shouldSendHangup=!0,c._signalingStatus=I.State.Pending,c._soundcache=new Map,c._status=I.State.Pending,c._wasConnected=!1,c.toString=function(){return"[Twilio.Call instance]"},c._emitWarning=function(A,L,D,N,U,W){var q=U?"-cleared":"-raised",G=A+"warning"+q;if(!(L==="constant-audio-input-level"&&c.isMuted())){var J=U?"info":"warning";L==="constant-audio-output-level"&&(J="info");var Q={threshold:D};if(N&&(N instanceof Array?Q.values=N.map(function(Re){return typeof Re=="number"?Math.round(Re*100)/100:N}):Q.value=N),c._publisher.post(J,G,L,{data:Q},c),L!=="constant-audio-output-level"){var ae=U?"warning-cleared":"warning";c._log.debug("#"+ae,L),c.emit(ae,L,W&&!U?W:null)}}},c._onAck=function(A){var L=A.acktype,D=A.callsid,N=A.voiceeventsid;if(c.parameters.CallSid!==D){c._log.warn("Received ack from a different callsid: "+D);return}L==="message"&&c._onMessageSent(N)},c._onAnswer=function(A){typeof A.reconnect=="string"&&(c._signalingReconnectToken=A.reconnect),!(c._isAnswered&&c._status!==I.State.Reconnecting)&&(c._setCallSid(A),c._isAnswered=!0,c._maybeTransitionToOpen())},c._onCancel=function(A){var L=A.callsid;c.parameters.CallSid===L&&(c._isCancelled=!0,c._publisher.info("connection","cancel",null,c),c._cleanupEventListeners(),c._mediaHandler.close(),c._status=I.State.Closed,c._log.debug("#cancel"),c.emit("cancel"),c._pstream.removeListener("cancel",c._onCancel))},c._onConnected=function(){c._log.info("Received connected from pstream"),c._signalingReconnectToken&&c._mediaHandler.version&&c._pstream.reconnect(c._mediaHandler.version.getSDP(),c.parameters.CallSid,c._signalingReconnectToken)},c._onHangup=function(A){if(c.status()!==I.State.Closed){if(A.callsid&&(c.parameters.CallSid||c.outboundConnectionId)){if(A.callsid!==c.parameters.CallSid&&A.callsid!==c.outboundConnectionId)return}else if(A.callsid)return;if(c._log.info("Received HANGUP from gateway"),A.error){var L=A.error.code,D=o.getPreciseSignalingErrorByCode(c._options.enableImprovedSignalingErrorPrecision,L),N=typeof D<"u"?new D(A.error.message):new o.GeneralErrors.ConnectionError("Error sent from gateway in HANGUP",A.error);c._log.error("Received an error from the gateway:",N),c._log.debug("#error",N),c.emit("error",N)}c._shouldSendHangup=!1,c._publisher.info("connection","disconnected-by-remote",null,c),c._disconnect(null,!0),c._cleanupEventListeners()}},c._onMediaFailure=function(A){var L=I.MediaFailure,D=L.ConnectionDisconnected,N=L.ConnectionFailed,U=L.IceGatheringFailed,W=L.LowBytes,q=A===N||A===U;if(!f.isChrome(window,window.navigator)&&A===N)return c._mediaHandler.onerror(E);if(c._mediaStatus===I.State.Reconnecting){if(q){if(Date.now()-c._mediaReconnectStartTime>_.max)return c._log.warn("Exceeded max ICE retries"),c._mediaHandler.onerror(E);try{c._mediaReconnectBackoff.backoff()}catch(Re){if(!(Re.message&&Re.message==="Backoff in progress."))throw Re}}return}var G=c._mediaHandler.version.pc,J=G&&G.iceConnectionState==="disconnected",Q=c._monitor.hasActiveWarning("bytesSent","min")||c._monitor.hasActiveWarning("bytesReceived","min");if(A===W&&J||A===D&&Q||q){var ae=new o.MediaErrors.ConnectionError("Media connection failed.");c._log.warn("ICE Connection disconnected."),c._publisher.warn("connection","error",ae,c),c._publisher.info("connection","reconnecting",null,c),c._mediaReconnectStartTime=Date.now(),c._status=I.State.Reconnecting,c._mediaStatus=I.State.Reconnecting,c._mediaReconnectBackoff.reset(),c._mediaReconnectBackoff.backoff(),c._log.debug("#reconnecting"),c.emit("reconnecting",ae)}},c._onMediaReconnected=function(){c._mediaStatus===I.State.Reconnecting&&(c._log.info("ICE Connection reestablished."),c._mediaStatus=I.State.Open,c._signalingStatus===I.State.Open&&(c._publisher.info("connection","reconnected",null,c),c._log.debug("#reconnected"),c.emit("reconnected"),c._status=I.State.Open))},c._onMessageReceived=function(A){var L=A.callsid,D=A.content,N=A.contenttype,U=A.messagetype,W=A.voiceeventsid;if(c.parameters.CallSid!==L){c._log.warn("Received a message from a different callsid: "+L);return}var q={content:D,contentType:N,messageType:U,voiceEventSid:W};c._publisher.info("call-message",U,{content_type:N,event_type:"received",voice_event_sid:W},c),c._log.debug("#messageReceived",JSON.stringify(q)),c.emit("messageReceived",q)},c._onMessageSent=function(A){if(!c._messages.has(A)){c._log.warn("Received a messageSent with a voiceEventSid that doesn't exists: "+A);return}var L=c._messages.get(A);c._messages.delete(A),c._publisher.info("call-message",L==null?void 0:L.messageType,{content_type:L==null?void 0:L.contentType,event_type:"sent",voice_event_sid:A},c),c._log.debug("#messageSent",JSON.stringify(L)),c.emit("messageSent",L)},c._onRinging=function(A){if(c._setCallSid(A),!(c._status!==I.State.Connecting&&c._status!==I.State.Ringing)){var L=!!A.sdp;c._status=I.State.Ringing,c._publisher.info("connection","outgoing-ringing",{hasEarlyMedia:L},c),c._log.debug("#ringing"),c.emit("ringing",L)}},c._onRTCSample=function(A){var L=t(t({},A),{inputVolume:c._latestInputVolume,outputVolume:c._latestOutputVolume});c._codec=L.codecName,c._metricsSamples.push(L),c._metricsSamples.length>=l&&c._publishMetrics(),c.emit("sample",A)},c._onSignalingError=function(A){var L=A.callsid,D=A.voiceeventsid,N=A.error;if(c.parameters.CallSid!==L){c._log.warn("Received an error from a different callsid: "+L);return}if(D&&c._messages.has(D)){c._messages.delete(D),c._log.warn("Received an error while sending a message.",A),c._publisher.error("call-message","error",{code:N.code,message:N.message,voice_event_sid:D},c);var U=void 0,W=o.getPreciseSignalingErrorByCode(!!c._options.enableImprovedSignalingErrorPrecision,N.code);typeof W<"u"&&(U=new W(N)),U||(c._log.error("Unknown Call Message Error: ",N),U=new o.GeneralErrors.UnknownError(N.message,N)),c._log.debug("#error",N,U),c.emit("error",U)}},c._onSignalingReconnected=function(){c._signalingStatus===I.State.Reconnecting&&(c._log.info("Signaling Connection reestablished."),c._signalingStatus=I.State.Open,c._mediaStatus===I.State.Open&&(c._publisher.info("connection","reconnected",null,c),c._log.debug("#reconnected"),c.emit("reconnected"),c._status=I.State.Open))},c._onTransportClose=function(){c._log.error("Received transportClose from pstream"),c._log.debug("#transportClose"),c.emit("transportClose"),c._signalingReconnectToken?(c._status=I.State.Reconnecting,c._signalingStatus=I.State.Reconnecting,c._log.debug("#reconnecting"),c.emit("reconnecting",new o.SignalingErrors.ConnectionDisconnected)):(c._status=I.State.Closed,c._signalingStatus=I.State.Closed)},c._reemitWarning=function(A,L){var D=/^audio/.test(A.name)?"audio-level-":"network-quality-",N=R[A.threshold.name],U;A.name in m?U=m[A.name][A.threshold.name]:A.name in w&&(U=w[A.name]);var W=N+U;c._emitWarning(D,W,A.threshold.value,A.values||A.value,L,A)},c._reemitWarningCleared=function(A){c._reemitWarning(A,!0)},c._isUnifiedPlanDefault=T.isUnifiedPlanDefault,c._soundcache=T.soundcache,typeof T.onIgnore=="function"&&(c._onIgnore=T.onIgnore);var F=P&&P.twimlParams||{};c.customParameters=new Map(Object.entries(F).map(function(A){var L=A[0],D=A[1];return[L,String(D)]})),Object.assign(c._options,P),c._options.callParameters&&(c.parameters=c._options.callParameters),c._options.reconnectToken&&(c._signalingReconnectToken=c._options.reconnectToken),c._voiceEventSidGenerator=c._options.voiceEventSidGenerator||v.generateVoiceEventSid,c._direction=c.parameters.CallSid&&!c._options.reconnectCallSid?I.CallDirection.Incoming:I.CallDirection.Outgoing,c.parameters?c.callerInfo=c.parameters.StirStatus?{isVerified:c.parameters.StirStatus==="TN-Validation-Passed-A"}:null:c.callerInfo=null,c._mediaReconnectBackoff=new r.default(_),c._mediaReconnectBackoff.on("ready",function(){return c._mediaHandler.iceRestart()}),c.outboundConnectionId=j();var k=c._publisher=T.publisher;c._direction===I.CallDirection.Incoming?k.info("connection","incoming",null,c):k.info("connection","outgoing",{preflight:c._options.preflight,reconnect:!!c._options.reconnectCallSid},c);var M=c._monitor=new(c._options.StatsMonitor||p.default);return M.on("sample",c._onRTCSample),M.disableWarnings(),setTimeout(function(){return M.enableWarnings()},C),M.on("warning",function(A,L){(A.name==="bytesSent"||A.name==="bytesReceived")&&c._onMediaFailure(I.MediaFailure.LowBytes),c._reemitWarning(A,L)}),M.on("warning-cleared",function(A){c._reemitWarningCleared(A)}),c._mediaHandler=new c._options.MediaHandler(T.audioHelper,T.pstream,{RTCPeerConnection:c._options.RTCPeerConnection,codecPreferences:c._options.codecPreferences,dscp:c._options.dscp,forceAggressiveIceNomination:c._options.forceAggressiveIceNomination,isUnifiedPlan:c._isUnifiedPlanDefault,maxAverageBitrate:c._options.maxAverageBitrate}),c.on("volume",function(A,L){c._inputVolumeStreak=c._checkVolume(A,c._inputVolumeStreak,c._latestInputVolume,"input"),c._outputVolumeStreak=c._checkVolume(L,c._outputVolumeStreak,c._latestOutputVolume,"output"),c._latestInputVolume=A,c._latestOutputVolume=L}),c._mediaHandler.onaudio=function(A){c._log.debug("#audio"),c.emit("audio",A)},c._mediaHandler.onvolume=function(A,L,D,N){M.addVolumes(D/255*32767,N/255*32767),c.emit("volume",A,L)},c._mediaHandler.ondtlstransportstatechange=function(A){var L=A==="failed"?"error":"debug";c._publisher.post(L,"dtls-transport-state",A,null,c)},c._mediaHandler.onpcconnectionstatechange=function(A){var L="debug",D=c._mediaHandler.getRTCDtlsTransport();A==="failed"&&(L=D&&D.state==="failed"?"error":"warning"),c._publisher.post(L,"pc-connection-state",A,null,c)},c._mediaHandler.onicecandidate=function(A){var L=new d.IceCandidate(A).toPayload();c._publisher.debug("ice-candidate","ice-candidate",L,c)},c._mediaHandler.onselectedcandidatepairchange=function(A){var L=new d.IceCandidate(A.local).toPayload(),D=new d.IceCandidate(A.remote,!0).toPayload();c._publisher.debug("ice-candidate","selected-ice-candidate-pair",{local_candidate:L,remote_candidate:D},c)},c._mediaHandler.oniceconnectionstatechange=function(A){var L=A==="failed"?"error":"debug";c._publisher.post(L,"ice-connection-state",A,null,c)},c._mediaHandler.onicegatheringfailure=function(A){c._publisher.warn("ice-gathering-state",A,null,c),c._onMediaFailure(I.MediaFailure.IceGatheringFailed)},c._mediaHandler.onicegatheringstatechange=function(A){c._publisher.debug("ice-gathering-state",A,null,c)},c._mediaHandler.onsignalingstatechange=function(A){c._publisher.debug("signaling-state",A,null,c)},c._mediaHandler.ondisconnected=function(A){c._log.warn(A),c._publisher.warn("network-quality-warning-raised","ice-connectivity-lost",{message:A},c),c._log.debug("#warning","ice-connectivity-lost"),c.emit("warning","ice-connectivity-lost"),c._onMediaFailure(I.MediaFailure.ConnectionDisconnected)},c._mediaHandler.onfailed=function(A){c._onMediaFailure(I.MediaFailure.ConnectionFailed)},c._mediaHandler.onconnected=function(){c._status===I.State.Reconnecting&&c._onMediaReconnected()},c._mediaHandler.onreconnected=function(A){c._log.info(A),c._publisher.info("network-quality-warning-cleared","ice-connectivity-lost",{message:A},c),c._log.debug("#warning-cleared","ice-connectivity-lost"),c.emit("warning-cleared","ice-connectivity-lost"),c._onMediaReconnected()},c._mediaHandler.onerror=function(A){A.disconnect===!0&&c._disconnect(A.info&&A.info.message);var L=A.info.twilioError||new o.GeneralErrors.UnknownError(A.info.message);c._log.error("Received an error from MediaStream:",A),c._log.debug("#error",L),c.emit("error",L)},c._mediaHandler.onopen=function(){c._status===I.State.Open||c._status===I.State.Reconnecting||(c._status===I.State.Ringing||c._status===I.State.Connecting?(c.mute(c._mediaHandler.isMuted),c._mediaStatus=I.State.Open,c._maybeTransitionToOpen()):c._mediaHandler.close())},c._mediaHandler.onclose=function(){c._status=I.State.Closed,c._options.shouldPlayDisconnect&&c._options.shouldPlayDisconnect()&&!c._isCancelled&&!c._isRejected&&c._soundcache.get(i.default.SoundName.Disconnect).play(),M.disable(),c._publishMetrics(),!c._isCancelled&&!c._isRejected&&(c._log.debug("#disconnect"),c.emit("disconnect",c))},c._pstream=T.pstream,c._pstream.on("ack",c._onAck),c._pstream.on("cancel",c._onCancel),c._pstream.on("error",c._onSignalingError),c._pstream.on("ringing",c._onRinging),c._pstream.on("transportClose",c._onTransportClose),c._pstream.on("connected",c._onConnected),c._pstream.on("message",c._onMessageReceived),c.on("error",function(A){c._publisher.error("connection","error",{code:A.code,message:A.message},c),c._pstream&&c._pstream.status==="disconnected"&&c._cleanupEventListeners()}),c.on("disconnect",function(){c._cleanupEventListeners()}),c}return Object.defineProperty(I.prototype,"direction",{get:function(){return this._direction},enumerable:!1,configurable:!0}),Object.defineProperty(I.prototype,"codec",{get:function(){return this._codec},enumerable:!1,configurable:!0}),Object.defineProperty(I.prototype,"connectToken",{get:function(){var T=this,P=this._signalingReconnectToken,c=this.parameters&&this.parameters.CallSid?this.parameters.CallSid:void 0;if(!(!P||!c)){var F=this.customParameters&&typeof this.customParameters.keys=="function"?Array.from(this.customParameters.keys()).reduce(function(M,A){return M[A]=T.customParameters.get(A),M},{}):{},k=this.parameters||{};return btoa(encodeURIComponent(JSON.stringify({customParameters:F,parameters:k,signalingReconnectToken:P})))}},enumerable:!1,configurable:!0}),I.prototype._setInputTracksFromStream=function(T){return this._mediaHandler.setInputTracksFromStream(T)},I.prototype._setSinkIds=function(T){return this._mediaHandler._setSinkIds(T)},I.prototype.accept=function(T){var P=this;if(this._log.debug(".accept",T),this._status!==I.State.Pending){this._log.debug(".accept noop. status is '"+this._status+"'");return}T=T||{};var c=T.rtcConfiguration||this._options.rtcConfiguration,F=T.rtcConstraints||this._options.rtcConstraints||{},k={audio:typeof F.audio<"u"?F.audio:!0};this._status=I.State.Connecting;var M=function(){if(P._status!==I.State.Connecting){P._cleanupEventListeners(),P._mediaHandler.close();return}var D=function(W){var q=P._direction===I.CallDirection.Incoming?"accepted-by-local":"accepted-by-remote";P._publisher.info("connection",q,null,P);var G=u.getPreferredCodecInfo(P._mediaHandler.version.getSDP()),J=G.codecName,Q=G.codecParams;P._publisher.info("settings","codec",{codec_params:Q,selected_codec:J},P),P._monitor.enable(W)},N=typeof P._options.getSinkIds=="function"&&P._options.getSinkIds();if(Array.isArray(N)&&P._mediaHandler._setSinkIds(N).catch(function(){}),P._pstream.addListener("hangup",P._onHangup),P._direction===I.CallDirection.Incoming)P._isAnswered=!0,P._pstream.on("answer",P._onAnswer),P._mediaHandler.answerIncomingCall(P.parameters.CallSid,P._options.offerSdp,c,D);else{var U=Array.from(P.customParameters.entries()).map(function(W){return encodeURIComponent(W[0])+"="+encodeURIComponent(W[1])}).join("&");P._pstream.on("answer",P._onAnswer),P._mediaHandler.makeOutgoingCall(U,P._signalingReconnectToken,P._options.reconnectCallSid||P.outboundConnectionId,c,D)}};this._options.beforeAccept&&this._options.beforeAccept(this);var A=typeof this._options.getInputStream=="function"&&this._options.getInputStream(),L=A?this._mediaHandler.setInputTracksFromStream(A):this._mediaHandler.openDefaultDeviceWithConstraints(k);L.then(function(){P._publisher.info("get-user-media","succeeded",{data:{audioConstraints:k}},P),M()},function(D){var N;D.code===31208||["PermissionDeniedError","NotAllowedError"].indexOf(D.name)!==-1?(N=new o.UserMediaErrors.PermissionDeniedError,P._publisher.error("get-user-media","denied",{data:{audioConstraints:k,error:D}},P)):(N=new o.UserMediaErrors.AcquisitionFailedError,P._publisher.error("get-user-media","failed",{data:{audioConstraints:k,error:D}},P)),P._disconnect(),P._log.debug("#error",D),P.emit("error",N)})},I.prototype.disconnect=function(){this._log.debug(".disconnect"),this._disconnect()},I.prototype.getLocalStream=function(){return this._mediaHandler&&this._mediaHandler.stream},I.prototype.getRemoteStream=function(){return this._mediaHandler&&this._mediaHandler._remoteStream},I.prototype.ignore=function(){if(this._log.debug(".ignore"),this._status!==I.State.Pending){this._log.debug(".ignore noop. status is '"+this._status+"'");return}this._status=I.State.Closed,this._mediaHandler.ignore(this.parameters.CallSid),this._publisher.info("connection","ignored-by-local",null,this),this._onIgnore&&this._onIgnore()},I.prototype.isMuted=function(){return this._mediaHandler.isMuted},I.prototype.mute=function(T){T===void 0&&(T=!0),this._log.debug(".mute",T);var P=this._mediaHandler.isMuted;this._mediaHandler.mute(T);var c=this._mediaHandler.isMuted;P!==c&&(this._publisher.info("connection",c?"muted":"unmuted",null,this),this._log.debug("#mute",c),this.emit("mute",c,this))},I.prototype.postFeedback=function(T,P){if(typeof T>"u"||T===null)return this._postFeedbackDeclined();if(!Object.values(I.FeedbackScore).includes(T))throw new o.InvalidArgumentError("Feedback score must be one of: "+Object.values(I.FeedbackScore));if(typeof P<"u"&&P!==null&&!Object.values(I.FeedbackIssue).includes(P))throw new o.InvalidArgumentError("Feedback issue must be one of: "+Object.values(I.FeedbackIssue));return this._publisher.info("feedback","received",{issue_name:P,quality_score:T},this,!0)},I.prototype.reject=function(){if(this._log.debug(".reject"),this._status!==I.State.Pending){this._log.debug(".reject noop. status is '"+this._status+"'");return}this._isRejected=!0,this._pstream.reject(this.parameters.CallSid),this._mediaHandler.reject(this.parameters.CallSid),this._publisher.info("connection","rejected-by-local",null,this),this._cleanupEventListeners(),this._mediaHandler.close(),this._status=I.State.Closed,this._log.debug("#reject"),this.emit("reject")},I.prototype.sendDigits=function(T){var P=this;if(this._log.debug(".sendDigits",T),T.match(/[^0-9*#w]/))throw new o.InvalidArgumentError("Illegal character passed into sendDigits");var c=this._options.customSounds||{},F=[];T.split("").forEach(function(D){var N=D!=="w"?"dtmf"+D:"";N==="dtmf*"&&(N="dtmfs"),N==="dtmf#"&&(N="dtmfh"),F.push(N)});var k=function(){var D=F.shift();D&&(P._options.dialtonePlayer&&!c[D]?P._options.dialtonePlayer.play(D):P._soundcache.get(D).play()),F.length&&setTimeout(function(){return k()},200)};k();var M=this._mediaHandler.getOrCreateDTMFSender();function A(D){if(D.length){var N=D.shift();N&&N.length&&M.insertDTMF(N,g,b),setTimeout(A.bind(null,D),S)}}if(M){if(!("canInsertDTMF"in M)||M.canInsertDTMF){this._log.info("Sending digits using RTCDTMFSender"),A(T.split("w"));return}this._log.info("RTCDTMFSender cannot insert DTMF")}if(this._log.info("Sending digits over PStream"),this._pstream!==null&&this._pstream.status!=="disconnected")this._pstream.dtmf(this.parameters.CallSid,T);else{var L=new o.GeneralErrors.ConnectionError("Could not send DTMF: Signaling channel is disconnected");this._log.debug("#error",L),this.emit("error",L)}},I.prototype.sendMessage=function(T){this._log.debug(".sendMessage",JSON.stringify(T));var P=T.content,c=T.contentType,F=T.messageType;if(typeof P>"u"||P===null)throw new o.InvalidArgumentError("`content` is empty");if(typeof F!="string")throw new o.InvalidArgumentError("`messageType` must be a string.");if(F.length===0)throw new o.InvalidArgumentError("`messageType` must be a non-empty string.");if(this._pstream===null)throw new o.InvalidStateError("Could not send CallMessage; Signaling channel is disconnected");var k=this.parameters.CallSid;if(typeof this.parameters.CallSid>"u")throw new o.InvalidStateError("Could not send CallMessage; Call has no CallSid");var M=this._voiceEventSidGenerator();return this._messages.set(M,{content:P,contentType:c,messageType:F,voiceEventSid:M}),this._pstream.sendMessage(k,P,c,F,M),M},I.prototype.status=function(){return this._status},I.prototype._checkVolume=function(T,P,c,F){var k=P>=10,M=0;return c===T&&(M=P),M>=10?this._emitWarning("audio-level-","constant-audio-"+F+"-level",10,M,!1):k&&this._emitWarning("audio-level-","constant-audio-"+F+"-level",10,M,!0),M},I.prototype._cleanupEventListeners=function(){var T=this,P=function(){T._pstream&&(T._pstream.removeListener("ack",T._onAck),T._pstream.removeListener("answer",T._onAnswer),T._pstream.removeListener("cancel",T._onCancel),T._pstream.removeListener("error",T._onSignalingError),T._pstream.removeListener("hangup",T._onHangup),T._pstream.removeListener("ringing",T._onRinging),T._pstream.removeListener("transportClose",T._onTransportClose),T._pstream.removeListener("connected",T._onConnected),T._pstream.removeListener("message",T._onMessageReceived))};P(),setTimeout(P,0)},I.prototype._createMetricPayload=function(){var T={call_sid:this.parameters.CallSid,dscp:!!this._options.dscp,sdk_version:y.RELEASE_VERSION};return this._options.gateway&&(T.gateway=this._options.gateway),T.direction=this._direction,T},I.prototype._disconnect=function(T,P){if(T=typeof T=="string"?T:null,!(this._status!==I.State.Open&&this._status!==I.State.Connecting&&this._status!==I.State.Reconnecting&&this._status!==I.State.Ringing)){if(this._log.info("Disconnecting..."),this._pstream!==null&&this._pstream.status!=="disconnected"&&this._shouldSendHangup){var c=this.parameters.CallSid||this.outboundConnectionId;c&&this._pstream.hangup(c,T)}this._cleanupEventListeners(),this._mediaHandler.close(),P||this._publisher.info("connection","disconnected-by-local",null,this)}},I.prototype._maybeTransitionToOpen=function(){this._wasConnected,this._isAnswered&&(this._onSignalingReconnected(),this._signalingStatus=I.State.Open,this._mediaHandler&&this._mediaHandler.status==="open"&&(this._status=I.State.Open,this._wasConnected||(this._wasConnected=!0,this._log.debug("#accept"),this.emit("accept",this))))},I.prototype._postFeedbackDeclined=function(){return this._publisher.info("feedback","received-none",null,this,!0)},I.prototype._publishMetrics=function(){var T=this;this._metricsSamples.length!==0&&this._publisher.postMetrics("quality-metrics-samples","metrics-sample",this._metricsSamples.splice(0),this._createMetricPayload(),this).catch(function(P){T._log.warn("Unable to post metrics to Insights. Received error:",P)})},I.prototype._setCallSid=function(T){var P=T.callsid;P&&(this.parameters.CallSid=P,this._mediaHandler.callSid=P)},I.toString=function(){return"[Twilio.Call class]"},I}(e.EventEmitter);(function(x){(function(I){I.Closed="closed",I.Connecting="connecting",I.Open="open",I.Pending="pending",I.Reconnecting="reconnecting",I.Ringing="ringing"})(x.State||(x.State={})),function(I){I.AudioLatency="audio-latency",I.ChoppyAudio="choppy-audio",I.DroppedCall="dropped-call",I.Echo="echo",I.NoisyCall="noisy-call",I.OneWayAudio="one-way-audio"}(x.FeedbackIssue||(x.FeedbackIssue={})),function(I){I[I.One=1]="One",I[I.Two=2]="Two",I[I.Three=3]="Three",I[I.Four=4]="Four",I[I.Five=5]="Five"}(x.FeedbackScore||(x.FeedbackScore={})),function(I){I.Incoming="INCOMING",I.Outgoing="OUTGOING"}(x.CallDirection||(x.CallDirection={})),function(I){I.Opus="opus",I.PCMU="pcmu"}(x.Codec||(x.Codec={})),function(I){I.None="none",I.Timeout="timeout"}(x.IceGatheringFailureReason||(x.IceGatheringFailureReason={})),function(I){I.ConnectionDisconnected="ConnectionDisconnected",I.ConnectionFailed="ConnectionFailed",I.IceGatheringFailed="IceGatheringFailed",I.LowBytes="LowBytes"}(x.MediaFailure||(x.MediaFailure={}))})(h||(h={}));function j(){return"TJSxxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(x){var I=Math.random()*16|0,T=x==="x"?I:I&3|8;return T.toString(16)})}return $e.default=h,$e}(function(n){Object.defineProperty(n,"__esModule",{value:!0}),n.TwilioError=n.Logger=n.PreflightTest=n.Device=n.Call=void 0;var t=_n();n.Call=t.default;var e=_t();n.Device=e.default;var r=oe;n.TwilioError=r;var i=se;Object.defineProperty(n,"Logger",{enumerable:!0,get:function(){return i.Logger}});var o=Yr();Object.defineProperty(n,"PreflightTest",{enumerable:!0,get:function(){return o.PreflightTest}})})(kr);bi.forwardRef((n,t)=>O.jsx(Si,{direction:"up",ref:t,...n}));const Lc=({open:n,onClose:t,toNumber:e,candidate:r})=>{const i=z.useRef(null),o=z.useRef(null),[a,s]=z.useState("Connecting..."),[d,u]=z.useState(!1),[p,f]=z.useState(1),[v,y]=z.useState(!1),[_,b]=z.useState(""),S=async()=>(await(await fetch("https://x74v19wh-5001.inc1.devtunnels.ms/twillio/token?identity=browser_user")).json()).token,g=async()=>{y(!0),b("");try{const h=await S(),j=new kr.Device(h,{codecPreferences:["opus","pcmu"],debug:!0});j.on("ready",()=>{console.log("Twilio Device is ready"),s("Ready to Call"),y(!1)}),j.on("error",x=>{console.error("Twilio Error:",x),s(`Error: ${x.message}`),b(x.message),y(!1)}),j.on("connect",()=>{console.log("Call connected"),s("On Call")}),j.on("disconnect",()=>{console.log("Call disconnected"),s("Call Ended")}),j.on("incoming",x=>{console.log("Incoming call:",x),x.accept()}),o.current&&(j.audio.outgoing=o.current),i.current=j}catch(h){console.error("Device initialization failed:",h),b("Failed to initialize device."),y(!1)}};z.useEffect(()=>(n&&g(),()=>{var h;(h=i.current)==null||h.destroy(),console.log("Twilio Device destroyed")}),[n]),z.useEffect(()=>{o.current&&(o.current.volume=p)},[p]);const l=()=>{if(!i.current){b("Device not ready. Please wait...");return}if(!e){b("No number to call.");return}s("Calling...");const h={To:e},j=i.current.connect({params:h});j.on("accept",()=>{console.log("Call accepted"),s("Call Connected")}),j.on("disconnect",()=>{console.log("Call disconnected"),s("Call Ended")}),j.on("error",x=>{console.error("Call error:",x),s(`Error: ${x.message}`)})},C=()=>{var h;s("Call Ended"),(h=i.current)==null||h.disconnectAll(),t()},E=()=>{var j,x;const h=(j=i.current)==null?void 0:j.activeConnection();if(h){const I=!d;(x=h.mute)==null||x.call(h,I),u(I)}},m=()=>{f(h=>Math.min(1,h+.1))},w=()=>{f(h=>Math.max(0,h-.1))},R=h=>{f(Number(h.target.value))};return O.jsxs(qt,{open:n,onClose:t,fullScreen:!1,maxWidth:!1,PaperProps:{style:{width:600,height:800,maxWidth:"90vw",maxHeight:"90vh",borderRadius:24,boxShadow:"0 8px 32px 0 rgba(31, 38, 135, 0.37)",background:"linear-gradient(135deg, #0f2027 0%, #2c5364 100%)",color:"white",display:"flex",alignItems:"center",justifyContent:"center",padding:0}},children:[O.jsx("audio",{ref:o,style:{display:"none"}}),O.jsxs(Z,{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",height:"100%",width:"100%",sx:{position:"relative"},children:[v&&O.jsx(le,{variant:"h6",sx:{color:"#94ff76",mb:2},children:"Press Green Button to connect"}),_&&O.jsx(le,{variant:"h6",sx:{color:"#ff7675",mb:2},children:_}),O.jsxs(Z,{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",mt:4,children:[O.jsx(Ht,{alt:"Contact",src:"https://ui-avatars.com/api/?name=User",sx:{width:100,height:100,mb:2,boxShadow:"0 8px 32px 0 rgba(31, 38, 135, 0.37)",border:"4px solid #fff",background:"linear-gradient(135deg, #43cea2 0%, #185a9d 100%)"}}),O.jsx(le,{variant:"h5",sx:{fontWeight:600,letterSpacing:1,color:"#fff"},children:e||"Unknown"})]}),O.jsxs(Z,{display:"flex",justifyContent:"center",alignItems:"center",gap:2,mt:6,children:[O.jsx(De,{onClick:E,size:"large",sx:{background:d?"rgba(255,255,255,0.15)":"rgba(255,255,255,0.08)",color:d?"#ff7675":"#fff",width:60,height:60,borderRadius:"50%",boxShadow:d?"0 0 0 2px #ff7675":"none",transition:"all 0.2s"},children:O.jsx(to,{fontSize:"large"})}),O.jsx(De,{onClick:l,sx:{background:"#4caf50",color:"white",width:60,height:60,borderRadius:"50%",boxShadow:"0 4px 20px 0 rgba(76,175,80,0.3)",mx:2},children:O.jsx(Zi,{fontSize:"inherit",style:{fontSize:40}})}),O.jsx(De,{onClick:C,sx:{background:"#f44336",color:"white",width:60,height:60,borderRadius:"50%",boxShadow:"0 4px 20px 0 rgba(244,67,54,0.3)",mx:2},children:O.jsx(eo,{fontSize:"inherit",style:{fontSize:40}})}),O.jsxs(De,{size:"large",sx:{background:"rgba(255,255,255,0.08)",color:"white",width:60,height:60,borderRadius:"50%"},onClick:w,title:"Decrease volume",children:[O.jsx(Rn,{style:{transform:"rotate(360deg)"},fontSize:"large"}),"-"]}),O.jsx("input",{type:"range",min:0,max:1,step:.01,value:p,onChange:R,style:{width:60,margin:"0 8px",verticalAlign:"middle"},title:"Adjust vol6me"}),O.jsxs(De,{size:"large",sx:{background:"rgba(255,255,255,0.08)",color:"white",width:60,height:60,borderRadius:"50%"},onClick:m,title:"Increase volume",children:[O.jsx(Rn,{fontSize:"large"}),"+"]})]}),O.jsx(le,{variant:"caption",sx:{color:"#b2bec3",mt:1,textAlign:"center"},children:"Adjust call volume using the slider or buttons above."}),O.jsx(le,{variant:"caption",sx:{mt:6,color:"#b2bec3",fontWeight:400,letterSpacing:1},children:"Tap red to end call"})]})]})};export{Oc as S,Lc as T,Mc as U,Dc as W,jc as a};
