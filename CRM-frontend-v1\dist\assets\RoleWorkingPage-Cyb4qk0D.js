import{r as d,af as oe,ag as ne,a7 as ie,ae as q,j as e,d as je,e as B,s as i,az as Q,u as ae,a2 as E,av as p,bi as we,aY as re,aZ as De,aC as $e,aD as ze,aE as _e,C as Le,aw as Ne,bj as U,bk as Ue,a4 as Z,bl as Be,R as be,bm as We,bn as H,bo as G,y as ee,aA as Me,aI as Oe,aK as le,aJ as Ve,bp as fe}from"./index-C8YzRPez.js";/* empty css                          */import{F as se}from"./FocusPointBox-BS774mbV.js";import{C as Ge}from"./CVFormatWithJSPDF-DAO_IRU3.js";import{F as He}from"./FileSaver.min-Cr1ZWOZa.js";import{F as ve}from"./index-BWafDGV1.js";import{F as Je,a as de,R as te,b as Qe}from"./EditOutlined-tFjA3QW-.js";import{T as Ye}from"./Timeline-x8bERb7R.js";import{M as X}from"./index-1vqLypFX.js";import{S as xe}from"./index-CV2s8tJR.js";import{D as Ke,U as Se}from"./index-mWLKL6OE.js";import{P as Xe}from"./progress-DT8MvaHz.js";import{F as Ze}from"./FilePreview-Dz6aRE4w.js";import"./DeleteOutlined-BzDmogJC.js";import"./useClosable-D2fLMMij.js";const qe="/assets/morning-C9NLkkVc.png";var et={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M484 443.1V528h-84.5c-4.1 0-7.5 3.1-7.5 7v42c0 3.8 3.4 7 7.5 7H484v84.9c0 3.9 3.2 7.1 7 7.1h42c3.9 0 7-3.2 7-7.1V584h84.5c4.1 0 7.5-3.2 7.5-7v-42c0-3.9-3.4-7-7.5-7H540v-84.9c0-3.9-3.1-7.1-7-7.1h-42c-3.8 0-7 3.2-7 7.1zm396-144.7H521L403.7 186.2a8.15 8.15 0 00-5.5-2.2H144c-17.7 0-32 14.3-32 32v592c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V330.4c0-17.7-14.3-32-32-32zM840 768H184V256h188.5l119.6 114.4H840V768z"}}]},name:"folder-add",theme:"outlined"},tt={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M885.2 446.3l-.2-.8-112.2-285.1c-5-16.1-19.9-27.2-36.8-27.2H281.2c-17 0-32.1 11.3-36.9 27.6L139.4 443l-.3.7-.2.8c-1.3 4.9-1.7 9.9-1 14.8-.1 1.6-.2 3.2-.2 4.8V830a60.9 60.9 0 0060.8 60.8h627.2c33.5 0 60.8-27.3 60.9-60.8V464.1c0-1.3 0-2.6-.1-3.7.4-4.9 0-9.6-1.3-14.1zm-295.8-43l-.3 15.7c-.8 44.9-31.8 75.1-77.1 75.1-22.1 0-41.1-7.1-54.8-20.6S436 441.2 435.6 419l-.3-15.7H229.5L309 210h399.2l81.7 193.3H589.4zm-375 76.8h157.3c24.3 57.1 76 90.8 140.4 90.8 33.7 0 65-9.4 90.3-27.2 22.2-15.6 39.5-37.4 50.7-63.6h156.5V814H214.4V480.1z"}}]},name:"inbox",theme:"outlined"},st=function(n,o){return d.createElement(oe,ne({},n,{ref:o,icon:Ke}))},ot=d.forwardRef(st),nt=function(n,o){return d.createElement(oe,ne({},n,{ref:o,icon:Je}))},at=d.forwardRef(nt),rt=function(n,o){return d.createElement(oe,ne({},n,{ref:o,icon:et}))},lt=d.forwardRef(rt),it=function(n,o){return d.createElement(oe,ne({},n,{ref:o,icon:tt}))},dt=d.forwardRef(it);function ct(r){const n=ie(f=>f.user.userId),[o,y]=d.useState({}),{roleId:a}=q(),x=(f,l)=>{try{je({roleId:a,type:f,userId:n,message:l,added_at:new Date().toISOString(),updated_at:new Date().toISOString()},B.addFocusPoint,T=>{g()},T=>{var k;i.error(((k=T==null?void 0:T.response)==null?void 0:k.message)||"Failed to add focus point. Try refreshing the page!")})}catch{i.error("Failed to add focus point. Try refreshing the page!")}},g=()=>{try{Q({roleId:a,userId:null},B.getRoleFocusPoints,f=>{y(f)},f=>{var l;i.error(((l=f==null?void 0:f.response)==null?void 0:l.message)||"Failed to get focus points. Try refreshing the page!")})}catch{i.error("Failed to get focus points. Try refreshing the page!")}};return d.useEffect(()=>{g()},[a]),e.jsxs("div",{style:{width:"100%",padding:"15px"},children:[e.jsx(se,{messages:o==null?void 0:o.INFO,onSendMessage:f=>{x("INFO",f)},title:"Info Box"}),e.jsx("br",{}),e.jsx(se,{messages:o==null?void 0:o.QUERY,onSendMessage:f=>{x("QUERY",f)},title:"Query Box"}),e.jsx("br",{}),e.jsx(se,{messages:o==null?void 0:o.SUGGESTION,onSendMessage:f=>{x("SUGGESTION",f)},title:"Suggestion Box"}),e.jsx("br",{}),e.jsx(se,{messages:o==null?void 0:o.ISSUE,onSendMessage:f=>{x("ISSUE",f)},title:"Issue Box"})]})}function ht(){const{roleService:r,roleId:n}=q(),o=decodeURIComponent(r),y=ie(c=>c.user.userId),a=ae(),[x,g]=d.useState([]),[f,l]=d.useState(!1),[T,k]=d.useState(null),[D,O]=d.useState(""),[S,b]=d.useState(!1),[$,w]=d.useState(!1),[m,h]=d.useState({current:1,pageSize:10,total:0}),P=[{title:"Name",dataIndex:"full_name",key:"full_name",render:c=>e.jsx("a",{href:c,target:"_blank",rel:"noopener noreferrer",style:{color:"#1A84DE",textDecoration:"none",fontWeight:500,cursor:"pointer"},children:c||"N/A"})},{title:"Linkedin",dataIndex:"profile_url",key:"profile_url",render:c=>e.jsx("a",{href:c,target:"_blank",rel:"noopener noreferrer",style:{color:"#1A84DE",textDecoration:"none",fontWeight:500,cursor:"pointer"},children:c||"N/A"})},{title:"LI Status",dataIndex:"candidate_status",key:"candidate_status",render:c=>e.jsx("p",{style:{backgroundColor:c==="PENDING"?"red":"#E6FFED",color:c==="PENDING"?"white":"#4CAF50",margin:0,borderRadius:"4px",display:"flex",justifyContent:"center",alignItems:"center",padding:"5px 4px",textAlign:"center",fontWeight:500,fontSize:"10px",width:"95px"},children:c||"N/A"})},{title:"Actions",dataIndex:"actions",key:"actions",align:"center",render:(c,A)=>e.jsxs("div",{style:{display:"flex",justifyContent:"center",gap:"15px",alignItems:"center"},children:[e.jsx(E,{type:"primary",style:{color:"#344665",border:"2px solid #344665",borderRadius:0,height:"30px",width:"85px",fontSize:"14px",backgroundColor:"white",fontWeight:500},onClick:()=>{console.log("record",A),O(A.li_type),k(A.candidate),b(!0),w(!1)},children:"Preview"}),e.jsx(p,{icon:"ic:outline-download",width:"22",height:"22",style:{cursor:"pointer"},onClick:()=>{k(A.candidate),b(!0),w(!0)}}),e.jsx(p,{icon:"ic:baseline-delete",width:"22",height:"22",onClick:()=>{we({},`${B.deleteRoleCandidate}?id=${A.id}`,C=>{i.success("Candidate deleted successfully!"),u()},C=>{var F;i.error(((F=C==null?void 0:C.response)==null?void 0:F.message)||"Failed to delete candidate. Try again!")})}}),e.jsx(p,{icon:"icon-park-outline:reverse-rotation",width:"20",height:"20",onClick:()=>{Q({roleId:n,userId:y,profile_url:A.profile_url},B.changeCandidateStatus,C=>{i.success("Candidate status changed successfully!"),u()},C=>{var F;i.error(((F=C==null?void 0:C.response)==null?void 0:F.message)||"Failed to change candidate status. Try again!")})}})]})}],u=(c=1,A=10)=>{l(!0),Q({roleId:n,type:"LINKEDIN",page:c,limit:A},B.getRoleCandidates,C=>{const F=[];C==null||C.forEach(v=>{var _,W,J,V;F.push({id:v==null?void 0:v.id,key:(_=v==null?void 0:v.candidate)==null?void 0:_.profile_url,full_name:((W=v==null?void 0:v.candidate)==null?void 0:W.first_name)+" "+((J=v==null?void 0:v.candidate)==null?void 0:J.last_name),candidate_status:v==null?void 0:v.candidate_status,candidate:v==null?void 0:v.candidate,profile_url:(V=v==null?void 0:v.candidate)==null?void 0:V.profile_url,li_type:v==null?void 0:v.li_type})}),g(F),h({current:c,pageSize:A,total:(C==null?void 0:C.total)||0}),l(!1)},C=>{var F;i.error(((F=C==null?void 0:C.response)==null?void 0:F.message)||"Failed to get role LIs."),l(!1)})};d.useEffect(()=>{u()},[n]),console.log("roledfsfdsfLIs",T);const j=()=>{const c=x,A=_=>({Status:_.li_type,"Candidate Name":`${_.candidate.first_name||""} ${_.candidate.last_name||""}`.trim(),Email:(_.candidate.emails||[]).map(W=>W.email).join(" | ")||"",Number:(_.candidate.phones||[]).map(W=>W.phone_number).join(" | ")||"",Location:_.candidate.location||""}),C=re.book_new(),F=re.json_to_sheet(c.map(_=>A(_)));re.book_append_sheet(C,F,"LinkedIn Profiles");const v=De(C,{bookType:"xlsx",type:"array"});He.saveAs(new Blob([v],{type:"application/octet-stream"}),"Candidate_Sheet.xlsx")};return e.jsxs("div",{style:{width:"100%",padding:"10px"},children:[e.jsxs(ve,{style:{marginTop:0},children:[e.jsx(E,{type:"primary",icon:e.jsx(p,{icon:"material-symbols:add",width:"18",height:"18"}),style:{borderRadius:"8px",height:"35px",width:"90px",fontSize:"14px",backgroundColor:"#1A84DE",fontWeight:500},onClick:()=>{a(`/${encodeURIComponent(o)}/${n}/role-details/add-lis`)},children:"Add LIs"}),e.jsx(E,{type:"primary",icon:e.jsx(p,{icon:"ic:outline-download",width:"18",height:"18"}),style:{borderRadius:"8px",height:"35px",width:"90px",marginLeft:"10px",fontSize:"14px",backgroundColor:"#1A84DE",fontWeight:500},onClick:()=>j(),children:"Excel"}),e.jsx(E,{type:"primary",icon:e.jsx(p,{icon:"material-symbols:refresh-rounded",width:"18",height:"18"}),style:{borderRadius:"8px",height:"35px",width:"35px",marginLeft:"10px",fontSize:"14px",backgroundColor:"#1A84DE",fontWeight:500},onClick:()=>u()})]}),e.jsx("div",{style:{backgroundColor:"#F5F5F5",padding:"10px",borderRadius:"8px",marginTop:"10px",display:"flex",justifyContent:"space-between"},children:e.jsxs("p",{style:{margin:0,fontSize:"14px",fontWeight:500},children:["Total LIs: ",e.jsx("span",{style:{color:"#1A84DE"},children:x==null?void 0:x.length})]})}),e.jsx(de,{dataSource:x,columns:P,style:{marginTop:"10px"},rowClassName:()=>"custom-row",loading:f,pagination:{current:m.current,pageSize:m.pageSize,total:m.total,onChange:(c,A)=>u(c,A)}}),S&&e.jsx(Ge,{open:S,onClose:()=>b(!1),candidate:T,downloadLiAsPdf:$,liType:D})]})}const pt=({open:r,onClose:n,cvUrl:o})=>{console.log("CVPreviewDialog props:",{open:r,onClose:n,cvUrl:o});const[y,a]=d.useState(!0),x=(o==null?void 0:o.trim())!==""?o:null,g=x==null?void 0:x.split(".").pop().toLowerCase(),f=g==="pdf",l=g==="doc"||g==="docx",T=x?`https://docs.google.com/gview?url=${encodeURIComponent(x)}&embedded=true`:null,k=()=>{a(!1)};return e.jsxs($e,{open:r,onClose:n,maxWidth:"lg",fullWidth:!0,children:[e.jsx(ze,{children:"CV Preview"}),e.jsxs(_e,{style:{backgroundColor:"#F8F8F8",height:"80vh",position:"relative"},children:[y&&l&&e.jsx("div",{style:{position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)",zIndex:1},children:e.jsxs("p",{style:{display:"flex",alignItems:"center",justifyContent:"center"},children:["Loading preview... ",e.jsx(Le,{style:{color:"black"},thickness:2,size:20})]})}),f&&x&&e.jsx("iframe",{src:x,onLoad:k,style:{width:"100%",height:"100%",border:"none",visibility:y?"hidden":"visible"},title:"PDF Preview"}),l&&T&&e.jsx("iframe",{src:T,onLoad:k,style:{width:"100%",height:"100%",border:"none",visibility:y?"hidden":"visible"},title:"DOCX Preview"}),!f&&!l&&e.jsx("p",{children:"No Preview for Role description. Either description file is not found or file type is not supported"})]})]})};function gt(r){const[n,o]=d.useState(!1),[y,a]=d.useState(""),x=[{title:"Name",dataIndex:"name",key:"name"},{title:"CV Status",dataIndex:"candidate_status",key:"candidate_status",render:w=>e.jsx("p",{style:{backgroundColor:w==="PENDING"?"red":"#E6FFED",color:w==="PENDING"?"white":"#4CAF50",margin:0,borderRadius:"4px",display:"flex",justifyContent:"center",alignItems:"center",padding:"5px 4px",textAlign:"center",fontWeight:500,fontSize:"10px",width:"95px"},children:w||"N/A"})},{title:"Actions",dataIndex:"actions",key:"actions",align:"center",render:(w,m)=>e.jsxs("div",{style:{display:"flex",justifyContent:"center",gap:"15px",alignItems:"center"},children:[e.jsx(E,{type:"primary",style:{color:"#344665",border:"2px solid #344665",borderRadius:0,height:"30px",width:"85px",fontSize:"14px",backgroundColor:"white",fontWeight:500},onClick:()=>{var h;a((h=m==null?void 0:m.candidate)==null?void 0:h.cv_path),o(!0)},children:"Preview"}),e.jsx(p,{icon:"ic:outline-download",width:"22",height:"22",onClick:async()=>{var h,P,u,j;try{const c=await U.get(B.downloadFileFromS3Bucket,{params:{fileUrl:(h=m==null?void 0:m.candidate)==null?void 0:h.cv_path},responseType:"blob"}),A=c.headers["content-type"]||"application/octet-stream",C=new Blob([c.data],{type:A}),F=document.createElement("a"),v=((u=(P=m==null?void 0:m.candidate)==null?void 0:P.cv_path)==null?void 0:u.split("/").pop())||"downloaded_file";F.href=window.URL.createObjectURL(C),F.setAttribute("download",v),document.body.appendChild(F),F.click(),F.remove(),window.URL.revokeObjectURL(F.href)}catch(c){i.error(((j=c==null?void 0:c.response)==null?void 0:j.message)||"Failed to download CV. Try again!")}}}),e.jsx(p,{icon:"ic:baseline-delete",width:"22",height:"22",onClick:()=>{console.log("kdfdfrecord",m);try{we({},`${B.deleteRoleCandidate}?id=${m.id}`,h=>{i.success("Candidate deleted successfully!"),$()},h=>{var P;i.error(((P=h==null?void 0:h.response)==null?void 0:P.message)||"Failed to delete candidate. Try again!")})}catch{}}})]})}],{roleService:g,roleId:f}=q(),l=decodeURIComponent(g),T=ae(),[k,D]=d.useState([]),[O,S]=d.useState(!1),b=ie(w=>w.user.userId),$=()=>{try{S(!0),Q({roleId:f,userId:b,type:"CV"},B.getRoleCandidates,w=>{const m=[];w==null||w.forEach(h=>{var P,u,j,c;m.push({id:h==null?void 0:h.id,key:(P=h==null?void 0:h.candidate)==null?void 0:P.profile_url,name:((u=h==null?void 0:h.candidate)==null?void 0:u.first_name)+" "+((j=h==null?void 0:h.candidate)==null?void 0:j.last_name),candidate_status:h==null?void 0:h.candidate_status,candidate:h==null?void 0:h.candidate,profile_url:(c=h==null?void 0:h.candidate)==null?void 0:c.profile_url})}),D(m),S(!1)},w=>{var m;i.error(((m=w==null?void 0:w.response)==null?void 0:m.message)||"Failed to get role CVs. Try refreshing the page!"),S(!1)})}catch{i.error("Failed to get role CVs. Try refreshing the page!"),S(!1)}};return d.useEffect(()=>{$()},[f]),e.jsxs("div",{style:{width:"100%",padding:"10px",paddingTop:"0px"},children:[e.jsxs(ve,{style:{marginTop:0},children:[e.jsxs(Ne,{children:["Total CVs: ",k==null?void 0:k.length]}),e.jsx(E,{type:"primary",icon:e.jsx(p,{icon:"mdi:upload-outline",width:"18",height:"18"}),style:{borderRadius:"8px",height:"35px",width:"120px",marginLeft:"10px",fontSize:"14px",backgroundColor:"#1A84DE",fontFamily:"Poppins",fontWeight:500},onClick:()=>{T(`/${encodeURIComponent(l)}/${f}/role-details/add-cvs`)},children:"Upload CVs"}),e.jsx(E,{type:"primary",icon:e.jsx(p,{icon:"mdi:download-outline",width:"18",height:"18"}),style:{borderRadius:"8px",height:"35px",width:"fit-content",marginLeft:"10px",fontSize:"14px",backgroundColor:"#1A84DE",fontFamily:"Poppins",fontWeight:500},onClick:()=>{let w=[];k.forEach(m=>{var h;w.push((h=m==null?void 0:m.candidate)==null?void 0:h.cv_path)}),console.log("s3BucketUrls",w);try{Q({fileUrls:w},B.downloadAllCandidates,m=>{const h=new Blob([m.data],{type:"application/zip"}),P=window.URL.createObjectURL(h),u=document.createElement("a");u.href=P,u.setAttribute("download","cvs.zip"),document.body.appendChild(u),u.click(),u.remove()},m=>{i.error("Failed to download files")})}catch{i.error("Failed to download file")}},children:"Download All Cvs"})]}),e.jsx(de,{dataSource:k,columns:x,style:{marginTop:"10px"},rowClassName:()=>"custom-row",loading:O}),n&&e.jsx(pt,{open:n,onClose:()=>o(!1),cvUrl:y})]})}function ut(r){return e.jsxs("div",{style:{width:"100%"},children:[e.jsx(ht,{}),e.jsx(gt,{})]})}const{TextArea:ft}=Z;function xt(r){const n=ae(),{roleService:o,roleId:y}=q(),[a,x]=d.useState(""),g=decodeURIComponent(o),[f,l]=d.useState(!1),[T,k]=d.useState(!1),[D,O]=d.useState(),[S,b]=d.useState(""),w=(()=>{const j=new Date().getHours();let c;return j>=5&&j<12?c="Good Morning":j>=12&&j<17?c="Good Afternoon":j>=17&&j<21?c="Good Evening":c="Good night",c})();console.log("dsadsads",D);const m=()=>{const u=B.getRoleById.replace(":id",y);try{Q({},u,j=>{O(j)},j=>{var c;i.error(((c=j==null?void 0:j.response)==null?void 0:c.message)||"Failed to get role details. Try refreshing the page!")})}catch{i.error("Failed to get role details. Try refreshing the page!")}};d.useEffect(()=>{m()},[]);function h(u){const j=u.match(/"Boolean_String"\s*:\s*"([\s\S]*?)"(?=\s*[},])/);if(!j)throw new Error("Boolean_String not found");const A=j[1].replace(new RegExp('(?<!\\\\)"',"g"),'\\"'),C=u.replace(j[0],`"Boolean_String": "${A}"`);return JSON.parse(C)}const P=()=>{try{k(!0);const u=new FormData;u.append("url",D==null?void 0:D.attachments),je(u,Be.generateBooleanString,j=>{const c=h(j);b(c),k(!1),l(!0)},j=>{var c;i.error(((c=j==null?void 0:j.response)==null?void 0:c.message)||"Failed to generate boolean string. Try refreshing the page!"),k(!1),l(!1)})}catch{i.error("Failed to generate boolean string. Try refreshing the page!"),k(!1),l(!1)}};return console.log("dasdsa",S),e.jsxs("div",{style:{width:"100%",padding:"10px"},children:[e.jsxs("div",{style:{display:"flex",justifyContent:"center",flexDirection:"column",alignItems:"center"},children:[e.jsx("img",{src:qe,alt:"morning",height:"100px",width:"170px"}),e.jsxs("p",{style:{marginTop:"10px",fontSize:"20px",fontWeight:500},children:[w," "]}),"      "]}),e.jsx("div",{style:{marginBottom:"10px",textAlign:"left"},children:e.jsx("p",{style:{fontSize:"16px",fontWeight:500,color:"#333"},children:"How would you like to search?"})}),e.jsx(Ue,{name:"searchType",options:[{label:"Search by Boolean",value:"boolean"},{label:"Search by Filter",value:"filter"},{label:"Search by Both",value:"both"}],value:a,onChange:u=>{console.log("searchdsf",u),x(u),u.value==="filter"?n(`/${encodeURIComponent(g)}/${y}/role-details/search-results`,{state:{booleanString:S}}):u.value==="both"&&n(`/${encodeURIComponent(g)}/${y}/role-details/search-results`,{state:{booleanString:S}})},isClearable:!0,placeholder:"",className:"w-full",classNamePrefix:"react-select",menuPosition:"fixed",styles:{menu:u=>({...u,zIndex:100,maxHeight:"450px",overflowY:"auto"}),menuList:u=>({...u,maxHeight:"450px"}),control:u=>({...u,minHeight:"44px"})}}),(a==null?void 0:a.value)==="boolean"&&e.jsxs(e.Fragment,{children:[e.jsx("div",{style:{display:"flex",justifyContent:"center",marginTop:"20px"},children:e.jsx(Z,{placeholder:"Start searching by typing boolean string",style:{padding:"10px",height:"50px",borderRadius:0}})}),!f&&e.jsx("div",{style:{display:"flex",justifyContent:"center",marginTop:"10px"},children:e.jsx("p",{style:{marginTop:"5px",fontSize:"16px",fontWeight:500},children:"OR"})}),e.jsx("div",{style:{display:"flex",justifyContent:"center",marginTop:"10px"},children:e.jsx(E,{type:"primary",style:{borderRadius:0,height:"50px",width:"220px",fontSize:"16px",backgroundColor:"#1A84DE",fontWeight:500},onClick:()=>{P()},children:T?"Generating...":"Generate Boolean String"})})]}),e.jsx("br",{}),f&&e.jsxs(e.Fragment,{children:[e.jsx(ft,{rows:8,value:S==null?void 0:S.Boolean_String}),e.jsxs("div",{style:{display:"flex",justifyContent:"center",marginTop:"20px",gap:"100px"},children:[e.jsx(E,{type:"primary",style:{borderRadius:0,color:"#1A84DE",height:"50px",width:"200px",fontSize:"16px",border:"2px solid #1A84DE",backgroundColor:"white",fontWeight:500},onClick:()=>{P()},children:T?"Generating...":"Generate again"}),e.jsx(E,{type:"primary",style:{borderRadius:0,height:"50px",width:"200px",fontSize:"16px",backgroundColor:"#1A84DE",fontWeight:500},onClick:()=>{n(`/${encodeURIComponent(g)}/${y}/role-details/search-results`,{state:{booleanString:S}})},children:"Use this string"})]})]}),e.jsx("br",{})]})}function yt(){const{roleId:r}=q(),[n,o]=be.useState([]),y=()=>{try{Q({},B.getRoleActivity.replace(":roleId",r),a=>{const x=a==null?void 0:a.map(g=>({action:g==null?void 0:g.action,status:(g==null?void 0:g.log_status_type)+" at "+(g==null?void 0:g.log_status_at),date:g==null?void 0:g.created_at.split("T")[0],time:g==null?void 0:g.time_spent,by:(g==null?void 0:g.user.first_name)+" "+(g==null?void 0:g.user.last_name)}));o(x)},a=>{var x;i.error(((x=a==null?void 0:a.response)==null?void 0:x.message)||"Failed to get role timeline. Try refreshing the page!")})}catch{i.error("Failed to get role timeline. Try refreshing the page!")}};return d.useEffect(()=>{y()},[r]),e.jsx("div",{style:{width:"100%",marginLeft:"20px",height:"100%"},children:e.jsx(Ye,{mode:"alternate",items:[{children:e.jsx("div",{}),dot:e.jsx("span",{style:{display:"none"}})},(n==null?void 0:n.length)===0&&{children:e.jsxs("div",{style:{padding:"20px",textAlign:"center"},children:[e.jsx("p",{style:{fontWeight:600,fontFamily:"Poppins",fontSize:"16px"},children:"No Activity Found"}),e.jsx("p",{style:{fontWeight:400,fontFamily:"Poppins",fontSize:"14px"},children:"This role has no activity yet."})]}),dot:e.jsx("span",{style:{display:"none"}})},...n==null?void 0:n.flatMap(a=>[{children:e.jsxs("div",{style:{backgroundColor:"#EBFAFF",padding:"15px",borderLeft:"2px solid #1A84DE",flexDirection:"column"},children:[e.jsx("p",{style:{fontWeight:600,fontFamily:"Poppins",margin:0},children:a.status}),e.jsx("p",{style:{fontWeight:400,fontFamily:"Poppins",fontSize:"12px",marginTop:"5px"},children:a.date}),e.jsx("p",{style:{fontWeight:400,fontFamily:"Poppins",fontSize:"12px",marginTop:"5px"},children:a.time})]}),color:"#1A84DE",dot:e.jsx("span",{style:{display:"none"}})},{children:e.jsxs("div",{children:[e.jsx("p",{style:{fontWeight:400,fontFamily:"Poppins",fontSize:"14px",marginTop:"5px",backgroundColor:"#1A84DE",padding:"5px 10px",borderRadius:"20px",color:"white",width:`${a.status.length*8+40}px`},children:a.status}),e.jsxs("p",{style:{fontWeight:400,fontFamily:"Poppins",fontSize:"12px",marginTop:"-10px",marginLeft:"15px"},children:["By ",a.by]})]}),dot:e.jsx(p,{icon:"mdi:square",width:"16",height:"16"})}])]})})}const ye=[{key:"1",label:"Focus Points",component:e.jsx(ct,{})},{key:"2",label:"Add Candidates",component:e.jsx(ut,{})},{key:"3",label:"Search Strings",component:e.jsx(xt,{})},{key:"4",label:"Activity",component:e.jsx(yt,{})}];function mt(r){var y;const[n,o]=d.useState("1");return e.jsxs(e.Fragment,{children:[e.jsx("div",{style:{width:"100%",padding:"10px",position:"sticky",top:0,zIndex:1,backgroundColor:"white"},children:e.jsx(We,{defaultActiveKey:"1",items:ye.map(a=>({...a,label:e.jsx("span",{style:{color:"#424242",fontWeight:400,border:"none",marginBottom:0,fontSize:"14px",transition:"color 0.3s ease-in-out",fontFamily:"Poppins, sans-serif"},children:a.label})})),onChange:a=>{o(a)},activeKey:n,tabBarStyle:{marginBottom:0,fontWeight:500},className:"custom-tabs"})}),e.jsx("div",{style:{marginTop:"5px"},children:(y=ye.find(a=>a.key===n))==null?void 0:y.component})]})}const{Dragger:jt}=Se,N="https://19cxs75g-5001.inc1.devtunnels.ms",M={listFolder:async(r="")=>(await U.get(`${N}/s3bucket/list-folder`,{params:{prefix:r}})).data,uploadFile:async(r,n="")=>{const o=new FormData;return o.append("file",r),(await U.post(`${N}/s3bucket/upload-to-folder`,o,{params:{folderPath:n},headers:{"Content-Type":"multipart/form-data"}})).data},uploadFolder:async(r,n,o="")=>{const y=new FormData;return r.forEach(x=>{y.append("files",x)}),y.append("filePaths",JSON.stringify(n)),(await U.post(`${N}/s3bucket/upload-folder`,y,{params:{folderPath:o},headers:{"Content-Type":"multipart/form-data"}})).data},createFolder:async r=>(await U.post(`${N}/s3bucket/create-folder`,{folderName:r})).data,deleteFile:async r=>(await U.delete(`${N}/s3bucket/delete`,{params:{fileUrl:r}})).data,renameItem:async(r,n)=>(await U.post(`${N}/s3bucket/rename`,{oldPath:r,newName:n})).data,downloadFile:async r=>(await U.get(`${N}/s3bucket/downloadFile`,{params:{fileUrl:r},responseType:"blob"})).data,moveToTrash:async r=>{var n;console.log("🗑️ API Call: Move to trash",r),console.log("🗑️ API URL:",`${N}/s3bucket/move-to-trash`);try{const o=await U.post(`${N}/s3bucket/move-to-trash`,{filePath:r});return console.log("🗑️ API Response:",o.data),o.data}catch(o){throw console.error("🗑️ API Error:",o),o.code==="ECONNREFUSED"?new Error("Backend server is not running. Please start the backend server."):o.response?new Error(`Server error: ${o.response.status} - ${((n=o.response.data)==null?void 0:n.message)||o.response.statusText}`):o.request?new Error("No response from server. Check if backend is running."):new Error(`Request failed: ${o.message}`)}},listTrash:async()=>{console.log("📋 API Call: List trash");const r=await U.get(`${N}/s3bucket/list-trash`);return console.log("📋 API Response:",r.data),r.data},restoreFromTrash:async(r,n)=>{console.log("♻️ API Call: Restore from trash",{trashPath:r,originalPath:n});const o=await U.post(`${N}/s3bucket/restore-from-trash`,{trashPath:r,originalPath:n});return console.log("♻️ API Response:",o.data),o.data},permanentDelete:async r=>{console.log("🔥 API Call: Permanent delete",r);const n=await U.delete(`${N}/s3bucket/permanent-delete`,{data:{trashPath:r}});return console.log("🔥 API Response:",n.data),n.data},emptyTrash:async()=>{console.log("🧹 API Call: Empty trash");const r=await U.post(`${N}/s3bucket/empty-trash`);return console.log("🧹 API Response:",r.data),r.data},globalSearch:async(r,n=!1)=>{console.log("📡 API Call: Global search for",r,"includeTrash:",n);const o=await U.get(`${N}/s3bucket/global-search`,{params:{query:r,includeTrash:n}});return console.log("📡 API Response:",o.data),o.data}},wt=r=>{if(!r||r===0)return"0 B";const n=1024,o=["B","KB","MB","GB","TB"],y=Math.floor(Math.log(r)/Math.log(n));return parseFloat((r/Math.pow(n,y)).toFixed(2))+" "+o[y]},bt=r=>{if(!r)return"-";const n=new Date(r),y=Math.abs(new Date-n),a=Math.ceil(y/(1e3*60*60*24));return a===1?"Yesterday":a<7?`${a} days ago`:n.toLocaleDateString()},vt=r=>{var o;const n=(o=r.split(".").pop())==null?void 0:o.toLowerCase();return n?["pdf"].includes(n)?e.jsx(p,{icon:"vscode-icons:file-type-pdf2",width:"20",height:"20"}):["doc","docx"].includes(n)?e.jsx(p,{icon:"vscode-icons:file-type-word",width:"20",height:"20"}):["xls","xlsx"].includes(n)?e.jsx(p,{icon:"vscode-icons:file-type-excel",width:"20",height:"20"}):["ppt","pptx"].includes(n)?e.jsx(p,{icon:"vscode-icons:file-type-powerpoint",width:"20",height:"20"}):["jpg","jpeg","png","gif","bmp","svg"].includes(n)?e.jsx(p,{icon:"vscode-icons:file-type-image",width:"20",height:"20"}):["mp4","avi","mov","wmv"].includes(n)?e.jsx(p,{icon:"vscode-icons:file-type-video",width:"20",height:"20"}):["mp3","wav","flac"].includes(n)?e.jsx(p,{icon:"vscode-icons:file-type-audio",width:"20",height:"20"}):["zip","rar","7z"].includes(n)?e.jsx(p,{icon:"vscode-icons:file-type-zip",width:"20",height:"20"}):["txt","md"].includes(n)?e.jsx(p,{icon:"vscode-icons:file-type-text",width:"20",height:"20"}):e.jsx(at,{style:{color:"#8c8c8c"}}):e.jsx(p,{icon:"flat-color-icons:folder",width:"20",height:"20"})},me=(r,n)=>{if(!n||n.length<2)return r;const o=new RegExp(`(${n.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")})`,"gi");return r.split(o).map((a,x)=>o.test(a)?e.jsx("span",{style:{backgroundColor:"#fff2b8",color:"#d46b08",fontWeight:"bold",padding:"1px 2px",borderRadius:"2px"},children:a},x):a)},St=()=>{const[r,n]=d.useState([]),[o,y]=d.useState(""),[a,x]=d.useState([{path:"",name:"Root"}]),[g,f]=d.useState(!1),[l,T]=d.useState(""),[k,D]=d.useState(!1),[O,S]=d.useState(0),[b,$]=d.useState(!1),[w,m]=d.useState(!1),[h,P]=d.useState(!1),[u,j]=d.useState([]),[c,A]=d.useState(!1),[C,F]=d.useState(!1),[v,_]=d.useState(null),[W]=H.useForm(),[J]=H.useForm();d.useEffect(()=>{b?K():V()},[o,b]),d.useEffect(()=>{if(l.length>=2){m(!0),P(!0),h||j(r);const t=setTimeout(()=>{Ce(l)},300);return()=>{clearTimeout(t)}}else l.length===0&&(m(!1),P(!1),u.length>0?(n(u),j([])):b?K():V())},[l,b]);const V=async()=>{f(!0);try{const t=await M.listFolder(o);let s=t;l&&(s=t.filter(R=>R.name.toLowerCase().includes(l.toLowerCase()))),n(s)}catch(t){i.error("Failed to load files from S3"),console.error("Error loading files:",t)}finally{f(!1)}},K=async()=>{f(!0);try{const t=await M.listTrash();let s=t;l&&(s=t.filter(R=>R.name.toLowerCase().includes(l.toLowerCase()))),n(s)}catch(t){i.error("Failed to load trash items"),console.error("Error loading trash:",t),n([])}finally{f(!1)}},Ce=async t=>{var s,R;if(console.log("🔍 Search triggered for:",t),!t||t.length<2){console.log("❌ Query too short, clearing search"),m(!1);return}try{console.log("📡 Making API call to global search..."),console.log("📡 Search URL:",`${N}/s3bucket/global-search?query=${encodeURIComponent(t)}&includeTrash=${b}`);const I=new AbortController,Y=setTimeout(()=>{I.abort(),console.log("⏰ Search request timed out")},15e3);try{const z=await M.globalSearch(t,b);clearTimeout(Y),console.log("✅ Search response:",z);const L=z.results||z||[];n(L),L.length===0?i.info({content:`No items found matching "${t}". Try simpler terms like "Client", "2049", or file extensions.`,duration:3}):(console.log(`✅ Found ${L.length} items matching "${t}"`),t.length>15&&i.success({content:`Found ${L.length} result${L.length===1?"":"s"}`,duration:2}))}catch(z){throw clearTimeout(Y),z}}catch(I){console.error("❌ Error in search:",I),I.name==="AbortError"?i.error({content:"Search timed out. Please try simpler search terms.",duration:4}):I.code==="ECONNREFUSED"?i.error("Backend server is not running. Please start the backend server."):((s=I.response)==null?void 0:s.status)===404?i.error("Search endpoint not found. Please check backend configuration."):((R=I.response)==null?void 0:R.status)>=500?i.error("Server error occurred. Please try again later."):i.error(`Search failed: ${I.message||"Please try simpler terms"}`),(I.name!=="AbortError"||r.length===0)&&n([])}finally{m(!1)}},ce=(t,s)=>{if(console.log("🗂️ Navigating to:",t,s),console.log("🗂️ Current folderPath:",a),t==="")y(""),x([{path:"",name:"Root"}]);else{y(t);const R=a.findIndex(I=>I.path===t);R>=0?x(a.slice(0,R+1)):x([...a,{path:t,name:s}])}},ke=async t=>{D(!0),S(0);try{const s=setInterval(()=>{S(I=>I>=90?(clearInterval(s),90):I+10)},200);console.log(`📁 Uploading file "${t.name}" to folder: "${o}"`);const R=await M.uploadFile(t,o);console.log("📁 Upload result:",R),clearInterval(s),S(100),setTimeout(()=>{S(0),D(!1),l&&(T(""),P(!1));const I=o?`folder "${o}"`:"root folder";i.success(`File "${t.name}" uploaded successfully to ${I}`),V()},1e3)}catch(s){D(!1),S(0),i.error(`Failed to upload file "${t.name}": ${s.message||"Unknown error"}`),console.error("Error uploading file:",s)}return!1},he=async t=>{D(!0),S(0);try{console.log(`📁 Uploading ${t.length} files as folder to: "${o}"`);const s=[],R=[];for(const z of t){s.push(z);const L=z.webkitRelativePath||z.name;R.push(L)}const I=setInterval(()=>{S(z=>z>=90?(clearInterval(I),90):z+10)},300),Y=await M.uploadFolder(s,R,o);console.log("📁 Folder upload result:",Y),clearInterval(I),S(100),setTimeout(()=>{S(0),D(!1),l&&(T(""),P(!1));const z=o?`folder "${o}"`:"root folder";i.success(`${t.length} files uploaded successfully to ${z}`),V()},1e3)}catch(s){D(!1),S(0),i.error(`Failed to upload folder: ${s.message||"Unknown error"}`),console.error("Error uploading folder:",s)}},Fe=async t=>{try{const s=o?`${o}/${t.folderName}`:t.folderName;await M.createFolder(s),i.success("Folder created successfully in S3"),A(!1),W.resetFields(),V()}catch(s){i.error("Failed to create folder in S3"),console.error("Error creating folder:",s)}},Ie=async t=>{try{await M.renameItem(v.path,t.newName),i.success("Item renamed successfully in S3"),F(!1),J.resetFields(),_(null),V()}catch(s){i.error("Failed to rename item in S3"),console.error("Error renaming item:",s)}},pe=async t=>{try{console.log("🗑️ Moving to trash:",t),console.log("🗑️ Item path:",t.path),console.log("🗑️ Item type:",t.type),X.confirm({title:"Move to Trash",content:`Are you sure you want to move "${t.name}" to trash?`,okText:"Move to Trash",okType:"danger",onOk:async()=>{var s,R,I,Y,z;try{console.log("🗑️ Confirmed - calling API...");const L=await M.moveToTrash(t.path);console.log("🗑️ API Success:",L),i.success("Item moved to trash successfully"),b?K():V()}catch(L){console.error("🗑️ API Error Details:",{message:L.message,response:(s=L.response)==null?void 0:s.data,status:(R=L.response)==null?void 0:R.status,statusText:(I=L.response)==null?void 0:I.statusText});const Pe=((z=(Y=L.response)==null?void 0:Y.data)==null?void 0:z.message)||L.message||"Unknown error";i.error(`Failed to move item to trash: ${Pe}`)}}})}catch(s){console.error("🗑️ Outer Error:",s),i.error(`Failed to move item to trash: ${s.message}`)}},ge=async t=>{try{await M.restoreFromTrash(t.trashPath,t.originalPath),i.success("Item restored successfully"),K()}catch(s){i.error("Failed to restore item"),console.error("Error restoring item:",s)}},ue=async t=>{try{await M.permanentDelete(t.trashPath),i.success("Item permanently deleted"),K()}catch(s){i.error("Failed to permanently delete item"),console.error("Error permanently deleting:",s)}},Te=async()=>{try{await M.emptyTrash(),i.success("Trash emptied successfully"),K()}catch(t){i.error("Failed to empty trash"),console.error("Error emptying trash:",t)}},Re=async t=>{try{if(t.isFolder){i.warning("Folder download not supported");return}const s=document.createElement("a");s.href=t.url,s.download=t.name,s.target="_blank",document.body.appendChild(s),s.click(),document.body.removeChild(s),i.success("Download started")}catch(s){i.error("Failed to download file"),console.error("Error downloading file:",s)}},Ee=t=>{const s=[];return t.inTrash?(s.push({key:"restore",icon:e.jsx(p,{icon:"material-symbols:restore-from-trash",width:"16",height:"16"}),label:"Restore",onClick:()=>ge(t)}),s.push({key:"permanent-delete",icon:e.jsx(te,{style:{color:"red"}}),label:"Delete Permanently",onClick:()=>{X.confirm({title:"Permanently Delete",content:"Are you sure you want to permanently delete this item? This action cannot be undone.",okText:"Delete Permanently",okType:"danger",onOk:()=>ue(t)})}})):(s.push({key:"rename",icon:e.jsx(Qe,{}),label:"Rename",onClick:()=>{_(t),F(!0),J.setFieldsValue({newName:t.name})}}),t.isFolder||s.push({key:"download",icon:e.jsx(ot,{}),label:"Download",onClick:()=>Re(t)}),s.push({key:"move-to-trash",icon:e.jsx(te,{}),label:"Move to Trash",onClick:()=>pe(t)})),s},Ae=[{title:"Name",dataIndex:"name",key:"name",render:(t,s)=>e.jsxs(G,{children:[s.inTrash?e.jsx(p,{icon:"material-symbols:delete",width:"20",height:"20",style:{color:"#ff4d4f"}}):s.isFolder?e.jsx(p,{icon:"flat-color-icons:folder",width:"24",height:"24"}):vt(s.name),e.jsx("span",{style:{cursor:s.isFolder&&!s.inTrash?"pointer":"default",fontFamily:"Poppins",fontSize:"14px",fontWeight:s.isFolder?"bold":"normal",color:s.inTrash?"#999":"inherit",textDecoration:s.inTrash?"line-through":"none"},onClick:()=>s.isFolder&&!s.inTrash&&ce(s.path,s.name),children:l&&l.length>=2?me(t,l):t}),s.inTrash&&e.jsx("span",{style:{color:"#ff4d4f",fontSize:"12px",background:"#fff2f0",padding:"2px 6px",borderRadius:"4px",border:"1px solid #ffccc7"},children:"In Trash"})]})},{title:"Type",dataIndex:"type",key:"type",width:100,render:t=>e.jsx("span",{style:{color:"#666",fontSize:"12px",textTransform:"capitalize"},children:t})},{title:"Size",dataIndex:"size",key:"size",width:100,render:(t,s)=>e.jsx("span",{style:{color:"#666",fontSize:"12px"},children:s.isFolder?"-":wt(t)})},...l&&l.length>=2?[{title:"Location",dataIndex:"location",key:"location",width:150,render:t=>e.jsx("span",{style:{color:"#666",fontSize:"12px"},children:me(t||"Root",l)})}]:[],{title:b?"Deleted":"Modified",dataIndex:b?"deletedAt":"lastModified",key:"modified",width:120,render:t=>e.jsx("span",{style:{color:"#666",fontSize:"12px"},children:bt(t)})},{title:"Actions",key:"actions",width:160,render:(t,s)=>e.jsx(G,{children:s.inTrash?e.jsxs(e.Fragment,{children:[e.jsx(ee,{title:"Restore",children:e.jsx(E,{type:"text",size:"small",icon:e.jsx(p,{icon:"material-symbols:restore-from-trash",width:"16",height:"16"}),onClick:()=>ge(s)})}),e.jsx(ee,{title:"Delete Permanently",children:e.jsx(E,{type:"text",size:"small",danger:!0,icon:e.jsx(te,{}),onClick:()=>{X.confirm({title:"Permanently Delete",content:"Are you sure you want to permanently delete this item? This action cannot be undone.",okText:"Delete Permanently",okType:"danger",onOk:()=>ue(s)})}})})]}):e.jsxs(e.Fragment,{children:[e.jsx(ee,{title:"Move to Trash",children:e.jsx(E,{type:"text",size:"small",danger:!0,icon:e.jsx(te,{}),onClick:()=>pe(s)})}),e.jsx(Me,{menu:{items:Ee(s)},trigger:["click"],children:e.jsx(E,{type:"text",size:"small",children:e.jsx(p,{icon:"tabler:dots-vertical",width:"16",height:"16"})})})]})})}];return e.jsxs("div",{style:{backgroundColor:"white",padding:"20px",minHeight:"600px"},children:[e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"20px",flexWrap:"wrap",gap:"10px"},children:[e.jsxs("div",{style:{position:"relative",maxWidth:"400px"},children:[e.jsx(Z,{placeholder:l.length===1?"Type one more character to search...":"Search files and folders...",value:l,onChange:t=>T(t.target.value),style:{width:"500px",padding:"10px",borderRadius:6,border:l.length>=2?"2px solid #1890ff":"1px solid #d9d9d9",backgroundColor:l.length>=2?"#f0f9ff":"#fafafa",transition:"all 0.3s ease"},suffix:e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[w?e.jsx(xe,{size:"small"}):e.jsx(p,{icon:"mynaui:search",width:"20",height:"20",style:{color:l.length>=2?"#1890ff":"#999",transition:"color 0.3s ease"}}),l.length>0&&e.jsx(p,{icon:"material-symbols:close",width:"16",height:"16",style:{cursor:"pointer",color:"#999",transition:"color 0.3s ease"},onClick:()=>T(""),onMouseEnter:t=>t.target.style.color="#ff4d4f",onMouseLeave:t=>t.target.style.color="#999"})]})}),l.length>=2&&e.jsx("div",{style:{position:"absolute",top:"100%",left:0,right:0,background:"white",border:"1px solid #e8e8e8",borderTop:"none",borderRadius:"0 0 6px 6px",padding:"8px 12px",fontSize:"12px",color:"#666",boxShadow:"0 2px 8px rgba(0,0,0,0.1)",zIndex:1e3},children:w?e.jsxs("span",{style:{color:"#1890ff"},children:[e.jsx(p,{icon:"eos-icons:loading",width:"12",height:"12",style:{marginRight:"4px"}}),"Searching across all files and folders..."]}):r.length>0?e.jsxs("span",{style:{color:"#52c41a"},children:["✓ Found ",r.length," result",r.length!==1?"s":"",' for "',l,'"']}):e.jsxs("span",{style:{color:"#fa8c16"},children:['No results found for "',l,'"']})})]}),e.jsxs(G,{children:[e.jsx(ee,{title:b?"Show files":"Show trash",children:e.jsx(E,{icon:b?e.jsx(p,{icon:"material-symbols:folder",width:"16",height:"16"}):e.jsx(te,{}),onClick:()=>$(!b),style:{borderRadius:6},children:b?"Files":"Trash"})}),b&&r.length>0&&e.jsx(ee,{title:"Empty trash permanently",children:e.jsx(E,{danger:!0,icon:e.jsx(p,{icon:"material-symbols:delete-forever",width:"16",height:"16"}),onClick:()=>{X.confirm({title:"Empty Trash",content:"Are you sure you want to permanently delete all items in trash? This action cannot be undone.",okText:"Empty Trash",okType:"danger",onOk:Te})},style:{borderRadius:6},children:"Empty Trash"})}),!b&&e.jsxs(e.Fragment,{children:[e.jsx(E,{type:"primary",icon:e.jsx(lt,{}),onClick:()=>A(!0),style:{borderRadius:6},children:"New Folder"}),e.jsx(Se,{beforeUpload:(t,s)=>(he(s),!1),showUploadList:!1,multiple:!0,directory:!0,disabled:k,children:e.jsx(E,{icon:e.jsx(p,{icon:"material-symbols:upload-file",width:"16",height:"16"}),style:{borderRadius:6},disabled:k,children:"Upload Folder"})})]})]})]}),e.jsx("div",{style:{marginBottom:"20px",fontWeight:"bold",fontSize:"14px",fontFamily:"Poppins",display:"flex",alignItems:"center",gap:"8px"},children:l&&l.length>=2?e.jsxs(G,{children:[e.jsx(p,{icon:"material-symbols:search",width:"16",height:"16",style:{color:"#1890ff"}}),e.jsxs("span",{style:{color:"#1890ff"},children:['Search results for "',l,'" (',r.length," items)"]}),e.jsx(E,{type:"link",size:"small",onClick:()=>T(""),style:{padding:0,height:"auto"},children:"Clear Search"})]}):b?e.jsxs(G,{children:[e.jsx(p,{icon:"material-symbols:delete",width:"16",height:"16",style:{color:"#ff4d4f"}}),e.jsx("span",{style:{color:"#ff4d4f"},children:"Trash"})]}):e.jsx("div",{style:{display:"flex",alignItems:"center",gap:"4px",flexWrap:"wrap"},children:a.map((t,s)=>e.jsxs(be.Fragment,{children:[e.jsx("span",{style:{cursor:"pointer",color:s===a.length-1?"#333":"#1890ff",padding:"4px 8px",borderRadius:"4px",transition:"background-color 0.3s",fontWeight:s===a.length-1?"bold":"normal",backgroundColor:s===a.length-1?"#f0f0f0":"transparent"},onClick:()=>ce(t.path,t.name),onMouseEnter:R=>{s!==a.length-1&&(R.target.style.backgroundColor="#e6f7ff")},onMouseLeave:R=>{s!==a.length-1?R.target.style.backgroundColor="transparent":R.target.style.backgroundColor="#f0f0f0"},children:s===0?e.jsxs(G,{children:[e.jsx(p,{icon:"material-symbols:home",width:"16",height:"16"}),t.name]}):e.jsxs(G,{children:[e.jsx(p,{icon:"material-symbols:folder",width:"16",height:"16"}),t.name]})}),s<a.length-1&&e.jsx(p,{icon:"material-symbols:chevron-right",width:"16",height:"16",style:{color:"#999"}})]},t.path||"root"))})}),!b&&(!l||l.length<2)&&e.jsxs("div",{style:{marginBottom:"20px"},children:[e.jsxs(jt,{beforeUpload:(t,s)=>(s.some(I=>I.webkitRelativePath)||s.length>1?he(s):ke(t),!1),showUploadList:!1,disabled:k,multiple:!0,directory:!0,style:{border:"2px dashed #d9d9d9",borderRadius:6,background:"#fafafa",padding:"20px"},children:[e.jsx("p",{style:{fontSize:"24px",margin:"10px 0"},children:e.jsx(dt,{})}),e.jsx("p",{style:{fontSize:"16px",margin:"5px 0",fontFamily:"Poppins"},children:"Click or drag files/folders to upload to S3"}),e.jsxs("p",{style:{fontSize:"12px",color:"#666",margin:0},children:["Files will be uploaded to ",o?`folder: ${o}`:"the root folder"]}),e.jsx("p",{style:{fontSize:"11px",color:"#999",margin:"5px 0 0 0"},children:"Supports both individual files and entire folders"})]}),k&&e.jsx("div",{style:{marginTop:"10px"},children:e.jsx(Xe,{percent:O,status:"active"})})]}),b&&e.jsx("div",{style:{marginBottom:"20px",padding:"16px",background:"#fff7e6",border:"1px solid #ffd591",borderRadius:6,borderLeft:"4px solid #fa8c16"},children:e.jsxs(G,{children:[e.jsx(p,{icon:"material-symbols:info",width:"16",height:"16",style:{color:"#fa8c16"}}),e.jsx("span",{style:{color:"#fa8c16",fontFamily:"Poppins",fontSize:"14px"},children:"Items in trash will be automatically deleted after 30 days. You can restore them or delete them permanently."})]})}),e.jsx(xe,{spinning:g,children:e.jsx(de,{columns:Ae,dataSource:r,rowKey:t=>t.path||t.name,pagination:{showSizeChanger:!0,showQuickJumper:!0,showTotal:(t,s)=>`${s[0]}-${s[1]} of ${t} items`,style:{marginTop:"16px"}},locale:{emptyText:l&&l.length>=2?`No results found for "${l}"`:b?"Trash is empty":"No files or folders found in S3 bucket"},style:{background:"white",borderRadius:6,border:"1px solid #f0f0f0"}})}),e.jsx(X,{title:"Create New Folder",open:c,onCancel:()=>{A(!1),W.resetFields()},footer:null,children:e.jsxs(H,{form:W,onFinish:Fe,layout:"vertical",children:[e.jsx(H.Item,{name:"folderName",label:"Folder Name",rules:[{required:!0,message:"Please enter a folder name"},{max:255,message:"Folder name must be less than 255 characters"}],children:e.jsx(Z,{placeholder:"Enter folder name"})}),e.jsx(H.Item,{name:"description",label:"Description (Optional)",children:e.jsx(Z.TextArea,{placeholder:"Enter folder description",rows:3,maxLength:1e3})}),e.jsx(H.Item,{style:{marginBottom:0,textAlign:"right"},children:e.jsxs(G,{children:[e.jsx(E,{onClick:()=>{A(!1),W.resetFields()},children:"Cancel"}),e.jsx(E,{type:"primary",htmlType:"submit",children:"Create Folder"})]})})]})}),e.jsx(X,{title:"Rename",open:C,onCancel:()=>{F(!1),J.resetFields(),_(null)},footer:null,children:e.jsxs(H,{form:J,onFinish:Ie,layout:"vertical",children:[e.jsx(H.Item,{name:"newName",label:"New Name",rules:[{required:!0,message:"Please enter a new name"},{max:255,message:"Name must be less than 255 characters"}],children:e.jsx(Z,{placeholder:"Enter new name"})}),e.jsx(H.Item,{style:{marginBottom:0,textAlign:"right"},children:e.jsxs(G,{children:[e.jsx(E,{onClick:()=>{F(!1),J.resetFields(),_(null)},children:"Cancel"}),e.jsx(E,{type:"primary",htmlType:"submit",children:"Rename"})]})})]})})]})};function Ut(r){var O,S;const n=ae(),[o,y]=d.useState(!1),[a,x]=d.useState(),[g,f]=d.useState(!1),{roleId:l}=q(),[T,k]=d.useState(),D=()=>{try{const b=B.getRoleById.replace(":id",l);Q({},b,$=>{x($);const w=encodeURI($==null?void 0:$.attachments);k(w)},$=>{var w;console.log($),i.error(((w=$==null?void 0:$.response)==null?void 0:w.message)||"Failed to get role details. Try refreshing the page!")})}catch{i.error("Failed to get role details. Try refreshing the page!")}};return d.useEffect(()=>{D()},[]),console.log("resfwesrew",a),e.jsxs("div",{style:{width:"100%",height:"100%"},children:[e.jsx(p,{icon:"ion:arrow-back-outline",width:"20",height:"20",style:{cursor:"pointer"},onClick:()=>n(-1)}),e.jsxs(Oe,{gutter:[16,24],children:[!g&&e.jsxs(le,{span:o?24:6,className:"gutter-row scrollable-column",children:[e.jsxs("div",{style:{display:" flex",justifyContent:"space-between",alignItems:"center",padding:"15px"},children:[e.jsxs("div",{style:{display:"flex",gap:"10px"},children:[e.jsx(Ve,{size:55,style:{backgroundColor:"#BEE0FDFC",verticalAlign:"middle",fontSize:"25px",borderRadius:"10px"},children:((S=(O=a==null?void 0:a.title)==null?void 0:O.charAt(0))==null?void 0:S.toUpperCase())||""}),e.jsxs("div",{children:[e.jsx("h4",{style:{margin:0,color:"#344665"},children:a==null?void 0:a.title}),e.jsx("p",{style:{margin:0,fontWeight:400},children:a!=null&&a.client_number?a==null?void 0:a.client_number:""})]})]}),e.jsxs("div",{style:{display:"flex",gap:"10px",alignItems:"flex-start"},children:[e.jsx(p,{icon:"garden:maximize-stroke-16",width:"14",height:"14",onClick:()=>y(!0),style:{cursor:"pointer"}}),e.jsx(p,{icon:"garden:minimize-stroke-16",width:"16",height:"16",onClick:()=>y(!1),style:{cursor:"pointer"}})]})]}),e.jsx(fe,{style:{margin:0,borderColor:"#CED0DA",borderWidth:"2px"}}),e.jsx("h4",{style:{margin:0,color:"#344665",marginTop:"10px"},children:"Role Details"}),e.jsx(fe,{style:{margin:0,borderColor:"#CED0DA",borderWidth:"2px",marginTop:"10px"}}),e.jsx(Ze,{roleDetails:a})]}),!o&&e.jsxs(e.Fragment,{children:[!g&&e.jsx(le,{span:12,className:"gutter-row scrollable-column",children:e.jsx(mt,{})}),e.jsxs(le,{span:g?24:6,className:"gutter-row",style:{backgroundColor:"white",height:"100px"},children:[e.jsxs("div",{style:{display:" flex",justifyContent:"space-between",alignItems:"center",padding:"15px"},children:[e.jsx("div",{style:{display:"flex",gap:"10px"},children:e.jsx("div",{children:e.jsx("h2",{style:{margin:0,color:"#344665"},children:"AWS S3 Bucket Client 40"})})}),e.jsxs("div",{style:{display:"flex",gap:"10px",alignItems:"flex-start"},children:[e.jsx(p,{icon:"garden:maximize-stroke-16",width:"14",height:"14",onClick:()=>f(!0),style:{cursor:"pointer"}}),e.jsx(p,{icon:"garden:minimize-stroke-16",width:"16",height:"16",onClick:()=>f(!1),style:{cursor:"pointer"}})]})]}),e.jsx(St,{})]})]})]})]})}export{Ut as default};
