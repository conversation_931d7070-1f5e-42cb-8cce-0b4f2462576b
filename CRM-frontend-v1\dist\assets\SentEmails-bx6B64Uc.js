import{u,bH as f,r as o,j as a,aI as y,a2 as b,av as j,e3 as v,aL as E,e4 as C}from"./index-C8YzRPez.js";function T(w){var r;const i=[{key:"1",label:"All Emails",path:"/mailbox/sent/all"}],x=u(),n=f(),l=((r=i.find(e=>{var t;return(t=n.pathname)==null?void 0:t.includes(e.path)}))==null?void 0:r.key)||"1";o.useEffect(()=>{p(l)},[n.pathname]);const[d,p]=o.useState(l),[s,c]=o.useState([]),m=()=>{s.length<3?c([...s,Date.now()]):alert("You can only open 3 mails at a time")},h=e=>{c(s.filter(t=>t!==e))};return a.jsxs("div",{style:{width:"100%"},children:[a.jsx(y,{style:{backgroundColor:"white",padding:"5px",display:"flex",justifyContent:"space-between",alignItems:"center"},children:a.jsx(b,{type:"primary",icon:a.jsx(j,{icon:"mdi-light:email",width:"20",height:"20"}),style:{borderRadius:"8px",height:"35px",width:"170px",fontSize:"16px",marginLeft:"10px",backgroundColor:"#1A84DE",fontWeight:400},onClick:m,children:"Compose Email"})}),a.jsx(v,{tabs:i==null?void 0:i.map(({key:e,label:t})=>({key:e,label:t})),onChangeTab:e=>{var t;p(e),x((t=i.find(g=>g.key===e))==null?void 0:t.path)},activeKey:d}),a.jsx("div",{style:{marginTop:"15px"},children:a.jsx(E,{})}),a.jsx("div",{style:{position:"fixed",bottom:10,right:10,zIndex:1050,display:"flex",gap:"5px"},children:s==null?void 0:s.map((e,t)=>a.jsx(C,{id:t,onClose:()=>h(e)},e))})]})}export{T as default};
