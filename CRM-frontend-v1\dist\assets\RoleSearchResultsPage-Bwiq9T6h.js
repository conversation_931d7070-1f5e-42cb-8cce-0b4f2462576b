import{r as u,bs as ys,bt as Ve,bu as gs,bv as zt,bw as fs,b4 as ft,bx as Ze,b6 as bt,b5 as Be,by as et,ai as qt,aj as Yt,aq as Fe,j as e,ak as be,ar as de,al as F,b0 as wt,a$ as Ct,bz as tt,bA as Gt,bB as bs,an as Xt,b8 as $e,b2 as Jt,as as Qt,_ as vs,v as Zt,m as js,i as es,k as At,h as Ss,K as Dt,E as ks,J as ws,N as Te,M as Cs,bC as Is,Q as Ps,U as Ls,V as $s,$ as Es,bD as Ts,bE as zs,a6 as b,aJ as Ft,y as As,av as S,aI as X,aK as L,bm as Mt,aw as R,a3 as C,T as h,aG as _,bF as z,B as Le,bG as vt,bk as Ds,bj as Fs,e as ts,R as ss,aB as Ms,aC as Rs,aD as Ws,aE as _s,a4 as It,a2 as fe,bH as Bs,u as Os,aM as Ns,C as jt,bp as St,d as Rt,bl as Wt,s as Pe,az as Hs}from"./index-C8YzRPez.js";/* empty css                          */import{S as Ks,C as Vs}from"./Checkbox-CbmcUeBk.js";import{T as kt}from"./index-C2EZYIAX.js";import{C as O}from"./Chip-Cl5XdTlP.js";import{M as Ee}from"./MenuItem--Pxpzvee.js";import{F as Us}from"./FormGroup-CcAw1A75.js";import{F as qs}from"./FormControlLabel-Cvw0ROlx.js";import{C as ns}from"./Collapse-Cwu689ta.js";import{F as Ys}from"./index-BWafDGV1.js";import"./useClosable-D2fLMMij.js";const Gs={border:0,clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"absolute",whiteSpace:"nowrap",width:"1px"};function Xs(t,n,l=(r,a)=>r===a){return t.length===n.length&&t.every((r,a)=>l(r,n[a]))}const Js=2;function is(t,n){return t-n}function _t(t,n){var l;const{index:r}=(l=t.reduce((a,c,p)=>{const i=Math.abs(n-c);return a===null||i<a.distance||i===a.distance?{distance:i,index:p}:a},null))!=null?l:{};return r}function Ye(t,n){if(n.current!==void 0&&t.changedTouches){const l=t;for(let r=0;r<l.changedTouches.length;r+=1){const a=l.changedTouches[r];if(a.identifier===n.current)return{x:a.clientX,y:a.clientY}}return!1}return{x:t.clientX,y:t.clientY}}function st(t,n,l){return(t-n)*100/(l-n)}function Qs(t,n,l){return(l-n)*t+n}function Zs(t){if(Math.abs(t)<1){const l=t.toExponential().split("e-"),r=l[0].split(".")[1];return(r?r.length:0)+parseInt(l[1],10)}const n=t.toString().split(".")[1];return n?n.length:0}function en(t,n,l){const r=Math.round((t-l)/n)*n+l;return Number(r.toFixed(Zs(n)))}function Bt({values:t,newValue:n,index:l}){const r=t.slice();return r[l]=n,r.sort(is)}function Ge({sliderRef:t,activeIndex:n,setActive:l}){var r,a;const c=Ze(t.current);if(!((r=t.current)!=null&&r.contains(c.activeElement))||Number(c==null||(a=c.activeElement)==null?void 0:a.getAttribute("data-index"))!==n){var p;(p=t.current)==null||p.querySelector(`[type="range"][data-index="${n}"]`).focus()}l&&l(n)}function Xe(t,n){return typeof t=="number"&&typeof n=="number"?t===n:typeof t=="object"&&typeof n=="object"?Xs(t,n):!1}const tn={horizontal:{offset:t=>({left:`${t}%`}),leap:t=>({width:`${t}%`})},"horizontal-reverse":{offset:t=>({right:`${t}%`}),leap:t=>({width:`${t}%`})},vertical:{offset:t=>({bottom:`${t}%`}),leap:t=>({height:`${t}%`})}},sn=t=>t;let Je;function Ot(){return Je===void 0&&(typeof CSS<"u"&&typeof CSS.supports=="function"?Je=CSS.supports("touch-action","none"):Je=!0),Je}function nn(t){const{"aria-labelledby":n,defaultValue:l,disabled:r=!1,disableSwap:a=!1,isRtl:c=!1,marks:p=!1,max:i=100,min:o=0,name:s,onChange:d,onChangeCommitted:m,orientation:x="horizontal",rootRef:v,scale:W=sn,step:I=1,shiftStep:K=10,tabIndex:me,value:ce}=t,N=u.useRef(),[te,se]=u.useState(-1),[Y,ne]=u.useState(-1),[H,le]=u.useState(!1),ee=u.useRef(0),[J,ve]=ys({controlled:ce,default:l??o,name:"Slider"}),ie=d&&((y,g,k)=>{const $=y.nativeEvent||y,T=new $.constructor($.type,$);Object.defineProperty(T,"target",{writable:!0,value:{value:g,name:s}}),d(T,g,k)}),pe=Array.isArray(J);let q=pe?J.slice().sort(is):[J];q=q.map(y=>y==null?o:Ve(y,o,i));const je=p===!0&&I!==null?[...Array(Math.floor((i-o)/I)+1)].map((y,g)=>({value:o+I*g})):p||[],V=je.map(y=>y.value),{isFocusVisibleRef:oe,onBlur:Oe,onFocus:Ne,ref:He}=gs(),[ze,ye]=u.useState(-1),Q=u.useRef(),Ce=zt(He,Q),B=zt(v,Ce),Se=y=>g=>{var k;const $=Number(g.currentTarget.getAttribute("data-index"));Ne(g),oe.current===!0&&ye($),ne($),y==null||(k=y.onFocus)==null||k.call(y,g)},ke=y=>g=>{var k;Oe(g),oe.current===!1&&ye(-1),ne(-1),y==null||(k=y.onBlur)==null||k.call(y,g)},Ae=(y,g)=>{const k=Number(y.currentTarget.getAttribute("data-index")),$=q[k],T=V.indexOf($);let P=g;if(je&&I==null){const ge=V[V.length-1];P>ge?P=ge:P<V[0]?P=V[0]:P=P<$?V[T-1]:V[T+1]}if(P=Ve(P,o,i),pe){a&&(P=Ve(P,q[k-1]||-1/0,q[k+1]||1/0));const ge=P;P=Bt({values:q,newValue:P,index:k});let we=k;a||(we=P.indexOf(ge)),Ge({sliderRef:Q,activeIndex:we})}ve(P),ye(k),ie&&!Xe(P,J)&&ie(y,P,k),m&&m(y,P)},Me=y=>g=>{var k;if(I!==null){const $=Number(g.currentTarget.getAttribute("data-index")),T=q[$];let P=null;(g.key==="ArrowLeft"||g.key==="ArrowDown")&&g.shiftKey||g.key==="PageDown"?P=Math.max(T-K,o):((g.key==="ArrowRight"||g.key==="ArrowUp")&&g.shiftKey||g.key==="PageUp")&&(P=Math.min(T+K,i)),P!==null&&(Ae(g,P),g.preventDefault())}y==null||(k=y.onKeyDown)==null||k.call(y,g)};fs(()=>{if(r&&Q.current.contains(document.activeElement)){var y;(y=document.activeElement)==null||y.blur()}},[r]),r&&te!==-1&&se(-1),r&&ze!==-1&&ye(-1);const Re=y=>g=>{var k;(k=y.onChange)==null||k.call(y,g),Ae(g,g.target.valueAsNumber)},De=u.useRef();let f=x;c&&x==="horizontal"&&(f+="-reverse");const w=({finger:y,move:g=!1})=>{const{current:k}=Q,{width:$,height:T,bottom:P,left:ge}=k.getBoundingClientRect();let we;f.indexOf("vertical")===0?we=(P-y.y)/T:we=(y.x-ge)/$,f.indexOf("-reverse")!==-1&&(we=1-we);let D;if(D=Qs(we,o,i),I)D=en(D,I,o);else{const _e=_t(V,D);D=V[_e]}D=Ve(D,o,i);let xe=0;if(pe){g?xe=De.current:xe=_t(q,D),a&&(D=Ve(D,q[xe-1]||-1/0,q[xe+1]||1/0));const _e=D;D=Bt({values:q,newValue:D,index:xe}),a&&g||(xe=D.indexOf(_e),De.current=xe)}return{newValue:D,activeIndex:xe}},j=ft(y=>{const g=Ye(y,N);if(!g)return;if(ee.current+=1,y.type==="mousemove"&&y.buttons===0){E(y);return}const{newValue:k,activeIndex:$}=w({finger:g,move:!0});Ge({sliderRef:Q,activeIndex:$,setActive:se}),ve(k),!H&&ee.current>Js&&le(!0),ie&&!Xe(k,J)&&ie(y,k,$)}),E=ft(y=>{const g=Ye(y,N);if(le(!1),!g)return;const{newValue:k}=w({finger:g,move:!0});se(-1),y.type==="touchend"&&ne(-1),m&&m(y,k),N.current=void 0,ae()}),A=ft(y=>{if(r)return;Ot()||y.preventDefault();const g=y.changedTouches[0];g!=null&&(N.current=g.identifier);const k=Ye(y,N);if(k!==!1){const{newValue:T,activeIndex:P}=w({finger:k});Ge({sliderRef:Q,activeIndex:P,setActive:se}),ve(T),ie&&!Xe(T,J)&&ie(y,T,P)}ee.current=0;const $=Ze(Q.current);$.addEventListener("touchmove",j,{passive:!0}),$.addEventListener("touchend",E,{passive:!0})}),ae=u.useCallback(()=>{const y=Ze(Q.current);y.removeEventListener("mousemove",j),y.removeEventListener("mouseup",E),y.removeEventListener("touchmove",j),y.removeEventListener("touchend",E)},[E,j]);u.useEffect(()=>{const{current:y}=Q;return y.addEventListener("touchstart",A,{passive:Ot()}),()=>{y.removeEventListener("touchstart",A),ae()}},[ae,A]),u.useEffect(()=>{r&&ae()},[r,ae]);const Ue=y=>g=>{var k;if((k=y.onMouseDown)==null||k.call(y,g),r||g.defaultPrevented||g.button!==0)return;g.preventDefault();const $=Ye(g,N);if($!==!1){const{newValue:P,activeIndex:ge}=w({finger:$});Ge({sliderRef:Q,activeIndex:ge,setActive:se}),ve(P),ie&&!Xe(P,J)&&ie(g,P,ge)}ee.current=0;const T=Ze(Q.current);T.addEventListener("mousemove",j,{passive:!0}),T.addEventListener("mouseup",E)},G=st(pe?q[0]:o,o,i),We=st(q[q.length-1],o,i)-G,it=(y={})=>{const g=bt(y),k={onMouseDown:Ue(g||{})},$=Be({},g,k);return Be({},y,{ref:B},$)},rt=y=>g=>{var k;(k=y.onMouseOver)==null||k.call(y,g);const $=Number(g.currentTarget.getAttribute("data-index"));ne($)},lt=y=>g=>{var k;(k=y.onMouseLeave)==null||k.call(y,g),ne(-1)};return{active:te,axis:f,axisProps:tn,dragging:H,focusedThumbIndex:ze,getHiddenInputProps:(y={})=>{var g;const k=bt(y),$={onChange:Re(k||{}),onFocus:Se(k||{}),onBlur:ke(k||{}),onKeyDown:Me(k||{})},T=Be({},k,$);return Be({tabIndex:me,"aria-labelledby":n,"aria-orientation":x,"aria-valuemax":W(i),"aria-valuemin":W(o),name:s,type:"range",min:t.min,max:t.max,step:t.step===null&&t.marks?"any":(g=t.step)!=null?g:void 0,disabled:r},y,T,{style:Be({},Gs,{direction:c?"rtl":"ltr",width:"100%",height:"100%"})})},getRootProps:it,getThumbProps:(y={})=>{const g=bt(y),k={onMouseOver:rt(g||{}),onMouseLeave:lt(g||{})};return Be({},y,g,k)},marks:je,open:Y,range:pe,rootRef:B,trackLeap:We,trackOffset:G,values:q,getThumbStyle:y=>({pointerEvents:te!==-1&&te!==y?"none":void 0})}}const rn=t=>!t||!et(t);function ln(t){return Yt("MuiSlider",t)}const he=qt("MuiSlider",["root","active","colorPrimary","colorSecondary","colorError","colorInfo","colorSuccess","colorWarning","disabled","dragging","focusVisible","mark","markActive","marked","markLabel","markLabelActive","rail","sizeSmall","thumb","thumbColorPrimary","thumbColorSecondary","thumbColorError","thumbColorSuccess","thumbColorInfo","thumbColorWarning","track","trackInverted","trackFalse","thumbSizeSmall","valueLabel","valueLabelOpen","valueLabelCircle","valueLabelLabel","vertical"]),on=t=>{const{open:n}=t;return{offset:Fe(n&&he.valueLabelOpen),circle:he.valueLabelCircle,label:he.valueLabelLabel}};function an(t){const{children:n,className:l,value:r}=t,a=on(t);return n?u.cloneElement(n,{className:Fe(n.props.className)},e.jsxs(u.Fragment,{children:[n.props.children,e.jsx("span",{className:Fe(a.offset,l),"aria-hidden":!0,children:e.jsx("span",{className:a.circle,children:e.jsx("span",{className:a.label,children:r})})})]})):null}const dn=["aria-label","aria-valuetext","aria-labelledby","component","components","componentsProps","color","classes","className","disableSwap","disabled","getAriaLabel","getAriaValueText","marks","max","min","name","onChange","onChangeCommitted","orientation","shiftStep","size","step","scale","slotProps","slots","tabIndex","track","value","valueLabelDisplay","valueLabelFormat"],cn=Jt();function Nt(t){return t}const pn=be("span",{name:"MuiSlider",slot:"Root",overridesResolver:(t,n)=>{const{ownerState:l}=t;return[n.root,n[`color${de(l.color)}`],l.size!=="medium"&&n[`size${de(l.size)}`],l.marked&&n.marked,l.orientation==="vertical"&&n.vertical,l.track==="inverted"&&n.trackInverted,l.track===!1&&n.trackFalse]}})(({theme:t})=>{var n;return{borderRadius:12,boxSizing:"content-box",display:"inline-block",position:"relative",cursor:"pointer",touchAction:"none",WebkitTapHighlightColor:"transparent","@media print":{colorAdjust:"exact"},[`&.${he.disabled}`]:{pointerEvents:"none",cursor:"default",color:(t.vars||t).palette.grey[400]},[`&.${he.dragging}`]:{[`& .${he.thumb}, & .${he.track}`]:{transition:"none"}},variants:[...Object.keys(((n=t.vars)!=null?n:t).palette).filter(l=>{var r;return((r=t.vars)!=null?r:t).palette[l].main}).map(l=>({props:{color:l},style:{color:(t.vars||t).palette[l].main}})),{props:{orientation:"horizontal"},style:{height:4,width:"100%",padding:"13px 0","@media (pointer: coarse)":{padding:"20px 0"}}},{props:{orientation:"horizontal",size:"small"},style:{height:2}},{props:{orientation:"horizontal",marked:!0},style:{marginBottom:20}},{props:{orientation:"vertical"},style:{height:"100%",width:4,padding:"0 13px","@media (pointer: coarse)":{padding:"0 20px"}}},{props:{orientation:"vertical",size:"small"},style:{width:2}},{props:{orientation:"vertical",marked:!0},style:{marginRight:44}}]}}),un=be("span",{name:"MuiSlider",slot:"Rail",overridesResolver:(t,n)=>n.rail})({display:"block",position:"absolute",borderRadius:"inherit",backgroundColor:"currentColor",opacity:.38,variants:[{props:{orientation:"horizontal"},style:{width:"100%",height:"inherit",top:"50%",transform:"translateY(-50%)"}},{props:{orientation:"vertical"},style:{height:"100%",width:"inherit",left:"50%",transform:"translateX(-50%)"}},{props:{track:"inverted"},style:{opacity:1}}]}),xn=be("span",{name:"MuiSlider",slot:"Track",overridesResolver:(t,n)=>n.track})(({theme:t})=>{var n;return{display:"block",position:"absolute",borderRadius:"inherit",border:"1px solid currentColor",backgroundColor:"currentColor",transition:t.transitions.create(["left","width","bottom","height"],{duration:t.transitions.duration.shortest}),variants:[{props:{size:"small"},style:{border:"none"}},{props:{orientation:"horizontal"},style:{height:"inherit",top:"50%",transform:"translateY(-50%)"}},{props:{orientation:"vertical"},style:{width:"inherit",left:"50%",transform:"translateX(-50%)"}},{props:{track:!1},style:{display:"none"}},...Object.keys(((n=t.vars)!=null?n:t).palette).filter(l=>{var r;return((r=t.vars)!=null?r:t).palette[l].main}).map(l=>({props:{color:l,track:"inverted"},style:F({},t.vars?{backgroundColor:t.vars.palette.Slider[`${l}Track`],borderColor:t.vars.palette.Slider[`${l}Track`]}:F({backgroundColor:wt(t.palette[l].main,.62),borderColor:wt(t.palette[l].main,.62)},t.applyStyles("dark",{backgroundColor:Ct(t.palette[l].main,.5)}),t.applyStyles("dark",{borderColor:Ct(t.palette[l].main,.5)})))}))]}}),hn=be("span",{name:"MuiSlider",slot:"Thumb",overridesResolver:(t,n)=>{const{ownerState:l}=t;return[n.thumb,n[`thumbColor${de(l.color)}`],l.size!=="medium"&&n[`thumbSize${de(l.size)}`]]}})(({theme:t})=>{var n;return{position:"absolute",width:20,height:20,boxSizing:"border-box",borderRadius:"50%",outline:0,backgroundColor:"currentColor",display:"flex",alignItems:"center",justifyContent:"center",transition:t.transitions.create(["box-shadow","left","bottom"],{duration:t.transitions.duration.shortest}),"&::before":{position:"absolute",content:'""',borderRadius:"inherit",width:"100%",height:"100%",boxShadow:(t.vars||t).shadows[2]},"&::after":{position:"absolute",content:'""',borderRadius:"50%",width:42,height:42,top:"50%",left:"50%",transform:"translate(-50%, -50%)"},[`&.${he.disabled}`]:{"&:hover":{boxShadow:"none"}},variants:[...Object.keys(((n=t.vars)!=null?n:t).palette).filter(l=>{var r;return((r=t.vars)!=null?r:t).palette[l].main}).map(l=>({props:{color:l},style:{[`&:hover, &.${he.focusVisible}`]:F({},t.vars?{boxShadow:`0px 0px 0px 8px rgba(${t.vars.palette[l].mainChannel} / 0.16)`}:{boxShadow:`0px 0px 0px 8px ${tt(t.palette[l].main,.16)}`},{"@media (hover: none)":{boxShadow:"none"}}),[`&.${he.active}`]:F({},t.vars?{boxShadow:`0px 0px 0px 14px rgba(${t.vars.palette[l].mainChannel} / 0.16)}`}:{boxShadow:`0px 0px 0px 14px ${tt(t.palette[l].main,.16)}`})}})),{props:{size:"small"},style:{width:12,height:12,"&::before":{boxShadow:"none"}}},{props:{orientation:"horizontal"},style:{top:"50%",transform:"translate(-50%, -50%)"}},{props:{orientation:"vertical"},style:{left:"50%",transform:"translate(-50%, 50%)"}}]}}),mn=be(an,{name:"MuiSlider",slot:"ValueLabel",overridesResolver:(t,n)=>n.valueLabel})(({theme:t})=>F({zIndex:1,whiteSpace:"nowrap"},t.typography.body2,{fontWeight:500,transition:t.transitions.create(["transform"],{duration:t.transitions.duration.shortest}),position:"absolute",backgroundColor:(t.vars||t).palette.grey[600],borderRadius:2,color:(t.vars||t).palette.common.white,display:"flex",alignItems:"center",justifyContent:"center",padding:"0.25rem 0.75rem",variants:[{props:{orientation:"horizontal"},style:{transform:"translateY(-100%) scale(0)",top:"-10px",transformOrigin:"bottom center","&::before":{position:"absolute",content:'""',width:8,height:8,transform:"translate(-50%, 50%) rotate(45deg)",backgroundColor:"inherit",bottom:0,left:"50%"},[`&.${he.valueLabelOpen}`]:{transform:"translateY(-100%) scale(1)"}}},{props:{orientation:"vertical"},style:{transform:"translateY(-50%) scale(0)",right:"30px",top:"50%",transformOrigin:"right center","&::before":{position:"absolute",content:'""',width:8,height:8,transform:"translate(-50%, -50%) rotate(45deg)",backgroundColor:"inherit",right:-8,top:"50%"},[`&.${he.valueLabelOpen}`]:{transform:"translateY(-50%) scale(1)"}}},{props:{size:"small"},style:{fontSize:t.typography.pxToRem(12),padding:"0.25rem 0.5rem"}},{props:{orientation:"vertical",size:"small"},style:{right:"20px"}}]})),yn=be("span",{name:"MuiSlider",slot:"Mark",shouldForwardProp:t=>Gt(t)&&t!=="markActive",overridesResolver:(t,n)=>{const{markActive:l}=t;return[n.mark,l&&n.markActive]}})(({theme:t})=>({position:"absolute",width:2,height:2,borderRadius:1,backgroundColor:"currentColor",variants:[{props:{orientation:"horizontal"},style:{top:"50%",transform:"translate(-1px, -50%)"}},{props:{orientation:"vertical"},style:{left:"50%",transform:"translate(-50%, 1px)"}},{props:{markActive:!0},style:{backgroundColor:(t.vars||t).palette.background.paper,opacity:.8}}]})),gn=be("span",{name:"MuiSlider",slot:"MarkLabel",shouldForwardProp:t=>Gt(t)&&t!=="markLabelActive",overridesResolver:(t,n)=>n.markLabel})(({theme:t})=>F({},t.typography.body2,{color:(t.vars||t).palette.text.secondary,position:"absolute",whiteSpace:"nowrap",variants:[{props:{orientation:"horizontal"},style:{top:30,transform:"translateX(-50%)","@media (pointer: coarse)":{top:40}}},{props:{orientation:"vertical"},style:{left:36,transform:"translateY(50%)","@media (pointer: coarse)":{left:44}}},{props:{markLabelActive:!0},style:{color:(t.vars||t).palette.text.primary}}]})),fn=t=>{const{disabled:n,dragging:l,marked:r,orientation:a,track:c,classes:p,color:i,size:o}=t,s={root:["root",n&&"disabled",l&&"dragging",r&&"marked",a==="vertical"&&"vertical",c==="inverted"&&"trackInverted",c===!1&&"trackFalse",i&&`color${de(i)}`,o&&`size${de(o)}`],rail:["rail"],track:["track"],mark:["mark"],markActive:["markActive"],markLabel:["markLabel"],markLabelActive:["markLabelActive"],valueLabel:["valueLabel"],thumb:["thumb",n&&"disabled",o&&`thumbSize${de(o)}`,i&&`thumbColor${de(i)}`],active:["active"],disabled:["disabled"],focusVisible:["focusVisible"]};return Qt(s,ln,p)},bn=({children:t})=>t,nt=u.forwardRef(function(n,l){var r,a,c,p,i,o,s,d,m,x,v,W,I,K,me,ce,N,te,se,Y,ne,H,le,ee;const J=cn({props:n,name:"MuiSlider"}),ve=bs(),{"aria-label":ie,"aria-valuetext":pe,"aria-labelledby":q,component:je="span",components:V={},componentsProps:oe={},color:Oe="primary",classes:Ne,className:He,disableSwap:ze=!1,disabled:ye=!1,getAriaLabel:Q,getAriaValueText:Ce,marks:B=!1,max:Se=100,min:ke=0,orientation:Ae="horizontal",shiftStep:Me=10,size:Re="medium",step:De=1,scale:f=Nt,slotProps:w,slots:j,track:E="normal",valueLabelDisplay:A="off",valueLabelFormat:ae=Nt}=J,Ue=Xt(J,dn),G=F({},J,{isRtl:ve,max:Se,min:ke,classes:Ne,disabled:ye,disableSwap:ze,orientation:Ae,marks:B,color:Oe,size:Re,step:De,shiftStep:Me,scale:f,track:E,valueLabelDisplay:A,valueLabelFormat:ae}),{axisProps:We,getRootProps:it,getHiddenInputProps:rt,getThumbProps:lt,open:Pt,active:ot,axis:Ke,focusedThumbIndex:y,range:g,dragging:k,marks:$,values:T,trackOffset:P,trackLeap:ge,getThumbStyle:we}=nn(F({},G,{rootRef:l}));G.marked=$.length>0&&$.some(U=>U.label),G.dragging=k,G.focusedThumbIndex=y;const D=fn(G),xe=(r=(a=j==null?void 0:j.root)!=null?a:V.Root)!=null?r:pn,_e=(c=(p=j==null?void 0:j.rail)!=null?p:V.Rail)!=null?c:un,Lt=(i=(o=j==null?void 0:j.track)!=null?o:V.Track)!=null?i:xn,$t=(s=(d=j==null?void 0:j.thumb)!=null?d:V.Thumb)!=null?s:hn,Et=(m=(x=j==null?void 0:j.valueLabel)!=null?x:V.ValueLabel)!=null?m:mn,at=(v=(W=j==null?void 0:j.mark)!=null?W:V.Mark)!=null?v:yn,dt=(I=(K=j==null?void 0:j.markLabel)!=null?K:V.MarkLabel)!=null?I:gn,Tt=(me=(ce=j==null?void 0:j.input)!=null?ce:V.Input)!=null?me:"input",ct=(N=w==null?void 0:w.root)!=null?N:oe.root,os=(te=w==null?void 0:w.rail)!=null?te:oe.rail,pt=(se=w==null?void 0:w.track)!=null?se:oe.track,ut=(Y=w==null?void 0:w.thumb)!=null?Y:oe.thumb,xt=(ne=w==null?void 0:w.valueLabel)!=null?ne:oe.valueLabel,as=(H=w==null?void 0:w.mark)!=null?H:oe.mark,ds=(le=w==null?void 0:w.markLabel)!=null?le:oe.markLabel,cs=(ee=w==null?void 0:w.input)!=null?ee:oe.input,ps=$e({elementType:xe,getSlotProps:it,externalSlotProps:ct,externalForwardedProps:Ue,additionalProps:F({},rn(xe)&&{as:je}),ownerState:F({},G,ct==null?void 0:ct.ownerState),className:[D.root,He]}),us=$e({elementType:_e,externalSlotProps:os,ownerState:G,className:D.rail}),xs=$e({elementType:Lt,externalSlotProps:pt,additionalProps:{style:F({},We[Ke].offset(P),We[Ke].leap(ge))},ownerState:F({},G,pt==null?void 0:pt.ownerState),className:D.track}),ht=$e({elementType:$t,getSlotProps:lt,externalSlotProps:ut,ownerState:F({},G,ut==null?void 0:ut.ownerState),className:D.thumb}),hs=$e({elementType:Et,externalSlotProps:xt,ownerState:F({},G,xt==null?void 0:xt.ownerState),className:D.valueLabel}),mt=$e({elementType:at,externalSlotProps:as,ownerState:G,className:D.mark}),yt=$e({elementType:dt,externalSlotProps:ds,ownerState:G,className:D.markLabel}),ms=$e({elementType:Tt,getSlotProps:rt,externalSlotProps:cs,ownerState:G});return e.jsxs(xe,F({},ps,{children:[e.jsx(_e,F({},us)),e.jsx(Lt,F({},xs)),$.filter(U=>U.value>=ke&&U.value<=Se).map((U,Z)=>{const gt=st(U.value,ke,Se),qe=We[Ke].offset(gt);let Ie;return E===!1?Ie=T.indexOf(U.value)!==-1:Ie=E==="normal"&&(g?U.value>=T[0]&&U.value<=T[T.length-1]:U.value<=T[0])||E==="inverted"&&(g?U.value<=T[0]||U.value>=T[T.length-1]:U.value>=T[0]),e.jsxs(u.Fragment,{children:[e.jsx(at,F({"data-index":Z},mt,!et(at)&&{markActive:Ie},{style:F({},qe,mt.style),className:Fe(mt.className,Ie&&D.markActive)})),U.label!=null?e.jsx(dt,F({"aria-hidden":!0,"data-index":Z},yt,!et(dt)&&{markLabelActive:Ie},{style:F({},qe,yt.style),className:Fe(D.markLabel,yt.className,Ie&&D.markLabelActive),children:U.label})):null]},Z)}),T.map((U,Z)=>{const gt=st(U,ke,Se),qe=We[Ke].offset(gt),Ie=A==="off"?bn:Et;return e.jsx(Ie,F({},!et(Ie)&&{valueLabelFormat:ae,valueLabelDisplay:A,value:typeof ae=="function"?ae(f(U),Z):ae,index:Z,open:Pt===Z||ot===Z||A==="on",disabled:ye},hs,{children:e.jsx($t,F({"data-index":Z},ht,{className:Fe(D.thumb,ht.className,ot===Z&&D.active,y===Z&&D.focusVisible),style:F({},qe,we(Z),ht.style),children:e.jsx(Tt,F({"data-index":Z,"aria-label":Q?Q(Z):ie,"aria-valuenow":f(U),"aria-labelledby":q,"aria-valuetext":Ce?Ce(f(U),Z):pe,value:T[Z]},ms))}))}),Z)})]}))});function vn(t){return Yt("MuiSwitch",t)}const re=qt("MuiSwitch",["root","edgeStart","edgeEnd","switchBase","colorPrimary","colorSecondary","sizeSmall","sizeMedium","checked","disabled","input","thumb","track"]),jn=["className","color","edge","size","sx"],Sn=Jt(),kn=t=>{const{classes:n,edge:l,size:r,color:a,checked:c,disabled:p}=t,i={root:["root",l&&`edge${de(l)}`,`size${de(r)}`],switchBase:["switchBase",`color${de(a)}`,c&&"checked",p&&"disabled"],thumb:["thumb"],track:["track"],input:["input"]},o=Qt(i,vn,n);return F({},n,o)},wn=be("span",{name:"MuiSwitch",slot:"Root",overridesResolver:(t,n)=>{const{ownerState:l}=t;return[n.root,l.edge&&n[`edge${de(l.edge)}`],n[`size${de(l.size)}`]]}})({display:"inline-flex",width:34+12*2,height:14+12*2,overflow:"hidden",padding:12,boxSizing:"border-box",position:"relative",flexShrink:0,zIndex:0,verticalAlign:"middle","@media print":{colorAdjust:"exact"},variants:[{props:{edge:"start"},style:{marginLeft:-8}},{props:{edge:"end"},style:{marginRight:-8}},{props:{size:"small"},style:{width:40,height:24,padding:7,[`& .${re.thumb}`]:{width:16,height:16},[`& .${re.switchBase}`]:{padding:4,[`&.${re.checked}`]:{transform:"translateX(16px)"}}}}]}),Cn=be(Ks,{name:"MuiSwitch",slot:"SwitchBase",overridesResolver:(t,n)=>{const{ownerState:l}=t;return[n.switchBase,{[`& .${re.input}`]:n.input},l.color!=="default"&&n[`color${de(l.color)}`]]}})(({theme:t})=>({position:"absolute",top:0,left:0,zIndex:1,color:t.vars?t.vars.palette.Switch.defaultColor:`${t.palette.mode==="light"?t.palette.common.white:t.palette.grey[300]}`,transition:t.transitions.create(["left","transform"],{duration:t.transitions.duration.shortest}),[`&.${re.checked}`]:{transform:"translateX(20px)"},[`&.${re.disabled}`]:{color:t.vars?t.vars.palette.Switch.defaultDisabledColor:`${t.palette.mode==="light"?t.palette.grey[100]:t.palette.grey[600]}`},[`&.${re.checked} + .${re.track}`]:{opacity:.5},[`&.${re.disabled} + .${re.track}`]:{opacity:t.vars?t.vars.opacity.switchTrackDisabled:`${t.palette.mode==="light"?.12:.2}`},[`& .${re.input}`]:{left:"-100%",width:"300%"}}),({theme:t})=>({"&:hover":{backgroundColor:t.vars?`rgba(${t.vars.palette.action.activeChannel} / ${t.vars.palette.action.hoverOpacity})`:tt(t.palette.action.active,t.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:"transparent"}},variants:[...Object.entries(t.palette).filter(([,n])=>n.main&&n.light).map(([n])=>({props:{color:n},style:{[`&.${re.checked}`]:{color:(t.vars||t).palette[n].main,"&:hover":{backgroundColor:t.vars?`rgba(${t.vars.palette[n].mainChannel} / ${t.vars.palette.action.hoverOpacity})`:tt(t.palette[n].main,t.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:"transparent"}},[`&.${re.disabled}`]:{color:t.vars?t.vars.palette.Switch[`${n}DisabledColor`]:`${t.palette.mode==="light"?wt(t.palette[n].main,.62):Ct(t.palette[n].main,.55)}`}},[`&.${re.checked} + .${re.track}`]:{backgroundColor:(t.vars||t).palette[n].main}}}))]})),In=be("span",{name:"MuiSwitch",slot:"Track",overridesResolver:(t,n)=>n.track})(({theme:t})=>({height:"100%",width:"100%",borderRadius:14/2,zIndex:-1,transition:t.transitions.create(["opacity","background-color"],{duration:t.transitions.duration.shortest}),backgroundColor:t.vars?t.vars.palette.common.onBackground:`${t.palette.mode==="light"?t.palette.common.black:t.palette.common.white}`,opacity:t.vars?t.vars.opacity.switchTrack:`${t.palette.mode==="light"?.38:.3}`})),Pn=be("span",{name:"MuiSwitch",slot:"Thumb",overridesResolver:(t,n)=>n.thumb})(({theme:t})=>({boxShadow:(t.vars||t).shadows[1],backgroundColor:"currentColor",width:20,height:20,borderRadius:"50%"})),Ln=u.forwardRef(function(n,l){const r=Sn({props:n,name:"MuiSwitch"}),{className:a,color:c="primary",edge:p=!1,size:i="medium",sx:o}=r,s=Xt(r,jn),d=F({},r,{color:c,edge:p,size:i}),m=kn(d),x=e.jsx(Pn,{className:m.thumb,ownerState:d});return e.jsxs(wn,{className:Fe(m.root,a),sx:o,ownerState:d,children:[e.jsx(Cn,F({type:"checkbox",icon:x,checkedIcon:x,ref:l,ownerState:d},s,{classes:F({},m,{root:m.switchBase})})),e.jsx(In,{className:m.track,ownerState:d})]})});var $n=["prefixCls","className","checked","defaultChecked","disabled","loadingIcon","checkedChildren","unCheckedChildren","onClick","onChange","onKeyDown"],rs=u.forwardRef(function(t,n){var l,r=t.prefixCls,a=r===void 0?"rc-switch":r,c=t.className,p=t.checked,i=t.defaultChecked,o=t.disabled,s=t.loadingIcon,d=t.checkedChildren,m=t.unCheckedChildren,x=t.onClick,v=t.onChange,W=t.onKeyDown,I=vs(t,$n),K=Zt(!1,{value:p,defaultValue:i}),me=js(K,2),ce=me[0],N=me[1];function te(H,le){var ee=ce;return o||(ee=H,N(ee),v==null||v(ee,le)),ee}function se(H){H.which===Dt.LEFT?te(!1,H):H.which===Dt.RIGHT&&te(!0,H),W==null||W(H)}function Y(H){var le=te(!ce,H);x==null||x(le,H)}var ne=es(a,c,(l={},At(l,"".concat(a,"-checked"),ce),At(l,"".concat(a,"-disabled"),o),l));return u.createElement("button",Ss({},I,{type:"button",role:"switch","aria-checked":ce,disabled:o,className:ne,ref:n,onKeyDown:se,onClick:Y}),s,u.createElement("span",{className:"".concat(a,"-inner")},u.createElement("span",{className:"".concat(a,"-inner-checked")},d),u.createElement("span",{className:"".concat(a,"-inner-unchecked")},m)))});rs.displayName="Switch";const En=t=>{const{componentCls:n,trackHeightSM:l,trackPadding:r,trackMinWidthSM:a,innerMinMarginSM:c,innerMaxMarginSM:p,handleSizeSM:i,calc:o}=t,s=`${n}-inner`,d=Te(o(i).add(o(r).mul(2)).equal()),m=Te(o(p).mul(2).equal());return{[n]:{[`&${n}-small`]:{minWidth:a,height:l,lineHeight:Te(l),[`${n}-inner`]:{paddingInlineStart:p,paddingInlineEnd:c,[`${s}-checked, ${s}-unchecked`]:{minHeight:l},[`${s}-checked`]:{marginInlineStart:`calc(-100% + ${d} - ${m})`,marginInlineEnd:`calc(100% - ${d} + ${m})`},[`${s}-unchecked`]:{marginTop:o(l).mul(-1).equal(),marginInlineStart:0,marginInlineEnd:0}},[`${n}-handle`]:{width:i,height:i},[`${n}-loading-icon`]:{top:o(o(i).sub(t.switchLoadingIconSize)).div(2).equal(),fontSize:t.switchLoadingIconSize},[`&${n}-checked`]:{[`${n}-inner`]:{paddingInlineStart:c,paddingInlineEnd:p,[`${s}-checked`]:{marginInlineStart:0,marginInlineEnd:0},[`${s}-unchecked`]:{marginInlineStart:`calc(100% - ${d} + ${m})`,marginInlineEnd:`calc(-100% + ${d} - ${m})`}},[`${n}-handle`]:{insetInlineStart:`calc(100% - ${Te(o(i).add(r).equal())})`}},[`&:not(${n}-disabled):active`]:{[`&:not(${n}-checked) ${s}`]:{[`${s}-unchecked`]:{marginInlineStart:o(t.marginXXS).div(2).equal(),marginInlineEnd:o(t.marginXXS).mul(-1).div(2).equal()}},[`&${n}-checked ${s}`]:{[`${s}-checked`]:{marginInlineStart:o(t.marginXXS).mul(-1).div(2).equal(),marginInlineEnd:o(t.marginXXS).div(2).equal()}}}}}}},Tn=t=>{const{componentCls:n,handleSize:l,calc:r}=t;return{[n]:{[`${n}-loading-icon${t.iconCls}`]:{position:"relative",top:r(r(l).sub(t.fontSize)).div(2).equal(),color:t.switchLoadingIconColor,verticalAlign:"top"},[`&${n}-checked ${n}-loading-icon`]:{color:t.switchColor}}}},zn=t=>{const{componentCls:n,trackPadding:l,handleBg:r,handleShadow:a,handleSize:c,calc:p}=t,i=`${n}-handle`;return{[n]:{[i]:{position:"absolute",top:l,insetInlineStart:l,width:c,height:c,transition:`all ${t.switchDuration} ease-in-out`,"&::before":{position:"absolute",top:0,insetInlineEnd:0,bottom:0,insetInlineStart:0,backgroundColor:r,borderRadius:p(c).div(2).equal(),boxShadow:a,transition:`all ${t.switchDuration} ease-in-out`,content:'""'}},[`&${n}-checked ${i}`]:{insetInlineStart:`calc(100% - ${Te(p(c).add(l).equal())})`},[`&:not(${n}-disabled):active`]:{[`${i}::before`]:{insetInlineEnd:t.switchHandleActiveInset,insetInlineStart:0},[`&${n}-checked ${i}::before`]:{insetInlineEnd:0,insetInlineStart:t.switchHandleActiveInset}}}}},An=t=>{const{componentCls:n,trackHeight:l,trackPadding:r,innerMinMargin:a,innerMaxMargin:c,handleSize:p,calc:i}=t,o=`${n}-inner`,s=Te(i(p).add(i(r).mul(2)).equal()),d=Te(i(c).mul(2).equal());return{[n]:{[o]:{display:"block",overflow:"hidden",borderRadius:100,height:"100%",paddingInlineStart:c,paddingInlineEnd:a,transition:`padding-inline-start ${t.switchDuration} ease-in-out, padding-inline-end ${t.switchDuration} ease-in-out`,[`${o}-checked, ${o}-unchecked`]:{display:"block",color:t.colorTextLightSolid,fontSize:t.fontSizeSM,transition:`margin-inline-start ${t.switchDuration} ease-in-out, margin-inline-end ${t.switchDuration} ease-in-out`,pointerEvents:"none",minHeight:l},[`${o}-checked`]:{marginInlineStart:`calc(-100% + ${s} - ${d})`,marginInlineEnd:`calc(100% - ${s} + ${d})`},[`${o}-unchecked`]:{marginTop:i(l).mul(-1).equal(),marginInlineStart:0,marginInlineEnd:0}},[`&${n}-checked ${o}`]:{paddingInlineStart:a,paddingInlineEnd:c,[`${o}-checked`]:{marginInlineStart:0,marginInlineEnd:0},[`${o}-unchecked`]:{marginInlineStart:`calc(100% - ${s} + ${d})`,marginInlineEnd:`calc(-100% + ${s} - ${d})`}},[`&:not(${n}-disabled):active`]:{[`&:not(${n}-checked) ${o}`]:{[`${o}-unchecked`]:{marginInlineStart:i(r).mul(2).equal(),marginInlineEnd:i(r).mul(-1).mul(2).equal()}},[`&${n}-checked ${o}`]:{[`${o}-checked`]:{marginInlineStart:i(r).mul(-1).mul(2).equal(),marginInlineEnd:i(r).mul(2).equal()}}}}}},Dn=t=>{const{componentCls:n,trackHeight:l,trackMinWidth:r}=t;return{[n]:Object.assign(Object.assign(Object.assign(Object.assign({},Cs(t)),{position:"relative",display:"inline-block",boxSizing:"border-box",minWidth:r,height:l,lineHeight:Te(l),verticalAlign:"middle",background:t.colorTextQuaternary,border:"0",borderRadius:100,cursor:"pointer",transition:`all ${t.motionDurationMid}`,userSelect:"none",[`&:hover:not(${n}-disabled)`]:{background:t.colorTextTertiary}}),Is(t)),{[`&${n}-checked`]:{background:t.switchColor,[`&:hover:not(${n}-disabled)`]:{background:t.colorPrimaryHover}},[`&${n}-loading, &${n}-disabled`]:{cursor:"not-allowed",opacity:t.switchDisabledOpacity,"*":{boxShadow:"none",cursor:"not-allowed"}},[`&${n}-rtl`]:{direction:"rtl"}})}},Fn=t=>{const{fontSize:n,lineHeight:l,controlHeight:r,colorWhite:a}=t,c=n*l,p=r/2,i=2,o=c-i*2,s=p-i*2;return{trackHeight:c,trackHeightSM:p,trackMinWidth:o*2+i*4,trackMinWidthSM:s*2+i*2,trackPadding:i,handleBg:a,handleSize:o,handleSizeSM:s,handleShadow:`0 2px 4px 0 ${new Ps("#00230b").setA(.2).toRgbString()}`,innerMinMargin:o/2,innerMaxMargin:o+i+i*2,innerMinMarginSM:s/2,innerMaxMarginSM:s+i+i*2}},Mn=ks("Switch",t=>{const n=ws(t,{switchDuration:t.motionDurationMid,switchColor:t.colorPrimary,switchDisabledOpacity:t.opacityLoading,switchLoadingIconSize:t.calc(t.fontSizeIcon).mul(.75).equal(),switchLoadingIconColor:`rgba(0, 0, 0, ${t.opacityLoading})`,switchHandleActiveInset:"-30%"});return[Dn(n),An(n),zn(n),Tn(n),En(n)]},Fn);var Rn=function(t,n){var l={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&n.indexOf(r)<0&&(l[r]=t[r]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(t);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(t,r[a])&&(l[r[a]]=t[r[a]]);return l};const Wn=u.forwardRef((t,n)=>{const{prefixCls:l,size:r,disabled:a,loading:c,className:p,rootClassName:i,style:o,checked:s,value:d,defaultChecked:m,defaultValue:x,onChange:v}=t,W=Rn(t,["prefixCls","size","disabled","loading","className","rootClassName","style","checked","value","defaultChecked","defaultValue","onChange"]),[I,K]=Zt(!1,{value:s??d,defaultValue:m??x}),{getPrefixCls:me,direction:ce,switch:N}=u.useContext(Ls),te=u.useContext($s),se=(a??te)||c,Y=me("switch",l),ne=u.createElement("div",{className:`${Y}-handle`},c&&u.createElement(zs,{className:`${Y}-loading-icon`})),[H,le,ee]=Mn(Y),J=Es(r),ve=es(N==null?void 0:N.className,{[`${Y}-small`]:J==="small",[`${Y}-loading`]:c,[`${Y}-rtl`]:ce==="rtl"},p,i,le,ee),ie=Object.assign(Object.assign({},N==null?void 0:N.style),o),pe=function(){K(arguments.length<=0?void 0:arguments[0]),v==null||v.apply(void 0,arguments)};return H(u.createElement(Ts,{component:"Switch"},u.createElement(rs,Object.assign({},W,{checked:I,onChange:pe,prefixCls:Y,className:ve,style:ie,disabled:se,ref:n,loadingIcon:ne}))))}),ls=Wn;ls.__ANT_SWITCH=!0;const{Text:M}=R,Ht=t=>t?new Date(t).toLocaleDateString("en-US",{year:"numeric",month:"short"}):"Present",_n=(t,n)=>{const l=new Set;return t&&((t.match(/"([^"]+)"/g)||[]).forEach(c=>{const p=c.replace(/"/g,"").trim();p&&l.add(p.toLowerCase())}),t.replace(/"[^"]*"/g,"").replace(/[()]/g," ").split(/\s+/).filter(c=>c.length>2&&!["and","or","not"].includes(c.toLowerCase())).forEach(c=>{const p=c.replace(/[^\w]/g,"").toLowerCase();p.length>2&&l.add(p)})),n&&Object.values(n).forEach(r=>{Array.isArray(r)&&r.forEach(a=>{typeof a=="string"&&a.trim()&&l.add(a.toLowerCase())})}),Array.from(l)},ue=(t,n,l)=>{if(!t)return t;const r=_n(n,l);if(r.length===0)return t;const a=r.map(i=>i.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")),c=new RegExp(`(${a.join("|")})`,"gi");return t.split(c).map((i,o)=>r.some(d=>i.toLowerCase().includes(d.toLowerCase()))?e.jsx("span",{style:{backgroundColor:"#fff3cd",fontWeight:"bold",padding:"1px 2px",borderRadius:"2px",border:"1px solid #ffeaa7"},children:i},o):i)};function Bn({candidate:t,index:n,searchString:l,filters:r}){var d,m,x,v,W;const[a,c]=u.useState("1"),[p,i]=u.useState("1");(d=t==null?void 0:t.experiences)!=null&&d[0],(m=t==null?void 0:t.experiences)!=null&&m.length;const o=[{key:"1",label:"Recent Experience"},{key:"2",label:"Profile Summary"}],s=[{key:"1",label:"Current Status"},{key:"2",label:"Contact Details"},{key:"3",label:"Key Skills"}];return e.jsxs("div",{style:{width:"100%",backgroundColor:"white",marginLeft:"10px",marginRight:"10px",marginBottom:"10px",padding:"15px",borderRadius:"8px",boxShadow:"0 2px 8px rgba(0,0,0,0.1)",border:"1px solid #f0f0f0"},children:[e.jsxs(b,{display:"flex",justifyContent:"space-between",alignItems:"center",sx:{borderRadius:"10px"},children:[e.jsxs(b,{display:"flex",alignItems:"center",children:[e.jsx(Vs,{style:{transform:"scale(1.5)"}}),e.jsxs(Ft,{src:t==null?void 0:t.profile_img,size:50,style:{marginLeft:"15px",marginRight:"15px"},children:[(x=t==null?void 0:t.first_name)==null?void 0:x[0],(v=t==null?void 0:t.last_name)==null?void 0:v[0]]}),e.jsxs("div",{children:[e.jsxs(M,{style:{fontSize:"20px",color:"#1A84DE",cursor:"pointer",fontFamily:"Poppins",fontWeight:600,display:"block"},children:[n+1,". ",ue(`${t==null?void 0:t.first_name} ${t==null?void 0:t.last_name}`,l,r)]}),e.jsx(M,{style:{fontSize:"14px",color:"#666",fontFamily:"Poppins"},children:ue((t==null?void 0:t.headline)||(t==null?void 0:t.current_title),l,r)})]})]}),e.jsxs(b,{display:"flex",gap:"8px",alignItems:"center",children:[e.jsx(kt,{color:"blue",children:t==null?void 0:t.profile_source}),e.jsx(kt,{color:(t==null?void 0:t.prospect_status)==="CONTACTED"?"green":"orange",children:t==null?void 0:t.prospect_status}),(t==null?void 0:t.profile_url)&&e.jsx(As,{title:"View LinkedIn Profile",children:e.jsx(S,{icon:"mdi:linkedin",style:{fontSize:"24px",color:"#0077B5",cursor:"pointer"},onClick:()=>window.open(t.profile_url,"_blank")})})]})]}),e.jsx(b,{style:{marginLeft:"80px",marginTop:"5px",marginBottom:"15px"},children:e.jsxs(M,{style:{fontSize:"16px",color:"#6E7787",fontFamily:"Poppins"},children:[e.jsx(S,{icon:"carbon:location",style:{marginRight:"5px"}}),ue(t==null?void 0:t.location,l,r)]})}),e.jsx("hr",{style:{width:"100%",color:"#CAC5C5",margin:"15px 0"}}),e.jsxs(X,{gutter:[16,16],children:[e.jsxs(L,{className:"gutter-row",span:12,style:{borderRight:"1px solid #CAC5C5",minHeight:"300px"},children:[e.jsx(Mt,{defaultActiveKey:"1",items:o.map(I=>({...I,label:e.jsx("span",{style:{color:"#424242",fontWeight:400,border:"none",marginBottom:0,fontSize:"14px",transition:"color 0.3s ease-in-out",fontFamily:"Poppins, sans-serif"},children:I.label})})),onChange:I=>{c(I)},activeKey:a,tabBarStyle:{marginBottom:0,fontWeight:500},className:"custom-tabs"}),a==="1"&&e.jsx("div",{style:{padding:"15px 10px"},children:t!=null&&t.experiences&&t.experiences.length>0?t.experiences.slice(0,2).map((I,K)=>e.jsxs("div",{style:{marginBottom:"20px",paddingBottom:"15px",borderBottom:K<1?"1px solid #f0f0f0":"none"},children:[e.jsxs("div",{style:{display:"flex",alignItems:"center",marginBottom:"8px"},children:[I.company_img&&e.jsx(Ft,{src:I.company_img,size:24,style:{marginRight:"8px"}}),e.jsx(M,{strong:!0,style:{fontSize:"14px",fontFamily:"Poppins"},children:ue(I.position,l,r)})]}),e.jsx(M,{style:{fontSize:"13px",color:"#666",fontFamily:"Poppins",display:"block"},children:ue(I.company_name,l,r)}),e.jsx(M,{style:{fontSize:"12px",color:"#999",fontFamily:"Poppins",display:"block"},children:I.duration||`${Ht(I.start_date)} - ${Ht(I.end_date)}`}),I.location&&e.jsxs(M,{style:{fontSize:"12px",color:"#666",fontFamily:"Poppins",display:"block",marginTop:"4px"},children:[e.jsx(S,{icon:"carbon:location",style:{marginRight:"4px"}}),ue(I.location,l,r)]}),I.responsibilities&&e.jsx(M,{style:{fontSize:"12px",color:"#555",fontFamily:"Poppins",display:"block",marginTop:"8px",lineHeight:"1.4"},children:ue(I.responsibilities.length>150?`${I.responsibilities.substring(0,150)}...`:I.responsibilities,l,r)})]},K)):e.jsx(M,{style:{fontSize:"14px",color:"#999",fontFamily:"Poppins"},children:"No experience information available"})}),a==="2"&&e.jsxs("div",{style:{padding:"15px 10px"},children:[t!=null&&t.summary?e.jsx(M,{style:{fontSize:"13px",color:"#555",fontFamily:"Poppins",lineHeight:"1.5"},children:ue(t.summary,l,r)}):e.jsx(M,{style:{fontSize:"14px",color:"#999",fontFamily:"Poppins"},children:"No summary available"}),(t==null?void 0:t.headline)&&e.jsxs("div",{style:{marginTop:"15px"},children:[e.jsx(M,{strong:!0,style:{fontSize:"13px",fontFamily:"Poppins",display:"block",marginBottom:"5px"},children:"Professional Headline:"}),e.jsx(M,{style:{fontSize:"13px",color:"#555",fontFamily:"Poppins"},children:ue(t.headline,l,r)})]}),(t==null?void 0:t.industry)&&e.jsxs("div",{style:{marginTop:"10px"},children:[e.jsx(M,{strong:!0,style:{fontSize:"13px",fontFamily:"Poppins",display:"block",marginBottom:"5px"},children:"Industry:"}),e.jsx(M,{style:{fontSize:"13px",color:"#555",fontFamily:"Poppins"},children:ue(t.industry,l,r)})]})]})]}),e.jsxs(L,{className:"gutter-row",span:12,children:[e.jsx(Mt,{defaultActiveKey:"1",items:s.map(I=>({...I,label:e.jsx("span",{style:{color:"#424242",fontWeight:400,border:"none",marginBottom:0,fontSize:"14px",transition:"color 0.3s ease-in-out",fontFamily:"Poppins, sans-serif"},children:I.label})})),onChange:I=>{i(I)},activeKey:p,tabBarStyle:{marginBottom:0,fontWeight:500},className:"custom-tabs"}),p==="1"&&e.jsxs("div",{style:{padding:"15px 10px"},children:[e.jsxs(b,{display:"flex",alignItems:"center",marginBottom:"10px",children:[e.jsx(S,{icon:"carbon:location",style:{fontSize:"18px",color:"#1A84DE"}}),e.jsx(M,{style:{fontSize:"14px",marginLeft:"8px",fontFamily:"Poppins"},children:ue(t==null?void 0:t.location,l,r)})]}),e.jsxs(b,{display:"flex",alignItems:"center",marginBottom:"10px",children:[e.jsx(S,{icon:"mdi:account-circle",style:{fontSize:"18px",color:"#1A84DE"}}),e.jsx(M,{style:{fontSize:"14px",marginLeft:"8px",fontFamily:"Poppins"},children:ue(t==null?void 0:t.current_title,l,r)})]}),e.jsxs(b,{display:"flex",alignItems:"center",marginBottom:"10px",children:[e.jsx(S,{icon:"mdi:briefcase",style:{fontSize:"18px",color:"#1A84DE"}}),e.jsxs(M,{style:{fontSize:"14px",marginLeft:"8px",fontFamily:"Poppins"},children:[((W=t==null?void 0:t.experiences)==null?void 0:W.length)||0," work experiences"]})]}),e.jsxs(b,{display:"flex",alignItems:"center",marginBottom:"10px",children:[e.jsx(S,{icon:"mdi:account-check",style:{fontSize:"18px",color:"#1A84DE"}}),e.jsxs(M,{style:{fontSize:"14px",marginLeft:"8px",fontFamily:"Poppins"},children:["Status: ",t==null?void 0:t.prospect_status]})]}),e.jsxs(b,{display:"flex",alignItems:"center",marginBottom:"10px",children:[e.jsx(S,{icon:"mdi:calendar",style:{fontSize:"18px",color:"#1A84DE"}}),e.jsxs(M,{style:{fontSize:"14px",marginLeft:"8px",fontFamily:"Poppins"},children:["Added: ",new Date(t==null?void 0:t.created_at).toLocaleDateString()]})]}),e.jsxs(b,{display:"flex",alignItems:"center",marginBottom:"10px",children:[e.jsx(S,{icon:"mdi:source-branch",style:{fontSize:"18px",color:"#1A84DE"}}),e.jsxs(M,{style:{fontSize:"14px",marginLeft:"8px",fontFamily:"Poppins"},children:["Source: ",t==null?void 0:t.profile_source]})]})]}),p==="2"&&e.jsxs("div",{style:{padding:"15px 10px"},children:[e.jsxs(b,{display:"flex",alignItems:"center",marginBottom:"10px",children:[e.jsx(S,{icon:"mdi:account",style:{fontSize:"18px",color:"#1A84DE"}}),e.jsxs(M,{style:{fontSize:"14px",marginLeft:"8px",fontFamily:"Poppins"},children:[t==null?void 0:t.first_name," ",t==null?void 0:t.last_name]})]}),(t==null?void 0:t.profile_url)&&e.jsxs(b,{display:"flex",alignItems:"center",marginBottom:"10px",children:[e.jsx(S,{icon:"mdi:linkedin",style:{fontSize:"18px",color:"#0077B5"}}),e.jsx(M,{style:{fontSize:"14px",marginLeft:"8px",fontFamily:"Poppins",color:"#0077B5",cursor:"pointer"},onClick:()=>window.open(t.profile_url,"_blank"),children:"View LinkedIn Profile"})]}),e.jsxs(b,{display:"flex",alignItems:"center",marginBottom:"10px",children:[e.jsx(S,{icon:"mdi:email",style:{fontSize:"18px",color:"#1A84DE"}}),e.jsx(M,{style:{fontSize:"14px",marginLeft:"8px",fontFamily:"Poppins"},children:"Contact via platform"})]}),e.jsxs(b,{display:"flex",alignItems:"center",marginBottom:"10px",children:[e.jsx(S,{icon:"mdi:calendar-clock",style:{fontSize:"18px",color:"#1A84DE"}}),e.jsxs(M,{style:{fontSize:"14px",marginLeft:"8px",fontFamily:"Poppins"},children:["Last updated: ",new Date(t==null?void 0:t.updated_at).toLocaleDateString()]})]}),e.jsxs(b,{display:"flex",alignItems:"center",marginBottom:"10px",children:[e.jsx(S,{icon:"mdi:id-card",style:{fontSize:"18px",color:"#1A84DE"}}),e.jsxs(M,{style:{fontSize:"14px",marginLeft:"8px",fontFamily:"Poppins"},children:["ID: ",t==null?void 0:t.id]})]})]}),p==="3"&&e.jsx("div",{style:{padding:"15px 10px"},children:t!=null&&t.skills&&t.skills.length>0?e.jsx("div",{children:t.skills.map((I,K)=>e.jsx(kt,{style:{marginBottom:"8px",marginRight:"8px",backgroundColor:"#f0f8ff",border:"1px solid #1A84DE",color:"#1A84DE"},children:ue(I.skill_name,l,r)},K))}):e.jsx(M,{style:{fontSize:"14px",color:"#999",fontFamily:"Poppins"},children:"No skills information available"})})]})]})]})}const On=C(z)`
  padding: 16px;
  cursor: pointer;
  border: 1px solid #ced0da;
  border-radius: 4px;
  margin-bottom: 16px;

  &:hover {
    border-color: #1a84de;
  }
`,Nn=[{id:"english",label:"English"},{id:"german",label:"German"},{id:"french",label:"French"},{id:"spanish",label:"Spanish"},{id:"chinese",label:"Chinese"},{id:"japanese",label:"Japanese"}],Hn=[{id:"any",label:"Any"},{id:"elementary",label:"Elementary"},{id:"limited_working",label:"Limited Working"},{id:"professional_working",label:"Professional Working"},{id:"full_professional",label:"Full Professional"},{id:"native",label:"Native or Bilingual"}];function Kn({onChange:t}){const[n,l]=u.useState(!1),[r,a]=u.useState([]),[c,p]=u.useState(""),[i,o]=u.useState(""),[s,d]=u.useState(""),m=()=>{if(!c.trim())return;const v=Nn.find(W=>W.label.toLowerCase()===c.toLowerCase())||{id:c,label:c};a([...r,{language:v.label,proficiency:i,filter:s}]),p(""),o(""),d(""),t==null||t([...r,{language:v.label,proficiency:i,filter:s}])},x=v=>{const W=r.filter((I,K)=>K!==v);a(W),t==null||t(W)};return e.jsx("div",{children:e.jsxs(On,{onClick:()=>l(!n),style:{height:n?"auto":"85px"},children:[e.jsx("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:e.jsxs("div",{children:[e.jsx(h,{variant:"h6",style:{fontWeight:500,marginBottom:4},children:"Spoken Languages"}),e.jsxs(h,{variant:"body2",color:"textSecondary",style:{display:"flex"},children:[e.jsx(S,{icon:"material-symbols:add",width:"20",height:"20",style:{color:"#1a84de"}})," Spoken languages proficiency"]})]})}),n&&e.jsxs("div",{style:{padding:"16px"},onClick:v=>v.stopPropagation(),children:[e.jsx("div",{style:{marginTop:8,display:"flex",flexWrap:"wrap",gap:8},children:r.map((v,W)=>e.jsx(O,{label:`${v.language} (${v.proficiency||"Any"})`,onDelete:()=>x(W)},W))}),e.jsxs(b,{mt:2,display:"flex",gap:1,children:[e.jsx(_,{style:{width:"80%"},variant:"outlined",placeholder:"Enter a language...",size:"small",value:c,onChange:v=>{p(v.target.value)},onKeyPress:v=>{v.key==="Enter"&&(v.preventDefault(),m())}}),e.jsx(_,{style:{width:"20%"},select:!0,size:"small",fullWidth:!0,value:s,onChange:v=>d(v.target.value),defaultValue:"",children:["Can have","Must have","Doesn't have"].map(v=>e.jsx(Ee,{value:v,children:v},v))})]}),e.jsxs(b,{mt:2,children:[e.jsx(h,{variant:"body2",sx:{fontWeight:600,mb:1},children:"Proficiency:"}),e.jsx(_,{select:!0,fullWidth:!0,size:"small",value:i,onChange:v=>o(v.target.value),children:Hn.map(v=>e.jsx(Ee,{value:v.label,children:v.label},v.id))})]})]})]})})}const Vn=C(z)`
  padding: 16px;
  cursor: pointer;
  border: 1px solid #ced0da;
  border-radius: 4px;
  margin-bottom: 16px;

  &:hover {
    border-color: #1a84de;
  }
`;function Un({onNamesChange:t}){const[n,l]=u.useState(!1),[r,a]=u.useState([]),[c,p]=u.useState(""),i=()=>{if(c.trim()){const s=[...r,c.trim()];a(s),p(""),t==null||t(s)}},o=s=>{const d=r.filter((m,x)=>x!==s);a(d),t==null||t(d)};return e.jsx("div",{children:e.jsxs(Vn,{onClick:()=>l(!n),style:{height:n?"auto":"85px"},children:[e.jsx("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:e.jsxs("div",{children:[e.jsx(h,{variant:"h6",style:{fontWeight:500,marginBottom:4},children:"First Names"}),e.jsxs(h,{variant:"body2",color:"textSecondary",style:{display:"flex"},children:[e.jsx(S,{icon:"material-symbols:add",width:"20",height:"20",style:{color:"#1a84de"}})," Filter by first names"]})]})}),n&&e.jsxs("div",{style:{padding:"16px"},onClick:s=>s.stopPropagation(),children:[e.jsx("div",{style:{marginTop:8,display:"flex",flexWrap:"wrap",gap:8},children:r.map((s,d)=>e.jsx(O,{label:s,onDelete:()=>o(d)},d))}),e.jsx(b,{mt:2,display:"flex",gap:1,children:e.jsx(_,{fullWidth:!0,variant:"outlined",placeholder:"Enter a first name...",size:"small",value:c,onChange:s=>{p(s.target.value)},onKeyPress:s=>{s.key==="Enter"&&(s.preventDefault(),i())}})})]})]})})}const qn=C(z)`
  padding: 16px;
  cursor: pointer;
  border: 1px solid #ced0da;
  border-radius: 4px;
  margin-bottom: 16px;

  &:hover {
    border-color: #1a84de;
  }
`;function Yn({onNamesChange:t}){const[n,l]=u.useState(!1),[r,a]=u.useState([]),[c,p]=u.useState(""),i=()=>{if(c.trim()){const s=[...r,c.trim()];a(s),p(""),t==null||t(s)}},o=s=>{const d=r.filter((m,x)=>x!==s);a(d),t==null||t(d)};return e.jsx("div",{children:e.jsxs(qn,{onClick:()=>l(!n),style:{height:n?"auto":"85px"},children:[e.jsx("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:e.jsxs("div",{children:[e.jsx(h,{variant:"h6",style:{fontWeight:500,marginBottom:4},children:"Last Names"}),e.jsxs(h,{variant:"body2",color:"textSecondary",style:{display:"flex"},children:[e.jsx(S,{icon:"material-symbols:add",width:"20",height:"20",style:{color:"#1a84de"}})," Filter by last names"]})]})}),n&&e.jsxs("div",{style:{padding:"16px"},onClick:s=>s.stopPropagation(),children:[e.jsx("div",{style:{marginTop:8,display:"flex",flexWrap:"wrap",gap:8},children:r.map((s,d)=>e.jsx(O,{label:s,onDelete:()=>o(d)},d))}),e.jsx(b,{mt:2,display:"flex",gap:1,children:e.jsx(_,{fullWidth:!0,variant:"outlined",placeholder:"Enter a last name...",size:"small",value:c,onChange:s=>{p(s.target.value)},onKeyPress:s=>{s.key==="Enter"&&(s.preventDefault(),i())}})})]})]})})}const Gn=C(z)`
  padding: 16px;
  cursor: pointer;
  border: 1px solid #ced0da;
  border-radius: 4px;
  margin-bottom: 16px;

  &:hover {
    border-color: #1a84de;
  }
`;function Xn({onCodeChange:t}){const[n,l]=u.useState(!1),[r,a]=u.useState([]),[c,p]=u.useState(""),i=()=>{if(c.trim()){const s=[...r,c.trim()];a(s),p(""),t==null||t(s)}},o=s=>{const d=r.filter((m,x)=>x!==s);a(d),t==null||t(d)};return e.jsx("div",{children:e.jsxs(Gn,{style:{height:n?"auto":"85px"},onClick:()=>l(!n),children:[e.jsx("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:e.jsxs("div",{children:[e.jsx(h,{variant:"h6",style:{fontWeight:500,marginBottom:4},children:"Postal Code/ Zip Code"}),e.jsxs(h,{variant:"body2",color:"textSecondary",style:{display:"flex"},children:[e.jsx(S,{icon:"material-symbols:add",width:"20",height:"20",style:{color:"#1a84de"}})," Filter by postal code/zip code"]})]})}),n&&e.jsxs("div",{style:{padding:"16px"},onClick:s=>s.stopPropagation(),children:[e.jsx("div",{style:{marginTop:8,display:"flex",flexWrap:"wrap",gap:8},children:r.map((s,d)=>e.jsx(O,{label:s,onDelete:()=>o(d)},d))}),e.jsx(b,{mt:2,display:"flex",gap:1,children:e.jsx(_,{fullWidth:!0,variant:"outlined",placeholder:"Enter a postcal code...",size:"small",value:c,onChange:s=>{p(s.target.value)},onKeyPress:s=>{s.key==="Enter"&&(s.preventDefault(),i())}})})]})]})})}const Jn=C(z)`
  padding: 16px;
  cursor: pointer;
  border: 1px solid #ced0da;
  border-radius: 4px;
  margin-bottom: 16px;

  &:hover {
    border-color: #1a84de;
  }
`,Qn=[{id:"english",label:"English"},{id:"german",label:"German"},{id:"spanish",label:"Spanish"},{id:"french",label:"French"},{id:"portuguese",label:"Portuguese"},{id:"italian",label:"Italian"},{id:"dutch",label:"Dutch"},{id:"chinese",label:"Chinese"},{id:"russian",label:"Russian"},{id:"turkish",label:"Turkish"},{id:"polish",label:"Polish"},{id:"czech",label:"Czech"},{id:"japanese",label:"Japanese"},{id:"romanian",label:"Romanian"},{id:"norwegian",label:"Norwegian"},{id:"thai",label:"Thai"},{id:"indonesian",label:"Bahasa Indonesia"},{id:"malay",label:"Malay"},{id:"swedish",label:"Swedish"},{id:"danish",label:"Danish"},{id:"korean",label:"Korean"}],Zn=C.div`
    padding: 8px 16px;
    cursor: pointer;
    text-decoration: underline;
    color: #1a84de;
    &:hover {
        background-color: rgba(25, 118, 210, 0.08);
    }
`;function ei({onChange:t}){const[n,l]=u.useState(!1),[r,a]=u.useState([]),c=i=>{if(r.some(s=>s.language===i.label))return;const o=[...r,{language:i.label,proficiency:"Any"}];a(o),t==null||t(o)},p=i=>{const o=r.filter((s,d)=>d!==i);a(o),t==null||t(o)};return e.jsx("div",{children:e.jsxs(Jn,{onClick:()=>l(!n),style:{height:n?"auto":"85px"},children:[e.jsx("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:e.jsxs("div",{children:[e.jsx(h,{variant:"h6",style:{fontWeight:500,marginBottom:4},children:"Profile Languages"}),e.jsxs(h,{variant:"body2",color:"textSecondary",style:{display:"flex"},children:[e.jsx(S,{icon:"material-symbols:add",width:"20",height:"20",style:{color:"#1a84de"}})," Languages used on LinkedIn"]})]})}),"                ",n&&e.jsxs("div",{style:{padding:"16px"},onClick:i=>i.stopPropagation(),children:[e.jsx("div",{style:{marginTop:8,display:"flex",flexWrap:"wrap",gap:8},children:r.map((i,o)=>e.jsx(O,{label:i.language,onDelete:()=>p(o)},o))}),e.jsx(b,{children:Qn.map(i=>e.jsx(Zn,{onClick:()=>c(i),children:i.label},i.id))})]})]})})}const ti=C(z)`
  padding: 16px;
  cursor: pointer;
  border: 1px solid #ced0da;
  border-radius: 4px;
  margin-bottom: 16px;

  &:hover {
    border-color: #1a84de;
  }
`,si=[{id:"1",label:"1st Connections"},{id:"2",label:"2nd Connections"},{id:"group",label:"Group Members"},{id:"3rd",label:"3rd + Everyone else"}],ni=C.div`
    padding: 8px 16px;
    cursor: pointer;
    text-decoration: underline;
    color: #1a84de;
    &:hover {
        background-color: rgba(25, 118, 210, 0.08);
    }
`;function ii({onChange:t}){const[n,l]=u.useState(!1),[r,a]=u.useState([]),c=i=>{if(r.some(s=>s.language===i.label))return;const o=[...r,{language:i.label,proficiency:"Any"}];a(o),t==null||t(o)},p=i=>{const o=r.filter((s,d)=>d!==i);a(o),t==null||t(o)};return e.jsx("div",{children:e.jsxs(ti,{onClick:()=>l(!n),style:{height:n?"auto":"85px"},children:[e.jsx("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:e.jsxs("div",{children:[e.jsx(h,{variant:"h6",style:{fontWeight:500,marginBottom:4},children:"Network Relationships"}),e.jsxs(h,{variant:"body2",color:"textSecondary",style:{display:"flex"},children:[e.jsx(S,{icon:"material-symbols:add",width:"20",height:"20",style:{color:"#1a84de"}})," How closely you are connected"]})]})}),"                ",n&&e.jsxs("div",{style:{padding:"16px"},onClick:i=>i.stopPropagation(),children:[e.jsx("div",{style:{marginTop:8,display:"flex",flexWrap:"wrap",gap:8},children:r.map((i,o)=>e.jsx(O,{label:i.language,onDelete:()=>p(o)},o))}),e.jsx(b,{children:si.map(i=>e.jsx(ni,{onClick:()=>c(i),children:i.label},i.id))})]})]})})}const ri=C(z)`
  padding: 16px;
  cursor: pointer;
  border: 1px solid #ced0da;
  border-radius: 4px;
  margin-bottom: 16px;

  &:hover {
    border-color: #1a84de;
  }
`,li=[{id:"1",label:"1 day ago"},{id:"2-7",label:"2-7 days ago"},{id:"8-14",label:"8-14 days ago"},{id:"15-30",label:"15-30 days ago"},{id:"1-3",label:"1-3 months ago"}],oi=C.div`
    padding: 8px 16px;
    cursor: pointer;

    color: #1a84de;
    &:hover {
        background-color: rgba(25, 118, 210, 0.08);
    }
`;function ai({onChange:t}){const[n,l]=u.useState(!1),[r,a]=u.useState([]),c=i=>{if(r.some(s=>s.language===i.label))return;const o=[...r,{language:i.label,proficiency:"Any"}];a(o),t==null||t(o)},p=i=>{const o=r.filter((s,d)=>d!==i);a(o),t==null||t(o)};return e.jsx("div",{children:e.jsxs(ri,{onClick:()=>l(!n),style:{height:n?"auto":"85px"},children:[e.jsx("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:e.jsxs("div",{children:[e.jsx(h,{variant:"h6",style:{fontWeight:500,marginBottom:4},children:"Recently Joined LinkedIn"}),e.jsxs(h,{variant:"body2",color:"textSecondary",style:{display:"flex"},children:[e.jsx(S,{icon:"material-symbols:add",width:"20",height:"20",style:{color:"#1a84de"}})," People who recently joined"]})]})}),n&&e.jsxs("div",{style:{padding:"16px"},onClick:i=>i.stopPropagation(),children:[e.jsx("div",{style:{marginTop:8,display:"flex",flexWrap:"wrap",gap:8},children:r.map((i,o)=>e.jsx(O,{label:i.language,onDelete:()=>p(o)},o))}),e.jsx(b,{children:li.map(i=>e.jsx(oi,{onClick:()=>c(i),children:i.label},i.id))})]})]})})}const di=C(z)`
  padding: 16px;
  cursor: pointer;
  border: 1px solid #ced0da;
  border-radius: 4px;
  margin-bottom: 16px;

  &:hover {
    border-color: #1a84de;
  }
`;function ci({onChange:t}){const[n,l]=u.useState(!1),[r,a]=u.useState([0,30]),c=(s,d)=>{a(d)},p=s=>d=>{let m=d.target.value===""?0:Number(d.target.value);s===0?m=Math.max(0,Math.min(m,r[1])):m=Math.max(r[0],Math.min(m,30));const x=[...r];x[s]=m,x[0]<=x[1]&&a(x)},i=()=>{a([0,30])},o=()=>{t&&t(r)};return e.jsx("div",{children:e.jsxs(di,{onClick:()=>l(!n),style:{height:n?"auto":"85px"},children:[e.jsx("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:e.jsxs("div",{children:[e.jsx(h,{variant:"h6",style:{fontWeight:500,marginBottom:4},children:"Years of experience"}),e.jsxs(h,{variant:"body2",color:"textSecondary",style:{display:"flex"},children:[e.jsx(S,{icon:"material-symbols:add",width:"20",height:"20",style:{color:"#1a84de"}})," Total years of work experience"]})]})}),"                ",n&&e.jsxs(b,{sx:{mt:3,px:2},onClick:s=>s.stopPropagation(),children:[e.jsxs(b,{sx:{display:"flex",gap:2,mb:3,alignItems:"center"},children:[e.jsx(h,{children:"From"}),"                            ",e.jsx(_,{value:r[0],onChange:p(0),type:"number",size:"small",sx:{width:100},inputProps:{min:0,max:r[1],inputMode:"numeric",pattern:"[0-9]*"},onKeyPress:s=>{(s.key==="-"||s.key==="+"||s.key==="e"||s.key==="E")&&s.preventDefault()}}),e.jsx(h,{children:"to"}),e.jsx(_,{value:r[1],onChange:p(1),type:"number",size:"small",sx:{width:100},inputProps:{min:r[0],max:30,inputMode:"numeric",pattern:"[0-9]*"},onKeyPress:s=>{(s.key==="-"||s.key==="+"||s.key==="e"||s.key==="E")&&s.preventDefault()}}),e.jsx(h,{children:"years"})]}),"                        ",e.jsxs(b,{sx:{px:1},children:[e.jsx(nt,{value:r,onChange:c,min:0,max:30,step:1,sx:{"& .MuiSlider-thumb":{backgroundColor:"#fff",border:"2px solid #1a84de"},"& .MuiSlider-track":{backgroundColor:"#1a84de"},"& .MuiSlider-rail":{backgroundColor:"#ced0da"}}}),e.jsxs(b,{sx:{display:"flex",justifyContent:"space-between",mt:1,px:1,color:"text.secondary",fontSize:"0.875rem"},children:[e.jsx(h,{variant:"body2",children:"Less than 1"}),e.jsx(h,{variant:"body2",children:"30+"})]})]}),e.jsxs(b,{sx:{display:"flex",justifyContent:"flex-end",gap:2,mt:2},children:[e.jsx(Le,{variant:"outlined",onClick:s=>{s.stopPropagation(),i()},children:"Reset"}),e.jsx(Le,{variant:"contained",onClick:s=>{s.stopPropagation(),o()},sx:{bgcolor:"#1a84de"},children:"Update"})]})]})]})})}const pi=C(z)`
  padding: 16px;
  cursor: pointer;
  border: 1px solid #ced0da;
  border-radius: 4px;
  margin-bottom: 16px;

  &:hover {
    border-color: #1a84de;
  }
`;function ui({onChange:t}){const[n,l]=u.useState(!1),[r,a]=u.useState([0,30]),c=(s,d)=>{a(d)},p=s=>d=>{let m=d.target.value===""?0:Number(d.target.value);s===0?m=Math.max(0,Math.min(m,r[1])):m=Math.max(r[0],Math.min(m,30));const x=[...r];x[s]=m,x[0]<=x[1]&&a(x)},i=()=>{a([0,30])},o=()=>{t&&t(r)};return e.jsx("div",{children:e.jsxs(pi,{onClick:()=>l(!n),style:{height:n?"auto":"85px"},children:[e.jsx("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:e.jsxs("div",{children:[e.jsx(h,{variant:"h6",style:{fontWeight:500,marginBottom:4},children:"Years in current company"}),e.jsxs(h,{variant:"body2",color:"textSecondary",style:{display:"flex"},children:[e.jsx(S,{icon:"material-symbols:add",width:"20",height:"20",style:{color:"#1a84de"}})," Years working in their current company"]})]})}),"                ",n&&e.jsxs(b,{sx:{mt:3,px:2},onClick:s=>s.stopPropagation(),children:[e.jsxs(b,{sx:{display:"flex",gap:2,mb:3,alignItems:"center"},children:[e.jsx(h,{children:"From"}),"                            ",e.jsx(_,{value:r[0],onChange:p(0),type:"number",size:"small",sx:{width:100},inputProps:{min:0,max:r[1],inputMode:"numeric",pattern:"[0-9]*"},onKeyPress:s=>{(s.key==="-"||s.key==="+"||s.key==="e"||s.key==="E")&&s.preventDefault()}}),e.jsx(h,{children:"to"}),e.jsx(_,{value:r[1],onChange:p(1),type:"number",size:"small",sx:{width:100},inputProps:{min:r[0],max:30,inputMode:"numeric",pattern:"[0-9]*"},onKeyPress:s=>{(s.key==="-"||s.key==="+"||s.key==="e"||s.key==="E")&&s.preventDefault()}}),e.jsx(h,{children:"years"})]}),"                        ",e.jsxs(b,{sx:{px:1},children:[e.jsx(nt,{value:r,onChange:c,min:0,max:30,step:1,sx:{"& .MuiSlider-thumb":{backgroundColor:"#fff",border:"2px solid #1a84de"},"& .MuiSlider-track":{backgroundColor:"#1a84de"},"& .MuiSlider-rail":{backgroundColor:"#ced0da"}}}),e.jsxs(b,{sx:{display:"flex",justifyContent:"space-between",mt:1,px:1,color:"text.secondary",fontSize:"0.875rem"},children:[e.jsx(h,{variant:"body2",children:"Less than 1"}),e.jsx(h,{variant:"body2",children:"30+"})]})]}),e.jsxs(b,{sx:{display:"flex",justifyContent:"flex-end",gap:2,mt:2},children:[e.jsx(Le,{variant:"outlined",onClick:s=>{s.stopPropagation(),i()},children:"Reset"}),e.jsx(Le,{variant:"contained",onClick:s=>{s.stopPropagation(),o()},sx:{bgcolor:"#1a84de"},children:"Update"})]})]})]})})}const xi=C(z)`
  padding: 16px;
  cursor: pointer;
  border: 1px solid #ced0da;
  border-radius: 4px;
  margin-bottom: 16px;

  &:hover {
    border-color: #1a84de;
  }
`;function hi({onSchoolsChange:t}){const[n,l]=u.useState(!1),[r,a]=u.useState([]),[c,p]=u.useState(""),i=()=>{if(c.trim()){const s=[...r,c.trim()];a(s),p(""),t==null||t(s)}},o=s=>{const d=r.filter((m,x)=>x!==s);a(d),t==null||t(d)};return e.jsx("div",{children:e.jsxs(xi,{onClick:()=>l(!n),style:{height:n?"auto":"85px"},children:[e.jsx("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:e.jsxs("div",{children:["                        ",e.jsx(h,{variant:"h6",style:{fontWeight:500,marginBottom:4},children:"Schools"}),e.jsxs(h,{variant:"body2",color:"textSecondary",style:{display:"flex"},children:[e.jsx(S,{icon:"material-symbols:add",width:"20",height:"20",style:{color:"#1a84de"}})," Schools attended"]})]})}),n&&e.jsxs("div",{style:{padding:"16px"},onClick:s=>s.stopPropagation(),children:[e.jsxs("div",{style:{marginTop:8,display:"flex",flexWrap:"wrap",gap:8},children:["                            ",r.map((s,d)=>e.jsx(O,{label:s,onDelete:()=>o(d)},d))]}),e.jsx(b,{mt:2,display:"flex",gap:1,children:e.jsx(_,{fullWidth:!0,variant:"outlined",placeholder:"Enter a school name...",size:"small",value:c,onChange:s=>{p(s.target.value)},onKeyPress:s=>{s.key==="Enter"&&(s.preventDefault(),i())}})})]})]})})}const mi=C(z)`
  padding: 16px;
  cursor: pointer;
  border: 1px solid #ced0da;
  border-radius: 4px;
  margin-bottom: 16px;

  &:hover {
    border-color: #1a84de;
  }
`;function yi({onChange:t}){const[n,l]=u.useState(!1),[r,a]=u.useState([0,30]),c=(s,d)=>{a(d)},p=s=>d=>{let m=d.target.value===""?0:Number(d.target.value);s===0?m=Math.max(0,Math.min(m,r[1])):m=Math.max(r[0],Math.min(m,30));const x=[...r];x[s]=m,x[0]<=x[1]&&a(x)},i=()=>{a([0,30])},o=()=>{t&&t(r)};return e.jsx("div",{children:e.jsxs(mi,{onClick:()=>l(!n),style:{height:n?"auto":"85px"},children:[e.jsx("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:e.jsxs("div",{children:[e.jsx(h,{variant:"h6",style:{fontWeight:500,marginBottom:4},children:"Years in current position"}),e.jsxs(h,{variant:"body2",color:"textSecondary",style:{display:"flex"},children:[e.jsx(S,{icon:"material-symbols:add",width:"20",height:"20",style:{color:"#1a84de"}})," Years working in their current position"]})]})}),"                ",n&&e.jsxs(b,{sx:{mt:3,px:2},onClick:s=>s.stopPropagation(),children:[e.jsxs(b,{sx:{display:"flex",gap:2,mb:3,alignItems:"center"},children:[e.jsx(h,{children:"From"}),"                            ",e.jsx(_,{value:r[0],onChange:p(0),type:"number",size:"small",sx:{width:100},inputProps:{min:0,max:r[1],inputMode:"numeric",pattern:"[0-9]*"},onKeyPress:s=>{(s.key==="-"||s.key==="+"||s.key==="e"||s.key==="E")&&s.preventDefault()}}),e.jsx(h,{children:"to"}),e.jsx(_,{value:r[1],onChange:p(1),type:"number",size:"small",sx:{width:100},inputProps:{min:r[0],max:30,inputMode:"numeric",pattern:"[0-9]*"},onKeyPress:s=>{(s.key==="-"||s.key==="+"||s.key==="e"||s.key==="E")&&s.preventDefault()}}),e.jsx(h,{children:"years"})]}),"                        ",e.jsxs(b,{sx:{px:1},children:[e.jsx(nt,{value:r,onChange:c,min:0,max:30,step:1,sx:{"& .MuiSlider-thumb":{backgroundColor:"#fff",border:"2px solid #1a84de"},"& .MuiSlider-track":{backgroundColor:"#1a84de"},"& .MuiSlider-rail":{backgroundColor:"#ced0da"}}}),e.jsxs(b,{sx:{display:"flex",justifyContent:"space-between",mt:1,px:1,color:"text.secondary",fontSize:"0.875rem"},children:[e.jsx(h,{variant:"body2",children:"Less than 1"}),e.jsx(h,{variant:"body2",children:"30+"})]})]}),e.jsxs(b,{sx:{display:"flex",justifyContent:"flex-end",gap:2,mt:2},children:[e.jsx(Le,{variant:"outlined",onClick:s=>{s.stopPropagation(),i()},children:"Reset"}),e.jsx(Le,{variant:"contained",onClick:s=>{s.stopPropagation(),o()},sx:{bgcolor:"#1a84de"},children:"Update"})]})]})]})})}const gi=C(z)`
  padding: 16px;
  cursor: pointer;
  border: 1px solid #ced0da;
  border-radius: 4px;
  margin-bottom: 16px;

  &:hover {
    border-color: #1a84de;
  }
`;function fi({onFieldsChange:t}){const[n,l]=u.useState(!1),[r,a]=u.useState([]),[c,p]=u.useState(""),i=()=>{if(c.trim()){const s=[...r,c.trim()];a(s),p(""),t==null||t(s)}},o=s=>{const d=r.filter((m,x)=>x!==s);a(d),t==null||t(d)};return e.jsx("div",{children:e.jsxs(gi,{onClick:()=>l(!n),style:{height:n?"auto":"85px"},children:[e.jsx("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:e.jsxs("div",{children:["                    ",e.jsx(h,{variant:"h6",style:{fontWeight:500,marginBottom:4},children:"Fields of Study"}),e.jsxs(h,{variant:"body2",color:"textSecondary",style:{display:"flex"},children:[e.jsx(S,{icon:"material-symbols:add",width:"20",height:"20",style:{color:"#1a84de"}})," Fields of study added"]})]})}),n&&e.jsxs("div",{style:{padding:"16px"},onClick:s=>s.stopPropagation(),children:["                        ",e.jsxs("div",{style:{marginTop:8,display:"flex",flexWrap:"wrap",gap:8},children:["                            ",r.map((s,d)=>e.jsx(O,{label:s,onDelete:()=>o(d)},d))]}),e.jsx(b,{mt:2,display:"flex",gap:1,children:e.jsx(_,{fullWidth:!0,variant:"outlined",placeholder:"Enter a field of study...",size:"small",value:c,onChange:s=>{p(s.target.value)},onKeyPress:s=>{s.key==="Enter"&&(s.preventDefault(),i())}})})]})]})})}const bi=C(z)`
  padding: 16px;
  cursor: pointer;
  border: 1px solid #ced0da;
  border-radius: 4px;
  margin-bottom: 16px;

  &:hover {
    border-color: #1a84de;
  }
`,vi=[{id:"full-time",label:"Full-time"},{id:"part-time",label:"Part-time"},{id:"contract",label:"Contract"},{id:"internship",label:"Internship"}],ji=C.div`
    padding: 8px 16px;
    cursor: pointer;
    text-decoration: underline;
    color: #1a84de;
    &:hover {
        background-color: rgba(25, 118, 210, 0.08);
    }
`;function Si({onChange:t}){const[n,l]=u.useState(!1),[r,a]=u.useState([]),c=i=>{if(r.some(s=>s.type===i.label))return;const o=[...r,{type:i.label}];a(o),t==null||t(o)},p=i=>{const o=r.filter((s,d)=>d!==i);a(o),t==null||t(o)};return e.jsx("div",{children:e.jsxs(bi,{onClick:()=>l(!n),style:{height:n?"auto":"85px"},children:[e.jsx("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:e.jsxs("div",{children:[e.jsx(h,{variant:"h6",style:{fontWeight:500,marginBottom:4},children:"Employment type"}),e.jsxs(h,{variant:"body2",color:"textSecondary",style:{display:"flex"},children:[e.jsx(S,{icon:"material-symbols:add",width:"20",height:"20",style:{color:"#1a84de"}})," May be open to these types of employment"]})]})}),"                ",n&&e.jsxs("div",{style:{padding:"16px"},onClick:i=>i.stopPropagation(),children:["                        ",e.jsx("div",{style:{marginTop:8,display:"flex",flexWrap:"wrap",gap:8},children:r.map((i,o)=>e.jsx(O,{label:i.type,onDelete:()=>p(o)},o))}),e.jsx(b,{children:vi.map(i=>e.jsx(ji,{onClick:()=>c(i),children:i.label},i.id))})]})]})})}const ki=C(z)`
  padding: 16px;
  cursor: pointer;
  border: 1px solid #ced0da;
  border-radius: 4px;
  margin-bottom: 16px;

  &:hover {
    border-color: #1a84de;
  }
`,wi=[{id:"bachelors",label:"Bachelor's Degree"},{id:"masters",label:"Master's Degree"},{id:"bachelor_of_science",label:"Bachelor of Science"},{id:"phd",label:"Ph.D."},{id:"associate",label:"Associate Degree"}],Ci=C.div`
    padding: 8px 16px;
    cursor: pointer;
    text-decoration: underline;
    color: #1a84de;
    &:hover {
        background-color: rgba(25, 118, 210, 0.08);
    }
`;function Ii({onChange:t}){const[n,l]=u.useState(!1),[r,a]=u.useState([]),[c,p]=u.useState(""),i=d=>{if(r.some(x=>x.type===d.label))return;const m=[...r,{type:d.label}];a(m),t==null||t(m)},o=d=>{const m=r.filter((x,v)=>v!==d);a(m),t==null||t(m)},s=d=>{d.key==="Enter"&&c.trim()&&(i({label:c.trim()}),p(""))};return e.jsx("div",{children:e.jsxs(ki,{onClick:()=>l(!n),style:{height:n?"auto":"85px"},children:[e.jsx("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:e.jsxs("div",{children:[e.jsx(h,{variant:"h6",style:{fontWeight:500,marginBottom:4},children:"Degree"}),e.jsxs(h,{variant:"body2",color:"textSecondary",style:{display:"flex"},children:[e.jsx(S,{icon:"material-symbols:add",width:"20",height:"20",style:{color:"#1a84de"}})," Types of degrees"]})]})}),n&&e.jsxs("div",{style:{padding:"16px"},onClick:d=>d.stopPropagation(),children:[e.jsx("div",{style:{marginTop:8,display:"flex",flexWrap:"wrap",gap:8},children:r.map((d,m)=>e.jsx(O,{label:d.type,onDelete:()=>o(m)},m))}),e.jsx(_,{fullWidth:!0,size:"small",value:c,onChange:d=>p(d.target.value),onKeyPress:s,placeholder:"Enter a degree",style:{marginBottom:"5px",marginTop:"5px"}}),e.jsx(b,{children:wi.map(d=>e.jsx(Ci,{onClick:()=>i(d),children:d.label},d.id))})]})]})})}const Pi=C(z)`
  padding: 16px;
  cursor: pointer;
  border: 1px solid #ced0da;
  border-radius: 4px;
  margin-bottom: 16px;

  &:hover {
    border-color: #1a84de;
  }
`,Li=[{id:"full-time",label:"Full-time"},{id:"part-time",label:"Part-time"},{id:"contract",label:"Contract"},{id:"internship",label:"Internship"}],$i=C.div`
    padding: 8px 16px;
    cursor: pointer;
    text-decoration: underline;
    color: #1a84de;
    &:hover {
        background-color: rgba(25, 118, 210, 0.08);
    }
`;function Ei({onChange:t}){const[n,l]=u.useState(!1),[r,a]=u.useState([]),c=i=>{if(r.some(s=>s.type===i.label))return;const o=[...r,{type:i.label}];a(o),t==null||t(o)},p=i=>{const o=r.filter((s,d)=>d!==i);a(o),t==null||t(o)};return e.jsx("div",{children:e.jsxs(Pi,{onClick:()=>l(!n),style:{height:n?"auto":"85px"},children:[e.jsx("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:e.jsxs("div",{children:["                        ",e.jsx(h,{variant:"h6",style:{fontWeight:500,marginBottom:4},children:"Workplace types"}),e.jsxs(h,{variant:"body2",color:"textSecondary",style:{display:"flex"},children:[e.jsx(S,{icon:"material-symbols:add",width:"20",height:"20",style:{color:"#1a84de"}})," Draws from open to work prefrence"]})]})}),"                ",n&&e.jsxs("div",{style:{padding:"16px"},onClick:i=>i.stopPropagation(),children:["                        ",e.jsx("div",{style:{marginTop:8,display:"flex",flexWrap:"wrap",gap:8},children:r.map((i,o)=>e.jsx(O,{label:i.type,onDelete:()=>p(o)},o))}),e.jsxs(b,{children:["                            ",Li.map(i=>e.jsx($i,{onClick:()=>c(i),children:i.label},i.id))]})]})]})})}const Ti=C(z)`
  padding: 16px;
  cursor: pointer;
  border: 1px solid #ced0da;
  border-radius: 4px;
  margin-bottom: 16px;

  &:hover {
    border-color: #1a84de;
  }
`;function zi({onChange:t}){const[n,l]=u.useState(!1),[r,a]=u.useState([1975,2035]),c=(s,d)=>{a(d)},p=s=>d=>{let m=d.target.value===""?1975:Number(d.target.value);s===0?m=Math.max(1975,Math.min(m,r[1])):m=Math.max(r[0],Math.min(m,2035));const x=[...r];x[s]=m,x[0]<=x[1]&&a(x)},i=()=>{a([1975,2035])},o=()=>{t&&t(r)};return e.jsx("div",{children:e.jsxs(Ti,{onClick:()=>l(!n),style:{height:n?"auto":"85px"},children:[e.jsx("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:e.jsxs("div",{children:["                        ",e.jsx(h,{variant:"h6",style:{fontWeight:500,marginBottom:4},children:"Year of graduation"}),e.jsxs(h,{variant:"body2",color:"textSecondary",style:{display:"flex"},children:[e.jsx(S,{icon:"material-symbols:add",width:"20",height:"20",style:{color:"#1a84de"}})," Add graduation year range"]})]})}),"                ",n&&e.jsxs(b,{sx:{mt:3,px:2},onClick:s=>s.stopPropagation(),children:[e.jsxs(b,{sx:{display:"flex",gap:2,mb:3,alignItems:"center"},children:[e.jsx(h,{children:"From"}),"                            ",e.jsx(_,{value:r[0],onChange:p(0),type:"number",size:"small",sx:{width:100},inputProps:{min:1975,max:r[1],inputMode:"numeric",pattern:"[0-9]*"},onKeyPress:s=>{(s.key==="-"||s.key==="+"||s.key==="e"||s.key==="E")&&s.preventDefault()}}),e.jsx(h,{children:"to"}),e.jsx(_,{value:r[1],onChange:p(1),type:"number",size:"small",sx:{width:100},inputProps:{min:r[0],max:2035,inputMode:"numeric",pattern:"[0-9]*"},onKeyPress:s=>{(s.key==="-"||s.key==="+"||s.key==="e"||s.key==="E")&&s.preventDefault()}}),"                            ",e.jsx(h,{children:"year"})]}),"                        ",e.jsxs(b,{sx:{px:1},children:[e.jsx(nt,{value:r,onChange:c,min:1975,max:2035,step:1,sx:{"& .MuiSlider-thumb":{backgroundColor:"#fff",border:"2px solid #1a84de"},"& .MuiSlider-track":{backgroundColor:"#1a84de"},"& .MuiSlider-rail":{backgroundColor:"#ced0da"}}}),e.jsxs(b,{sx:{display:"flex",justifyContent:"space-between",mt:1,px:1,color:"text.secondary",fontSize:"0.875rem"},children:["                                ",e.jsx(h,{variant:"body2",children:"1975"}),e.jsx(h,{variant:"body2",children:"2035"})]})]}),e.jsxs(b,{sx:{display:"flex",justifyContent:"flex-end",gap:2,mt:2},children:[e.jsx(Le,{variant:"outlined",onClick:s=>{s.stopPropagation(),i()},children:"Reset"}),e.jsx(Le,{variant:"contained",onClick:s=>{s.stopPropagation(),o()},sx:{bgcolor:"#1a84de"},children:"Update"})]})]})]})})}const Ai=C(z)`
  padding: 16px;
  cursor: pointer;
  border: 1px solid #ced0da;
  border-radius: 4px;
  margin-bottom: 16px;

  &:hover {
    border-color: #1a84de;
  }
`;function Di({onFieldsChange:t}){const[n,l]=u.useState(!1),[r,a]=u.useState([]),[c,p]=u.useState(""),i=()=>{if(c.trim()){const s=[...r,c.trim()];a(s),p(""),t==null||t(s)}},o=s=>{const d=r.filter((m,x)=>x!==s);a(d),t==null||t(d)};return e.jsx("div",{children:e.jsxs(Ai,{onClick:()=>l(!n),style:{height:n?"auto":"85px"},children:[e.jsx("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:e.jsxs("div",{children:["                    ",e.jsx(h,{variant:"h6",style:{fontWeight:500,marginBottom:4},children:"Military Veterans"}),e.jsxs(h,{variant:"body2",color:"textSecondary",style:{display:"flex"},children:[e.jsx(S,{icon:"material-symbols:add",width:"20",height:"20",style:{color:"#1a84de"}})," People with US military background"]})]})}),n&&e.jsxs("div",{style:{padding:"16px"},onClick:s=>s.stopPropagation(),children:["                        ",e.jsxs("div",{style:{marginTop:8,display:"flex",flexWrap:"wrap",gap:8},children:["                            ",r.map((s,d)=>e.jsx(O,{label:s,onDelete:()=>o(d)},d))]}),e.jsx(b,{mt:2,display:"flex",gap:1,children:e.jsx(_,{fullWidth:!0,variant:"outlined",placeholder:"",size:"small",value:c,onChange:s=>{p(s.target.value)},onKeyPress:s=>{s.key==="Enter"&&(s.preventDefault(),i())}})})]})]})})}const Fi=C(z)`
  padding: 16px;
  cursor: pointer;
  border: 1px solid #ced0da;
  border-radius: 4px;
  margin-bottom: 16px;

  &:hover {
    border-color: #1a84de;
  }
`,Mi=[{id:"entry",label:"Entry"},{id:"senior",label:"Senior"},{id:"manager",label:"Manager"},{id:"director",label:"Director"},{id:"owner",label:"Owner"},{id:"vp",label:"VP"},{id:"cxo",label:"CXO"},{id:"training",label:"Training"},{id:"unpaid",label:"Unpaid"},{id:"partner",label:"Partner"}],Ri=C.div`
    padding: 8px 16px;
    cursor: pointer;
    text-decoration: underline;
    color: #1a84de;
    &:hover {
        background-color: rgba(25, 118, 210, 0.08);
    }
`;function Wi({onChange:t}){const[n,l]=u.useState(!1),[r,a]=u.useState([]),c=i=>{if(r.some(s=>s.type===i.label))return;const o=[...r,{type:i.label}];a(o),t==null||t(o)},p=i=>{const o=r.filter((s,d)=>d!==i);a(o),t==null||t(o)};return e.jsx("div",{children:e.jsxs(Fi,{onClick:()=>l(!n),style:{height:n?"auto":"85px"},children:[e.jsx("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:e.jsxs("div",{children:["                        ",e.jsx(h,{variant:"h6",style:{fontWeight:500,marginBottom:4},children:"Seniority Level"}),e.jsxs(h,{variant:"body2",color:"textSecondary",style:{display:"flex"},children:[e.jsx(S,{icon:"material-symbols:add",width:"20",height:"20",style:{color:"#1a84de"}})," Select preferred seniority levels"]})]})}),"                ",n&&e.jsxs("div",{style:{padding:"16px"},onClick:i=>i.stopPropagation(),children:["                        ",e.jsx("div",{style:{marginTop:8,display:"flex",flexWrap:"wrap",gap:8},children:r.map((i,o)=>e.jsx(O,{label:i.type,onDelete:()=>p(o)},o))}),e.jsxs(b,{children:["                            ",Mi.map(i=>e.jsx(Ri,{onClick:()=>c(i),children:i.label},i.id))]})]})]})})}const _i=C(z)`
  padding: 16px;
  cursor: pointer;
  border: 1px solid #ced0da;
  border-radius: 4px;
  margin-bottom: 16px;

  &:hover {
    border-color: #1a84de;
  }
`;function Bi({onNamesChange:t}){const[n,l]=u.useState(!1),[r,a]=u.useState([]),[c,p]=u.useState(""),i=()=>{if(c.trim()){const s=[...r,c.trim()];a(s),p(""),t==null||t(s)}},o=s=>{const d=r.filter((m,x)=>x!==s);a(d),t==null||t(d)};return e.jsx("div",{children:e.jsxs(_i,{onClick:()=>l(!n),style:{height:n?"auto":"85px"},children:[e.jsx("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:e.jsxs("div",{children:[e.jsx(h,{variant:"h6",style:{fontWeight:500,marginBottom:4},children:"Current companies"}),e.jsxs(h,{variant:"body2",color:"textSecondary",style:{display:"flex"},children:[e.jsx(S,{icon:"material-symbols:add",width:"20",height:"20",style:{color:"#1a84de"}})," Current employers"]})]})}),n&&e.jsxs("div",{style:{padding:"16px"},onClick:s=>s.stopPropagation(),children:[e.jsxs("div",{style:{marginTop:8,display:"flex",flexWrap:"wrap",gap:8},children:["                            ",r.map((s,d)=>e.jsx(O,{label:s,onDelete:()=>o(d)},d))]}),e.jsx(b,{mt:2,display:"flex",gap:1,children:e.jsx(_,{fullWidth:!0,variant:"outlined",placeholder:"Enter a company name...",size:"small",value:c,onChange:s=>{p(s.target.value)},onKeyPress:s=>{s.key==="Enter"&&(s.preventDefault(),i())}})})]})]})})}const Oi=C(z)`
  padding: 16px;
  cursor: pointer;
  border: 1px solid #ced0da;
  border-radius: 4px;
  margin-bottom: 16px;

  &:hover {
    border-color: #1a84de;
  }
`,Ni=[{id:"self",label:"Self-employed"},{id:"1-10",label:"1-10"},{id:"11-50",label:"11-50"},{id:"51-200",label:"51-200"},{id:"201-500",label:"201-500"},{id:"501-1000",label:"501-1000"},{id:"1001-5000",label:"1001-5000"},{id:"5001-10,000",label:"5001-10,000"},{id:"10,000+",label:"10,000+"}],Hi=C.div`
    padding: 8px 16px;
    cursor: pointer;
    text-decoration: underline;
    color: #1a84de;
    &:hover {
        background-color: rgba(25, 118, 210, 0.08);
    }
`;function Ki({onChange:t}){const[n,l]=u.useState(!1),[r,a]=u.useState([]),c=i=>{if(r.some(s=>s.type===i.label))return;const o=[...r,{type:i.label}];a(o),t==null||t(o)},p=i=>{const o=r.filter((s,d)=>d!==i);a(o),t==null||t(o)};return e.jsx("div",{children:e.jsxs(Oi,{onClick:()=>l(!n),style:{height:n?"auto":"85px"},children:[e.jsx("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:e.jsxs("div",{children:["                        ",e.jsx(h,{variant:"h6",style:{fontWeight:500,marginBottom:4},children:"Company Sizes"}),e.jsxs(h,{variant:"body2",color:"textSecondary",style:{display:"flex"},children:[e.jsx(S,{icon:"material-symbols:add",width:"20",height:"20",style:{color:"#1a84de"}})," Number of employees"]})]})}),"                ",n&&e.jsxs("div",{style:{padding:"16px"},onClick:i=>i.stopPropagation(),children:["                        ",e.jsx("div",{style:{marginTop:8,display:"flex",flexWrap:"wrap",gap:8},children:r.map((i,o)=>e.jsx(O,{label:i.type,onDelete:()=>p(o)},o))}),e.jsx(b,{children:Ni.map(i=>e.jsx(Hi,{onClick:()=>c(i),children:i.label},i.id))})]})]})})}const Vi=C(z)`
  padding: 16px;
  cursor: pointer;
  border: 1px solid #ced0da;
  border-radius: 4px;
  margin-bottom: 16px;

  &:hover {
    border-color: #1a84de;
  }
`;function Ui({onNamesChange:t}){const[n,l]=u.useState(!1),[r,a]=u.useState([]),[c,p]=u.useState(""),i=()=>{if(c.trim()){const s=[...r,c.trim()];a(s),p(""),t==null||t(s)}},o=s=>{const d=r.filter((m,x)=>x!==s);a(d),t==null||t(d)};return e.jsx("div",{children:e.jsxs(Vi,{onClick:()=>l(!n),style:{height:n?"auto":"85px"},children:[e.jsx("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:e.jsxs("div",{children:[e.jsx(h,{variant:"h6",style:{fontWeight:500,marginBottom:4},children:"Past companies"}),e.jsxs(h,{variant:"body2",color:"textSecondary",style:{display:"flex"},children:[e.jsx(S,{icon:"material-symbols:add",width:"20",height:"20",style:{color:"#1a84de"}})," Past employers"]})]})}),n&&e.jsxs("div",{style:{padding:"16px"},onClick:s=>s.stopPropagation(),children:[e.jsxs("div",{style:{marginTop:8,display:"flex",flexWrap:"wrap",gap:8},children:["                            ",r.map((s,d)=>e.jsx(O,{label:s,onDelete:()=>o(d)},d))]}),e.jsx(b,{mt:2,display:"flex",gap:1,children:e.jsx(_,{fullWidth:!0,variant:"outlined",placeholder:"Enter a company name...",size:"small",value:c,onChange:s=>{p(s.target.value)},onKeyPress:s=>{s.key==="Enter"&&(s.preventDefault(),i())}})})]})]})})}const qi=C(z)`
  padding: 16px;
  cursor: pointer;
  border: 1px solid #ced0da;
  border-radius: 4px;
  margin-bottom: 16px;

  &:hover {
    border-color: #1a84de;
  }
`;function Yi({onNamesChange:t}){const[n,l]=u.useState(!1),[r,a]=u.useState([]),[c,p]=u.useState(""),i=()=>{if(c.trim()){const s=[...r,c.trim()];a(s),p(""),t==null||t(s)}},o=s=>{const d=r.filter((m,x)=>x!==s);a(d),t==null||t(d)};return e.jsx("div",{children:e.jsxs(qi,{onClick:()=>l(!n),style:{height:n?"auto":"85px"},children:[e.jsx("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:e.jsxs("div",{children:[e.jsx(h,{variant:"h6",style:{fontWeight:500,marginBottom:4},children:"Company Followers"}),e.jsxs(h,{variant:"body2",color:"textSecondary",style:{display:"flex"},children:[e.jsx(S,{icon:"material-symbols:add",width:"20",height:"20",style:{color:"#1a84de"}})," Filter by people who follow your company"]})]})}),n&&e.jsxs("div",{style:{padding:"16px"},onClick:s=>s.stopPropagation(),children:[e.jsx("div",{style:{marginTop:8,display:"flex",flexWrap:"wrap",gap:8},children:r.map((s,d)=>e.jsx(O,{label:s,onDelete:()=>o(d)},d))}),e.jsx(b,{mt:2,display:"flex",gap:1,children:e.jsx(_,{fullWidth:!0,variant:"outlined",placeholder:"Enter a company...",size:"small",value:c,onChange:s=>{p(s.target.value)},onKeyPress:s=>{s.key==="Enter"&&(s.preventDefault(),i())}})})]})]})})}const Gi=C(z)`
  padding: 16px;
  cursor: pointer;
  border: 1px solid #ced0da;
  border-radius: 4px;
  margin-bottom: 16px;

  &:hover {
    border-color: #1a84de;
  }
`,Xi=[{id:"private-held",label:"Privately Held"},{id:"public-company",label:"Public Company"},{id:"educational-institute",label:"Educational Institution"},{id:"non-profit",label:"Non Profit"},{id:"goverment-agency",label:"Goverment Agency"},{id:"partnership",label:"Partnership"},{id:"self-owned",label:"Self Owned"},{id:"self-employed",label:"Self Employed"},{id:"college-page",label:"College Page"}],Ji=C.div`
    padding: 8px 16px;
    cursor: pointer;
    color: #1a84de;
    &:hover {
        background-color: rgba(25, 118, 210, 0.08);
    }
`;function Qi({onChange:t}){const[n,l]=u.useState(!1),[r,a]=u.useState([]),c=i=>{if(r.some(s=>s.type===i.label))return;const o=[...r,{type:i.label}];a(o),t==null||t(o)},p=i=>{const o=r.filter((s,d)=>d!==i);a(o),t==null||t(o)};return e.jsx("div",{children:e.jsxs(Gi,{onClick:()=>l(!n),style:{height:n?"auto":"85px"},children:[e.jsx("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:e.jsxs("div",{children:[e.jsx(h,{variant:"h6",style:{fontWeight:500,marginBottom:4},children:"Company types"}),e.jsxs(h,{variant:"body2",color:"textSecondary",style:{display:"flex"},children:[e.jsx(S,{icon:"material-symbols:add",width:"20",height:"20",style:{color:"#1a84de"}})," Public ,private, nonprofit, etc"]})]})}),"                ",n&&e.jsxs("div",{style:{padding:"16px"},onClick:i=>i.stopPropagation(),children:["                        ",e.jsx("div",{style:{marginTop:8,display:"flex",flexWrap:"wrap",gap:8},children:r.map((i,o)=>e.jsx(O,{label:i.type,onDelete:()=>p(o)},o))}),e.jsxs(b,{children:["                            ",Xi.map(i=>e.jsx(Ji,{onClick:()=>c(i),children:i.label},i.id))]})]})]})})}const Zi=C(z)`
  padding: 16px;
  cursor: pointer;
  border: 1px solid #ced0da;
  border-radius: 4px;
  margin-bottom: 16px;

  &:hover {
    border-color: #1a84de;
  }
`,er=[{id:"accounting",label:"Accounting"},{id:"administration",label:"Administration"},{id:"business-development",label:"Business Development"},{id:"consulting",label:"Consulting"},{id:"customer-service",label:"Customer Service"},{id:"engineering",label:"Engineering"},{id:"finance",label:"Finance"},{id:"human-resources",label:"Human Resources"},{id:"information-technology",label:"Information Technology"},{id:"legal",label:"Legal"},{id:"marketing",label:"Marketing"},{id:"operations",label:"Operations"},{id:"product-management",label:"Product Management"},{id:"project-management",label:"Project Management"},{id:"research",label:"Research"},{id:"sales",label:"Sales"},{id:"strategy",label:"Strategy"}],tr=C.div`
    padding: 8px 16px;
    cursor: pointer;
    color: #1a84de;
    &:hover {
        background-color: rgba(25, 118, 210, 0.08);
    }
`;function sr({onChange:t}){const[n,l]=u.useState(!1),[r,a]=u.useState([]),c=i=>{if(r.some(s=>s.type===i.label))return;const o=[...r,{type:i.label}];a(o),t==null||t(o)},p=i=>{const o=r.filter((s,d)=>d!==i);a(o),t==null||t(o)};return e.jsx("div",{children:e.jsxs(Zi,{onClick:()=>l(!n),style:{height:n?"auto":"85px"},children:[e.jsx("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:e.jsxs("div",{children:[e.jsx(h,{variant:"h6",style:{fontWeight:500,marginBottom:4},children:"Job Functions"}),e.jsxs(h,{variant:"body2",color:"textSecondary",style:{display:"flex"},children:[e.jsx(S,{icon:"material-symbols:add",width:"20",height:"20",style:{color:"#1a84de"}})," Areas of expertise"]})]})}),"                ",n&&e.jsxs("div",{style:{padding:"16px"},onClick:i=>i.stopPropagation(),children:[e.jsx("div",{style:{marginTop:8,display:"flex",flexWrap:"wrap",gap:8},children:r.map((i,o)=>e.jsx(O,{label:i.type,onDelete:()=>p(o)},o))}),"                        ",e.jsxs(b,{children:["                            ",er.map(i=>e.jsx(tr,{onClick:()=>c(i),children:i.label},i.id))]})]})]})})}const nr=C(z)`
  padding: 16px;
  cursor: pointer;
  border: 1px solid #ced0da;
  border-radius: 4px;
  margin-bottom: 16px;

  &:hover {
    border-color: #1a84de;
  }
`;function ir({onChange:t}){const[n,l]=u.useState(!1),[r,a]=u.useState(""),[c,p]=u.useState(""),[i,o]=u.useState("");return e.jsx("div",{children:e.jsxs(nr,{onClick:()=>l(!n),style:{height:n?"auto":"85px"},children:[e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:["                    ",e.jsxs("div",{children:[e.jsx(h,{variant:"h6",style:{fontWeight:500,marginBottom:4},children:"Recruiting activity"}),e.jsxs(h,{variant:"body2",color:"textSecondary",style:{display:"flex"},children:[e.jsx(S,{icon:"material-symbols:add",width:"20",height:"20",style:{color:"#1a84de"}})," Filter recruiting activities"]})]})]}),n&&e.jsx("div",{style:{padding:"16px"},onClick:s=>s.stopPropagation(),children:e.jsxs(b,{mt:2,display:"flex",gap:1,children:["                            ",e.jsxs(vt,{value:r,onChange:s=>a(s.target.value),displayEmpty:!0,size:"small",sx:{width:"33%",marginTop:"5px",marginBottom:"10px",borderRadius:"2px",backgroundColor:"white","& .MuiOutlinedInput-notchedOutline":{borderColor:"#CED0DA"},"&:hover .MuiOutlinedInput-notchedOutline":{borderColor:"#CED0DA"},"&.Mui-focused .MuiOutlinedInput-notchedOutline":{borderColor:"#CED0DA"}},children:[e.jsx(Ee,{value:"",children:e.jsx("em",{children:"Choose recruiting"})}),["Messages","Notes","Tags","Projects","Resumes","Reviews"].map(s=>e.jsx(Ee,{value:s,children:s},s))]}),e.jsxs(vt,{value:c,onChange:s=>p(s.target.value),displayEmpty:!0,size:"small",sx:{width:"33%",marginTop:"5px",marginBottom:"10px",borderRadius:"2px",backgroundColor:"white","& .MuiOutlinedInput-notchedOutline":{borderColor:"#CED0DA"},"&:hover .MuiOutlinedInput-notchedOutline":{borderColor:"#CED0DA"},"&.Mui-focused .MuiOutlinedInput-notchedOutline":{borderColor:"#CED0DA"}},children:[e.jsx(Ee,{value:"",children:e.jsx("em",{children:"Choose condition"})}),["Can have","Must have","Doesn't have"].map(s=>e.jsx(Ee,{value:s,children:s},s))]}),e.jsxs(vt,{value:i,onChange:s=>o(s.target.value),displayEmpty:!0,size:"small",sx:{width:"33%",marginTop:"5px",marginBottom:"10px",borderRadius:"2px",backgroundColor:"white","& .MuiOutlinedInput-notchedOutline":{borderColor:"#CED0DA"},"&:hover .MuiOutlinedInput-notchedOutline":{borderColor:"#CED0DA"},"&.Mui-focused .MuiOutlinedInput-notchedOutline":{borderColor:"#CED0DA"}},children:[e.jsx(Ee,{value:"",children:e.jsx("em",{children:"Choose time range"})}),["Anytime","Past one day","Past one week","Past two weeks","Past three months","Past six months","Past year","Past 2 years"].map(s=>e.jsx(Ee,{value:s,children:s},s))]})]})})]})})}const rr=C(z)`
  padding: 16px;
  cursor: pointer;
  border: 1px solid #ced0da;
  border-radius: 4px;
  margin-bottom: 16px;

  &:hover {
    border-color: #1a84de;
  }
`;function lr({onNamesChange:t}){const[n,l]=u.useState(!1),[r,a]=u.useState([]),[c,p]=u.useState(""),i=()=>{if(c.trim()){const s=[...r,c.trim()];a(s),p(""),t==null||t(s)}},o=s=>{const d=r.filter((m,x)=>x!==s);a(d),t==null||t(d)};return e.jsx("div",{children:e.jsxs(rr,{onClick:()=>l(!n),style:{height:n?"auto":"85px"},children:[e.jsx("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:e.jsxs("div",{children:[e.jsx(h,{variant:"h6",style:{fontWeight:500,marginBottom:4},children:"Tags"}),e.jsxs(h,{variant:"body2",color:"textSecondary",style:{display:"flex"},children:[e.jsx(S,{icon:"material-symbols:add",width:"20",height:"20",style:{color:"#1a84de"}})," Filter by tags"]})]})}),n&&e.jsxs("div",{style:{padding:"16px"},onClick:s=>s.stopPropagation(),children:[e.jsx("div",{style:{marginTop:8,display:"flex",flexWrap:"wrap",gap:8},children:r.map((s,d)=>e.jsx(O,{label:s,onDelete:()=>o(d)},d))}),e.jsx(b,{mt:2,display:"flex",gap:1,children:e.jsx(_,{fullWidth:!0,variant:"outlined",placeholder:"Enter a tag...",size:"small",value:c,onChange:s=>{p(s.target.value)},onKeyPress:s=>{s.key==="Enter"&&(s.preventDefault(),i())}})})]})]})})}const or=C(z)`
  padding: 16px;
  cursor: pointer;
  border: 1px solid #ced0da;
  border-radius: 4px;
  margin-bottom: 16px;

  &:hover {
    border-color: #1a84de;
  }
`;function ar({onGroupsChange:t}){const[n,l]=u.useState(!1),[r,a]=u.useState([]),[c,p]=u.useState(""),i=()=>{if(c.trim()){const s=[...r,c.trim()];a(s),p(""),t==null||t(s)}},o=s=>{const d=r.filter((m,x)=>x!==s);a(d),t==null||t(d)};return e.jsx("div",{children:e.jsxs(or,{onClick:()=>l(!n),style:{height:n?"auto":"85px"},children:[e.jsx("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:e.jsxs("div",{children:[e.jsx(h,{variant:"h6",style:{fontWeight:500,marginBottom:4},children:"All Groups"}),e.jsxs(h,{variant:"body2",color:"textSecondary",style:{display:"flex"},children:[e.jsx(S,{icon:"material-symbols:add",width:"20",height:"20",style:{color:"#1a84de"}})," Find people associated with groups"]})]})}),n&&e.jsxs("div",{style:{padding:"16px"},onClick:s=>s.stopPropagation(),children:[e.jsx("div",{style:{marginTop:8,display:"flex",flexWrap:"wrap",gap:8},children:r.map((s,d)=>e.jsx(O,{label:s,onDelete:()=>o(d)},d))}),e.jsx(b,{mt:2,display:"flex",gap:1,children:e.jsx(_,{fullWidth:!0,variant:"outlined",placeholder:"Enter a group name...",size:"small",value:c,onChange:s=>{p(s.target.value)},onKeyPress:s=>{s.key==="Enter"&&(s.preventDefault(),i())}})})]})]})})}const dr=C(z)`
  padding: 16px;
  cursor: pointer;
  border: 1px solid #ced0da;
  border-radius: 4px;
  margin-bottom: 16px;

  &:hover {
    border-color: #1a84de;
  }
`;function cr({onNamesChange:t}){const[n,l]=u.useState(!1),[r,a]=u.useState([]),[c,p]=u.useState(""),i=()=>{if(c.trim()){const s=[...r,c.trim()];a(s),p(""),t==null||t(s)}},o=s=>{const d=r.filter((m,x)=>x!==s);a(d),t==null||t(d)};return e.jsx("div",{children:e.jsxs(dr,{onClick:()=>l(!n),style:{height:n?"auto":"85px"},children:[e.jsx("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:e.jsxs("div",{children:[e.jsx(h,{variant:"h6",style:{fontWeight:500,marginBottom:4},children:"Projects"}),e.jsxs(h,{variant:"body2",color:"textSecondary",style:{display:"flex"},children:[e.jsx(S,{icon:"material-symbols:add",width:"20",height:"20",style:{color:"#1a84de"}})," Search for projects"]})]})}),n&&e.jsxs("div",{style:{padding:"16px"},onClick:s=>s.stopPropagation(),children:[e.jsx("div",{style:{marginTop:8,display:"flex",flexWrap:"wrap",gap:8},children:r.map((s,d)=>e.jsx(O,{label:s,onDelete:()=>o(d)},d))}),e.jsx(b,{mt:2,display:"flex",gap:1,children:e.jsx(_,{fullWidth:!0,variant:"outlined",placeholder:"Search for project...",size:"small",value:c,onChange:s=>{p(s.target.value)},onKeyPress:s=>{s.key==="Enter"&&(s.preventDefault(),i())}})})]})]})})}const pr=C(z)`
  padding: 16px;
  cursor: pointer;
  border: 1px solid #ced0da;
  border-radius: 4px;
  margin-bottom: 16px;

  &:hover {
    border-color: #1a84de;
  }
`;function ur({onNamesChange:t}){const[n,l]=u.useState(!1),[r,a]=u.useState([]),[c,p]=u.useState(""),i=()=>{if(c.trim()){const s=[...r,c.trim()];a(s),p(""),t==null||t(s)}},o=s=>{const d=r.filter((m,x)=>x!==s);a(d),t==null||t(d)};return e.jsx("div",{children:e.jsxs(pr,{onClick:()=>l(!n),style:{height:n?"auto":"85px"},children:[e.jsx("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:e.jsxs("div",{children:[e.jsx(h,{variant:"h6",style:{fontWeight:500,marginBottom:4},children:"Notes"}),e.jsxs(h,{variant:"body2",color:"textSecondary",style:{display:"flex"},children:[e.jsx(S,{icon:"material-symbols:add",width:"20",height:"20",style:{color:"#1a84de"}})," Filter by notes"]})]})}),n&&e.jsxs("div",{style:{padding:"16px"},onClick:s=>s.stopPropagation(),children:[e.jsx("div",{style:{marginTop:8,display:"flex",flexWrap:"wrap",gap:8},children:r.map((s,d)=>e.jsx(O,{label:s,onDelete:()=>o(d)},d))}),e.jsx(b,{mt:2,display:"flex",gap:1,children:e.jsx(_,{fullWidth:!0,variant:"outlined",placeholder:"Enter note...",size:"small",value:c,onChange:s=>{p(s.target.value)},onKeyPress:s=>{s.key==="Enter"&&(s.preventDefault(),i())}})})]})]})})}const xr=C(z)`
  padding: 16px;
  cursor: pointer;
  border: 1px solid #ced0da;
  border-radius: 4px;
  margin-bottom: 16px;

  &:hover {
    border-color: #1a84de;
  }
`;function hr({onFilterChange:t}){const[n,l]=u.useState(!1),r=a=>{l(a.target.checked),t(a.target.checked)};return e.jsx("div",{children:e.jsxs(xr,{style:{height:"85px"},children:[e.jsx("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:e.jsx("div",{children:e.jsx(h,{variant:"h6",style:{fontWeight:500,marginBottom:4},children:"Past applicants"})})}),e.jsx("div",{onClick:a=>a.stopPropagation(),children:e.jsx(Us,{children:e.jsx(qs,{control:e.jsx(Ln,{checked:n,onChange:r,name:"pastApplicants",color:"primary"}),label:"Show only candidates who have previously applied"})})})]})})}const mr=C(z)`
  padding: 16px;
  cursor: pointer;
  border: 1px solid #ced0da;
  border-radius: 4px;
  margin-bottom: 16px;

  &:hover {
    border-color: #1a84de;
  }
`;function yr({onChange:t}){const[n,l]=u.useState(!1),[r,a]=u.useState([]),[c,p]=u.useState(null);u.useEffect(()=>{(async()=>{try{const d=await Fs.get(ts.getAllIndustries);a(d.data)}catch(d){console.error("Error fetching industries:",d)}})()},[]);const i=s=>s.replace(/\(.*?\)/g,"").replace(/\d[\d, -]*$/g,"").replace(/\d[\d,]*\+?/g,"").trim(),o=Array.from(new Set(((r==null?void 0:r.industries)||[]).map(i).filter(s=>s&&s.toLowerCase()!=="nan")));return e.jsx("div",{children:e.jsxs(mr,{onClick:()=>l(!n),style:{height:n?"auto":"85px"},children:[e.jsx("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:e.jsxs("div",{children:[e.jsx(h,{variant:"h6",style:{fontWeight:500,marginBottom:4},children:"Industries"}),e.jsxs(h,{variant:"body2",color:"textSecondary",style:{display:"flex"},children:[e.jsx(S,{icon:"material-symbols:add",width:"20",height:"20",style:{color:"#1a84de"}})," Candidate industries"]})]})}),n&&e.jsx("div",{style:{marginTop:"16px"},onClick:s=>s.stopPropagation(),children:e.jsx(Ds,{name:"industry",options:o.map(s=>({label:s,value:s})),value:c,onChange:s=>{p(s),t==null||t(s?s.value:null)},isClearable:!0,placeholder:"Select Industry",classNamePrefix:"react-select",menuPlacement:"auto",menuPosition:"fixed",styles:{menu:s=>({...s,zIndex:100,maxHeight:"450px",overflowY:"auto"}),menuList:s=>({...s,maxHeight:"450px"}),control:s=>({...s,minHeight:"44px"})}})})]})})}const{Panel:Qe}=ns,gr=ss.forwardRef(function(n,l){return e.jsx(Ms,{direction:"left",ref:l,...n,timeout:{enter:1e3,exit:500},easing:{enter:"cubic-bezier(0.4, 0, 0.2, 1)",exit:"cubic-bezier(0.4, 0, 0.2, 1)"}})});function fr({open:t,onClose:n,onApplyFilters:l}){const[r,a]=u.useState({}),c=i=>{a(o=>({...o,...i}))},p=()=>{l(r),n()};return e.jsxs(Rs,{open:t,onClose:n,TransitionComponent:gr,keepMounted:!0,fullScreen:!0,PaperProps:{sx:{ml:"auto",mt:0,mr:0,width:"1300px",height:"100vh",overflowY:"hidden",borderRadius:0}},BackdropProps:{sx:{backgroundColor:"rgba(0, 0, 0, 0.5)"}},children:[e.jsx(Ws,{children:e.jsxs(b,{display:"flex",justifyContent:"space-between",alignItems:"center",children:[e.jsx(h,{variant:"h4",className:"ml-8",children:"Advance Search"}),e.jsx(S,{icon:"material-symbols:close-rounded",width:"24",height:"24",style:{cursor:"pointer"},onClick:n})]})}),e.jsxs(_s,{sx:{width:"100%",overflowY:"auto","&::-webkit-scrollbar":{width:"5px",height:"5px"},"&::-webkit-scrollbar-thumb":{backgroundColor:"#d9d9d9",borderRadius:"4px"},"&::-webkit-scrollbar-thumb:hover":{backgroundColor:"#cccccc"},"&::-webkit-scrollbar-track":{backgroundColor:"#f0f0f0"}},children:[e.jsxs(ns,{defaultActiveKey:["1"],bordered:!1,children:[e.jsxs(Qe,{header:"Candidate details",style:{backgroundColor:"white",fontSize:"18px",fontWeight:500},children:[e.jsxs(X,{gutter:[16,16],children:[e.jsx(L,{span:12,children:e.jsx(Kn,{onChange:i=>c({spokenLanguages:i})})}),e.jsx(L,{span:12,children:e.jsx(Un,{onNamesChange:i=>{c({firstNames:i})}})})]}),e.jsxs(X,{gutter:[16,16],children:[e.jsx(L,{span:12,children:e.jsx(Xn,{})}),e.jsx(L,{span:12,children:e.jsx(Yn,{onNamesChange:i=>{c({lastNames:i})}})})]}),e.jsxs(X,{gutter:[16,16],children:[e.jsx(L,{span:12,children:e.jsx(ei,{onChange:i=>{c({profileLanguages:i})}})}),e.jsx(L,{span:12,children:e.jsx(ii,{onChange:i=>{c({networkRelationships:i})}})})]}),e.jsx(X,{gutter:[16,16],children:e.jsx(L,{span:12,children:e.jsx(ai,{onChange:i=>{c({recentLinkedin:i})}})})})]},"1"),e.jsxs(Qe,{header:"Education & experience",style:{backgroundColor:"white",fontSize:"18px",fontWeight:500},children:[e.jsxs(X,{gutter:[16,16],children:[e.jsx(L,{span:12,children:e.jsx(ci,{onChange:i=>{c({yearsOfExperience:i})}})}),e.jsx(L,{span:12,children:e.jsx(yr,{onChange:i=>{c({industries:i})}})})]}),e.jsxs(X,{gutter:[16,16],children:[e.jsx(L,{span:12,children:e.jsx(ui,{onChange:i=>{c({yearsInCurrentCompany:i})}})}),e.jsx(L,{span:12,children:e.jsx(hi,{onSchoolsChange:i=>{c({schools:i})}})})]}),e.jsxs(X,{gutter:[16,16],children:[e.jsx(L,{span:12,children:e.jsx(yi,{onChange:i=>{c({yearsInCurrentPosition:i})}})}),e.jsx(L,{span:12,children:e.jsx(fi,{onFieldsChange:i=>{c({fieldsOfStudy:i})}})})]}),e.jsxs(X,{gutter:[16,16],children:[e.jsx(L,{span:12,children:e.jsx(Si,{onChange:i=>{c({employmentType:i})}})}),e.jsx(L,{span:12,children:e.jsx(Ii,{onChange:i=>{c({degrees:i})}})})]}),e.jsxs(X,{gutter:[16,16],children:[e.jsx(L,{span:12,children:e.jsx(Ei,{onChange:i=>{c({workplaceType:i})}})}),e.jsx(L,{span:12,children:e.jsx(zi,{onChange:i=>{c({yearsOfGraduation:i})}})})]}),e.jsxs(X,{gutter:[16,16],children:[e.jsx(L,{span:12,children:e.jsx(Di,{onChange:i=>{c({military:i})}})}),e.jsx(L,{span:12,children:e.jsx(Wi,{onChange:i=>{c({seniority:i})}})})]})]},"2"),e.jsxs(Qe,{header:"Company",style:{backgroundColor:"white",fontSize:"18px",fontWeight:500},children:[e.jsxs(X,{gutter:[16,16],children:[e.jsx(L,{span:12,children:e.jsx(Bi,{onNamesChange:i=>{}})}),e.jsx(L,{span:12,children:e.jsx(Ki,{onChange:i=>{}})})]}),e.jsxs(X,{gutter:[16,16],children:[e.jsx(L,{span:12,children:e.jsx(Ui,{onNamesChange:i=>{}})}),e.jsx(L,{span:12,children:e.jsx(Yi,{onNamesChange:i=>{}})})]}),e.jsxs(X,{gutter:[16,16],children:[e.jsx(L,{span:12,children:e.jsx(Qi,{onChange:i=>{}})}),e.jsx(L,{span:12,children:e.jsx(sr,{onChange:()=>{}})})]})]},"3"),e.jsxs(Qe,{header:"Recruiting & candidate activity",style:{backgroundColor:"white",fontSize:"18px",fontWeight:500},children:[e.jsxs(X,{gutter:[16,16],children:[e.jsx(L,{span:12,children:e.jsx(ir,{onChange:i=>{}})}),e.jsx(L,{span:12,children:e.jsx(lr,{onNamesChange:i=>{}})})]}),e.jsxs(X,{gutter:[16,16],children:[e.jsx(L,{span:12,children:e.jsx(ar,{onGroupsChange:i=>{}})}),e.jsx(L,{span:12,children:e.jsx(cr,{onNamesChange:i=>{}})})]}),e.jsxs(X,{gutter:[16,16],children:[e.jsx(L,{span:12,children:e.jsx(ur,{onNamesChange:i=>{}})}),e.jsx(L,{span:12,children:e.jsx(hr,{onFilterChange:i=>{}})})]})]},"4")]}),e.jsx(b,{textAlign:"right",mt:4,children:e.jsx(Le,{variant:"contained",color:"primary",onClick:p,children:"Apply Filters"})})]})]})}const br=C.div`
  position: absolute;
  bottom: 100%;
  left: 0;
  right: 0;
  margin-bottom: 10px;
  height: 500px;
  width: 100%;
  border-radius: 10px;
  background-color: white;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #ced0da;
  z-index: 1000;
`,vr=C.div`
  background-color: #1a84de;
  color: white;
  padding: 12px 20px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  font-family: 'Poppins';
  font-weight: 500;
  font-size: 16px;
`,jr=C.div`
  flex-grow: 1;
  padding: 20px;
  overflow-y: auto;
`,Sr=C.div`
  display: flex;
  gap: 10px;
  padding: 15px 20px;
  border-top: 1px solid #ced0da;
  background-color: #f9f9f9;
`,kr=C(It)`
  padding: 10px;
  border-radius: 25px;
  border: 2px solid #ced0da;
  font-family: 'Poppins';
  flex: 1;
`,wr=C(fe)`
  height: 40px;
  width: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #1a84de;
  border: none;
  &:hover {
    background-color: #1a84de;
  }
`,Cr=({onSend:t,visible:n})=>{const[l,r]=ss.useState(""),a=()=>{l.trim()&&(t(l),r(""))},c=p=>{p.key==="Enter"&&!p.shiftKey&&(p.preventDefault(),a())};return n?e.jsxs(br,{children:[e.jsx(vr,{children:"Chat with AI Assistant"}),e.jsx(jr,{}),e.jsxs(Sr,{children:[e.jsx(kr,{placeholder:"Type your message...",value:l,onChange:p=>r(p.target.value),onKeyPress:c}),e.jsx(wr,{onClick:a,children:e.jsx(S,{icon:"material-symbols:send-rounded",color:"white"})})]})]}):null},{TextArea:Kt}=It,Vt=C(It)`
  padding: 10px;
  border-radius: 0;
  border: 2px solid #ced0da;
  font-family: 'Poppins';
`,Ut=C(L)`
  overflow-y: auto;
  height: auto;
  &::-webkit-scrollbar {
    width: 5px;
    height: 5px;
  }
  &::-webkit-scrollbar-thumb {
    background-color: #d9d9d9;
    border-radius: 4px;
  }
  &::-webkit-scrollbar-thumb:hover {
    background-color: #cccccc;
  }
  &::-webkit-scrollbar-track {
    background-color: #f0f0f0;
  }
`,Ir=["titles","locations","skills","companies","industries","keywords","postal_code"];function Wr(t){var Me,Re,De;const[n,l]=u.useState(!1),r=Bs(),a=Os(),[c,p]=u.useState(((Re=(Me=r==null?void 0:r.state)==null?void 0:Me.booleanString)==null?void 0:Re.Boolean_String)||""),[i,o]=u.useState([]),[s,d]=u.useState(""),m=Ns(),x=((De=r==null?void 0:r.state)==null?void 0:De.booleanString)||{},[v,W]=u.useState({titles:(x==null?void 0:x.Job_Title)||[],skills:(x==null?void 0:x.Skills)||[],industries:(x==null?void 0:x.Industry)||[],locations:Array.isArray(x==null?void 0:x.locations)?x.locations:[],companies:(x==null?void 0:x.Companies)||[],keywords:[],postal_code:[]}),[I,K]=u.useState([]),me=["Entry Level","Junior","Mid-Level","Senior","Lead","Manager","Director","Executive"],[ce,N]=u.useState(""),[te,se]=u.useState(""),[Y,ne]=u.useState(""),[H,le]=u.useState(""),[ee,J]=u.useState(""),[ve,ie]=u.useState({}),[pe,q]=u.useState(!1),[je,V]=u.useState(!1),oe=(f,w)=>{!w||v[f].includes(w)||(W(j=>({...j,[f]:[...j[f],w]})),le(""),N(""))},Oe=(f,w)=>{W(j=>({...j,[f]:j[f].filter((E,A)=>A!==w)}))},Ne=async f=>{try{const w=await Rt({need:f,filters:v,seniority:I},Wt.aiSearch,j=>{j!=null&&j.suggestions&&Pe.success("AI suggestions received")},j=>{var E;Pe.error(((E=j==null?void 0:j.response)==null?void 0:E.message)||"Failed to process AI request")})}catch(w){console.error("AI Chat Error:",w),Pe.error("Failed to process AI request")}},He=f=>{if(!Y)return Pe.error("Please enter a keyword to generate.");J(f),Rt({keywords:Y},Wt.generateKeywords,w=>{J("");const j=JSON.parse(w==null?void 0:w.boolean_string.replace(/'/g,'"'));ie(E=>({...E,[f]:j})),ne("")},w=>{var j;J(""),Pe.error(((j=w==null?void 0:w.response)==null?void 0:j.message)||"Failed to generate keywords.")})},[ze,ye]=u.useState([]),[Q,Ce]=u.useState(!1),[B,Se]=u.useState({current:1,pageSize:20,total:0,totalPages:0}),ke=(f=1,w=20)=>{try{Ce(!0);const j=new URLSearchParams;(s||c)&&j.append("q",s||c),v.titles.length>0&&j.append("titles",v.titles.join(",")),v.locations.length>0&&j.append("locations",v.locations.join(",")),v.skills.length>0&&j.append("skills",v.skills.join(",")),v.companies.length>0&&j.append("companies",v.companies.join(",")),v.industries.length>0&&j.append("industries",v.industries.join(",")),v.keywords.length>0&&j.append("keywords",v.keywords.join(",")),I.length>0&&j.append("seniority",I.join(",")),pe&&j.append("hideViewed","true"),j.append("page",f.toString()),j.append("pageSize",w.toString());const E=`${ts.getCandidateBySearchString}?${j.toString()}`;Hs({},E,A=>{Ce(!1),A&&A.results?(ye(A.results),Se({current:A.page||1,pageSize:A.pageSize||20,total:A.total||0,totalPages:A.totalPages||0}),Pe.success(`Found ${A.total} candidates`)):(ye([]),Se({current:1,pageSize:20,total:0,totalPages:0}),Pe.info("No candidates found"))},A=>{Ce(!1),Pe.error("Failed to search candidates"),console.error("Search error:",A)})}catch(j){Ce(!1),Pe.error("Failed to get candidates"),console.error("Search error:",j)}},Ae=(f,w)=>{ke(f,w)};return u.useEffect(()=>{const f=setTimeout(()=>{ke()},500);return()=>clearTimeout(f)},[v,I,pe,s,c]),e.jsxs("div",{style:{width:"100%",position:"relative"},children:[e.jsx(S,{icon:"ion:arrow-back-outline",width:"20",height:"20",style:{cursor:"pointer"},onClick:()=>a(-1)}),e.jsx("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",backgroundColor:"#E5F4E3",padding:"15px",height:"75px",marginBottom:"10px"},children:e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"70px"},children:[e.jsx(R.Text,{style:{fontSize:"17px",color:"#344665",fontWeight:500,fontFamily:"Poppins"},children:"Client No: 346"}),e.jsx(R.Text,{style:{fontSize:"17px",color:"#344665",fontWeight:500,fontFamily:"Poppins"},children:"Role Title: Designer"}),e.jsx(R.Text,{style:{fontSize:"17px",color:"#344665",fontWeight:500,fontFamily:"Poppins"},children:"Status: Pending"}),e.jsx(R.Text,{style:{fontSize:"17px",color:"#344665",fontWeight:500,fontFamily:"Poppins"},children:"LIs: 0"}),e.jsx(R.Text,{style:{fontSize:"17px",color:"#344665",fontWeight:500,fontFamily:"Poppins"},children:"CVs: 0"})]})}),e.jsx(R.Text,{style:{fontSize:"17px",fontFamily:"Poppins",fontWeight:500,marginBottom:"10px"},children:"Search String"}),e.jsx(Kt,{rows:4,value:s||c,onChange:f=>{d(f.target.value),p(f.target.value)}}),e.jsx(fe,{onClick:()=>ke(),style:{width:"20%",height:"50px",borderRadius:"25px",marginTop:"30px",marginLeft:"80%",background:"#1A84DE",color:"white"},children:"Search"}),e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",marginTop:"10px",alignItems:"center",padding:"15px",height:"75px"},children:[e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"70px"},children:[e.jsxs(R.Text,{style:{fontSize:"26px",color:"#344665",fontWeight:500,fontFamily:"Poppins"},children:["Candidates: ",B.total]}),Q&&e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"10px"},children:[e.jsx(jt,{size:20,thickness:4}),e.jsx(R.Text,{style:{fontSize:"16px",color:"#666"},children:"Searching..."})]})]}),e.jsxs(R.Text,{style:{fontSize:"26px",color:"#344665",fontWeight:500,fontFamily:"Poppins"},children:["Page ",B.current," of ",B.totalPages]})]}),e.jsx(St,{style:{marginTop:0,borderColor:"#CED0DA",borderWidth:"2px",marginBottom:"10px"}}),e.jsxs(R.Text,{style:{fontSize:"16px",color:"#979797",fontWeight:400,fontFamily:"Poppins"},children:[" ","Search anything in CV or Profile : Web Designer , Current or desired job titles : N/A , skills: N/A , within 45 mints of UK search type: Standard, Salary Annual: Unspecified, Hide profiles viewdsince 5 days ago. Sear ch anything in CV or Profile : Web Designer , Current or desired job titles : N/A , skills: N/A , within 45 mints of UK search type: Standard, Salary Annual: Unspecified, Hide profiles viewdsince 5 days ago. Search anything in CV or Profile : Web Designer , Current or desired job titles : N/A , skills: N/A , within 45 mints of UK search type: Standard, Salary Annual: Unspecified, Hide profiles viewdsince 5 days ago."]}),e.jsxs(X,{gutter:[16,24],style:{marginTop:"15px"},children:[e.jsxs(Ut,{span:8,className:"gutter-row ",style:{padding:"15px",backgroundColor:"white"},children:[e.jsxs("div",{style:{display:"flex"},children:[e.jsx(S,{icon:"mage:filter-fill",width:"24",height:"24",style:{color:"#1A84DE"}}),e.jsx(R.Text,{style:{fontSize:"17px",color:"#1A84DE",fontWeight:500,fontFamily:"Poppins",marginLeft:"10px"},children:"Custom filters"})]}),e.jsx(St,{style:{marginTop:"8px",borderColor:"#CED0DA",borderWidth:"1px",marginBottom:"10px"}}),e.jsx(R.Text,{style:{fontSize:"17px",fontFamily:"Poppins",fontWeight:500},children:"Search String"}),e.jsx(Kt,{rows:4,value:s||c,onChange:f=>{d(f.target.value),p(f.target.value)}}),e.jsx(St,{style:{marginTop:"8px",borderColor:"#CED0DA",borderWidth:"1px",marginBottom:"10px"}}),Ir.map(f=>{var w,j;return e.jsxs("div",{style:{marginBottom:30},children:[e.jsx(R.Text,{style:{fontSize:"17px",fontWeight:500,fontFamily:"Poppins"},children:f.replace(/_/g," ").replace(/\b\w/g,E=>E.toUpperCase())}),e.jsx("div",{style:{display:"flex",flexWrap:"wrap",gap:"8px",marginTop:"10px"},children:(w=v[f])==null?void 0:w.map((E,A)=>e.jsxs("div",{style:{display:"flex",alignItems:"center",padding:"5px 10px",background:m.palette.background.default,borderRadius:"4px"},children:[e.jsx(R.Text,{children:E}),e.jsx(S,{icon:"material-symbols:close-rounded",style:{cursor:"pointer",marginLeft:5},onClick:()=>Oe(f,A)})]},A))}),ce===f?e.jsx(e.Fragment,{children:e.jsxs("div",{style:{display:"flex",gap:"10px",marginTop:"10px"},children:[e.jsx(Vt,{value:H,onChange:E=>le(E.target.value),placeholder:`Enter ${f}`}),e.jsx(fe,{onClick:()=>oe(f,H),icon:e.jsx(S,{icon:"mdi:tick"}),style:{height:"40px",width:"40px"}}),e.jsx(fe,{onClick:()=>N(""),icon:e.jsx(S,{icon:"oui:cross"}),style:{height:"40px",width:"40px"}})]})}):e.jsxs("div",{style:{display:"flex",gap:"20px",marginTop:"10px"},children:[e.jsx(R.Text,{onClick:()=>N(f),style:{color:"#1A84DE",cursor:"pointer"},children:"+ Add"}),e.jsx(R.Text,{children:"or"}),e.jsx(R.Text,{onClick:()=>se(f),style:{color:"#1A84DE",cursor:"pointer"},children:"Generate"})]}),"              ",te===f&&e.jsxs("div",{style:{display:"flex",gap:"10px",marginTop:"10px"},children:[e.jsx(Vt,{value:Y,onChange:E=>ne(E.target.value),placeholder:`Generate ${f}`}),e.jsx(fe,{onClick:()=>He(f),icon:e.jsx(S,{icon:"mdi:tick"}),style:{height:"40px",width:"40px"}}),e.jsx(fe,{onClick:()=>se(""),icon:e.jsx(S,{icon:"oui:cross"}),style:{height:"40px",width:"40px"}})]}),ee===f&&e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"10px",marginTop:"10px"},children:[e.jsxs(R.Text,{children:["Generating ",f]}),e.jsx(jt,{size:20,thickness:4})]}),(j=ve[f])==null?void 0:j.map((E,A)=>e.jsx("div",{onClick:()=>{oe(f,E),ie(ae=>({...ae,[f]:ae[f].filter((Ue,G)=>G!==A)}))},style:{marginTop:5,padding:"5px 10px",background:m.palette.background.default,borderRadius:"4px",cursor:"pointer"},children:E},A)),"            "]},f)}),e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"20px",marginBottom:"15px"},children:[e.jsx(R.Text,{style:{fontSize:"17px",fontFamily:"Poppins",fontWeight:500},children:"Hide previously viewed"}),e.jsx(ls,{checked:pe,onChange:q})]}),"          ",e.jsx(R.Text,{style:{fontSize:"17px",fontFamily:"Poppins",fontWeight:500},children:"Seniority"}),e.jsx("div",{style:{display:"flex",flexWrap:"wrap",gap:"8px",marginTop:"10px"},children:I.map((f,w)=>e.jsxs("div",{style:{display:"flex",alignItems:"center",padding:"5px 10px",background:m.palette.background.default,borderRadius:"4px"},children:[e.jsx(R.Text,{children:f}),e.jsx(S,{icon:"material-symbols:close-rounded",style:{cursor:"pointer",marginLeft:5},onClick:()=>K(j=>j.filter((E,A)=>A!==w))})]},w))}),e.jsx("div",{style:{marginTop:"10px"},children:me.map(f=>!I.includes(f)&&e.jsxs(R.Text,{onClick:()=>K(w=>[...w,f]),style:{color:"#1A84DE",cursor:"pointer",display:"block",marginBottom:"5px"},children:["+ ",f]},f))}),e.jsx(fe,{onClick:()=>{l(!0)},style:{width:"100%",height:"50px",borderRadius:"25px",marginTop:"30px"},children:"Advance Search"}),e.jsx(fe,{onClick:()=>{W({titles:[],skills:[],industries:[],locations:[],companies:[],keywords:[],postal_code:[]}),K([]),d(""),p(""),ye([]),Se({current:1,pageSize:20,total:0,totalPages:0})},style:{width:"100%",height:"50px",borderRadius:"25px",marginTop:"15px",marginBottom:"20px"},children:"Clear All Filters"}),e.jsx("div",{style:{position:"relative",marginBottom:"5px"},children:je&&e.jsx(Cr,{visible:je,onSend:Ne})}),e.jsxs(fe,{onClick:()=>V(!je),style:{width:"100%",height:"50px",borderRadius:"25px"},children:[je?"Close AI Chat":"Search using AI",e.jsx(S,{icon:"iconoir:spark-solid",width:"24",height:"24",style:{color:"#32ade6"}})]})]}),e.jsx(Ut,{span:16,className:"gutter-row",children:Q?e.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"400px"},children:e.jsxs("div",{style:{textAlign:"center"},children:[e.jsx(jt,{size:40,thickness:4}),e.jsx(R.Text,{style:{display:"block",marginTop:"20px",fontSize:"16px",color:"#666"},children:"Searching candidates..."})]})}):ze.length>0?e.jsxs(e.Fragment,{children:[ze.map((f,w)=>e.jsx(Ys,{style:{marginBottom:"16px"},children:e.jsx(Bn,{index:w,candidate:f,searchString:s||c||"search",filters:v})},f==null?void 0:f.id)),e.jsxs("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",marginTop:"30px",gap:"20px"},children:[e.jsx(fe,{disabled:B.current<=1,onClick:()=>Ae(B.current-1,B.pageSize),style:{backgroundColor:B.current<=1?"#f5f5f5":"#1A84DE",color:B.current<=1?"#999":"white",border:"none"},children:"Previous"}),e.jsxs(R.Text,{style:{fontSize:"16px",fontFamily:"Poppins"},children:["Page ",B.current," of ",B.totalPages,"(",B.total," total candidates)"]}),e.jsx(fe,{disabled:B.current>=B.totalPages,onClick:()=>Ae(B.current+1,B.pageSize),style:{backgroundColor:B.current>=B.totalPages?"#f5f5f5":"#1A84DE",color:B.current>=B.totalPages?"#999":"white",border:"none"},children:"Next"})]})]}):e.jsxs("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"400px",flexDirection:"column",gap:"20px"},children:[e.jsx(S,{icon:"mdi:account-search",width:"80",height:"80",style:{color:"#ccc"}}),e.jsx(R.Text,{style:{fontSize:"18px",color:"#666",fontFamily:"Poppins"},children:"No candidates found"}),e.jsx(R.Text,{style:{fontSize:"14px",color:"#999",textAlign:"center",maxWidth:"400px"},children:"Try adjusting your search criteria or filters to find more candidates"})]})})]}),n&&e.jsx(fr,{open:n,onClose:()=>l(!1),onApplyFilters:f=>o(f)})]})}export{Wr as default};
