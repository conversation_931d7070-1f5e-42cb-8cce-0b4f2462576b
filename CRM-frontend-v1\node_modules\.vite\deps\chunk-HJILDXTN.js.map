{"version": 3, "sources": ["../../@mui/base/node_modules/@babel/runtime/helpers/esm/extends.js", "../../@mui/base/utils/isHostComponent.js", "../../@mui/base/utils/appendOwnerState.js", "../../@mui/base/node_modules/@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js", "../../@mui/base/utils/extractEventHandlers.js", "../../@mui/base/utils/omitEventHandlers.js", "../../@mui/base/utils/mergeSlotProps.js", "../../@mui/base/utils/resolveComponentProps.js", "../../@mui/base/utils/useSlotProps.js", "../../@mui/base/utils/areArraysEqual.js", "../../@mui/base/utils/ClassNameConfigurator.js", "../../@mui/base/utils/useRootElementName.js", "../../@mui/base/utils/prepareForSlot.js"], "sourcesContent": ["export default function _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}", "/**\n * Determines if a given element is a DOM element name (i.e. not a React component).\n */\nexport function isHostComponent(element) {\n  return typeof element === 'string';\n}", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { isHostComponent } from './isHostComponent';\n\n/**\n * Type of the ownerState based on the type of an element it applies to.\n * This resolves to the provided OwnerState for React components and `undefined` for host components.\n * Falls back to `OwnerState | undefined` when the exact type can't be determined in development time.\n */\n\n/**\n * Appends the ownerState object to the props, merging with the existing one if necessary.\n *\n * @param elementType Type of the element that owns the `existingProps`. If the element is a DOM node or undefined, `ownerState` is not applied.\n * @param otherProps Props of the element.\n * @param ownerState\n */\nexport function appendOwnerState(elementType, otherProps, ownerState) {\n  if (elementType === undefined || isHostComponent(elementType)) {\n    return otherProps;\n  }\n  return _extends({}, otherProps, {\n    ownerState: _extends({}, otherProps.ownerState, ownerState)\n  });\n}", "export default function _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  for (var key in source) {\n    if (Object.prototype.hasOwnProperty.call(source, key)) {\n      if (excluded.indexOf(key) >= 0) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}", "/**\n * Extracts event handlers from a given object.\n * A prop is considered an event handler if it is a function and its name starts with `on`.\n *\n * @param object An object to extract event handlers from.\n * @param excludeKeys An array of keys to exclude from the returned object.\n */\nexport function extractEventHandlers(object, excludeKeys = []) {\n  if (object === undefined) {\n    return {};\n  }\n  const result = {};\n  Object.keys(object).filter(prop => prop.match(/^on[A-Z]/) && typeof object[prop] === 'function' && !excludeKeys.includes(prop)).forEach(prop => {\n    result[prop] = object[prop];\n  });\n  return result;\n}", "/**\n * Removes event handlers from the given object.\n * A field is considered an event handler if it is a function with a name beginning with `on`.\n *\n * @param object Object to remove event handlers from.\n * @returns Object with event handlers removed.\n */\nexport function omitEventHandlers(object) {\n  if (object === undefined) {\n    return {};\n  }\n  const result = {};\n  Object.keys(object).filter(prop => !(prop.match(/^on[A-Z]/) && typeof object[prop] === 'function')).forEach(prop => {\n    result[prop] = object[prop];\n  });\n  return result;\n}", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport clsx from 'clsx';\nimport { extractEventHandlers } from './extractEventHandlers';\nimport { omitEventHandlers } from './omitEventHandlers';\n/**\n * Merges the slot component internal props (usually coming from a hook)\n * with the externally provided ones.\n *\n * The merge order is (the latter overrides the former):\n * 1. The internal props (specified as a getter function to work with get*Props hook result)\n * 2. Additional props (specified internally on a Base UI component)\n * 3. External props specified on the owner component. These should only be used on a root slot.\n * 4. External props specified in the `slotProps.*` prop.\n * 5. The `className` prop - combined from all the above.\n * @param parameters\n * @returns\n */\nexport function mergeSlotProps(parameters) {\n  const {\n    getSlotProps,\n    additionalProps,\n    externalSlotProps,\n    externalForwardedProps,\n    className\n  } = parameters;\n  if (!getSlotProps) {\n    // The simpler case - getSlotProps is not defined, so no internal event handlers are defined,\n    // so we can simply merge all the props without having to worry about extracting event handlers.\n    const joinedClasses = clsx(additionalProps == null ? void 0 : additionalProps.className, className, externalForwardedProps == null ? void 0 : externalForwardedProps.className, externalSlotProps == null ? void 0 : externalSlotProps.className);\n    const mergedStyle = _extends({}, additionalProps == null ? void 0 : additionalProps.style, externalForwardedProps == null ? void 0 : externalForwardedProps.style, externalSlotProps == null ? void 0 : externalSlotProps.style);\n    const props = _extends({}, additionalProps, externalForwardedProps, externalSlotProps);\n    if (joinedClasses.length > 0) {\n      props.className = joinedClasses;\n    }\n    if (Object.keys(mergedStyle).length > 0) {\n      props.style = mergedStyle;\n    }\n    return {\n      props,\n      internalRef: undefined\n    };\n  }\n\n  // In this case, getSlotProps is responsible for calling the external event handlers.\n  // We don't need to include them in the merged props because of this.\n\n  const eventHandlers = extractEventHandlers(_extends({}, externalForwardedProps, externalSlotProps));\n  const componentsPropsWithoutEventHandlers = omitEventHandlers(externalSlotProps);\n  const otherPropsWithoutEventHandlers = omitEventHandlers(externalForwardedProps);\n  const internalSlotProps = getSlotProps(eventHandlers);\n\n  // The order of classes is important here.\n  // Emotion (that we use in libraries consuming Base UI) depends on this order\n  // to properly override style. It requires the most important classes to be last\n  // (see https://github.com/mui/material-ui/pull/33205) for the related discussion.\n  const joinedClasses = clsx(internalSlotProps == null ? void 0 : internalSlotProps.className, additionalProps == null ? void 0 : additionalProps.className, className, externalForwardedProps == null ? void 0 : externalForwardedProps.className, externalSlotProps == null ? void 0 : externalSlotProps.className);\n  const mergedStyle = _extends({}, internalSlotProps == null ? void 0 : internalSlotProps.style, additionalProps == null ? void 0 : additionalProps.style, externalForwardedProps == null ? void 0 : externalForwardedProps.style, externalSlotProps == null ? void 0 : externalSlotProps.style);\n  const props = _extends({}, internalSlotProps, additionalProps, otherPropsWithoutEventHandlers, componentsPropsWithoutEventHandlers);\n  if (joinedClasses.length > 0) {\n    props.className = joinedClasses;\n  }\n  if (Object.keys(mergedStyle).length > 0) {\n    props.style = mergedStyle;\n  }\n  return {\n    props,\n    internalRef: internalSlotProps.ref\n  };\n}", "/**\n * If `componentProps` is a function, calls it with the provided `ownerState`.\n * Otherwise, just returns `componentProps`.\n */\nexport function resolveComponentProps(componentProps, ownerState, slotState) {\n  if (typeof componentProps === 'function') {\n    return componentProps(ownerState, slotState);\n  }\n  return componentProps;\n}", "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"elementType\", \"externalSlotProps\", \"ownerState\", \"skipResolvingSlotProps\"];\nimport { unstable_useForkRef as useForkRef } from '@mui/utils';\nimport { appendOwnerState } from './appendOwnerState';\nimport { mergeSlotProps } from './mergeSlotProps';\nimport { resolveComponentProps } from './resolveComponentProps';\n/**\n * @ignore - do not document.\n * Builds the props to be passed into the slot of an unstyled component.\n * It merges the internal props of the component with the ones supplied by the user, allowing to customize the behavior.\n * If the slot component is not a host component, it also merges in the `ownerState`.\n *\n * @param parameters.getSlotProps - A function that returns the props to be passed to the slot component.\n */\nexport function useSlotProps(parameters) {\n  var _parameters$additiona;\n  const {\n      elementType,\n      externalSlotProps,\n      ownerState,\n      skipResolvingSlotProps = false\n    } = parameters,\n    rest = _objectWithoutPropertiesLoose(parameters, _excluded);\n  const resolvedComponentsProps = skipResolvingSlotProps ? {} : resolveComponentProps(externalSlotProps, ownerState);\n  const {\n    props: mergedProps,\n    internalRef\n  } = mergeSlotProps(_extends({}, rest, {\n    externalSlotProps: resolvedComponentsProps\n  }));\n  const ref = useForkRef(internalRef, resolvedComponentsProps == null ? void 0 : resolvedComponentsProps.ref, (_parameters$additiona = parameters.additionalProps) == null ? void 0 : _parameters$additiona.ref);\n  const props = appendOwnerState(elementType, _extends({}, mergedProps, {\n    ref\n  }), ownerState);\n  return props;\n}", "export function areArraysEqual(array1, array2, itemComparer = (a, b) => a === b) {\n  return array1.length === array2.length && array1.every((value, index) => itemComparer(value, array2[index]));\n}", "'use client';\n\nimport * as React from 'react';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst defaultContextValue = {\n  disableDefaultClasses: false\n};\nconst ClassNameConfiguratorContext = /*#__PURE__*/React.createContext(defaultContextValue);\nif (process.env.NODE_ENV !== 'production') {\n  ClassNameConfiguratorContext.displayName = 'ClassNameConfiguratorContext';\n}\n/**\n * @ignore - internal hook.\n *\n * Wraps the `generateUtilityClass` function and controls how the classes are generated.\n * Currently it only affects whether the classes are applied or not.\n *\n * @returns Function to be called with the `generateUtilityClass` function specific to a component to generate the classes.\n */\nexport function useClassNamesOverride(generateUtilityClass) {\n  const {\n    disableDefaultClasses\n  } = React.useContext(ClassNameConfiguratorContext);\n  return slot => {\n    if (disableDefaultClasses) {\n      return '';\n    }\n    return generateUtilityClass(slot);\n  };\n}\n\n/**\n * Allows to configure the components within to not apply any built-in classes.\n */\nexport function ClassNameConfigurator(props) {\n  const {\n    disableDefaultClasses,\n    children\n  } = props;\n  const contextValue = React.useMemo(() => ({\n    disableDefaultClasses: disableDefaultClasses != null ? disableDefaultClasses : false\n  }), [disableDefaultClasses]);\n  return /*#__PURE__*/_jsx(ClassNameConfiguratorContext.Provider, {\n    value: contextValue,\n    children: children\n  });\n}", "'use client';\n\nimport * as React from 'react';\n/**\n * @ignore - do not document.\n *\n * Use this function determine the host element correctly on the server (in a SSR context, for example Next.js)\n */\nexport function useRootElementName(parameters) {\n  const {\n    rootElementName: rootElementNameProp = '',\n    componentName\n  } = parameters;\n  const [rootElementName, setRootElementName] = React.useState(rootElementNameProp.toUpperCase());\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useEffect(() => {\n      if (rootElementNameProp && rootElementName !== rootElementNameProp.toUpperCase()) {\n        console.error(`useRootElementName: the \\`rootElementName\\` prop of ${componentName ? `the ${componentName} component` : 'a component'} expected the '${rootElementNameProp}' element, but a '${rootElementName.toLowerCase()}' was rendered instead`, 'This may cause hydration issues in an SSR context, for example in a Next.js app');\n      }\n    }, [rootElementNameProp, rootElementName, componentName]);\n  }\n  const updateRootElementName = React.useCallback(instance => {\n    var _instance$tagName;\n    setRootElementName((_instance$tagName = instance == null ? void 0 : instance.tagName) != null ? _instance$tagName : '');\n  }, []);\n  return [rootElementName, updateRootElementName];\n}", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"ownerState\"];\nimport * as React from 'react';\nexport function prepareForSlot(Component) {\n  return /*#__PURE__*/React.forwardRef(function Slot(props, ref) {\n    const other = _objectWithoutPropertiesLoose(props, _excluded);\n    return /*#__PURE__*/React.createElement(Component, _extends({}, other, {\n      ref\n    }));\n  });\n}"], "mappings": ";;;;;;;;;;;;;;;;;AAAe,SAAR,WAA4B;AACjC,aAAW,OAAO,SAAS,OAAO,OAAO,KAAK,IAAI,SAAU,QAAQ;AAClE,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAI,SAAS,UAAU,CAAC;AACxB,eAAS,OAAO,QAAQ;AACtB,YAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AACrD,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,SAAO,SAAS,MAAM,MAAM,SAAS;AACvC;;;ACVO,SAAS,gBAAgB,SAAS;AACvC,SAAO,OAAO,YAAY;AAC5B;;;ACWO,SAAS,iBAAiB,aAAa,YAAY,YAAY;AACpE,MAAI,gBAAgB,UAAa,gBAAgB,WAAW,GAAG;AAC7D,WAAO;AAAA,EACT;AACA,SAAO,SAAS,CAAC,GAAG,YAAY;AAAA,IAC9B,YAAY,SAAS,CAAC,GAAG,WAAW,YAAY,UAAU;AAAA,EAC5D,CAAC;AACH;;;ACvBe,SAAR,8BAA+C,QAAQ,UAAU;AACtE,MAAI,UAAU;AAAM,WAAO,CAAC;AAC5B,MAAI,SAAS,CAAC;AACd,WAAS,OAAO,QAAQ;AACtB,QAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AACrD,UAAI,SAAS,QAAQ,GAAG,KAAK;AAAG;AAChC,aAAO,GAAG,IAAI,OAAO,GAAG;AAAA,IAC1B;AAAA,EACF;AACA,SAAO;AACT;;;ACHO,SAAS,qBAAqB,QAAQ,cAAc,CAAC,GAAG;AAC7D,MAAI,WAAW,QAAW;AACxB,WAAO,CAAC;AAAA,EACV;AACA,QAAM,SAAS,CAAC;AAChB,SAAO,KAAK,MAAM,EAAE,OAAO,UAAQ,KAAK,MAAM,UAAU,KAAK,OAAO,OAAO,IAAI,MAAM,cAAc,CAAC,YAAY,SAAS,IAAI,CAAC,EAAE,QAAQ,UAAQ;AAC9I,WAAO,IAAI,IAAI,OAAO,IAAI;AAAA,EAC5B,CAAC;AACD,SAAO;AACT;;;ACTO,SAAS,kBAAkB,QAAQ;AACxC,MAAI,WAAW,QAAW;AACxB,WAAO,CAAC;AAAA,EACV;AACA,QAAM,SAAS,CAAC;AAChB,SAAO,KAAK,MAAM,EAAE,OAAO,UAAQ,EAAE,KAAK,MAAM,UAAU,KAAK,OAAO,OAAO,IAAI,MAAM,WAAW,EAAE,QAAQ,UAAQ;AAClH,WAAO,IAAI,IAAI,OAAO,IAAI;AAAA,EAC5B,CAAC;AACD,SAAO;AACT;;;ACCO,SAAS,eAAe,YAAY;AACzC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,CAAC,cAAc;AAGjB,UAAMA,iBAAgB,aAAK,mBAAmB,OAAO,SAAS,gBAAgB,WAAW,WAAW,0BAA0B,OAAO,SAAS,uBAAuB,WAAW,qBAAqB,OAAO,SAAS,kBAAkB,SAAS;AAChP,UAAMC,eAAc,SAAS,CAAC,GAAG,mBAAmB,OAAO,SAAS,gBAAgB,OAAO,0BAA0B,OAAO,SAAS,uBAAuB,OAAO,qBAAqB,OAAO,SAAS,kBAAkB,KAAK;AAC/N,UAAMC,SAAQ,SAAS,CAAC,GAAG,iBAAiB,wBAAwB,iBAAiB;AACrF,QAAIF,eAAc,SAAS,GAAG;AAC5B,MAAAE,OAAM,YAAYF;AAAA,IACpB;AACA,QAAI,OAAO,KAAKC,YAAW,EAAE,SAAS,GAAG;AACvC,MAAAC,OAAM,QAAQD;AAAA,IAChB;AACA,WAAO;AAAA,MACL,OAAAC;AAAA,MACA,aAAa;AAAA,IACf;AAAA,EACF;AAKA,QAAM,gBAAgB,qBAAqB,SAAS,CAAC,GAAG,wBAAwB,iBAAiB,CAAC;AAClG,QAAM,sCAAsC,kBAAkB,iBAAiB;AAC/E,QAAM,iCAAiC,kBAAkB,sBAAsB;AAC/E,QAAM,oBAAoB,aAAa,aAAa;AAMpD,QAAM,gBAAgB,aAAK,qBAAqB,OAAO,SAAS,kBAAkB,WAAW,mBAAmB,OAAO,SAAS,gBAAgB,WAAW,WAAW,0BAA0B,OAAO,SAAS,uBAAuB,WAAW,qBAAqB,OAAO,SAAS,kBAAkB,SAAS;AAClT,QAAM,cAAc,SAAS,CAAC,GAAG,qBAAqB,OAAO,SAAS,kBAAkB,OAAO,mBAAmB,OAAO,SAAS,gBAAgB,OAAO,0BAA0B,OAAO,SAAS,uBAAuB,OAAO,qBAAqB,OAAO,SAAS,kBAAkB,KAAK;AAC7R,QAAM,QAAQ,SAAS,CAAC,GAAG,mBAAmB,iBAAiB,gCAAgC,mCAAmC;AAClI,MAAI,cAAc,SAAS,GAAG;AAC5B,UAAM,YAAY;AAAA,EACpB;AACA,MAAI,OAAO,KAAK,WAAW,EAAE,SAAS,GAAG;AACvC,UAAM,QAAQ;AAAA,EAChB;AACA,SAAO;AAAA,IACL;AAAA,IACA,aAAa,kBAAkB;AAAA,EACjC;AACF;;;AChEO,SAAS,sBAAsB,gBAAgB,YAAY,WAAW;AAC3E,MAAI,OAAO,mBAAmB,YAAY;AACxC,WAAO,eAAe,YAAY,SAAS;AAAA,EAC7C;AACA,SAAO;AACT;;;ACLA,IAAM,YAAY,CAAC,eAAe,qBAAqB,cAAc,wBAAwB;AAatF,SAAS,aAAa,YAAY;AACvC,MAAI;AACJ,QAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA,yBAAyB;AAAA,EAC3B,IAAI,YACJ,OAAO,8BAA8B,YAAY,SAAS;AAC5D,QAAM,0BAA0B,yBAAyB,CAAC,IAAI,sBAAsB,mBAAmB,UAAU;AACjH,QAAM;AAAA,IACJ,OAAO;AAAA,IACP;AAAA,EACF,IAAI,eAAe,SAAS,CAAC,GAAG,MAAM;AAAA,IACpC,mBAAmB;AAAA,EACrB,CAAC,CAAC;AACF,QAAM,MAAM,WAAW,aAAa,2BAA2B,OAAO,SAAS,wBAAwB,MAAM,wBAAwB,WAAW,oBAAoB,OAAO,SAAS,sBAAsB,GAAG;AAC7M,QAAM,QAAQ,iBAAiB,aAAa,SAAS,CAAC,GAAG,aAAa;AAAA,IACpE;AAAA,EACF,CAAC,GAAG,UAAU;AACd,SAAO;AACT;;;ACtCO,SAAS,eAAe,QAAQ,QAAQ,eAAe,CAAC,GAAG,MAAM,MAAM,GAAG;AAC/E,SAAO,OAAO,WAAW,OAAO,UAAU,OAAO,MAAM,CAAC,OAAO,UAAU,aAAa,OAAO,OAAO,KAAK,CAAC,CAAC;AAC7G;;;ACAA,YAAuB;AACvB,yBAA4B;AAC5B,IAAM,sBAAsB;AAAA,EAC1B,uBAAuB;AACzB;AACA,IAAM,+BAAkD,oBAAc,mBAAmB;AACzF,IAAI,MAAuC;AACzC,+BAA6B,cAAc;AAC7C;AASO,SAAS,sBAAsB,sBAAsB;AAC1D,QAAM;AAAA,IACJ;AAAA,EACF,IAAU,iBAAW,4BAA4B;AACjD,SAAO,UAAQ;AACb,QAAI,uBAAuB;AACzB,aAAO;AAAA,IACT;AACA,WAAO,qBAAqB,IAAI;AAAA,EAClC;AACF;;;AC3BA,IAAAC,SAAuB;AAMhB,SAAS,mBAAmB,YAAY;AAC7C,QAAM;AAAA,IACJ,iBAAiB,sBAAsB;AAAA,IACvC;AAAA,EACF,IAAI;AACJ,QAAM,CAAC,iBAAiB,kBAAkB,IAAU,gBAAS,oBAAoB,YAAY,CAAC;AAC9F,MAAI,MAAuC;AAEzC,IAAM,iBAAU,MAAM;AACpB,UAAI,uBAAuB,oBAAoB,oBAAoB,YAAY,GAAG;AAChF,gBAAQ,MAAM,uDAAuD,gBAAgB,OAAO,aAAa,eAAe,aAAa,kBAAkB,mBAAmB,qBAAqB,gBAAgB,YAAY,CAAC,0BAA0B,iFAAiF;AAAA,MACzU;AAAA,IACF,GAAG,CAAC,qBAAqB,iBAAiB,aAAa,CAAC;AAAA,EAC1D;AACA,QAAM,wBAA8B,mBAAY,cAAY;AAC1D,QAAI;AACJ,wBAAoB,oBAAoB,YAAY,OAAO,SAAS,SAAS,YAAY,OAAO,oBAAoB,EAAE;AAAA,EACxH,GAAG,CAAC,CAAC;AACL,SAAO,CAAC,iBAAiB,qBAAqB;AAChD;;;ACxBA,IAAAC,SAAuB;", "names": ["joinedClasses", "mergedStyle", "props", "React", "React"]}