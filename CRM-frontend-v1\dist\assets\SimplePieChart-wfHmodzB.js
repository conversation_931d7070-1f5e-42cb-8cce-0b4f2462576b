import{j as e,av as l,ax as x,ay as d}from"./index-C8YzRPez.js";function h({chartData:i,title:o,onDetailsClick:s,labelMarginTop:n,showDetails:a}){const r={chart:{type:"pie",height:340,zooming:{type:"xy"},panning:{enabled:!0,type:"xy"},panKey:"shift"},title:{text:""},tooltip:{valueSuffix:"",headerFormat:"",pointFormat:"<b>{point.name}: {point.count}</b> "},subtitle:{text:""},plotOptions:{pie:{allowPointSelect:!0,cursor:"pointer",dataLabels:[{enabled:!1,distance:40,style:{fontFamily:"Poppins",fontSize:"14px",fontWeight:400}},{enabled:!0,distance:-40,format:"{point.percentage:.1f}%",style:{fontSize:"14px",fontFamily:"Poppins",textOutline:"none",opacity:.7}}]}},series:[{name:"Pie Chart",colorByPoint:!0,data:i}]};return e.jsxs("div",{style:{flexGrow:1},children:[e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",marginBottom:"10px"},children:[e.jsx("p",{style:{fontSize:"20px",fontWeight:600},children:o}),a&&e.jsxs("p",{style:{fontSize:"16px",fontWeight:400,color:"#489eea",cursor:"pointer",textDecoration:"underline",display:"flex",alignItems:"center",gap:"5px"},onClick:()=>s(),children:["View Details ",e.jsx(l,{icon:"material-symbols-light:arrow-forward-rounded",width:"20",height:"20"})]})]}),e.jsx(x,{highcharts:d,options:r}),e.jsx("div",{style:{display:"grid",gridTemplateColumns:"repeat(3, 1fr)",gap:"5px 1px",alignItems:"start",marginTop:n||"0px"},children:i.map((t,p)=>e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"6px"},children:[e.jsx("div",{style:{width:"12px",height:"12px",backgroundColor:t.color,borderRadius:"50%",flexShrink:0,marginTop:"2px"}}),e.jsxs("div",{style:{fontSize:"12px",fontFamily:"Poppins"},children:[t.name," ",e.jsxs("span",{style:{marginLeft:"2px"},children:["(",t.count,")"]})]})]},p))})]})}export{h as S};
