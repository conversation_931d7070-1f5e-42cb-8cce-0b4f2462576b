{"version": 3, "sources": ["../../@mui/material/utils/createChainedFunction.js", "../../@mui/material/utils/debounce.js", "../../@mui/material/utils/deprecatedPropType.js", "../../@mui/material/utils/ownerDocument.js", "../../@mui/material/utils/ownerWindow.js", "../../@mui/material/utils/setRef.js", "../../@mui/material/utils/index.js"], "sourcesContent": ["import createChainedFunction from '@mui/utils/createChainedFunction';\nexport default createChainedFunction;", "import debounce from '@mui/utils/debounce';\nexport default debounce;", "import deprecatedPropType from '@mui/utils/deprecatedPropType';\nexport default deprecatedPropType;", "import ownerDocument from '@mui/utils/ownerDocument';\nexport default ownerDocument;", "import ownerWindow from '@mui/utils/ownerWindow';\nexport default ownerWindow;", "import setRef from '@mui/utils/setRef';\nexport default setRef;", "'use client';\n\nimport { unstable_ClassNameGenerator as ClassNameGenerator } from '@mui/base/ClassNameGenerator';\nexport { default as capitalize } from './capitalize';\nexport { default as createChainedFunction } from './createChainedFunction';\nexport { default as createSvgIcon } from './createSvgIcon';\nexport { default as debounce } from './debounce';\nexport { default as deprecatedPropType } from './deprecatedPropType';\nexport { default as isMuiElement } from './isMuiElement';\nexport { default as ownerDocument } from './ownerDocument';\nexport { default as ownerWindow } from './ownerWindow';\nexport { default as requirePropFactory } from './requirePropFactory';\nexport { default as setRef } from './setRef';\nexport { default as unstable_useEnhancedEffect } from './useEnhancedEffect';\nexport { default as unstable_useId } from './useId';\nexport { default as unsupportedProp } from './unsupportedProp';\nexport { default as useControlled } from './useControlled';\nexport { default as useEventCallback } from './useEventCallback';\nexport { default as useForkRef } from './useForkRef';\nexport { default as useIsFocusVisible } from './useIsFocusVisible';\n// TODO: remove this export once ClassNameGenerator is stable\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport const unstable_ClassNameGenerator = {\n  configure: generator => {\n    if (process.env.NODE_ENV !== 'production') {\n      console.warn(['MUI: `ClassNameGenerator` import from `@mui/material/utils` is outdated and might cause unexpected issues.', '', \"You should use `import { unstable_ClassNameGenerator } from '@mui/material/className'` instead\", '', 'The detail of the issue: https://github.com/mui/material-ui/issues/30011#issuecomment-1024993401', '', 'The updated documentation: https://mui.com/guides/classname-generator/'].join('\\n'));\n    }\n    ClassNameGenerator.configure(generator);\n  }\n};"], "mappings": ";;;;;;;;;;;;;AACA,IAAO,gCAAQ;;;ACAf,IAAO,mBAAQ;;;ACAf,IAAO,6BAAQ;;;ACAf,IAAO,wBAAQ;;;ACAf,IAAO,sBAAQ;;;ACAf,IAAO,iBAAQ;;;ACqBR,IAAM,8BAA8B;AAAA,EACzC,WAAW,eAAa;AACtB,QAAI,MAAuC;AACzC,cAAQ,KAAK,CAAC,8GAA8G,IAAI,kGAAkG,IAAI,oGAAoG,IAAI,wEAAwE,EAAE,KAAK,IAAI,CAAC;AAAA,IACpa;AACA,+BAAmB,UAAU,SAAS;AAAA,EACxC;AACF;", "names": []}