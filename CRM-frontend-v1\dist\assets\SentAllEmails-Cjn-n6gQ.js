import{r as a,j as i,az as p,e as d,s as o}from"./index-C8YzRPez.js";import{E}from"./EmailPage-HZFzdEJj.js";import"./EmailDetails-oABVr-vf.js";import"./Checkbox-CbmcUeBk.js";import"./index-BWafDGV1.js";function S(c){const[m,e]=a.useState(!1),[n,t]=a.useState([]),r=()=>{try{e(!0),p({email:null,type:"SENT"},d.getAllEmailsByEmailType,s=>{t(s),e(!1)},s=>{var l;console.log("erdsdsadror",s),o.error(((l=s==null?void 0:s.response)==null?void 0:l.message)||"Failed to get emails. Try again!"),e(!1)})}catch{o.error("Failed to get emails. Try again!"),e(!1)}};return a.useEffect(()=>{r()},[]),i.jsx("div",{children:i.jsx(E,{emails:n,onEmailsUpdate:s=>t(s),isLoading:m,type:"Sent"})})}export{S as default};
