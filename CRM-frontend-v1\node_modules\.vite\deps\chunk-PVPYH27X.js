import {
  useEventCallback_default,
  useIsFocusVisible
} from "./chunk-KN4E4F3G.js";

// node_modules/@mui/material/utils/useEventCallback.js
var useEventCallback_default2 = useEventCallback_default;

// node_modules/@mui/material/utils/useIsFocusVisible.js
var useIsFocusVisible_default = useIsFocusVisible;

export {
  useEventCallback_default2 as useEventCallback_default,
  useIsFocusVisible_default
};
//# sourceMappingURL=chunk-PVPYH27X.js.map
