import{aM as pe,r as l,j as t,a6 as he,B as ue,a7 as te,R as xe,aI as re,aN as me,aO as ge,aP as fe,aQ as Ce,aR as Y,aS as c,aT as be,av as z,aU as ae,aJ as le,aV as je,aW as Se,C as ye,aX as Te,ae as ke,az as Ie,e as oe,s as _,H as _e,a4 as Le,a2 as ee,d as Ee,aY as v,aZ as Pe}from"./index-C8YzRPez.js";import{V as Ne,S as De}from"./SendCandidateToClient-Ck2C0lTM.js";import{C as ce}from"./CVFormatWithJSPDF-DAO_IRU3.js";import{C as de}from"./Checkbox-CbmcUeBk.js";import"./Details-CjVzTy9y.js";import"./styles-Bufj3DzR.js";import{F as Ae}from"./FileSaver.min-Cr1ZWOZa.js";import"./TextEditor-DytKcPMl.js";import"./index-BWafDGV1.js";import"./CustomSelectForForms-BI2fpcGx.js";import"./CloseOutlined-CK98qAnm.js";import"./FileUpload-CLRSAav9.js";import"./MenuItem--Pxpzvee.js";import"./index-CV2s8tJR.js";import"./PrimaryButton-BOAlYSuK.js";import"./FocusPointBox-BS774mbV.js";import"./FilePreview-Dz6aRE4w.js";import"./index-C2EZYIAX.js";import"./useClosable-D2fLMMij.js";const Fe="/assets/LinkedIn_logo-Cab6NH-t.png";function Re({buttons:L,onButtonClick:T}){const A=pe(),[s,g]=l.useState(0);return t.jsx("div",{children:t.jsx(he,{sx:{width:{xs:"100%",sm:"100%",md:"100%",lg:"100%",xl:"100%"},display:"flex"},children:L==null?void 0:L.map((o,r)=>{const p=s===r;return t.jsxs(ue,{variant:"outlined",sx:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",borderRadius:0,padding:4,height:75,flexGrow:1,margin:.5,background:p?"#f9f9ff":"white",border:"none",borderTop:p?`5px solid ${o.borderTop}`:"5px solid transparent",borderLeft:"1px solid #E6EAEF",borderRight:"1px solid #E6EAEF",borderBottom:"1px solid #E6EAEF","&:hover":{backgroundColor:p?"#f9f9ff":"#f5f5f5",borderTop:p?`5px solid ${o.borderTop}`:"5px solid transparent"},"&:focus, &:active":{boxShadow:"none"},"& .MuiTouchRipple-root":{color:o.borderTop}},onClick:()=>{T(o==null?void 0:o.buttonText),g(r)},children:[t.jsx("p",{style:{fontSize:"15px",color:p?"black":"#27364F",margin:0},children:o==null?void 0:o.value}),t.jsx("p",{style:{fontSize:"14px",margin:0,color:p?"black":A.palette.text.primary,fontWeight:500},children:o==null?void 0:o.buttonText})]},r)})})})}const Z=[{id:"select",label:"",minWidth:100},{id:"actions",label:"",minWidth:20},{id:"name",label:"Name",minWidth:120},{id:"hiringPipeline",label:"Hiring Pipeline",minWidth:170},{id:"canStatus",label:"Can.Status",minWidth:100},{id:"currentPosition",label:"Current Position",minWidth:150},{id:"currentEmployee",label:"Current Employer",minWidth:150},{id:"email",label:"Email",minWidth:200},{id:"mobile",label:"Mobile",minWidth:100},{id:"resourcer",label:"Resourcer",minWidth:100}],We={new:!0,screening:!1,submission:!1,rejected:!1,interview:!1,offered:!1,hired:!1};function Be({onSelectionChange:L,candidates:T,isLoading:A}){const s=Array.isArray(T)?T:[];te(e=>e.user.userDesignation);const[g,o]=l.useState(0),[r,p]=l.useState(10),[x,E]=l.useState([]),[b,j]=l.useState(),V=e=>{E(d=>d!=null&&d.includes(e)?d.filter(N=>N!==e):[...d,e])},F=e=>{e.target.checked?E(s==null?void 0:s.map(d=>d.id)):E([])},q=e=>{o(e-1)},J=(e,d)=>{p(d),o(0)};xe.useEffect(()=>{const e=s.filter(d=>x==null?void 0:x.includes(d.id));L(e)},[x]);const[U,R]=l.useState(!1),[w,W]=l.useState(!1),f=s==null?void 0:s.slice(g*r,g*r+r),[k,P]=l.useState("");return t.jsxs("div",{children:[t.jsx(re,{style:{marginTop:"20px",marginBottom:"20px"},children:t.jsxs(me,{style:{width:"100%"},children:[t.jsx(ge,{sx:{maxHeight:900,overflowX:"auto",overflowY:"auto","&::-webkit-scrollbar":{width:"5px",height:"5px"},"&::-webkit-scrollbar-thumb":{backgroundColor:"#d9d9d9",borderRadius:"4px"},"&::-webkit-scrollbar-thumb:hover":{backgroundColor:"#cccccc"},"&::-webkit-scrollbar-track":{backgroundColor:"#f0f0f0"}},children:t.jsxs(fe,{stickyHeader:!0,children:[t.jsx(Ce,{children:t.jsxs(Y,{children:[t.jsx(c,{style:{backgroundColor:"white"},children:t.jsx(de,{checked:x.length===(s==null?void 0:s.length),indeterminate:x.length>0&&x.length<(s==null?void 0:s.length),onChange:F})}),Z==null?void 0:Z.slice(1).map(e=>t.jsx(c,{style:{minWidth:e.minWidth,fontWeight:600,backgroundColor:"white",textTransform:"none",textAlign:"center",color:"#676C7E"},children:e.label},e.id))]})}),t.jsx(be,{children:(f==null?void 0:f.length)>0?t.jsx(t.Fragment,{children:f==null?void 0:f.map(e=>{var d,N,$,K,B,M,I,m,H,n,i;return t.jsx(t.Fragment,{children:t.jsxs(Y,{children:[t.jsx(c,{children:t.jsx(de,{checked:x==null?void 0:x.includes(e==null?void 0:e.id),onChange:()=>V(e==null?void 0:e.id)})}),t.jsx(c,{children:(e==null?void 0:e.profile_source)==="LINKEDIN"?t.jsx("img",{src:Fe,alt:"CV",style:{width:"30px",height:"30px",borderRadius:"50%",cursor:"pointer"},onClick:()=>{R(!0),j(e),P(e.li_type)}}):t.jsx(z,{icon:"pepicons-pencil:cv-circle",width:"20",height:"20",style:{cursor:"pointer"},onClick:()=>{console.log("row",e),(e==null?void 0:e.source_type)!=="CV"?(R(!0),j(e),console.log("row",e),P(e.li_type)):(W(!0),j(e))}})}),t.jsx(c,{sx:{fontSize:"12px",display:"flex",alignItems:"center"},children:(()=>{var X,a,u,S,y,C,ne,se,ie;const h=(s==null?void 0:s.indexOf(e))%ae.length,D=((u=(a=(X=e==null?void 0:e.candidate)==null?void 0:X.first_name)==null?void 0:a.charAt(0))==null?void 0:u.toUpperCase())||"",O=((C=(y=(S=e==null?void 0:e.candidate)==null?void 0:S.last_name)==null?void 0:y.charAt(0))==null?void 0:C.toUpperCase())||"",G=`${((ne=e==null?void 0:e.candidate)==null?void 0:ne.first_name)||""} ${((se=e==null?void 0:e.candidate)==null?void 0:se.last_name)||""}`.trim(),Q=(ie=e==null?void 0:e.candidate)==null?void 0:ie.profile_img;return t.jsxs(t.Fragment,{children:[Q?t.jsx(le,{alt:G,src:Q,sx:{width:34,height:34}}):t.jsx(le,{sx:{backgroundColor:ae[h],color:je[h],fontWeight:600,width:34,height:34,textAlign:"center",borderRadius:"50%"},children:D+O}),t.jsx("p",{style:{marginLeft:"10px"},children:G||"N/A"})]})})()}),t.jsx(c,{sx:{fontSize:"12px",textAlign:"center"},children:t.jsx(Se,{pipeline:We})}),t.jsx(c,{sx:{fontSize:"12px",textAlign:"center"},children:(d=e==null?void 0:e.candidate)!=null&&d.is_accepted?"Approved":"Not Approved"}),t.jsx(c,{sx:{fontSize:"12px",textAlign:"center"},children:(N=e==null?void 0:e.candidate)!=null&&N.current_title?($=e==null?void 0:e.candidate)==null?void 0:$.current_title:"N/A"}),t.jsx(c,{sx:{fontSize:"12px",textAlign:"center"},children:e!=null&&e.currentEmployee?e==null?void 0:e.currentEmployee:"N/A"}),t.jsx(c,{sx:{fontSize:"12px",textAlign:"center"},children:((K=e==null?void 0:e.candidate)==null?void 0:K.emails.length)>0?(M=(B=e==null?void 0:e.candidate)==null?void 0:B.emails[0])==null?void 0:M.email:"N/A"}),t.jsx(c,{sx:{fontSize:"12px",textAlign:"center"},children:((I=e==null?void 0:e.candidate)==null?void 0:I.phones.length)>0?(H=(m=e==null?void 0:e.candidate)==null?void 0:m.phones[0])==null?void 0:H.phone_number:"N/A"}),t.jsx(c,{sx:{fontSize:"12px",textAlign:"center"},children:e!=null&&e.user?((n=e==null?void 0:e.user)==null?void 0:n.first_name)+" "+((i=e==null?void 0:e.user)==null?void 0:i.last_name):"N/A"})]},e==null?void 0:e.id)})})}):A?t.jsx(Y,{children:t.jsxs(c,{colSpan:12,style:{textAlign:"center"},children:["Loading Data... ",t.jsx(ye,{size:20})]})}):t.jsx(Y,{children:t.jsx(c,{colSpan:Z.length,style:{textAlign:"center"},children:"No candidates found."})})})]})}),t.jsx(Te,{current:g+1,total:s==null?void 0:s.length,pageSize:r,onChange:q,onShowSizeChange:J,showSizeChanger:!0,showTotal:()=>{const e=g*r+1,d=Math.min((g+1)*r,s==null?void 0:s.length);return`${e}-${d} of ${s==null?void 0:s.length} items`},style:{marginTop:"10px",display:"flex",justifyContent:"flex-end",marginBottom:"10px",marginRight:"10px"}})]})}),U&&t.jsx(ce,{open:U,onClose:()=>R(!1),candidate:b==null?void 0:b.candidate,downloadLiAsPdf:!1,liType:k}),w&&t.jsx(Ne,{open:w,onClose:()=>W(!1),fileURL:b==null?void 0:b.profile_url})]})}function nt(L){const T=te(n=>n.user.userDesignation),A=te(n=>n.user.userId),[s,g]=l.useState([]),[o,r]=l.useState(!1),[p,x]=l.useState([]),{roleId:E}=ke(),[b,j]=l.useState(!1),[V,F]=l.useState(!1),[q,J]=l.useState(),[U,R]=l.useState(""),[w,W]=l.useState([]),[f,k]=l.useState(null),P=Array.isArray(p)?p:[],e=P.reduce((n,i)=>{var D;const h=((D=i.profile_source)==null?void 0:D.toUpperCase())||"UNKNOWN";return n[h]=(n[h]||0)+1,n},{LINKEDIN:0,JOB_BOARD:0,JOB_POSTING:0,TOTAL:P.length||0}),d=[{buttonText:"Total",value:e.TOTAL,borderTop:"#F688F2",buttonFontSize:"22px"},{buttonText:"Job Board",value:e.TOTAL-e.LINKEDIN,borderTop:"#A4B0DB",buttonFontSize:"22px"},{buttonText:"LinkedIn",value:e.LINKEDIN,borderTop:"#FF9999",buttonFontSize:"22px"},{buttonText:"Job Posting",value:e.JOB_POSTING,borderTop:"#EBB794",buttonFontSize:"22px"}],N=l.useCallback(()=>{console.log("kjsdgfjisdhfsdf",f);try{j(!0),Ie({roleId:E},oe.getRoleCandidates,n=>{x(n),j(!1)},n=>{var i;_.error(((i=n==null?void 0:n.response)==null?void 0:i.message)||"Failed to get role candidates. Try refreshing the page!"),j(!1)})}catch{_.error("Failed to get role candidates. Try refreshing the page!"),j(!1)}},[E,A,f]);l.useEffect(()=>{N()},[f]);const $=n=>{try{Ee(n,oe.sendEmailWithAttachments,i=>{_.success("Candidates sent successfully!"),r(!1)},i=>{var h;_.error(((h=i==null?void 0:i.response)==null?void 0:h.message)||"Failed to send candidates. Try again!")})}catch{_.error("Failed to send candidates. Try again!")}},K=async()=>{const n=s.filter(i=>i.profile_source==="LINKEDIN");if(n.length===0){_.warning("No LinkedIn candidates selected.");return}W(n),B(n[0])},B=n=>{n&&(J(n==null?void 0:n.candidate),R(n.li_type),F(!0))},M=()=>{F(!1),setTimeout(()=>{W(n=>{const i=n.slice(1),h=i[0];return h?B(h):(_.success("All LinkedIn PDFs downloaded!"),g([]),J(null)),i})},500)},I=n=>(n||[]).map(i=>`${i}`.trim()).filter(i=>i&&i.toLowerCase()!=="undefined"&&i.toLowerCase()!=="null").join(", "),m=n=>{if(!n)return"";const i=`${n}`.trim();return i.toLowerCase()==="undefined"||i.toLowerCase()==="null"?"":i},H=()=>{const n=p.filter(a=>a.profile_source==="LINKEDIN"),i=p.filter(a=>a.source_type==="CV"),h=a=>{var S,y;const u=(a==null?void 0:a.candidate)||a;return{Status:m(a.li_type),"Candidate Linkedin URL":m(u.profile_url),"Personal Number":I((S=u.phones)==null?void 0:S.map(C=>C.phone_number)),"Personal Email":I((y=u.emails)==null?void 0:y.map(C=>C.email))}},D=a=>{var S,y;const u=(a==null?void 0:a.candidate)||a;return{"Candidate Name":`${m(u.first_name)} ${m(u.last_name)}`.trim(),Email:I((S=u.emails)==null?void 0:S.map(C=>C.email)),Number:I((y=u.phones)==null?void 0:y.map(C=>C.phone_number)),Location:m(u.location),Miles:m(a.radius_miles),"Job Board":m(u.profile_source),"Last active":m(a.months_back),"Minimum Salary":m(a.salary)}},O=v.book_new(),G=v.json_to_sheet(n.map(h));v.book_append_sheet(O,G,"LinkedIn Profiles");const Q=v.json_to_sheet(i.map(D));v.book_append_sheet(O,Q,"CVs");const X=Pe(O,{bookType:"xlsx",type:"array"});Ae.saveAs(new Blob([X],{type:"application/octet-stream"}),"Candidate_Sheet.xlsx")};return t.jsxs("div",{style:{width:"100%",padding:1.5},children:[t.jsx(_e,{children:t.jsx("title",{children:"All Candidates"})}),t.jsx(Re,{buttons:d,onButtonClick:n=>{k(n==="Total"?null:n==="Job Board"?"JOB_BOARD":n==="LinkedIn"?"LINKEDIN":n==="Job Posting"?"JOB_POSTING":null)}}),t.jsx(re,{style:{marginTop:"20px",display:"flex",justifyContent:"space-between"},children:t.jsxs("div",{style:{display:"flex",gap:"15px",justifyContent:"flex-start",alignItems:"center",width:"70%"},children:[t.jsx(Le,{variant:"borderless",placeholder:"Search name",style:{backgroundColor:"white",width:"30%",borderRadius:0,height:"40px"},suffix:t.jsx(z,{icon:"ant-design:search-outlined",width:"18",height:"18",style:{cursor:"pointer"}})}),s.length>0&&(T==="BUSINESS_DEVELOPMENT_EXECUTIVE"||T==="ACCOUNT_EXECUTIVE")&&t.jsxs(t.Fragment,{children:[t.jsxs("p",{style:{fontSize:"14px",fontWeight:500,fontFamily:"Poppins"},children:[s.length," selected"]}),t.jsx(ee,{type:"primary",icon:t.jsx(z,{icon:"fluent:send-32-regular",width:"16",height:"16",style:{cursor:"pointer",marginLeft:"5px"}}),iconPosition:"end",style:{borderRadius:0,height:"40px",fontSize:"16px",backgroundColor:"white",fontWeight:400,color:"black",fontFamily:"Poppins"},onClick:()=>{r(!0)},children:"Send"}),t.jsx(ee,{type:"primary",icon:t.jsx(z,{icon:"material-symbols:download-rounded",width:"16",height:"16",style:{cursor:"pointer",marginLeft:"5px"}}),iconPosition:"end",style:{borderRadius:0,height:"40px",fontSize:"16px",backgroundColor:"white",fontWeight:400,color:"black",fontFamily:"Poppins"},onClick:()=>{K()},children:"Download All LIs"}),t.jsx(ee,{type:"primary",icon:t.jsx(z,{icon:"mdi:excel",width:"16",height:"16"}),iconPosition:"end",style:{borderRadius:0,height:"40px",fontSize:"16px",backgroundColor:"white",fontWeight:400,color:"black",fontFamily:"Poppins"},onClick:H,children:"Download Candidate Sheet"})]})]})}),t.jsx(Be,{onSelectionChange:n=>{console.log("onSelectionChange",n),g(n)},candidates:P,isLoading:b}),o&&t.jsx(De,{open:o,onClose:()=>r(!1),selectedCandidates:s,onSubmit:n=>{$(n)}}),V&&t.jsx(ce,{open:V,onClose:()=>F(!1),candidate:q,downloadLiAsPdf:!0,liType:U,onDownloadComplete:M})]})}export{nt as default};
