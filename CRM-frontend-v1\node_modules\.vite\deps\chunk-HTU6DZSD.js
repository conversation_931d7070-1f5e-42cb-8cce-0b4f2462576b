import {
  require_react
} from "./chunk-XLKA4T3M.js";
import {
  __toESM
} from "./chunk-WXXH56N5.js";

// node_modules/@mui/material/List/ListContext.js
var React = __toESM(require_react());
var ListContext = React.createContext({});
if (true) {
  ListContext.displayName = "ListContext";
}
var ListContext_default = ListContext;

export {
  ListContext_default
};
//# sourceMappingURL=chunk-HTU6DZSD.js.map
